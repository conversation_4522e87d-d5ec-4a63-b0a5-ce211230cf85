<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_number',
        'order_date',
        'total_amount',
        'status',
        'payment_method',
        'payment_method_detail',
        'bnpl_transaction_id',
    ];

    protected $casts = [
        'order_date' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the user that owns the order.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the BNPL transaction associated with the order.
     */
    public function bnplTransaction()
    {
        return $this->hasOne(BnplTransaction::class);
    }
}

