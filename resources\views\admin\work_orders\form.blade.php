@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($workOrder) ? __("admin_views.edit_work_order") : __("admin_views.add_new_work_order") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    @if(session("error"))
        <div class="alert alert-danger">
            {{ session("error") }}
        </div>
    @endif

    <form action="{{ isset($workOrder) ? route("admin.work_orders.update", $workOrder->id) : route("admin.work_orders.store") }}" method="POST">
        @csrf
        @if(isset($workOrder))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="work_order_code">{{ __("admin_views.work_order_code") }}</label>
                    <input type="text" name="work_order_code" id="work_order_code" class="form-control" value="{{ old("work_order_code", $workOrder->work_order_code ?? ($newWorkOrderCode ?? 
oreply)) }}" {{ isset($workOrder) ? "readonly" : "" }}>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="item_id">{{ __("admin_views.item_to_produce") }} <span class="text-danger">*</span></label>
                    <select name="item_id" id="item_id" class="form-control" required {{ isset($workOrder) && $workOrder->status !== "planned" ? "disabled" : "" }}>
                        <option value="">{{ __("admin_views.select_item") }}</option>
                        @foreach($manufacturableItems as $item)
                            <option value="{{ $item->id }}" data-boms=\'{{ json_encode($item->boms()->where("is_active", true)->get(["id", "name_ar", "name_en"])) }}\' {{ old("item_id", $workOrder->item_id ?? "") == $item->id ? "selected" : "" }}>
                                {{ $item->name_ar }} ({{ $item->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="bom_id">{{ __("admin_views.bom") }} <span class="text-danger">*</span></label>
                    <select name="bom_id" id="bom_id" class="form-control" required {{ isset($workOrder) && $workOrder->status !== "planned" ? "disabled" : "" }}>
                        <option value="">{{ __("admin_views.select_bom_after_item") }}</option>
                         @if(isset($workOrder) && $workOrder->bom)
                            <option value="{{ $workOrder->bom_id }}" selected>{{ $workOrder->bom->name_ar }} ({{ $workOrder->bom->name_en }})</option>
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="quantity_to_produce">{{ __("admin_views.quantity_to_produce") }} <span class="text-danger">*</span></label>
                    <input type="number" step="0.0001" name="quantity_to_produce" id="quantity_to_produce" class="form-control" value="{{ old("quantity_to_produce", $workOrder->quantity_to_produce ?? "1.0000") }}" required {{ isset($workOrder) && $workOrder->status !== "planned" ? "readonly" : "" }}>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="branch_id">{{ __("admin_views.branch") }} <span class="text-danger">*</span></label>
                    <select name="branch_id" id="branch_id" class="form-control" required {{ isset($workOrder) && $workOrder->status !== "planned" ? "disabled" : "" }}>
                        <option value="">{{ __("admin_views.select_branch") }}</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ old("branch_id", $workOrder->branch_id ?? Auth::user()->branch_id ?? "") == $branch->id ? "selected" : "" }}>
                                {{ $branch->name_ar }} ({{ $branch->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="start_date">{{ __("admin_views.start_date") }}</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" value="{{ old("start_date", isset($workOrder) && $workOrder->start_date ? $workOrder->start_date->format("Y-m-d") : date("Y-m-d")) }}" {{ isset($workOrder) && $workOrder->status !== "planned" ? "readonly" : "" }}>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="end_date">{{ __("admin_views.end_date") }}</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" value="{{ old("end_date", isset($workOrder) && $workOrder->end_date ? $workOrder->end_date->format("Y-m-d") : date("Y-m-d")) }}" {{ isset($workOrder) && $workOrder->status !== "planned" ? "readonly" : "" }}>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="notes">{{ __("admin_views.notes") }}</label>
            <textarea name="notes" id="notes" class="form-control">{{ old("notes", $workOrder->notes ?? "") }}</textarea>
        </div>

        @if(isset($workOrder) && ($workOrder->status == "in_progress" || $workOrder->status == "completed" || $workOrder->status == "closed"))
            <h4>{{ __("admin_views.component_items_consumed") }}</h4>
            <table class="table table-sm table-bordered">
                <thead>
                    <tr>
                        <th>{{ __("admin_views.component_item") }}</th>
                        <th>{{ __("admin_views.required_quantity") }}</th>
                        <th>{{ __("admin_views.actual_consumed_quantity") }}</th>
                        <th>{{ __("admin_views.unit_of_measure_ar") }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($workOrder->workOrderItems as $woItem)
                        <tr>
                            <td>{{ $woItem->item->name_ar }}</td>
                            <td>{{ number_format($woItem->quantity_required, 4) }}</td>
                            <td>
                                @if($workOrder->status == "in_progress")
                                <input type="number" step="0.0001" name="consumed_items[{{ $woItem->id }}][quantity_consumed]" class="form-control form-control-sm" value="{{ old("consumed_items.{$woItem->id}.quantity_consumed", $woItem->quantity_consumed ?? $woItem->quantity_required) }}">
                                @else
                                {{ number_format($woItem->quantity_consumed, 4) }}
                                @endif
                            </td>
                            <td>{{ $woItem->unit_of_measure_ar ?? $woItem->item->unit_of_measure_ar }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            @if($workOrder->status == "in_progress")
            <div class="form-group">
                <label for="quantity_produced_so_far">{{ __("admin_views.quantity_produced_so_far") }}</label>
                <input type="number" step="0.0001" name="quantity_produced_so_far" id="quantity_produced_so_far" class="form-control" value="{{ old("quantity_produced_so_far", $workOrder->quantity_produced_so_far ?? "0.0000") }}">
            </div>
            @endif
        @endif

        @if(isset($workOrder) && $workOrder->status == "completed")
             <div class="form-group">
                <label for="actual_quantity_produced">{{ __("admin_views.actual_quantity_produced") }} <span class="text-danger">*</span></label>
                <input type="number" step="0.0001" name="actual_quantity_produced" id="actual_quantity_produced" class="form-control" value="{{ old("actual_quantity_produced", $workOrder->actual_quantity_produced ?? $workOrder->quantity_to_produce) }}" required>
            </div>
        @endif

        <div class="mt-3">
            @if(!isset($workOrder) || $workOrder->status == "planned")
                <button type="submit" class="btn btn-success">{{ isset($workOrder) ? __("admin_views.update_work_order") : __("admin_views.save_work_order") }}</button>
            @endif
            
            @if(isset($workOrder) && $workOrder->status == "planned")
                <button type="submit" name="action" value="start_production" class="btn btn-primary">{{ __("admin_views.start_production") }}</button>
            @endif

            @if(isset($workOrder) && $workOrder->status == "in_progress")
                <button type="submit" name="action" value="complete_production" class="btn btn-info">{{ __("admin_views.complete_production") }}</button>
            @endif

            @if(isset($workOrder) && $workOrder->status == "completed")
                <button type="submit" name="action" value="close_work_order" class="btn btn-dark">{{ __("admin_views.close_work_order_and_post_costs") }}</button>
            @endif

            <a href="{{ route("admin.work_orders.index") }}" class="btn btn-secondary">{{ __("admin_views.cancel") }}</a>
        </div>
    </form>
</div>
@endsection

@push("scripts")
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const itemIdSelect = document.getElementById("item_id");
        const bomIdSelect = document.getElementById("bom_id");
        const defaultBomId = "{{ old("bom_id", $workOrder->bom_id ?? "") }}";

        function populateBoms() {
            const selectedOption = itemIdSelect.options[itemIdSelect.selectedIndex];
            const bomsData = selectedOption.dataset.boms ? JSON.parse(selectedOption.dataset.boms) : [];
            
            bomIdSelect.innerHTML = `<option value="">{{ __("admin_views.select_bom") }}</option>`; // Clear existing options

            if (bomsData.length > 0) {
                bomsData.forEach(bom => {
                    const option = document.createElement("option");
                    option.value = bom.id;
                    option.textContent = `${bom.name_ar} (${bom.name_en})`;
                    if (bom.id == defaultBomId || (bomsData.length === 1 && !defaultBomId) || (bom.is_default && !defaultBomId) ) {
                        option.selected = true;
                    }
                    bomIdSelect.appendChild(option);
                });
            } else {
                 bomIdSelect.innerHTML = `<option value="">{{ __("admin_views.no_boms_for_this_item") }}</option>`;
            }
        }

        if (itemIdSelect.value) {
            populateBoms();
        }

        itemIdSelect.addEventListener("change", populateBoms);
    });
</script>
@endpush

