<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0"><?php echo e(__("Menu Items")); ?></h1>
                <a href="<?php echo e(route('admin.restaurant.menu-items.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__("Add New Item")); ?>

                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($menuItems->total()); ?></h4>
                            <p class="mb-0"><?php echo e(__("Total Items")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-utensils fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($menuItems->where('is_available', true)->count()); ?></h4>
                            <p class="mb-0"><?php echo e(__("Available Items")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($menuItems->where('is_available', false)->count()); ?></h4>
                            <p class="mb-0"><?php echo e(__("Unavailable Items")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e(number_format($menuItems->avg('price'), 2)); ?></h4>
                            <p class="mb-0"><?php echo e(__("Average Price")); ?> (ريال)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Menu Items List")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($menuItems->count() > 0): ?>
                        <div class="row">
                            <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card menu-item-card h-100">
                                        <?php if($item->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                                                 class="card-img-top" 
                                                 alt="<?php echo e($item->name); ?>"
                                                 style="height: 200px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                                 style="height: 200px;">
                                                <i class="fas fa-utensils fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo e($item->name); ?></h6>
                                                <div class="item-badges">
                                                    <?php if($item->is_spicy): ?>
                                                        <span class="badge bg-danger" title="<?php echo e(__('Spicy')); ?>">🌶️</span>
                                                    <?php endif; ?>
                                                    <?php if($item->is_vegetarian): ?>
                                                        <span class="badge bg-success" title="<?php echo e(__('Vegetarian')); ?>">🥬</span>
                                                    <?php endif; ?>
                                                    <?php if($item->is_vegan): ?>
                                                        <span class="badge bg-success" title="<?php echo e(__('Vegan')); ?>">🌱</span>
                                                    <?php endif; ?>
                                                    <?php if($item->is_gluten_free): ?>
                                                        <span class="badge bg-info" title="<?php echo e(__('Gluten Free')); ?>">GF</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <p class="card-text text-muted small"><?php echo e(Str::limit($item->description, 80)); ?></p>
                                            
                                            <div class="mb-2">
                                                <span class="badge bg-secondary"><?php echo e($item->category->name ?? __('No Category')); ?></span>
                                                <?php if($item->kitchenStation): ?>
                                                    <span class="badge bg-info"><?php echo e($item->kitchenStation->name); ?></span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div>
                                                    <strong class="text-primary"><?php echo e(number_format($item->price, 2)); ?> ريال</strong>
                                                    <?php if($item->cost_price): ?>
                                                        <small class="text-muted d-block"><?php echo e(__('Cost')); ?>: <?php echo e(number_format($item->cost_price, 2)); ?> ريال</small>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-end">
                                                    <?php if($item->preparation_time): ?>
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock"></i> <?php echo e($item->preparation_time); ?><?php echo e(__('min')); ?>

                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <?php if($item->is_available): ?>
                                                        <span class="badge bg-success"><?php echo e(__("Available")); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger"><?php echo e(__("Unavailable")); ?></span>
                                                    <?php endif; ?>
                                                    
                                                    <?php if(!$item->is_active): ?>
                                                        <span class="badge bg-secondary"><?php echo e(__("Inactive")); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.restaurant.menu-items.show', $item)); ?>" 
                                                       class="btn btn-sm btn-outline-info" title="<?php echo e(__('View')); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.restaurant.menu-items.edit', $item)); ?>" 
                                                       class="btn btn-sm btn-outline-warning" title="<?php echo e(__('Edit')); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <!-- Availability Toggle -->
                                                    <button class="btn btn-sm <?php echo e($item->is_available ? 'btn-outline-danger' : 'btn-outline-success'); ?>" 
                                                            onclick="toggleAvailability(<?php echo e($item->id); ?>, <?php echo e($item->is_available ? 'false' : 'true'); ?>)"
                                                            title="<?php echo e($item->is_available ? __('Mark Unavailable') : __('Mark Available')); ?>">
                                                        <i class="fas <?php echo e($item->is_available ? 'fa-times' : 'fa-check'); ?>"></i>
                                                    </button>
                                                    
                                                    <form action="<?php echo e(route('admin.restaurant.menu-items.destroy', $item)); ?>" 
                                                          method="POST" style="display: inline-block;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                title="<?php echo e(__('Delete')); ?>"
                                                                onclick="return confirm('<?php echo e(__('Are you sure you want to delete this menu item?')); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($menuItems->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo e(__("No menu items found")); ?></h5>
                            <p class="text-muted"><?php echo e(__("Start by creating your first menu item.")); ?></p>
                            <a href="<?php echo e(route('admin.restaurant.menu-items.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> <?php echo e(__("Add First Item")); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.menu-item-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
}

.menu-item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.item-badges .badge {
    margin-left: 2px;
}

.card-img-top {
    transition: transform 0.3s;
}

.menu-item-card:hover .card-img-top {
    transform: scale(1.05);
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleAvailability(itemId, isAvailable) {
    fetch(`/admin/restaurant/menu-items/${itemId}/availability`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ is_available: isAvailable })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('<?php echo e(__("Error updating item availability")); ?>');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('<?php echo e(__("Error updating item availability")); ?>');
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/restaurant/menu-items/index.blade.php ENDPATH**/ ?>