<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_items', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar'); // الاسم بالعربية
            $table->string('name_en'); // الاسم بالإنجليزية
            $table->text('description_ar')->nullable(); // الوصف بالعربية
            $table->text('description_en')->nullable(); // الوصف بالإنجليزية
            $table->string('sku')->nullable()->unique(); // رمز المنتج
            $table->string('barcode')->nullable(); // الباركود
            $table->decimal('price', 10, 2); // السعر
            $table->decimal('cost_price', 10, 2)->default(0); // سعر التكلفة
            $table->decimal('tax_rate', 5, 2)->default(0); // معدل الضريبة
            $table->string('image')->nullable(); // صورة الصنف
            $table->json('images')->nullable(); // صور إضافية
            $table->integer('preparation_time')->default(0); // وقت التحضير بالدقائق
            $table->integer('calories')->nullable(); // السعرات الحرارية
            $table->json('allergens')->nullable(); // مسببات الحساسية
            $table->json('ingredients')->nullable(); // المكونات
            $table->boolean('is_spicy')->default(false); // حار
            $table->boolean('is_vegetarian')->default(false); // نباتي
            $table->boolean('is_vegan')->default(false); // نباتي صرف
            $table->boolean('is_gluten_free')->default(false); // خالي من الجلوتين
            $table->boolean('is_available')->default(true); // متاح للطلب
            $table->boolean('is_active')->default(true);
            $table->json('availability_schedule')->nullable(); // جدول التوفر
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->foreignId('category_id')->constrained('menu_categories')->onDelete('cascade');
            $table->foreignId('kitchen_station_id')->nullable()->constrained('kitchen_stations')->onDelete('set null');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_items');
    }
};
