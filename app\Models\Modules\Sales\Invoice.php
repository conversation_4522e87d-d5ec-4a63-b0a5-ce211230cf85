<?php

namespace App\Models\Modules\Sales;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'invoice_date',
        'due_date',
        'invoice_number',
        'reference',
        'subtotal',
        'discount_total',
        'tax_total',
        'total_amount',
        'due_amount',
        'notes',
        'terms',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'float',
        'discount_total' => 'float',
        'tax_total' => 'float',
        'total_amount' => 'float',
        'due_amount' => 'float',
    ];

    /**
     * Get the customer that owns the invoice.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the items for the invoice.
     */
    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get the payments for the invoice.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the formatted total amount.
     *
     * @return string
     */
    public function getFormattedTotalAttribute()
    {
        return number_format($this->total_amount, 2);
    }

    /**
     * Get the formatted due amount.
     *
     * @return string
     */
    public function getFormattedDueAttribute()
    {
        return number_format($this->due_amount, 2);
    }

    /**
     * Get the formatted status.
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statuses = [
            'draft' => 'مسودة',
            'sent' => 'مرسلة',
            'paid' => 'مدفوعة',
            'partially_paid' => 'مدفوعة جزئياً',
            'overdue' => 'متأخرة',
            'cancelled' => 'ملغاة',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get the status badge class.
     *
     * @return string
     */
    public function getStatusBadgeClassAttribute()
    {
        $classes = [
            'draft' => 'badge-secondary',
            'sent' => 'badge-info',
            'paid' => 'badge-success',
            'partially_paid' => 'badge-warning',
            'overdue' => 'badge-danger',
            'cancelled' => 'badge-dark',
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }

    /**
     * Check if the invoice is overdue.
     *
     * @return bool
     */
    public function isOverdue()
    {
        return $this->due_date->isPast() && $this->due_amount > 0 && $this->status !== 'paid' && $this->status !== 'cancelled';
    }

    /**
     * Check if the invoice is fully paid.
     *
     * @return bool
     */
    public function isFullyPaid()
    {
        return $this->due_amount <= 0;
    }

    /**
     * Check if the invoice is partially paid.
     *
     * @return bool
     */
    public function isPartiallyPaid()
    {
        return $this->due_amount > 0 && $this->due_amount < $this->total_amount;
    }

    /**
     * Update the invoice status based on payments and due date.
     */
    public function updateStatus()
    {
        if ($this->status === 'cancelled') {
            return; // Don't change status if cancelled
        }

        if ($this->isFullyPaid()) {
            $this->status = 'paid';
        } elseif ($this->isPartiallyPaid()) {
            $this->status = 'partially_paid';
        } elseif ($this->isOverdue()) {
            $this->status = 'overdue';
        } elseif ($this->status === 'draft') {
            // Keep as draft
        } else {
            $this->status = 'sent';
        }

        $this->save();
    }
}
