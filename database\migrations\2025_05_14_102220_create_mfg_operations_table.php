<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_operations", function (Blueprint $table) {
            $table->id();
            $table->string("op_code")->unique();
            $table->string("name");
            $table->text("description")->nullable();
            $table->unsignedBigInteger("default_work_center_id")->nullable();
            $table->foreign("default_work_center_id")->references("id")->on("mfg_work_centers")->onDelete("set null");
            $table->decimal("standard_setup_time_hours", 8, 2)->nullable();
            $table->decimal("standard_run_time_per_unit_hours", 10, 4)->nullable(); // Time to produce one unit
            $table->decimal("standard_cost_per_hour", 15, 4)->nullable(); // Combined labor, machine, overhead for this operation if not using WC rates
            $table->text("instructions")->nullable();
            $table->boolean("is_active")->default(true);
            $table->unsignedBigInteger("created_by")->nullable();
            $table->foreign("created_by")->references("id")->on("users")->onDelete("set null");
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->foreign("updated_by")->references("id")->on("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_operations");
    }
};

