<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Item;
use App\Models\Modules\Branches\Branch;
use App\Models\Modules\GeneralLedger\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $items = Item::with("branch")->latest()->paginate(10);
        return view("admin.items.index", compact("items"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::where("is_active", true)->get();
        $accounts = Account::where("is_active", true)->get(); // Assuming you want active accounts
        return view("admin.items.form", compact("branches", "accounts"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "code" => "nullable|string|max:255|unique:items,code",
            "type" => "required|in:raw_material,finished_good,semi_finished_good,service,other",
            "unit_of_measure_ar" => "nullable|string|max:255",
            "unit_of_measure_en" => "nullable|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "standard_cost" => "nullable|numeric|min:0",
            "last_purchase_price" => "nullable|numeric|min:0",
            "selling_price" => "nullable|numeric|min:0",
            "branch_id" => "nullable|exists:branches,id",
            "inventory_account_id" => "nullable|exists:accounts,id",
            "cogs_account_id" => "nullable|exists:accounts,id",
            "sales_revenue_account_id" => "nullable|exists:accounts,id",
            "is_active" => "boolean",
            "is_manufactured" => "boolean",
            "is_purchased" => "boolean",
            "is_sold" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");
        $validatedData["is_manufactured"] = $request->has("is_manufactured");
        $validatedData["is_purchased"] = $request->has("is_purchased");
        $validatedData["is_sold"] = $request->has("is_sold");
        $validatedData["created_by_user_id"] = Auth::id(); // Assuming you have auth

        Item::create($validatedData);

        return redirect()->route("admin.items.index")->with("success", __("admin_messages.item_created_successfully"));
    }

    /**
     * Display the specified resource.
     */
    public function show(Item $item)
    {
        $item->load(["branch", "inventoryAccount", "cogsAccount", "salesRevenueAccount"]);
        return view("admin.items.show", compact("item"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Item $item)
    {
        $branches = Branch::where("is_active", true)->get();
        $accounts = Account::where("is_active", true)->get();
        return view("admin.items.form", compact("item", "branches", "accounts"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Item $item)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "code" => "nullable|string|max:255|unique:items,code," . $item->id,
            "type" => "required|in:raw_material,finished_good,semi_finished_good,service,other",
            "unit_of_measure_ar" => "nullable|string|max:255",
            "unit_of_measure_en" => "nullable|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "standard_cost" => "nullable|numeric|min:0",
            "last_purchase_price" => "nullable|numeric|min:0",
            "selling_price" => "nullable|numeric|min:0",
            "branch_id" => "nullable|exists:branches,id",
            "inventory_account_id" => "nullable|exists:accounts,id",
            "cogs_account_id" => "nullable|exists:accounts,id",
            "sales_revenue_account_id" => "nullable|exists:accounts,id",
            "is_active" => "boolean",
            "is_manufactured" => "boolean",
            "is_purchased" => "boolean",
            "is_sold" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");
        $validatedData["is_manufactured"] = $request->has("is_manufactured");
        $validatedData["is_purchased"] = $request->has("is_purchased");
        $validatedData["is_sold"] = $request->has("is_sold");
        $validatedData["updated_by_user_id"] = Auth::id(); // Assuming you have auth

        $item->update($validatedData);

        return redirect()->route("admin.items.index")->with("success", __("admin_messages.item_updated_successfully"));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Item $item)
    {
        // Add checks here if item is used in BOMs, Work Orders, Transactions etc. before deleting
        // For now, a simple delete
        try {
            $item->delete();
            return redirect()->route("admin.items.index")->with("success", __("admin_messages.item_deleted_successfully"));
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle foreign key constraint violation, etc.
            return redirect()->route("admin.items.index")->with("error", __("admin_messages.item_delete_failed_constraint"));
        }
    }
}

