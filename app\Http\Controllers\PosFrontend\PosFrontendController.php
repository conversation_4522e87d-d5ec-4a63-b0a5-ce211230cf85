<?php

namespace App\Http\Controllers\PosFrontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PosFrontendController extends Controller
{
    // Default index method - redirect to login or main based on authentication
    public function index()
    {
        // Check if user is authenticated (you can customize this logic)
        // For now, redirect to login form
        return redirect()->route('pos.login.form');
    }

    // Show login form
    public function showLoginForm()
    {
        // In a real app, if already logged in to POS, redirect to main POS page
        // For now, just show the login form
        return view("pos_frontend.auth.login");
    }

    // Handle login submission
    public function login(Request $request)
    {
        // Basic validation (in a real app, more robust validation)
        $credentials = $request->validate([
            "username" => "required|string",
            "password" => "required|string",
        ]);

        // Attempt to log the user in (using a custom guard for POS users if necessary)
        // For now, we'll simulate a successful login and redirect to the main POS page.
        // Replace this with actual authentication logic against your users table/guard.
        if ($credentials["username"] === "posuser" && $credentials["password"] === "password") {
            // Simulate session creation or token generation for POS
            // session(["pos_user_authenticated" => true, "pos_user_name" => $credentials["username"]]);
            // In a real Laravel app, you'd use Auth::attempt() with a specific guard
            // if (Auth::guard('pos_users_guard')->attempt($credentials)) {
            //     $request->session()->regenerate();
            //     return redirect()->intended(route("pos.main"));
            // }

            // For this example, we'll just redirect to main assuming login is successful
            // A proper auth system is needed here.
            return redirect()->route("pos.main");
        }

        return back()->withErrors([
            "username" => "بيانات الاعتماد المقدمة غير صحيحة.",
        ])->onlyInput("username");
    }

    // Show main POS interface
    public function main()
    {
        // Add any necessary data for the main POS view
        // For example, fetching product categories, initial products, etc.
        // For now, it's mostly static with dummy data in the Blade file.
        // if (!session("pos_user_authenticated")) {
        //     return redirect()->route("pos.login.form");
        // }
        return view("pos_frontend.main.main");
    }

    // Handle logout
    public function logout(Request $request)
    {
        // Auth::guard('pos_users_guard')->logout();
        // $request->session()->invalidate();
        // $request->session()->regenerateToken();
        // session()->forget("pos_user_authenticated");
        // session()->forget("pos_user_name");
        return redirect()->route("pos.login.form");
    }
}

