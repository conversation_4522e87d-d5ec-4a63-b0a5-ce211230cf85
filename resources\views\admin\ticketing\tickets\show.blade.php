@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <h1>{{ __("Ticket Details: ") }} {{ $ticket->ticket_number }}</h1>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-3">
                <div class="card-header">
                    <h4>{{ $ticket->subject }}</h4>
                </div>
                <div class="card-body">
                    <p><strong>{{ __("Description:") }}</strong></p>
                    <p>{!! nl2br(e($ticket->description)) !!}</p>

                    @if($ticket->attachments->count() > 0)
                        <hr>
                        <h5>{{ __("Ticket Attachments:") }}</h5>
                        <ul>
                            @foreach($ticket->attachments as $attachment)
                                <li><a href="{{ Storage::url($attachment->file_path) }}" target="_blank">{{ $attachment->file_name }}</a> ({{ round($attachment->file_size / 1024, 2) }} KB)</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header">{{ __("Replies") }}</div>
                <div class="card-body">
                    @forelse($ticket->replies()->latest()->get() as $reply)
                        <div class="card mb-2 {{ $reply->is_internal_note ? "bg-light" : "" }}">
                            <div class="card-body">
                                <p class="card-text">{!! nl2br(e($reply->body)) !!}</p>
                                <small class="text-muted">
                                    {{ __("By:") }} {{ $reply->user->name }} {{ __("on") }} {{ $reply->created_at->format("Y-m-d H:i A") }}
                                    @if($reply->is_internal_note)
                                        <span class="badge badge-warning ml-2">{{ __("Internal Note") }}</span>
                                    @endif
                                </small>
                                @if($reply->attachments->count() > 0)
                                    <br><small>{{ __("Reply Attachments:") }}</small>
                                    <ul>
                                        @foreach($reply->attachments as $attachment)
                                            <li><a href="{{ Storage::url($attachment->file_path) }}" target="_blank">{{ $attachment->file_name }}</a> ({{ round($attachment->file_size / 1024, 2) }} KB)</li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div>
                    @empty
                        <p>{{ __("No replies yet.") }}</p>
                    @endforelse
                </div>
            </div>

            <div class="card">
                <div class="card-header">{{ __("Add Reply") }}</div>
                <div class="card-body">
                    <form action="{{ route("admin.ticketing.tickets.update", $ticket->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method("PUT")
                        <input type="hidden" name="update_type" value="reply"> {{-- Custom field to distinguish reply update --}}
                        <div class="form-group">
                            <textarea name="reply_body" id="reply_body" class="form-control @error("reply_body") is-invalid @enderror" rows="4" placeholder="{{ __("Type your reply here...") }}" required>{{ old("reply_body") }}</textarea>
                            @error("reply_body")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                        <div class="form-group form-check">
                            <input type="checkbox" name="is_internal_note" id="is_internal_note" class="form-check-input" value="1" {{ old("is_internal_note") ? "checked" : "" }}>
                            <label class="form-check-label" for="is_internal_note">{{ __("Internal Note (visible to support team only)") }}</label>
                        </div>
                        <div class="form-group">
                            <label for="reply_attachments">{{ __("Add Attachments to Reply") }}</label>
                            <input type="file" name="reply_attachments[]" id="reply_attachments" class="form-control-file @error("reply_attachments.*") is-invalid @enderror" multiple>
                             @error("reply_attachments.*")
                                <span class="invalid-feedback d-block" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary">{{ __("Submit Reply") }}</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    {{ __("Ticket Properties") }}
                    <a href="{{ route("admin.ticketing.tickets.edit", $ticket->id) }}" class="btn btn-sm btn-outline-primary">{{ __("Edit Properties") }}</a>
                </div>
                <div class="card-body">
                    <p><strong>{{ __("Ticket Number:") }}</strong> {{ $ticket->ticket_number }}</p>
                    <p><strong>{{ __("User (Customer):") }}</strong> {{ $ticket->user->name }} ({{ $ticket->user->email }})</p>
                    <p><strong>{{ __("Category:") }}</strong> <span class="badge" style="background-color: {{ $ticket->category->color ?? "#ddd" }}; color: #fff;">{{ $ticket->category->name }}</span></p>
                    <p><strong>{{ __("Priority:") }}</strong> <span class="badge" style="background-color: {{ $ticket->priority->color ?? "#ddd" }}; color: #fff;">{{ $ticket->priority->name }}</span></p>
                    <p><strong>{{ __("Status:") }}</strong> <span class="badge" style="background-color: {{ $ticket->status->color ?? "#ddd" }}; color: #fff;">{{ $ticket->status->name }}</span></p>
                    <p><strong>{{ __("Assigned To:") }}</strong> {{ $ticket->assignedTo->name ?? __("Unassigned") }}</p>
                    <p><strong>{{ __("Branch:") }}</strong> {{ $ticket->branch->name ?? __("N/A") }}</p>
                    <p><strong>{{ __("Created At:") }}</strong> {{ $ticket->created_at->format("Y-m-d H:i A") }}</p>
                    <p><strong>{{ __("Last Updated:") }}</strong> {{ $ticket->updated_at->format("Y-m-d H:i A") }}</p>
                    <p><strong>{{ __("Last Reply At:") }}</strong> {{ $ticket->last_reply_at ? $ticket->last_reply_at->format("Y-m-d H:i A") : __("N/A") }}</p>
                    @if($ticket->closed_at)
                        <p><strong>{{ __("Closed At:") }}</strong> {{ $ticket->closed_at->format("Y-m-d H:i A") }}</p>
                    @endif
                </div>
            </div>
            <a href="{{ route("admin.ticketing.tickets.index") }}" class="btn btn-secondary btn-block">{{ __("Back to Tickets List") }}</a>
        </div>
    </div>
</div>
@endsection

