@extends('layouts.admin')

@section('title', 'إضافة طلب شراء جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة طلب شراء جديد</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.purchases.orders.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.purchases.orders.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="order_number">رقم الطلب</label>
                                    <input type="text" class="form-control" id="order_number" name="order_number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="supplier_id">المورد</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id" required>
                                        <option value="">اختر المورد</option>
                                        <!-- سيتم إضافة الموردين هنا لاحقاً -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="order_date">تاريخ الطلب</label>
                                    <input type="date" class="form-control" id="order_date" name="order_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expected_delivery_date">تاريخ التسليم المتوقع</label>
                                    <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">الأولوية</label>
                                    <select class="form-control" id="priority" name="priority" required>
                                        <option value="">اختر الأولوية</option>
                                        <option value="low">منخفضة</option>
                                        <option value="medium">متوسطة</option>
                                        <option value="high">عالية</option>
                                        <option value="urgent">عاجلة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="pending">في الانتظار</option>
                                        <option value="approved">موافق عليه</option>
                                        <option value="ordered">تم الطلب</option>
                                        <option value="received">تم الاستلام</option>
                                        <option value="cancelled">ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الطلب
                            </button>
                            <a href="{{ route('admin.purchases.orders.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
