<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_subscription_id')->constrained('tenant_subscriptions')->onDelete('cascade');
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->date('invoice_date'); // تاريخ الفاتورة
            $table->date('due_date'); // تاريخ الاستحقاق
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 2); // المبلغ الإجمالي
            $table->enum('status', ['pending', 'paid', 'overdue', 'cancelled'])->default('pending'); // حالة الفاتورة
            $table->date('paid_date')->nullable(); // تاريخ الدفع
            $table->string('payment_method')->nullable(); // طريقة الدفع
            $table->string('payment_reference')->nullable(); // مرجع الدفع
            $table->json('invoice_items'); // عناصر الفاتورة
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_invoices');
    }
};
