<?php

namespace App\Models\Modules\WhatsAppIntegration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User; // Import the User model

class WhatsAppMessageLog extends Model
{
    use HasFactory;

    protected $table = "whatsapp_message_logs";

    protected $fillable = [
        "message_sid",
        "user_id",
        "recipient_phone_number",
        "template_id",
        "message_content",
        "message_type",
        "status",
        "direction",
        "error_code",
        "error_message",
        "sent_at",
        "delivered_at",
        "read_at",
        "related_model_type",
        "related_model_id",
    ];

    protected $casts = [
        "sent_at" => "datetime",
        "delivered_at" => "datetime",
        "read_at" => "datetime",
    ];

    /**
     * Get the user (recipient) associated with the message log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the message template used for this log entry.
     */
    public function template()
    {
        return $this->belongsTo(WhatsAppMessageTemplate::class, "template_id");
    }

    /**
     * Get the parent model that this log entry is related to (e.g., Invoice, Ticket).
     */
    public function relatedModel()
    {
        return $this->morphTo();
    }
}

