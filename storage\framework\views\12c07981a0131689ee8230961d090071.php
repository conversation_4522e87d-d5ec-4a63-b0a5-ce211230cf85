<?php if(empty($rootAccounts)): ?>
    <div class="empty-state text-center p-5">
        <i class="bi bi-folder2-open display-4 text-muted mb-3"></i>
        <h5>لا توجد حسابات</h5>
        <p>لم يتم إضافة أي حسابات بعد. يمكنك إضافة حسابات جديدة من خلال النقر على زر "إضافة حساب جديد".</p>
        <a href="<?php echo e(route('admin.accounts.create')); ?>" class="btn btn-primary mt-3">
            <i class="bi bi-plus-circle me-1"></i> إضافة حساب جديد
        </a>
    </div>
<?php else: ?>
    <div class="modern-accounts-tree">
        <?php $__currentLoopData = $accountTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(isset($rootAccounts[$type->id])): ?>
                <div class="account-type-card mb-4">
                    <div class="account-type-header" data-bs-toggle="collapse" data-bs-target="#type-<?php echo e($type->id); ?>" aria-expanded="true">
                        <div class="d-flex align-items-center">
                            <div class="account-type-icon type-bg-<?php echo e($type->slug); ?>">
                                <?php if($type->slug == 'assets'): ?>
                                    <i class="bi bi-cash-coin"></i>
                                <?php elseif($type->slug == 'liabilities'): ?>
                                    <i class="bi bi-credit-card"></i>
                                <?php elseif($type->slug == 'equity'): ?>
                                    <i class="bi bi-pie-chart"></i>
                                <?php elseif($type->slug == 'revenue'): ?>
                                    <i class="bi bi-graph-up-arrow"></i>
                                <?php elseif($type->slug == 'expenses'): ?>
                                    <i class="bi bi-graph-down-arrow"></i>
                                <?php else: ?>
                                    <i class="bi bi-journal-bookmark"></i>
                                <?php endif; ?>
                            </div>
                            <div class="account-type-info">
                                <h6 class="account-type-name mb-0"><?php echo e($type->name_ar); ?></h6>
                                <small class="text-muted"><?php echo e($rootAccounts[$type->id]['accounts']->count()); ?> حساب</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <?php
                                // حساب إجمالي الرصيد لهذا النوع من الحسابات
                                $typeBalance = 0;
                                foreach($rootAccounts[$type->id]['accounts'] as $account) {
                                    $accountBalance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                                    $typeBalance += $accountBalance;

                                    // إضافة أرصدة الحسابات الفرعية
                                    if($account->children && $account->children->count() > 0) {
                                        foreach($account->children as $child) {
                                            $childBalance = ($child->opening_balance_debit ?? 0) - ($child->opening_balance_credit ?? 0);
                                            $typeBalance += $childBalance;
                                        }
                                    }
                                }
                                $balanceClass = $typeBalance < 0 ? 'text-danger' : 'text-success';
                            ?>
                            <div class="account-type-balance <?php echo e($balanceClass); ?> ms-3 text-start">
                                <span class="balance-amount"><?php echo e(number_format(abs($typeBalance), 2)); ?></span>
                                <small><?php echo e($typeBalance < 0 ? 'دائن' : 'مدين'); ?></small>
                            </div>
                            <i class="bi bi-chevron-down toggle-icon"></i>
                        </div>
                    </div>

                    <div class="collapse show account-type-content" id="type-<?php echo e($type->id); ?>">
                        <div class="modern-tree-container">
                            <ul class="modern-tree">
                                <?php $__currentLoopData = $rootAccounts[$type->id]['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="modern-tree-node">
                                        <div class="modern-tree-node-content <?php if($account->is_control_account): ?> parent-node <?php endif; ?>">
                                            <?php if($account->children && $account->children->count() > 0): ?>
                                                <span class="modern-tree-node-toggle tree-toggle" data-bs-toggle="collapse" data-bs-target="#account-<?php echo e($account->id); ?>">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                            <?php else: ?>
                                                <span class="modern-tree-node-toggle" style="visibility: hidden;">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                            <?php endif; ?>

                                            <span class="modern-tree-node-icon">
                                                <?php if($account->is_control_account): ?>
                                                    <i class="bi bi-folder2" style="color: #f6c23e;"></i>
                                                <?php else: ?>
                                                    <i class="bi bi-file-earmark-text" style="color: #4e73df;"></i>
                                                <?php endif; ?>
                                            </span>

                                            <div class="modern-tree-node-details">
                                                <div class="modern-tree-node-text">
                                                    <span class="modern-tree-node-code"><?php echo e($account->code); ?></span>
                                                    <span class="modern-tree-node-name">
                                                        <?php echo e($account->name_ar); ?>

                                                    </span>
                                                </div>

                                                <?php
                                                    $balance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                                                    $balanceClass = $balance < 0 ? 'negative' : '';
                                                ?>

                                                <span class="modern-tree-node-balance <?php echo e($balanceClass); ?>">
                                                    <?php echo e(number_format(abs($balance), 2)); ?>

                                                    <small><?php echo e($balance < 0 ? 'دائن' : 'مدين'); ?></small>
                                                </span>
                                            </div>

                                            <div class="modern-tree-node-actions">
                                                <a href="<?php echo e(route('admin.accounts.show', $account->id)); ?>" class="btn btn-sm btn-light" title="عرض">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.accounts.edit', $account->id)); ?>" class="btn btn-sm btn-light" title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </div>

                                        <?php if($account->children && $account->children->count() > 0): ?>
                                            <div class="collapse show modern-tree-children" id="account-<?php echo e($account->id); ?>">
                                                <ul class="modern-tree">
                                                    <?php $__currentLoopData = $account->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php echo $__env->make('admin.accounts._modern_tree_node', ['account' => $child], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/accounts/_modern_tree_view.blade.php ENDPATH**/ ?>