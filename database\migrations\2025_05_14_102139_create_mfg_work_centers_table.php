<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mfg_work_centers', function (Blueprint $table) {
            $table->id();
            $table->string('wc_code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('type')->nullable(); // e.g., Machine, Assembly Line, Quality Check Station
            $table->unsignedBigInteger('location_id')->nullable(); // Link to locations table if applicable
            // $table->foreign('location_id')->references('id')->on('locations')->onDelete('set null');
            $table->decimal('capacity_hours_per_day', 8, 2)->nullable();
            $table->integer('number_of_machines')->nullable();
            $table->decimal('machine_efficiency_percentage', 5, 2)->nullable(); // OEE or similar
            $table->decimal('labor_efficiency_percentage', 5, 2)->nullable();
            $table->decimal('standard_machine_hour_rate', 15, 4)->nullable();
            $table->decimal('standard_labor_hour_rate', 15, 4)->nullable();
            $table->decimal('standard_overhead_rate_per_hour', 15, 4)->nullable(); // For applying overheads
            $table->unsignedBigInteger('cost_center_id')->nullable(); // Link to cost centers if applicable
            // $table->foreign('cost_center_id')->references('id')->on('cost_centers')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mfg_work_centers');
    }
};

