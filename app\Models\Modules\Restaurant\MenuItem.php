<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'sku',
        'barcode',
        'price',
        'cost_price',
        'tax_rate',
        'image',
        'images',
        'preparation_time',
        'calories',
        'allergens',
        'ingredients',
        'is_spicy',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_available',
        'is_active',
        'availability_schedule',
        'sort_order',
        'category_id',
        'kitchen_station_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'images' => 'array',
        'preparation_time' => 'integer',
        'calories' => 'integer',
        'allergens' => 'array',
        'ingredients' => 'array',
        'is_spicy' => 'boolean',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_available' => 'boolean',
        'is_active' => 'boolean',
        'availability_schedule' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Get the category that owns the menu item.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(MenuCategory::class, 'category_id');
    }

    /**
     * Get the kitchen station that owns the menu item.
     */
    public function kitchenStation(): BelongsTo
    {
        return $this->belongsTo(KitchenStation::class, 'kitchen_station_id');
    }

    /**
     * Get the branch that owns the menu item.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the menu item.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the modifiers for the menu item.
     */
    public function modifiers(): BelongsToMany
    {
        return $this->belongsToMany(MenuItemModifier::class, 'menu_item_modifier_pivot', 'menu_item_id', 'modifier_id')
            ->withPivot('sort_order')
            ->withTimestamps()
            ->orderBy('pivot_sort_order');
    }

    /**
     * Get the order items for the menu item.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(RestaurantOrderItem::class, 'menu_item_id');
    }

    /**
     * Get the localized name.
     */
    public function getNameAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get the localized description.
     */
    public function getDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * Get the final price including tax.
     */
    public function getFinalPriceAttribute(): float
    {
        return $this->price + ($this->price * $this->tax_rate / 100);
    }

    /**
     * Get the profit margin.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }
        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Check if item is available now.
     */
    public function isAvailableNow(): bool
    {
        if (!$this->is_active || !$this->is_available) {
            return false;
        }

        // Check availability schedule if exists
        if ($this->availability_schedule) {
            // Implementation for schedule checking
            // This would check current time against schedule
        }

        return true;
    }

    /**
     * Scope a query to only include active items.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include available items.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_active', true)->where('is_available', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name_ar');
    }
}
