<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Subscription\TenantSubscription;
use App\Models\Subscription\SubscriptionInvoice;

class SubscriptionController extends Controller
{
    public function plans()
    {
        $plans = SubscriptionPlan::active()
            ->ordered()
            ->get()
            ->groupBy('billing_cycle');

        $currentSubscription = null;
        if (Auth::check()) {
            $tenantId = Auth::user()->tenant_id ?? Auth::id();
            $currentSubscription = TenantSubscription::where('tenant_id', $tenantId)
                ->whereIn('status', ['active', 'trial'])
                ->with('subscriptionPlan')
                ->first();
        }

        return view('subscription.plans', compact('plans', 'currentSubscription'));
    }

    public function subscribe(Request $request, SubscriptionPlan $plan)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        // Check if user already has an active subscription
        $existingSubscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial'])
            ->first();

        if ($existingSubscription) {
            return redirect()->route('subscription.current')
                ->with('error', 'لديك اشتراك نشط بالفعل. يمكنك ترقية أو تغيير الباقة من صفحة الاشتراك الحالي.');
        }

        // Create new subscription
        $subscription = TenantSubscription::create([
            'tenant_id' => $tenantId,
            'subscription_plan_id' => $plan->id,
            'subscription_code' => TenantSubscription::generateSubscriptionCode(),
            'status' => 'trial', // Start with trial
            'start_date' => now(),
            'end_date' => now()->addDays(14), // 14 days trial
            'trial_end_date' => now()->addDays(14),
            'amount_paid' => 0,
            'auto_renew' => true,
            'next_billing_date' => now()->addDays(14),
        ]);

        return redirect()->route('subscription.current')
            ->with('success', 'تم تفعيل الفترة التجريبية بنجاح! لديك 14 يوم لتجربة النظام.');
    }

    public function current()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $subscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial', 'expired'])
            ->with(['subscriptionPlan', 'subscriptionInvoices'])
            ->latest()
            ->first();

        if (!$subscription) {
            return redirect()->route('subscription.plans')
                ->with('error', 'لا يوجد لديك اشتراك حالي. يرجى اختيار باقة للبدء.');
        }

        // Calculate usage statistics
        $usageStats = $this->calculateUsageStats($subscription);

        return view('subscription.current', compact('subscription', 'usageStats'));
    }

    public function upgrade()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $currentSubscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial'])
            ->with('subscriptionPlan')
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('subscription.plans');
        }

        $availablePlans = SubscriptionPlan::active()
            ->where('price', '>', $currentSubscription->subscriptionPlan->price)
            ->ordered()
            ->get();

        return view('subscription.upgrade', compact('currentSubscription', 'availablePlans'));
    }

    public function processUpgrade(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $currentSubscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial'])
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('subscription.plans');
        }

        // Calculate prorated amount
        $proratedAmount = $this->calculateProratedAmount($currentSubscription, $plan);

        // Update subscription
        $currentSubscription->update([
            'subscription_plan_id' => $plan->id,
            'status' => 'active',
            'payment_method' => $request->payment_method,
            'amount_paid' => $proratedAmount,
        ]);

        // Create invoice
        $this->createSubscriptionInvoice($currentSubscription, $proratedAmount, 'upgrade');

        return redirect()->route('subscription.current')
            ->with('success', 'تم ترقية باقتك بنجاح!');
    }

    public function cancel()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $subscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial'])
            ->first();

        if (!$subscription) {
            return redirect()->route('subscription.plans');
        }

        return view('subscription.cancel', compact('subscription'));
    }

    public function processCancel(Request $request)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'confirm' => 'required|accepted',
        ]);

        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $subscription = TenantSubscription::where('tenant_id', $tenantId)
            ->whereIn('status', ['active', 'trial'])
            ->first();

        if (!$subscription) {
            return redirect()->route('subscription.plans');
        }

        $subscription->cancel($request->reason);

        return redirect()->route('subscription.plans')
            ->with('success', 'تم إلغاء اشتراكك بنجاح. نأسف لرؤيتك تغادر!');
    }

    public function expired()
    {
        return view('subscription.expired');
    }

    public function invoices()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        $invoices = SubscriptionInvoice::whereHas('tenantSubscription', function($query) use ($tenantId) {
            $query->where('tenant_id', $tenantId);
        })
        ->with('tenantSubscription.subscriptionPlan')
        ->latest()
        ->paginate(10);

        return view('subscription.invoices', compact('invoices'));
    }

    private function calculateUsageStats(TenantSubscription $subscription): array
    {
        $tenantId = $subscription->tenant_id;
        
        return [
            'branches' => [
                'current' => \App\Models\Modules\Branches\Branch::where('tenant_id', $tenantId)->count(),
                'limit' => $subscription->getLimit('branches'),
            ],
            'users' => [
                'current' => \App\Models\User::where('tenant_id', $tenantId)->count(),
                'limit' => $subscription->getLimit('users'),
            ],
            'customers' => [
                'current' => \App\Models\Modules\Sales\Customer::where('tenant_id', $tenantId)->count(),
                'limit' => $subscription->getLimit('customers'),
            ],
            'sales_representatives' => [
                'current' => \App\Models\Modules\SalesRepresentatives\SalesRepresentative::where('tenant_id', $tenantId)->count(),
                'limit' => $subscription->getLimit('sales_representatives'),
            ],
            'invoices_this_month' => [
                'current' => \App\Models\Invoice::where('tenant_id', $tenantId)
                    ->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()])
                    ->count(),
                'limit' => $subscription->getLimit('invoices_per_month'),
            ],
        ];
    }

    private function calculateProratedAmount(TenantSubscription $currentSubscription, SubscriptionPlan $newPlan): float
    {
        $currentPlan = $currentSubscription->subscriptionPlan;
        $daysRemaining = $currentSubscription->end_date->diffInDays(now());
        $totalDays = $currentSubscription->start_date->diffInDays($currentSubscription->end_date);
        
        $currentPlanDailyRate = $currentPlan->price / $totalDays;
        $newPlanDailyRate = $newPlan->price / $totalDays;
        
        $refund = $currentPlanDailyRate * $daysRemaining;
        $newCharge = $newPlanDailyRate * $daysRemaining;
        
        return max(0, $newCharge - $refund);
    }

    private function createSubscriptionInvoice(TenantSubscription $subscription, float $amount, string $type = 'subscription'): SubscriptionInvoice
    {
        return SubscriptionInvoice::create([
            'tenant_subscription_id' => $subscription->id,
            'invoice_number' => 'INV-' . now()->format('Ymd') . '-' . str_pad(SubscriptionInvoice::count() + 1, 4, '0', STR_PAD_LEFT),
            'invoice_date' => now(),
            'due_date' => now()->addDays(7),
            'subtotal' => $amount,
            'tax_amount' => $amount * 0.15, // 15% VAT
            'total_amount' => $amount * 1.15,
            'status' => 'paid',
            'paid_date' => now(),
            'payment_method' => $subscription->payment_method,
            'payment_reference' => $subscription->payment_reference,
            'invoice_items' => [
                [
                    'description' => $subscription->subscriptionPlan->name . ' - ' . ucfirst($type),
                    'quantity' => 1,
                    'unit_price' => $amount,
                    'total' => $amount,
                ]
            ],
        ]);
    }
}
