<?php $__env->startSection('title', 'عمليات التصنيع'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">عمليات التصنيع</h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('admin.manufacturing.operations.create')); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة عملية تصنيع جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في العمليات...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="planned">مخطط</option>
                                <option value="in_progress">قيد التنفيذ</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="assembly">تجميع</option>
                                <option value="production">إنتاج</option>
                                <option value="packaging">تعبئة</option>
                                <option value="quality_check">فحص جودة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="dateFilter" placeholder="تاريخ العملية">
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم العملية</th>
                                    <th>اسم العملية</th>
                                    <th>النوع</th>
                                    <th>المنتج</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>الكمية المنتجة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>المسؤول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>MFG-2024-001</td>
                                    <td>تجميع أجهزة لابتوب</td>
                                    <td><span class="badge badge-info">تجميع</span></td>
                                    <td>لابتوب ديل XPS 13</td>
                                    <td>50</td>
                                    <td>35</td>
                                    <td>2024-03-10</td>
                                    <td>2024-03-20</td>
                                    <td><span class="badge badge-warning">قيد التنفيذ</span></td>
                                    <td>أحمد محمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.manufacturing.operations.show', 1)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.manufacturing.operations.edit', 1)); ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.manufacturing.operations.destroy', 1)); ?>" method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>MFG-2024-002</td>
                                    <td>إنتاج ماوس لاسلكي</td>
                                    <td><span class="badge badge-success">إنتاج</span></td>
                                    <td>ماوس لاسلكي لوجيتك</td>
                                    <td>200</td>
                                    <td>200</td>
                                    <td>2024-03-05</td>
                                    <td>2024-03-15</td>
                                    <td><span class="badge badge-success">مكتمل</span></td>
                                    <td>فاطمة أحمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.manufacturing.operations.show', 2)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.manufacturing.operations.edit', 2)); ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.manufacturing.operations.destroy', 2)); ?>" method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>MFG-2024-003</td>
                                    <td>تعبئة كيبورد ميكانيكي</td>
                                    <td><span class="badge badge-primary">تعبئة</span></td>
                                    <td>كيبورد ميكانيكي</td>
                                    <td>100</td>
                                    <td>0</td>
                                    <td>2024-03-20</td>
                                    <td>2024-03-25</td>
                                    <td><span class="badge badge-secondary">مخطط</span></td>
                                    <td>محمد علي</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.manufacturing.operations.show', 3)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.manufacturing.operations.edit', 3)); ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.manufacturing.operations.destroy', 3)); ?>" method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>MFG-2024-004</td>
                                    <td>فحص جودة الشاشات</td>
                                    <td><span class="badge badge-dark">فحص جودة</span></td>
                                    <td>شاشة سامسونج 24 بوصة</td>
                                    <td>30</td>
                                    <td>25</td>
                                    <td>2024-03-12</td>
                                    <td>2024-03-18</td>
                                    <td><span class="badge badge-warning">قيد التنفيذ</span></td>
                                    <td>سارة خالد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.manufacturing.operations.show', 4)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.manufacturing.operations.edit', 4)); ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.manufacturing.operations.destroy', 4)); ?>" method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة عمليات التصنيع
        console.log('تم تعطيل DataTables في صفحة عمليات التصنيع');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة عمليات التصنيع');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة عمليات التصنيع');
                return this;
            };
        }
        
        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });

        // فلترة حسب الحالة
        $('#statusFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                }
            });
        });

        // فلترة حسب النوع
        $('#typeFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                }
            });
        });

        // فلترة حسب التاريخ
        $('#dateFilter').on('change', function() {
            var value = $(this).val();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().indexOf(value) > -1);
                }
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/manufacturing/operations/index.blade.php ENDPATH**/ ?>