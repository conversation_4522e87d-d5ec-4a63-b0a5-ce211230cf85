@extends('layouts.admin')

@section('title', 'إضافة مرتجع شراء جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة مرتجع شراء جديد</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.purchases.returns.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.purchases.returns.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="return_number">رقم المرتجع</label>
                                    <input type="text" class="form-control" id="return_number" name="return_number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="supplier_id">المورد</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id" required>
                                        <option value="">اختر المورد</option>
                                        <!-- سيتم إضافة الموردين هنا لاحقاً -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="original_invoice">رقم الفاتورة الأصلية</label>
                                    <input type="text" class="form-control" id="original_invoice" name="original_invoice" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="return_date">تاريخ المرتجع</label>
                                    <input type="date" class="form-control" id="return_date" name="return_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="reason">سبب المرتجع</label>
                                    <select class="form-control" id="reason" name="reason" required>
                                        <option value="">اختر السبب</option>
                                        <option value="defective">منتج معيب</option>
                                        <option value="wrong_item">منتج خاطئ</option>
                                        <option value="damaged">منتج تالف</option>
                                        <option value="expired">منتج منتهي الصلاحية</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المرتجع
                            </button>
                            <a href="{{ route('admin.purchases.returns.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
