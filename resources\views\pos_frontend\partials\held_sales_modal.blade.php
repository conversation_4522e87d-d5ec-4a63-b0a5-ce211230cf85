{{-- Held Sales Modal --}}
<div class="modal fade" id="heldSalesModal" tabindex="-1" aria-labelledby="heldSalesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="heldSalesModalLabel"><i class="bi bi-arrow-down-left-circle-fill me-2"></i>استرجاع فاتورة معلقة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <div id="held-sales-list-container">
                    {{-- Held sales will be populated here by JavaScript --}}
                    <p class="text-muted text-center" id="no-held-sales-message">لا توجد فواتير معلقة حالياً.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"><i class="bi bi-x-lg me-1"></i>إغلاق</button>
            </div>
        </div>
    </div>
</div>

@push("scripts")
<script>
    // heldSales array is assumed to be managed globally in main.blade.php (loaded from localStorage)
    // Functions like retrieveHeldSale, deleteHeldSale, calculateSaleTotal, saveHeldSalesToStorage, loadHeldSalesFromStorage
    // are also assumed to be defined in main.blade.php or a shared script file.

    function displayHeldSales() {
        const heldSalesListContainer = document.getElementById("held-sales-list-container");
        const noHeldSalesMessage = document.getElementById("no-held-sales-message");

        if (typeof heldSales === "undefined" || heldSales.length === 0) {
            heldSalesListContainer.innerHTML = '<p class="text-muted text-center">لا توجد فواتير معلقة حالياً.</p>';
            if(noHeldSalesMessage) noHeldSalesMessage.style.display = "block";
            return;
        }
        if(noHeldSalesMessage) noHeldSalesMessage.style.display = "none";

        // Sort by newest first
        const sortedSales = [...heldSales].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        let salesHtml = '<div class="list-group">';
        sortedSales.forEach((sale, index) => {
            // Calculate total for display. Ensure calculateSaleTotal is available.
            let displayTotal = "0.00";
            if (typeof calculateSaleTotal === "function") {
                displayTotal = calculateSaleTotal(sale.cart, sale.deliveryCost).toFixed(2);
            } else {
                // Fallback or simple sum if specific function not found
                const subtotal = sale.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                const tax = subtotal * 0.15;
                displayTotal = (subtotal + tax + (sale.deliveryCost || 0)).toFixed(2);
            }

            const customerName = sale.customer ? sale.customer.name : "بدون عميل";
            const saleTime = new Date(sale.timestamp).toLocaleTimeString("ar-SA", { hour: "2-digit", minute: "2-digit" });
            const saleDate = new Date(sale.timestamp).toLocaleDateString("ar-SA", { day: "2-digit", month: "short"});

            salesHtml += `
                <div class="list-group-item list-group-item-action mb-2 border rounded p-3">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">فاتورة #${sale.id}</h6>
                        <small class="text-muted">${saleDate} - ${saleTime}</small>
                    </div>
                    <p class="mb-1">العميل: <strong>${customerName}</strong> | المنتجات: ${sale.cart.length} | الإجمالي: <strong>${displayTotal} ريال</strong></p>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary me-2" onclick="retrieveHeldSale(${sale.id})"><i class="bi bi-arrow-clockwise me-1"></i>استرجاع</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteHeldSale(${sale.id})"><i class="bi bi-trash me-1"></i>حذف</button>
                    </div>
                </div>`;
        });
        salesHtml += "</div>";
        heldSalesListContainer.innerHTML = salesHtml;
    }

    // Ensure this is called when the modal is shown to refresh the list
    var heldSalesModalEl = document.getElementById("heldSalesModal");
    if (heldSalesModalEl) {
        heldSalesModalEl.addEventListener("show.bs.modal", function () {
            if (typeof loadHeldSalesFromStorage === "function") {
                loadHeldSalesFromStorage(); // Ensure data is fresh from storage
            }
            displayHeldSales(); // Then display it
        });
    }

    // calculateSaleTotal might be defined in main.blade.php, ensure it considers deliveryCost
    // function calculateSaleTotal(saleCart, deliveryCost = 0) {
    //     const subtotal = saleCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    //     const taxRate = 0.15; // 15% VAT
    //     const tax = subtotal * taxRate;
    //     return subtotal + tax + deliveryCost;
    // }

</script>
@endpush

