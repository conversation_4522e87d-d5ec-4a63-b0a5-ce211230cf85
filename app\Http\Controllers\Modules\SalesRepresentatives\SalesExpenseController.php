<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesExpense;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\SalesRepresentatives\SalesVisit;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SalesExpenseController extends Controller
{
    public function index()
    {
        $expenses = SalesExpense::with(['salesRepresentative', 'salesVisit', 'approvedBy', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.sales-representatives.expenses.index', compact('expenses'));
    }

    public function create()
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $visits = SalesVisit::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->with(['customer', 'salesRepresentative'])
            ->latest()
            ->take(100)
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.expenses.form', compact('representatives', 'visits', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'sales_visit_id' => 'nullable|exists:sales_visits,id',
            'expense_type' => 'required|in:fuel,meals,accommodation,transportation,communication,entertainment,other',
            'expense_date' => 'required|date',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'description' => 'required|string',
            'receipt_number' => 'nullable|string|max:255',
            'vendor_name' => 'nullable|string|max:255',
            'receipt_images' => 'nullable|array',
            'receipt_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_reimbursable' => 'boolean',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['expense_code'] = SalesExpense::generateExpenseCode();
        $validatedData['approval_status'] = 'pending';
        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_reimbursable'] = $request->has('is_reimbursable');

        // Handle receipt images
        if ($request->hasFile('receipt_images')) {
            $images = [];
            foreach ($request->file('receipt_images') as $image) {
                $path = $image->store('expenses/receipts', 'public');
                $images[] = $path;
            }
            $validatedData['receipt_images'] = $images;
        }

        SalesExpense::create($validatedData);

        return redirect()->route('admin.sales-representatives.expenses.index')
            ->with('success', __('Sales expense created successfully.'));
    }

    public function show(SalesExpense $expense)
    {
        $expense->load(['salesRepresentative', 'salesVisit', 'approvedBy', 'branch']);

        return view('admin.sales-representatives.expenses.show', compact('expense'));
    }

    public function edit(SalesExpense $expense)
    {
        if ($expense->approval_status === 'paid') {
            return redirect()->route('admin.sales-representatives.expenses.index')
                ->with('error', __('Cannot edit paid expense.'));
        }

        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $visits = SalesVisit::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->with(['customer', 'salesRepresentative'])
            ->latest()
            ->take(100)
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.expenses.form', compact('expense', 'representatives', 'visits', 'branches'));
    }

    public function update(Request $request, SalesExpense $expense)
    {
        if ($expense->approval_status === 'paid') {
            return redirect()->route('admin.sales-representatives.expenses.index')
                ->with('error', __('Cannot update paid expense.'));
        }

        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'sales_visit_id' => 'nullable|exists:sales_visits,id',
            'expense_type' => 'required|in:fuel,meals,accommodation,transportation,communication,entertainment,other',
            'expense_date' => 'required|date',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'description' => 'required|string',
            'receipt_number' => 'nullable|string|max:255',
            'vendor_name' => 'nullable|string|max:255',
            'receipt_images' => 'nullable|array',
            'receipt_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_reimbursable' => 'boolean',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['is_reimbursable'] = $request->has('is_reimbursable');

        // Handle receipt images
        if ($request->hasFile('receipt_images')) {
            // Delete old images
            if ($expense->receipt_images) {
                foreach ($expense->receipt_images as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }

            $images = [];
            foreach ($request->file('receipt_images') as $image) {
                $path = $image->store('expenses/receipts', 'public');
                $images[] = $path;
            }
            $validatedData['receipt_images'] = $images;
        }

        $expense->update($validatedData);

        return redirect()->route('admin.sales-representatives.expenses.index')
            ->with('success', __('Sales expense updated successfully.'));
    }

    public function destroy(SalesExpense $expense)
    {
        if ($expense->approval_status === 'paid') {
            return redirect()->route('admin.sales-representatives.expenses.index')
                ->with('error', __('Cannot delete paid expense.'));
        }

        // Delete receipt images
        if ($expense->receipt_images) {
            foreach ($expense->receipt_images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $expense->delete();

        return redirect()->route('admin.sales-representatives.expenses.index')
            ->with('success', __('Sales expense deleted successfully.'));
    }

    public function approve(Request $request, SalesExpense $expense)
    {
        $request->validate([
            'approval_notes' => 'nullable|string|max:500'
        ]);

        if ($expense->approval_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => __('Expense is not pending approval.')
            ], 422);
        }

        $expense->approve(Auth::user(), $request->approval_notes);

        return response()->json([
            'success' => true,
            'message' => __('Expense approved successfully.'),
            'status' => $expense->approval_status
        ]);
    }

    public function reject(Request $request, SalesExpense $expense)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        if ($expense->approval_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => __('Expense is not pending approval.')
            ], 422);
        }

        $expense->reject(Auth::user(), $request->reason);

        return response()->json([
            'success' => true,
            'message' => __('Expense rejected successfully.'),
            'status' => $expense->approval_status
        ]);
    }

    public function markAsPaid(Request $request, SalesExpense $expense)
    {
        $request->validate([
            'payment_reference' => 'required|string|max:255',
            'amount' => 'nullable|numeric|min:0'
        ]);

        if ($expense->approval_status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => __('Expense must be approved before payment.')
            ], 422);
        }

        $expense->markAsPaid($request->payment_reference, $request->amount);

        return response()->json([
            'success' => true,
            'message' => __('Expense marked as paid successfully.'),
            'status' => $expense->approval_status
        ]);
    }

    public function bulkApprove(Request $request)
    {
        $request->validate([
            'expense_ids' => 'required|array',
            'expense_ids.*' => 'exists:sales_expenses,id',
            'approval_notes' => 'nullable|string|max:500'
        ]);

        $expenses = SalesExpense::whereIn('id', $request->expense_ids)
            ->where('approval_status', 'pending')
            ->get();

        foreach ($expenses as $expense) {
            $expense->approve(Auth::user(), $request->approval_notes);
        }

        return response()->json([
            'success' => true,
            'message' => __('Expenses approved successfully.'),
            'count' => $expenses->count()
        ]);
    }

    public function getExpensesByRepresentative(Request $request)
    {
        $representativeId = $request->get('representative_id');
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $expenses = SalesExpense::with(['salesVisit'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('sales_representative_id', $representativeId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $expenses
        ]);
    }

    public function getPendingExpenses()
    {
        $expenses = SalesExpense::with(['salesRepresentative', 'salesVisit'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('approval_status', 'pending')
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $expenses
        ]);
    }

    public function downloadReceipt(SalesExpense $expense, $imageIndex)
    {
        if (!$expense->receipt_images || !isset($expense->receipt_images[$imageIndex])) {
            abort(404);
        }

        $imagePath = $expense->receipt_images[$imageIndex];
        
        if (!Storage::disk('public')->exists($imagePath)) {
            abort(404);
        }

        return Storage::disk('public')->download($imagePath);
    }
}
