<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\TenantScoped;

class AccountType extends Model
{
    use HasFactory, TenantScoped;

    protected $fillable = [
        "name_ar",
        "name_en",
        "slug",
        "description_ar",
        "description_en",
        "is_primary",
        "parent_id",
        "is_active",
        "tenant_id",
    ];

    protected $casts = [
        "is_primary" => "boolean",
        "is_active" => "boolean",
    ];

    public function accounts()
    {
        return $this->hasMany(Account::class);
    }
}

