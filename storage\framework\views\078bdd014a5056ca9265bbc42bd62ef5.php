<?php $__env->startSection("content"); ?>
    <div class="container">
        <h1>Add New Manufacturing Item</h1>
        <form action="<?php echo e(route("admin.manufacturing.items.store")); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="form-group">
                <label for="name">Item Name</label>
                <input type="text" name="name" id="name" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="item_code">Item Code</label>
                <input type="text" name="item_code" id="item_code" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" id="description" class="form-control"></textarea>
            </div>
            <div class="form-group">
                <label for="item_type">Item Type</label>
                <select name="item_type" id="item_type" class="form-control">
                    <option value="raw_material">Raw Material</option>
                    <option value="semi_finished_good">Semi-Finished Good</option>
                    <option value="finished_good">Finished Good</option>
                    <option value="service">Service</option>
                    <option value="asset">Asset</option>
                    <option value="expense">Expense</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="unit_of_measure">Unit of Measure</label>
                <input type="text" name="unit_of_measure" id="unit_of_measure" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="standard_cost">Standard Cost</label>
                <input type="number" step="0.0001" name="standard_cost" id="standard_cost" class="form-control" value="0.0000">
            </div>
            <div class="form-group">
                <label for="branch_id">Branch</label>
                <select name="branch_id" id="branch_id" class="form-control">
                    
                    
                    
                    <option value="">Select Branch (Optional)</option> 
                </select>
            </div>
             <div class="form-group">
                <label for="account_id">Default Account</label>
                <select name="account_id" id="account_id" class="form-control">
                    
                    
                    
                     <option value="">Select Account (Optional)</option>
                </select>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_manufactured" id="is_manufactured" class="form-check-input" value="1">
                <label for="is_manufactured" class="form-check-label">Is Manufactured</label>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_purchased" id="is_purchased" class="form-check-input" value="1" checked>
                <label for="is_purchased" class="form-check-label">Is Purchased</label>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_sold" id="is_sold" class="form-check-input" value="1">
                <label for="is_sold" class="form-check-label">Is Sold</label>
            </div>
            <div class="form-group">
                <label for="min_stock_level">Min Stock Level</label>
                <input type="number" step="0.0001" name="min_stock_level" id="min_stock_level" class="form-control">
            </div>
            <div class="form-group">
                <label for="max_stock_level">Max Stock Level</label>
                <input type="number" step="0.0001" name="max_stock_level" id="max_stock_level" class="form-control">
            </div>

            <button type="submit" class="btn btn-success mt-3">Save Item</button>
        </form>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/manufacturing/items/create.blade.php ENDPATH**/ ?>