<?php $__env->startSection("content"); ?>
<div class="container">
    <h1>Configure ZATCA E-Invoicing Integration</h1>

    

    <form action="<?php echo e(route("admin.integrations.zatca.update")); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field("PUT"); ?> 

        <div class="card mb-3">
            <div class="card-header">Onboarding & Credentials</div>
            <div class="card-body">
                <div class="form-group">
                    <label for="zatca_phase">Compliance Phase</label>
                    <select name="zatca_phase" id="zatca_phase" class="form-control">
                        <option value="phase_1" >Phase 1: Generation & Storage</option>
                        <option value="phase_2" >Phase 2: Integration (Clearance/Reporting)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="onboarding_status">Onboarding Status with ZATCA</label>
                    <select name="onboarding_status" id="onboarding_status" class="form-control">
                        <option value="pending" >Pending</option>
                        <option value="onboarded" >Onboarded (CSID Received)</option>
                        <option value="failed" >Failed</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="zatca_environment">ZATCA Environment</label>
                    <select name="zatca_environment" id="zatca_environment" class="form-control">
                        <option value="simulation" >Simulation (Test)</option>
                        <option value="production" >Production (Live)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="csid_production">Production CSID (Cryptographic Stamp ID)</label>
                    <textarea name="csid_production" id="csid_production" class="form-control" rows="3"></textarea>
                    <small class="form-text text-muted">The production cryptographic stamp ID provided by ZATCA after successful onboarding.</small>
                </div>

                <div class="form-group">
                    <label for="csid_simulation">Simulation CSID (Optional)</label>
                    <textarea name="csid_simulation" id="csid_simulation" class="form-control" rows="3"></textarea>
                    <small class="form-text text-muted">The simulation/testing cryptographic stamp ID if provided by ZATCA.</small>
                </div>

                <div class="form-group">
                    <label for="compliance_csid_production">Production Compliance CSID (for compliance checks)</label>
                    <input type="text" name="compliance_csid_production" id="compliance_csid_production" class="form-control" value="">
                </div>
                 <div class="form-group">
                    <label for="compliance_csid_simulation">Simulation Compliance CSID (for compliance checks)</label>
                    <input type="text" name="compliance_csid_simulation" id="compliance_csid_simulation" class="form-control" value="">
                </div>

                <div class="form-group">
                    <label for="secret_production">Production API Secret</label>
                    <input type="password" name="secret_production" id="secret_production" class="form-control" value="">
                </div>
                 <div class="form-group">
                    <label for="secret_simulation">Simulation API Secret</label>
                    <input type="password" name="secret_simulation" id="secret_simulation" class="form-control" value="">
                </div>

            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header">Solution & SDK Details</div>
            <div class="card-body">
                <div class="form-group">
                    <label for="sdk_provider">E-Invoicing Solution Provider (if any)</label>
                    <input type="text" name="sdk_provider" id="sdk_provider" class="form-control" value="">
                </div>
                <div class="form-group">
                    <label for="sdk_version">Solution/SDK Version</label>
                    <input type="text" name="sdk_version" id="sdk_version" class="form-control" value="">
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-success mt-3">Save ZATCA Configuration</button>
        <a href="<?php echo e(route("admin.integrations.zatca.index")); ?>" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/zatca/form.blade.php ENDPATH**/ ?>