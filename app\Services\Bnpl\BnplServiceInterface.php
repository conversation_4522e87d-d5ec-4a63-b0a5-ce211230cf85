<?php

namespace App\Services\Bnpl;

interface BnplServiceInterface
{
    /**
     * Initializes the service with provider-specific settings.
     *
     * @param array $settings
     * @return void
     */
    public function initialize(array $settings): void;

    /**
     * Creates a checkout session with the BNPL provider.
     *
     * @param array $orderDetails (e.g., items, total_amount, currency, customer_info, success_url, failure_url, cancel_url)
     * @return array (e.g., ["success" => true, "checkout_url" => "...", "session_id" => "..."] or ["success" => false, "error_message" => "..."])
     */
    public function createCheckoutSession(array $orderDetails): array;

    /**
     * Authorizes an order after customer approval.
     *
     * @param string $sessionId The session ID or order ID from the provider.
     * @param array $params Additional parameters if any.
     * @return array (e.g., ["success" => true, "order_id" => "...", "status" => "AUTHORIZED"] or ["success" => false, "error_message" => "..."])
     */
    public function authorizeOrder(string $sessionId, array $params = []): array;

    /**
     * Captures the payment for an authorized order.
     *
     * @param string $orderId The order ID from the provider.
     * @param float $amount The amount to capture.
     * @param string $currency The currency.
     * @param array $params Additional parameters if any.
     * @return array (e.g., ["success" => true, "capture_id" => "...", "status" => "CAPTURED"] or ["success" => false, "error_message" => "..."])
     */
    public function capturePayment(string $orderId, float $amount, string $currency, array $params = []): array;

    /**
     * Refunds a payment.
     *
     * @param string $orderId The order ID from the provider.
     * @param string $captureId The capture ID if applicable.
     * @param float $amount The amount to refund.
     * @param string $currency The currency.
     * @param string $reason The reason for refund.
     * @param array $params Additional parameters if any.
     * @return array (e.g., ["success" => true, "refund_id" => "...", "status" => "REFUNDED"] or ["success" => false, "error_message" => "..."])
     */
    public function refundPayment(string $orderId, ?string $captureId, float $amount, string $currency, string $reason, array $params = []): array;

    /**
     * Gets the status of an order from the BNPL provider.
     *
     * @param string $orderId The order ID from the provider.
     * @return array (e.g., ["success" => true, "status" => "...", "details" => [...]] or ["success" => false, "error_message" => "..."])
     */
    public function getOrderStatus(string $orderId): array;

    /**
     * Verifies the signature of a webhook notification.
     *
     * @param array $requestHeaders The headers from the incoming webhook request.
     * @param string $requestBody The raw body of the incoming webhook request.
     * @return bool True if the signature is valid, false otherwise.
     */
    public function verifyWebhookSignature(array $requestHeaders, string $requestBody): bool;

     /**
     * Handles the webhook notification content.
     *
     * @param array $webhookData The parsed data from the webhook.
     * @return array (e.g., ["success" => true, "message" => "Webhook processed"] or ["success" => false, "error_message" => "..."])
     */
    public function handleWebhook(array $webhookData): array;
}

