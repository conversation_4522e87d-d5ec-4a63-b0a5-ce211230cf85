<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
        'description',
        'is_system',
    ];

    protected $casts = [
        'is_system' => 'boolean',
    ];

    /**
     * Get the value with the appropriate type casting.
     *
     * @return mixed
     */
    public function getTypedValueAttribute()
    {
        switch ($this->type) {
            case 'boolean':
                return (bool) $this->value;
            case 'integer':
                return (int) $this->value;
            case 'float':
                return (float) $this->value;
            case 'json':
                return json_decode($this->value, true);
            default:
                return $this->value;
        }
    }

    /**
     * Set the value with the appropriate type casting.
     *
     * @param mixed $value
     * @return void
     */
    public function setTypedValueAttribute($value)
    {
        switch ($this->type) {
            case 'json':
                $this->attributes['value'] = json_encode($value);
                break;
            default:
                $this->attributes['value'] = (string) $value;
                break;
        }
    }

    /**
     * Get a setting by key.
     *
     * @param string $key
     * @return mixed
     */
    public static function getByKey($key)
    {
        $setting = self::where('key', $key)->first();

        if (!$setting) {
            return null;
        }

        return $setting->typed_value;
    }

    /**
     * Set a setting by key.
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string|null $group
     * @param string|null $description
     * @return Setting
     */
    public static function setByKey($key, $value, $type = 'string', $group = null, $description = null)
    {
        $setting = self::firstOrNew(['key' => $key]);

        $setting->value = $value;
        $setting->type = $type;

        if ($group) {
            $setting->group = $group;
        }

        if ($description) {
            $setting->description = $description;
        }

        $setting->save();

        return $setting;
    }
}
