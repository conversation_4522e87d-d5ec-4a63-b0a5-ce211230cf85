<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create("ticket_categories", function (Blueprint $table) {
            $table->id();
            $table->string("name_ar");
            $table->string("name_en");
            $table->text("description_ar")->nullable();
            $table->text("description_en")->nullable();
            $table->boolean("is_active")->default(true);
            $table->foreignId("created_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("updated_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("ticket_categories");
    }
};

