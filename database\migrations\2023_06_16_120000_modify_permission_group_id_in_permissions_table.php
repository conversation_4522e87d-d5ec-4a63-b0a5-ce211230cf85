<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // نظرًا لأن SQLite لا يدعم تعديل الأعمدة مباشرة، سنقوم بإنشاء جدول مؤقت ونسخ البيانات
        if (Schema::hasColumn('permissions', 'permission_group_id')) {
            // حذف الجدول المؤقت إذا كان موجوداً
            if (Schema::hasTable('permissions_temp')) {
                Schema::drop('permissions_temp');
            }

            // إنشاء جدول مؤقت بنفس هيكل جدول الصلاحيات ولكن مع عمود permission_group_id قابل للقيمة الفارغة
            Schema::create('permissions_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('permission_group_id')->nullable(); // تغيير هنا لجعله قابلاً للقيمة الفارغة
                $table->string('name');
                $table->string('display_name');
                $table->string('description')->nullable();
                $table->timestamps();
                $table->string('slug')->nullable();
                $table->string('module')->nullable();
                $table->string('group_name')->nullable();
            });

            // نسخ البيانات من الجدول الأصلي إلى الجدول المؤقت
            DB::statement('INSERT INTO permissions_temp SELECT id, permission_group_id, name, display_name, description, created_at, updated_at, slug, module, group_name FROM permissions');

            // حذف الجدول الأصلي
            Schema::drop('permissions');

            // إعادة تسمية الجدول المؤقت إلى الاسم الأصلي
            Schema::rename('permissions_temp', 'permissions');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // لا نحتاج إلى تنفيذ أي شيء هنا لأن SQLite لا يدعم تعديل الأعمدة بسهولة
        // وفي حالة الحاجة إلى إعادة العمود إلى غير قابل للقيمة الفارغة، يمكن إنشاء هجرة جديدة
    }
};
