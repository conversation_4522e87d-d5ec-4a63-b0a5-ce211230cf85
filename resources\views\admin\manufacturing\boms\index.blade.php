@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Manufacturing Bill of Materials (BOMs)</h1>
        <a href="{{ route("admin.manufacturing.boms.create") }}" class="btn btn-primary">Add New BOM</a>
        <table class="table mt-3">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Item (Produces)</th>
                    <th>Version</th>
                    <th>Is Active</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {{-- @foreach ($boms as $bom) --}}
                {{-- Example Row --}}
                <tr>
                    <td>1</td>
                    <td>Standard BOM for Product A</td>
                    <td>Product A</td>
                    <td>1.0</td>
                    <td>Yes</td>
                    <td>
                        <a href="#" class="btn btn-sm btn-info">View</a>
                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                        <form action="#" method="POST" style="display:inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                {{-- @endforeach --}}
            </tbody>
        </table>
    </div>
@endsection
