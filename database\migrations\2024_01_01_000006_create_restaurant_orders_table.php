<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique(); // رقم الطلب
            $table->enum('type', ['dine_in', 'takeaway', 'delivery'])->default('dine_in'); // نوع الطلب
            $table->enum('status', ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled'])->default('pending'); // حالة الطلب
            $table->foreignId('table_id')->nullable()->constrained('restaurant_tables')->onDelete('set null'); // الطاولة
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null'); // العميل
            $table->string('customer_name')->nullable(); // اسم العميل
            $table->string('customer_phone')->nullable(); // هاتف العميل
            $table->text('delivery_address')->nullable(); // عنوان التوصيل
            $table->integer('guests_count')->default(1); // عدد الضيوف
            $table->decimal('subtotal', 10, 2)->default(0); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('service_charge', 10, 2)->default(0); // رسوم الخدمة
            $table->decimal('delivery_charge', 10, 2)->default(0); // رسوم التوصيل
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 2)->default(0); // المبلغ الإجمالي
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('special_instructions')->nullable(); // تعليمات خاصة
            $table->timestamp('order_time')->nullable(); // وقت الطلب
            $table->timestamp('estimated_ready_time')->nullable(); // الوقت المتوقع للجاهزية
            $table->timestamp('ready_time')->nullable(); // وقت الجاهزية الفعلي
            $table->timestamp('served_time')->nullable(); // وقت التقديم
            $table->foreignId('waiter_id')->nullable()->constrained('users')->onDelete('set null'); // النادل
            $table->foreignId('cashier_id')->nullable()->constrained('users')->onDelete('set null'); // الكاشير
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_orders');
    }
};
