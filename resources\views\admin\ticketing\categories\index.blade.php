@extends("layouts.admin")

@section("content")
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ __("Ticket Categories") }}</h1>
        <a href="{{ route("admin.ticketing.ticket_categories.create") }}" class="btn btn-primary">{{ __("Create New Category") }}</a>
    </div>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>{{ __("ID") }}</th>
                <th>{{ __("Name") }}</th>
                <th>{{ __("Description") }}</th>
                <th>{{ __("Active") }}</th>
                <th>{{ __("Actions") }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($categories as $category)
                <tr>
                    <td>{{ $category->id }}</td>
                    <td>{{ $category->name }}</td>
                    <td>{{ Str::limit($category->description, 50) }}</td>
                    <td>
                        @if($category->is_active)
                            <span class="badge badge-success">{{ __("Yes") }}</span>
                        @else
                            <span class="badge badge-danger">{{ __("No") }}</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route("admin.ticketing.ticket_categories.show", $category->id) }}" class="btn btn-sm btn-info">{{ __("View") }}</a>
                        <a href="{{ route("admin.ticketing.ticket_categories.edit", $category->id) }}" class="btn btn-sm btn-warning">{{ __("Edit") }}</a>
                        <form action="{{ route("admin.ticketing.ticket_categories.destroy", $category->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("{{ __("Are you sure you want to delete this category?") }}")">{{ __("Delete") }}</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5" class="text-center">{{ __("No ticket categories found.") }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $categories->links() }}
</div>
@endsection

