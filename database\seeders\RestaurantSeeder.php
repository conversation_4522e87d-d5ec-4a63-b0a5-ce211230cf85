<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Modules\Restaurant\RestaurantArea;
use App\Models\Modules\Restaurant\RestaurantTable;
use App\Models\Modules\Restaurant\MenuCategory;
use App\Models\Modules\Restaurant\MenuItem;
use App\Models\Modules\Restaurant\KitchenStation;
use App\Models\Modules\Restaurant\KitchenPrinter;
use App\Models\Modules\Restaurant\MenuItemModifier;
use App\Models\Modules\Restaurant\MenuItemModifierOption;
use App\Models\Modules\Restaurant\PosTerminal;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class RestaurantSeeder extends Seeder
{
    public function run(): void
    {
        // Get first branch and user
        $branch = Branch::first();
        $user = User::first();

        if (!$branch || !$user) {
            $this->command->error('Please create a branch and user first');
            return;
        }

        // Create Restaurant Areas
        $areas = [
            ['name' => 'الصالة الرئيسية', 'color' => '#007bff', 'sort_order' => 1],
            ['name' => 'التراس الخارجي', 'color' => '#28a745', 'sort_order' => 2],
            ['name' => 'القسم العائلي', 'color' => '#dc3545', 'sort_order' => 3],
            ['name' => 'الصالة الخاصة', 'color' => '#ffc107', 'sort_order' => 4],
        ];

        foreach ($areas as $areaData) {
            $area = RestaurantArea::create([
                'name' => $areaData['name'],
                'description' => 'منطقة ' . $areaData['name'] . ' في المطعم',
                'color' => $areaData['color'],
                'sort_order' => $areaData['sort_order'],
                'is_active' => true,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);

            // Create tables for each area
            $tableCount = rand(4, 8);
            for ($i = 1; $i <= $tableCount; $i++) {
                RestaurantTable::create([
                    'name' => 'طاولة ' . $i . ' - ' . $areaData['name'],
                    'number' => $areaData['sort_order'] . sprintf('%02d', $i),
                    'area_id' => $area->id,
                    'capacity' => rand(2, 8),
                    'status' => collect(['available', 'occupied', 'reserved'])->random(),
                    'x_position' => rand(1, 20),
                    'y_position' => rand(1, 15),
                    'shape' => collect(['round', 'square', 'rectangle'])->random(),
                    'is_active' => true,
                    'branch_id' => $branch->id,
                    'tenant_id' => $user->id,
                ]);
            }
        }

        // Create Kitchen Stations
        $stations = [
            ['name' => 'المشاوي', 'color' => '#dc3545'],
            ['name' => 'السلطات والمقبلات', 'color' => '#28a745'],
            ['name' => 'المشروبات', 'color' => '#17a2b8'],
            ['name' => 'الحلويات', 'color' => '#ffc107'],
            ['name' => 'الأطباق الساخنة', 'color' => '#fd7e14'],
        ];

        foreach ($stations as $stationData) {
            $station = KitchenStation::create([
                'name' => $stationData['name'],
                'description' => 'محطة ' . $stationData['name'] . ' في المطبخ',
                'color' => $stationData['color'],
                'sort_order' => array_search($stationData, $stations) + 1,
                'is_active' => true,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);

            // Create printer for each station
            KitchenPrinter::create([
                'name' => 'طابعة ' . $stationData['name'],
                'ip_address' => '192.168.1.' . (100 + array_search($stationData, $stations)),
                'port' => 9100,
                'type' => 'thermal',
                'connection_type' => 'network',
                'paper_width' => 80,
                'auto_cut' => true,
                'is_active' => true,
                'kitchen_station_id' => $station->id,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
        }

        // Create Menu Categories
        $categories = [
            ['name_ar' => 'المقبلات', 'name_en' => 'Appetizers', 'color' => '#28a745'],
            ['name_ar' => 'السلطات', 'name_en' => 'Salads', 'color' => '#20c997'],
            ['name_ar' => 'الشوربات', 'name_en' => 'Soups', 'color' => '#fd7e14'],
            ['name_ar' => 'الأطباق الرئيسية', 'name_en' => 'Main Courses', 'color' => '#dc3545'],
            ['name_ar' => 'المشاوي', 'name_en' => 'Grills', 'color' => '#6f42c1'],
            ['name_ar' => 'المعكرونة', 'name_en' => 'Pasta', 'color' => '#e83e8c'],
            ['name_ar' => 'البيتزا', 'name_en' => 'Pizza', 'color' => '#fd7e14'],
            ['name_ar' => 'المشروبات', 'name_en' => 'Beverages', 'color' => '#17a2b8'],
            ['name_ar' => 'الحلويات', 'name_en' => 'Desserts', 'color' => '#ffc107'],
        ];

        foreach ($categories as $index => $categoryData) {
            MenuCategory::create([
                'name_ar' => $categoryData['name_ar'],
                'name_en' => $categoryData['name_en'],
                'description_ar' => 'فئة ' . $categoryData['name_ar'],
                'description_en' => $categoryData['name_en'] . ' category',
                'color' => $categoryData['color'],
                'sort_order' => $index + 1,
                'is_active' => true,
                'is_available' => true,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
        }

        // Create Menu Items
        $menuItems = [
            // المقبلات
            ['name_ar' => 'حمص بالطحينة', 'name_en' => 'Hummus with Tahini', 'price' => 25.00, 'category' => 1, 'station' => 2],
            ['name_ar' => 'متبل باذنجان', 'name_en' => 'Baba Ganoush', 'price' => 28.00, 'category' => 1, 'station' => 2],
            ['name_ar' => 'فتوش', 'name_en' => 'Fattoush', 'price' => 32.00, 'category' => 1, 'station' => 2],
            
            // السلطات
            ['name_ar' => 'سلطة يونانية', 'name_en' => 'Greek Salad', 'price' => 35.00, 'category' => 2, 'station' => 2],
            ['name_ar' => 'سلطة قيصر', 'name_en' => 'Caesar Salad', 'price' => 38.00, 'category' => 2, 'station' => 2],
            ['name_ar' => 'تبولة', 'name_en' => 'Tabbouleh', 'price' => 30.00, 'category' => 2, 'station' => 2],
            
            // الأطباق الرئيسية
            ['name_ar' => 'دجاج مشوي', 'name_en' => 'Grilled Chicken', 'price' => 65.00, 'category' => 4, 'station' => 1],
            ['name_ar' => 'لحم مشوي', 'name_en' => 'Grilled Beef', 'price' => 85.00, 'category' => 4, 'station' => 1],
            ['name_ar' => 'سمك مشوي', 'name_en' => 'Grilled Fish', 'price' => 75.00, 'category' => 4, 'station' => 1],
            
            // المشروبات
            ['name_ar' => 'عصير برتقال طازج', 'name_en' => 'Fresh Orange Juice', 'price' => 18.00, 'category' => 8, 'station' => 3],
            ['name_ar' => 'شاي أحمر', 'name_en' => 'Black Tea', 'price' => 12.00, 'category' => 8, 'station' => 3],
            ['name_ar' => 'قهوة عربية', 'name_en' => 'Arabic Coffee', 'price' => 15.00, 'category' => 8, 'station' => 3],
            
            // الحلويات
            ['name_ar' => 'كنافة نابلسية', 'name_en' => 'Nablus Knafeh', 'price' => 35.00, 'category' => 9, 'station' => 4],
            ['name_ar' => 'بقلاوة', 'name_en' => 'Baklava', 'price' => 28.00, 'category' => 9, 'station' => 4],
            ['name_ar' => 'مهلبية', 'name_en' => 'Muhallabia', 'price' => 22.00, 'category' => 9, 'station' => 4],
        ];

        foreach ($menuItems as $itemData) {
            MenuItem::create([
                'name_ar' => $itemData['name_ar'],
                'name_en' => $itemData['name_en'],
                'description_ar' => 'وصف ' . $itemData['name_ar'],
                'description_en' => $itemData['name_en'] . ' description',
                'sku' => 'ITEM' . str_pad(array_search($itemData, $menuItems) + 1, 3, '0', STR_PAD_LEFT),
                'price' => $itemData['price'],
                'cost_price' => $itemData['price'] * 0.6,
                'tax_rate' => 15.00,
                'preparation_time' => rand(10, 30),
                'calories' => rand(200, 800),
                'is_spicy' => rand(0, 1),
                'is_vegetarian' => rand(0, 1),
                'is_vegan' => rand(0, 1),
                'is_gluten_free' => rand(0, 1),
                'is_available' => true,
                'is_active' => true,
                'sort_order' => array_search($itemData, $menuItems) + 1,
                'category_id' => $itemData['category'],
                'kitchen_station_id' => $itemData['station'],
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
        }

        // Create Modifiers
        $modifiers = [
            [
                'name_ar' => 'درجة الطبخ',
                'name_en' => 'Cooking Level',
                'type' => 'single',
                'is_required' => true,
                'options' => [
                    ['name_ar' => 'نيء', 'name_en' => 'Rare', 'price' => 0],
                    ['name_ar' => 'متوسط', 'name_en' => 'Medium', 'price' => 0],
                    ['name_ar' => 'مطبوخ جيداً', 'name_en' => 'Well Done', 'price' => 0],
                ]
            ],
            [
                'name_ar' => 'الإضافات',
                'name_en' => 'Add-ons',
                'type' => 'multiple',
                'is_required' => false,
                'options' => [
                    ['name_ar' => 'جبن إضافي', 'name_en' => 'Extra Cheese', 'price' => 5],
                    ['name_ar' => 'خضار إضافية', 'name_en' => 'Extra Vegetables', 'price' => 8],
                    ['name_ar' => 'صوص إضافي', 'name_en' => 'Extra Sauce', 'price' => 3],
                ]
            ],
        ];

        foreach ($modifiers as $modifierData) {
            $modifier = MenuItemModifier::create([
                'name_ar' => $modifierData['name_ar'],
                'name_en' => $modifierData['name_en'],
                'type' => $modifierData['type'],
                'is_required' => $modifierData['is_required'],
                'min_selections' => $modifierData['is_required'] ? 1 : 0,
                'max_selections' => $modifierData['type'] === 'single' ? 1 : 3,
                'sort_order' => array_search($modifierData, $modifiers) + 1,
                'is_active' => true,
                'tenant_id' => $user->id,
            ]);

            foreach ($modifierData['options'] as $optionData) {
                MenuItemModifierOption::create([
                    'modifier_id' => $modifier->id,
                    'name_ar' => $optionData['name_ar'],
                    'name_en' => $optionData['name_en'],
                    'price_adjustment' => $optionData['price'],
                    'sort_order' => array_search($optionData, $modifierData['options']) + 1,
                    'is_default' => array_search($optionData, $modifierData['options']) === 0,
                    'is_active' => true,
                ]);
            }
        }

        // Create POS Terminal
        PosTerminal::create([
            'name' => 'نقطة البيع الرئيسية',
            'terminal_id' => 'POS001',
            'location' => 'الكاشير الرئيسي',
            'type' => 'restaurant',
            'receipt_printer_ip' => '*************',
            'receipt_printer_port' => 9100,
            'is_active' => true,
            'branch_id' => $branch->id,
            'tenant_id' => $user->id,
        ]);

        $this->command->info('Restaurant data seeded successfully!');
    }
}
