<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("bom_items", function (Blueprint $table) {
            $table->id();
            $table->foreignId("bom_id")->constrained("boms")->onDelete("cascade");
            $table->foreignId("item_id")->constrained("items")->comment("Component item (raw material or semi-finished good)");
            $table->decimal("quantity_required", 15, 4);
            $table->string("unit_of_measure_ar")->nullable(); // Unit of measure for the component
            $table->string("unit_of_measure_en")->nullable();
            $table->decimal("scrap_percentage", 8, 4)->default(0.0000)->comment("Expected scrap/waste percentage for this component");
            $table->text("notes_ar")->nullable();
            $table->text("notes_en")->nullable();
            // You might add fields for operation sequence if manufacturing steps are complex
            // $table->integer("operation_sequence")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("bom_items");
    }
};

