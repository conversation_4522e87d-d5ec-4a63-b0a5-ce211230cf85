@extends('layouts.admin')

@section('title', 'تكامل خدمات التوصيل')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-truck me-2"></i>
                        تكامل خدمات التوصيل
                    </h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <p class="text-muted mb-4">إدارة وتكوين التكامل مع مزودي خدمات التوصيل الخارجيين</p>

                    <div class="row">
                        <!-- تكامل أرامكس -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="bi bi-box-seam me-2"></i>
                                        أرامكس (Aramex)
                                    </h6>
                                    <span class="badge bg-light text-success">مفعل</span>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>الحالة:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-success">مفعل</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>مفتاح API:</strong></div>
                                        <div class="col-sm-8">
                                            <code>sk_live_********</code>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>آخر تحديث:</strong></div>
                                        <div class="col-sm-8">2024-01-15</div>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('admin.integrations.delivery.edit', 'aramex') }}" class="btn btn-warning btn-sm">
                                            <i class="bi bi-gear me-1"></i>
                                            تكوين
                                        </a>
                                        <button class="btn btn-info btn-sm" onclick="testConnection('aramex')">
                                            <i class="bi bi-wifi me-1"></i>
                                            اختبار
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تكامل SMSA -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="bi bi-truck me-2"></i>
                                        SMSA Express
                                    </h6>
                                    <span class="badge bg-light text-secondary">غير مفعل</span>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>الحالة:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-secondary">غير مفعل</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>مفتاح API:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="text-muted">غير محدد</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>آخر تحديث:</strong></div>
                                        <div class="col-sm-8">-</div>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('admin.integrations.delivery.edit', 'smsa') }}" class="btn btn-warning btn-sm">
                                            <i class="bi bi-gear me-1"></i>
                                            تكوين
                                        </a>
                                        <button class="btn btn-secondary btn-sm" disabled>
                                            <i class="bi bi-wifi me-1"></i>
                                            اختبار
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5 class="card-title">156</h5>
                                    <p class="card-text">شحنات اليوم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5 class="card-title">1,234</h5>
                                    <p class="card-text">شحنات هذا الشهر</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5 class="card-title">23</h5>
                                    <p class="card-text">في الانتظار</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5 class="card-title">98.5%</h5>
                                    <p class="card-text">معدل النجاح</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-center mt-4">
                        <a href="{{ route('admin.integrations.delivery.show') }}" class="btn btn-info me-2">
                            <i class="bi bi-list-ul me-1"></i>
                            عرض السجلات والتفاصيل
                        </a>
                        <a href="{{ route('admin.integrations.delivery.orders.index') }}" class="btn btn-primary">
                            <i class="bi bi-box-seam me-1"></i>
                            إدارة الطلبات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function testConnection(provider) {
    alert(`سيتم اختبار الاتصال مع ${provider === 'aramex' ? 'أرامكس' : 'SMSA Express'} قريباً`);
}
</script>

    {{-- This view might list multiple providers or a general status.
         The form view would be for specific provider configurations.
         The show view could be for logs or detailed status of all delivery integrations. --}}

</div>
@endsection

