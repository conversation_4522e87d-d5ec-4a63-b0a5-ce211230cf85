@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Delivery Service Integrations</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <p>Manage and configure integrations with external delivery service providers.</p>

    {{-- Example: Listing configured delivery services --}}
    <div class="card mb-3">
        <div class="card-header">Aramex Integration</div>
        <div class="card-body">
            <p>Status: {{-- $settings->aramex_enabled ? "Active" : "Inactive" --}} Active</p>
            <p>API Key: {{-- $settings->aramex_api_key ? substr($settings->aramex_api_key, 0, 5) . "..." : "Not Set" --}} sk_live_********</p>
            <a href="{{-- route("admin.integrations.delivery.edit", ["provider" => "aramex"]) --}}" class="btn btn-warning btn-sm">Configure Aramex</a>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">SMSA Express Integration</div>
        <div class="card-body">
            <p>Status: {{-- $settings->smsa_enabled ? "Active" : "Inactive" --}} Inactive</p>
            <p>API Key: {{-- $settings->smsa_api_key ? substr($settings->smsa_api_key, 0, 5) . "..." : "Not Set" --}} Not Set</p>
            <a href="{{-- route("admin.integrations.delivery.edit", ["provider" => "smsa"]) --}}" class="btn btn-warning btn-sm">Configure SMSA</a>
        </div>
    </div>

    {{-- Placeholder for a general settings or log view link --}}
    <a href="{{ route("admin.integrations.delivery.show") }}" class="btn btn-info">View Integration Logs/Details</a>

    {{-- This view might list multiple providers or a general status. 
         The form view would be for specific provider configurations. 
         The show view could be for logs or detailed status of all delivery integrations. --}}

</div>
@endsection

