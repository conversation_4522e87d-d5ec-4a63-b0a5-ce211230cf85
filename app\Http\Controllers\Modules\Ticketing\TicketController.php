<?php

namespace App\Http\Controllers\Modules\Ticketing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Ticketing\Ticket;
use App\Models\Modules\Ticketing\TicketCategory;
use App\Models\Modules\Ticketing\TicketPriority;
use App\Models\Modules\Ticketing\TicketStatus;
use App\Models\User;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Services\WhatsAppService; // Import WhatsAppService

class TicketController extends Controller
{
    protected $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
    }

    public function index()
    {
        $tickets = Ticket::with(["user", "category", "priority", "status", "assignedTo"])->latest()->paginate(15);
        return view("admin.ticketing.tickets.index", compact("tickets"));
    }

    public function create()
    {
        $users = User::whereHas("roles", function ($query) {
            $query->where("name", "!=", "superadmin");
        })->get();
        $agents = User::whereHas("roles", function ($query) {
            $query->whereIn("name", ["support_agent", "admin", "superadmin"]);
        })->get();
        $categories = TicketCategory::where("is_active", true)->orderBy("name")->get();
        $priorities = TicketPriority::where("is_active", true)->orderBy("level")->get();
        $statuses = TicketStatus::where("is_active", true)->orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        $defaultStatus = TicketStatus::where("is_default_new", true)->first();

        return view("admin.ticketing.tickets.form", compact("users", "categories", "priorities", "statuses", "branches", "agents", "defaultStatus"));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "subject" => "required|string|max:255",
            "description" => "required|string",
            "user_id" => "required|exists:users,id",
            "ticket_category_id" => "required|exists:ticket_categories,id",
            "ticket_priority_id" => "required|exists:ticket_priorities,id",
            "ticket_status_id" => "required|exists:ticket_statuses,id",
            "assigned_to_user_id" => "nullable|exists:users,id",
            "branch_id" => "nullable|exists:branches,id",
            "attachments.*" => "nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx,xls,xlsx,txt|max:2048"
        ]);

        $ticket = new Ticket($validatedData);
        $ticket->created_by_type = Auth::user()->isAdmin() ? "agent" : "user";
        $ticket->last_reply_at = now();
        $ticket->save();

        if ($request->hasFile("attachments")) {
            foreach ($request->file("attachments") as $file) {
                $filePath = $file->store("ticket_attachments/" . $ticket->id, "public");
                $ticket->attachments()->create([
                    "user_id" => Auth::id(),
                    "file_path" => $filePath,
                    "file_name" => $file->getClientOriginalName(),
                    "file_mime_type" => $file->getMimeType(),
                    "file_size" => $file->getSize(),
                ]);
            }
        }

        // Send WhatsApp notification for new ticket
        $ticketCreator = User::find($ticket->user_id);
        if ($ticketCreator && $ticketCreator->phone_number) {
            $this->whatsAppService->sendMessage(
                "new_ticket_created", // Assuming a template named "new_ticket_created"
                $ticketCreator,
                [
                    "ticket_id" => $ticket->id,
                    "ticket_subject" => $ticket->subject,
                    "user_name" => $ticketCreator->name
                ],
                $ticket
            );
        }

        return redirect()->route("admin.ticketing.tickets.show", $ticket->id)->with("success", __("Ticket created successfully."));
    }

    public function show(Ticket $ticket)
    {
        $ticket->load(["user", "category", "priority", "status", "assignedTo", "branch", "replies.user", "replies.attachments", "attachments"]);
        return view("admin.ticketing.tickets.show", compact("ticket"));
    }

    public function edit(Ticket $ticket)
    {
        $users = User::whereHas("roles", function ($query) {
            $query->where("name", "!=", "superadmin");
        })->get();
         $agents = User::whereHas("roles", function ($query) {
            $query->whereIn("name", ["support_agent", "admin", "superadmin"]);
        })->get();
        $categories = TicketCategory::where("is_active", true)->orderBy("name")->get();
        $priorities = TicketPriority::where("is_active", true)->orderBy("level")->get();
        $statuses = TicketStatus::where("is_active", true)->orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        return view("admin.ticketing.tickets.form", compact("ticket", "users", "categories", "priorities", "statuses", "branches", "agents"));
    }

    public function update(Request $request, Ticket $ticket)
    {
        if ($request->input("update_type") === "reply") {
            $validatedReply = $request->validate([
                "reply_body" => "required|string",
                "is_internal_note" => "nullable|boolean",
                "reply_attachments.*" => "nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx,xls,xlsx,txt|max:2048"
            ]);

            $reply = $ticket->replies()->create([
                "user_id" => Auth::id(),
                "body" => $validatedReply["reply_body"],
                "is_internal_note" => $request->has("is_internal_note"),
            ]);

            if ($request->hasFile("reply_attachments")) {
                foreach ($request->file("reply_attachments") as $file) {
                    $filePath = $file->store("ticket_attachments/" . $ticket->id . "/replies/" . $reply->id, "public");
                    $reply->attachments()->create([
                        "user_id" => Auth::id(),
                        "file_path" => $filePath,
                        "file_name" => $file->getClientOriginalName(),
                        "file_mime_type" => $file->getMimeType(),
                        "file_size" => $file->getSize(),
                    ]);
                }
            }
            $ticket->touch("last_reply_at");

            // Send WhatsApp notification for new reply (if not internal)
            if (!$reply->is_internal_note) {
                $ticketCreator = User::find($ticket->user_id);
                if ($ticketCreator && $ticketCreator->phone_number && $reply->user_id !== $ticketCreator->id) { // Notify ticket creator if replier is not them
                    $this->whatsAppService->sendMessage(
                        "new_ticket_reply", // Assuming a template named "new_ticket_reply"
                        $ticketCreator,
                        [
                            "ticket_id" => $ticket->id,
                            "ticket_subject" => $ticket->subject,
                            "replier_name" => $reply->user->name ?? "Agent"
                        ],
                        $ticket
                    );
                }
                // Notify assigned agent if exists and replier is not them
                if ($ticket->assignedTo && $ticket->assignedTo->phone_number && $reply->user_id !== $ticket->assigned_to_user_id) {
                     $this->whatsAppService->sendMessage(
                        "new_ticket_reply_agent", // Assuming a template for agent
                        $ticket->assignedTo,
                        [
                            "ticket_id" => $ticket->id,
                            "ticket_subject" => $ticket->subject,
                            "replier_name" => $reply->user->name ?? "User"
                        ],
                        $ticket
                    );
                }
            }

            return redirect()->route("admin.ticketing.tickets.show", $ticket->id)->with("success", __("Reply added successfully."));
        }

        $validatedData = $request->validate([
            "subject" => "required|string|max:255",
            "description" => "required|string",
            "user_id" => "required|exists:users,id",
            "ticket_category_id" => "required|exists:ticket_categories,id",
            "ticket_priority_id" => "required|exists:ticket_priorities,id",
            "ticket_status_id" => "required|exists:ticket_statuses,id",
            "assigned_to_user_id" => "nullable|exists:users,id",
            "branch_id" => "nullable|exists:branches,id",
            "attachments.*" => "nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx,xls,xlsx,txt|max:2048"
        ]);

        $oldStatusId = $ticket->ticket_status_id;
        $ticket->update($validatedData);

        if ($request->hasFile("attachments")) {
            foreach ($request->file("attachments") as $file) {
                $filePath = $file->store("ticket_attachments/" . $ticket->id, "public");
                $ticket->attachments()->create([
                    "user_id" => Auth::id(),
                    "file_path" => $filePath,
                    "file_name" => $file->getClientOriginalName(),
                    "file_mime_type" => $file->getMimeType(),
                    "file_size" => $file->getSize(),
                ]);
            }
        }

        $status = TicketStatus::find($validatedData["ticket_status_id"]);
        if ($status && $status->type === "closed" && !$ticket->closed_at) {
            $ticket->closed_at = now();
            $ticket->save();
        } elseif ($status && $status->type !== "closed" && $ticket->closed_at) {
            $ticket->closed_at = null;
            $ticket->save();
        }

        // Send WhatsApp notification for status change
        if ($oldStatusId !== $ticket->ticket_status_id) {
            $ticketCreator = User::find($ticket->user_id);
            if ($ticketCreator && $ticketCreator->phone_number) {
                $this->whatsAppService->sendMessage(
                    "ticket_status_updated", // Assuming a template named "ticket_status_updated"
                    $ticketCreator,
                    [
                        "ticket_id" => $ticket->id,
                        "ticket_subject" => $ticket->subject,
                        "new_status" => $ticket->status->name ?? "N/A"
                    ],
                    $ticket
                );
            }
        }

        return redirect()->route("admin.ticketing.tickets.show", $ticket->id)->with("success", __("Ticket updated successfully."));
    }

    public function destroy(Ticket $ticket)
    {
        $ticket->delete();
        return redirect()->route("admin.ticketing.tickets.index")->with("success", __("Ticket deleted successfully."));
    }

    public function reports()
    {
        // إحصائيات التذاكر
        $totalTickets = Ticket::count();
        $openTickets = Ticket::whereHas('status', function($query) {
            $query->where('type', 'open');
        })->count();
        $pendingTickets = Ticket::whereHas('status', function($query) {
            $query->where('type', 'pending');
        })->count();
        $closedTickets = Ticket::whereHas('status', function($query) {
            $query->where('type', 'closed');
        })->count();

        // إحصائيات حسب الفئة
        $ticketsByCategory = Ticket::with('category')
            ->selectRaw('ticket_category_id, count(*) as count')
            ->groupBy('ticket_category_id')
            ->get();

        // إحصائيات حسب الأولوية
        $ticketsByPriority = Ticket::with('priority')
            ->selectRaw('ticket_priority_id, count(*) as count')
            ->groupBy('ticket_priority_id')
            ->get();

        // إحصائيات حسب الحالة
        $ticketsByStatus = Ticket::with('status')
            ->selectRaw('ticket_status_id, count(*) as count')
            ->groupBy('ticket_status_id')
            ->get();

        // إحصائيات حسب الوكيل المعين
        $ticketsByAgent = Ticket::with('assignedTo')
            ->whereNotNull('assigned_to_user_id')
            ->selectRaw('assigned_to_user_id, count(*) as count')
            ->groupBy('assigned_to_user_id')
            ->get();

        // التذاكر الحديثة (آخر 30 يوم)
        $recentTickets = Ticket::where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.ticketing.reports.index', compact(
            'totalTickets',
            'openTickets',
            'pendingTickets',
            'closedTickets',
            'ticketsByCategory',
            'ticketsByPriority',
            'ticketsByStatus',
            'ticketsByAgent',
            'recentTickets'
        ));
    }
}

