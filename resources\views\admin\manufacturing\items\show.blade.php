@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Manufacturing Item Details: {{-- $item->name --}}</h1>
        <table class="table table-bordered mt-3">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $item->id --}}</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{-- $item->name --}}</td>
                </tr>
                <tr>
                    <th>Item Code</th>
                    <td>{{-- $item->item_code --}}</td>
                </tr>
                <tr>
                    <th>Description</th>
                    <td>{{-- $item->description --}}</td>
                </tr>
                <tr>
                    <th>Item Type</th>
                    <td>{{-- ucfirst(str_replace("_", " ", $item->item_type)) --}}</td>
                </tr>
                <tr>
                    <th>Unit of Measure</th>
                    <td>{{-- $item->unit_of_measure --}}</td>
                </tr>
                <tr>
                    <th>Standard Cost</th>
                    <td>{{-- number_format($item->standard_cost, 4) --}}</td>
                </tr>
                <tr>
                    <th>Branch</th>
                    <td>{{-- $item->branch ? $item->branch->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Default Account</th>
                    <td>{{-- $item->account ? $item->account->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Is Manufactured?</th>
                    <td>{{-- $item->is_manufactured ? "Yes" : "No" --}}</td>
                </tr>
                <tr>
                    <th>Is Purchased?</th>
                    <td>{{-- $item->is_purchased ? "Yes" : "No" --}}</td>
                </tr>
                <tr>
                    <th>Is Sold?</th>
                    <td>{{-- $item->is_sold ? "Yes" : "No" --}}</td>
                </tr>
                <tr>
                    <th>Min Stock Level</th>
                    <td>{{-- $item->min_stock_level ? number_format($item->min_stock_level, 4) : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Max Stock Level</th>
                    <td>{{-- $item->max_stock_level ? number_format($item->max_stock_level, 4) : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{-- $item->created_at --}}</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{-- $item->updated_at --}}</td>
                </tr>
            </tbody>
        </table>
        <a href="{{-- route("admin.manufacturing.items.index") --}}" class="btn btn-secondary">Back to List</a>
        <a href="{{-- route("admin.manufacturing.items.edit", $item->id) --}}" class="btn btn-warning">Edit</a>
    </div>
@endsection
