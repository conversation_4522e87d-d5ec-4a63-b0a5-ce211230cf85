@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($priority) ? __("Edit Ticket Priority") : __("Create Ticket Priority") }}</h1>
    <form action="{{ isset($priority) ? route("admin.ticketing.priorities.update", $priority->id) : route("admin.ticketing.priorities.store") }}" method="POST">
        @csrf
        @if(isset($priority))
            @method("PUT")
        @endif
        <div class="form-group">
            <label for="name">{{ __("Name") }}</label>
            <input type="text" name="name" id="name" class="form-control @error("name") is-invalid @enderror" value="{{ old("name", $priority->name ?? "") }}" required>
            @error("name")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group">
            <label for="level">{{ __("Level (e.g., 1 for Low, 4 for Critical)") }}</label>
            <input type="number" name="level" id="level" class="form-control @error("level") is-invalid @enderror" value="{{ old("level", $priority->level ?? "") }}" required>
            @error("level")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group">
            <label for="color">{{ __("Color (e.g., #RRGGBB)") }}</label>
            <input type="text" name="color" id="color" class="form-control @error("color") is-invalid @enderror" value="{{ old("color", $priority->color ?? "") }}">
            @error("color")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old("is_active", $priority->is_active ?? true) ? "checked" : "" }}>
            <label class="form-check-label" for="is_active">{{ __("Active") }}</label>
        </div>
        <button type="submit" class="btn btn-primary">{{ isset($priority) ? __("Update Priority") : __("Create Priority") }}</button>
        <a href="{{ route("admin.ticketing.priorities.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

