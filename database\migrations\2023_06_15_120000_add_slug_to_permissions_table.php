<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use App\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة عمود slug إلى جدول الصلاحيات إذا لم يكن موجوداً
        if (!Schema::hasColumn('permissions', 'slug')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->string('slug')->after('name')->unique()->nullable();
            });

            // تحديث قيم slug للصلاحيات الموجودة
            $permissions = Permission::all();
            foreach ($permissions as $permission) {
                $permission->slug = Str::slug($permission->name);
                $permission->save();
            }
        }

        // إضافة عمود module إلى جدول الصلاحيات إذا لم يكن موجوداً
        if (!Schema::hasColumn('permissions', 'module')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->string('module')->after('slug')->nullable();
            });
        }

        // إضافة عمود group_name إذا لم يكن موجوداً
        if (!Schema::hasColumn('permissions', 'group_name')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->string('group_name')->after('slug')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف عمود group_name
        if (Schema::hasColumn('permissions', 'group_name')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->dropColumn('group_name');
            });
        }

        // حذف عمود module
        if (Schema::hasColumn('permissions', 'module')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->dropColumn('module');
            });
        }

        // حذف عمود slug
        if (Schema::hasColumn('permissions', 'slug')) {
            Schema::table('permissions', function (Blueprint $table) {
                $table->dropColumn('slug');
            });
        }
    }
};
