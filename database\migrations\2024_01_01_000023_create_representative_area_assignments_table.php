<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('representative_area_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sales_representative_id')->constrained('sales_representatives')->onDelete('cascade');
            $table->foreignId('sales_area_id')->constrained('sales_areas')->onDelete('cascade');
            $table->date('assigned_date'); // تاريخ التعيين
            $table->date('effective_from'); // ساري من
            $table->date('effective_to')->nullable(); // ساري إلى
            $table->enum('assignment_type', ['primary', 'secondary', 'temporary'])->default('primary'); // نوع التعيين
            $table->decimal('commission_override', 5, 2)->nullable(); // تجاوز العمولة
            $table->text('assignment_notes')->nullable(); // ملاحظات التعيين
            $table->boolean('is_active')->default(true);
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade'); // من قام بالتعيين
            $table->timestamps();

            $table->unique(['sales_representative_id', 'sales_area_id', 'effective_from'], 'unique_rep_area_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('representative_area_assignments');
    }
};
