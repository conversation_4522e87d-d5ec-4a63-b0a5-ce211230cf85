<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Modules\GeneralLedger\Account;
// Assuming you might have a currencies table, if not, this can be simplified or removed.
// use App\Models\Currency; 

class ManufacturingProductionCost extends Model
{
    use HasFactory;

    protected $table = 'manufacturing_production_costs';

    protected $fillable = [
        'work_order_id',
        'item_id', // Optional: if cost is directly tied to a specific produced item from the WO
        'cost_type', // e.g., 'material', 'labor', 'overhead', 'other'
        'description',
        'amount',
        'currency_id', // Optional
        'account_id', // Account to debit/credit
        'cost_date',
        'created_by_id',
    ];

    protected $casts = [
        'amount' => 'decimal:4',
        'cost_date' => 'datetime',
    ];

    public function workOrder()
    {
        return $this->belongsTo(ManufacturingWorkOrder::class, 'work_order_id');
    }

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    // public function currency() // Uncomment if you have a Currency model
    // {
    //     return $this->belongsTo(Currency::class, 'currency_id');
    // }
}

