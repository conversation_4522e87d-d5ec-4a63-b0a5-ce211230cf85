@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("admin_views.boms_list") }}</h1>
    <a href="{{ route("admin.boms.create") }}" class="btn btn-primary mb-3">{{ __("admin_views.add_new_bom") }}</a>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>{{ __("admin_views.name_ar") }}</th>
                <th>{{ __("admin_views.name_en") }}</th>
                <th>{{ __("admin_views.bom_code") }}</th>
                <th>{{ __("admin_views.item_to_produce") }}</th>
                <th>{{ __("admin_views.branch") }}</th>
                <th>{{ __("admin_views.is_default") }}</th>
                <th>{{ __("admin_views.actions") }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($boms as $bom)
                <tr>
                    <td>{{ $bom->id }}</td>
                    <td>{{ $bom->name_ar }}</td>
                    <td>{{ $bom->name_en }}</td>
                    <td>{{ $bom->bom_code }}</td>
                    <td>{{ $bom->item ? $bom->item->name_ar : "-" }}</td>
                    <td>{{ $bom->branch ? $bom->branch->name_ar : "-" }}</td>
                    <td>{{ $bom->is_default ? __("admin_views.yes") : __("admin_views.no") }}</td>
                    <td>
                        <a href="{{ route("admin.boms.show", $bom->id) }}" class="btn btn-info btn-sm">{{ __("admin_views.view") }}</a>
                        <a href="{{ route("admin.boms.edit", $bom->id) }}" class="btn btn-warning btn-sm">{{ __("admin_views.edit") }}</a>
                        <form action="{{ route("admin.boms.destroy", $bom->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("{{ __("admin_views.confirm_delete") }}")">{{ __("admin_views.delete") }}</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="text-center">{{ __("admin_views.no_boms_found") }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $boms->links() }}
</div>
@endsection

