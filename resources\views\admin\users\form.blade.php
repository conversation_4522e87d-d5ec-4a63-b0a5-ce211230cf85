@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($user) ? __("Edit User") : __("Add New User") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($user) ? route("admin.users.update", $user->id) : route("admin.users.store") }}" method="POST">
        @csrf
        @if(isset($user))
            @method("PUT")
        @endif

        <div class="mb-3">
            <label for="name" class="form-label">{{ __("Full Name") }}</label>
            <input type="text" class="form-control" id="name" name="name" value="{{ old("name", $user->name ?? "") }}" required>
        </div>

        <div class="mb-3">
            <label for="username" class="form-label">{{ __("Username") }}</label>
            <input type="text" class="form-control" id="username" name="username" value="{{ old("username", $user->username ?? "") }}" required>
        </div>

        <div class="mb-3">
            <label for="email" class="form-label">{{ __("Email Address") }}</label>
            <input type="email" class="form-control" id="email" name="email" value="{{ old("email", $user->email ?? "") }}" required>
        </div>

        <div class="mb-3">
            <label for="phone" class="form-label">{{ __("Phone Number") }}</label>
            <input type="text" class="form-control" id="phone" name="phone" value="{{ old("phone", $user->phone ?? "") }}">
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">{{ __("Password") }}</label>
            <input type="password" class="form-control" id="password" name="password" {{ isset($user) ? "" : "required" }}>
            @if(isset($user))
                <small class="form-text text-muted">{{ __("Leave blank to keep current password.") }}</small>
            @endif
        </div>

        <div class="mb-3">
            <label for="password_confirmation" class="form-label">{{ __("Confirm Password") }}</label>
            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" {{ isset($user) ? "" : "required" }}>
        </div>

        <div class="mb-3">
            <label for="branch_id" class="form-label">{{ __("Branch") }}</label>
            <select class="form-control" id="branch_id" name="branch_id">
                <option value="">{{ __("Select Branch") }}</option>
                @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ (isset($user) && $user->branch_id == $branch->id) || old("branch_id") == $branch->id ? "selected" : "" }}>
                        {{ $branch->name_ar }} ({{ $branch->name_en }})
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-3">
            <label for="role_id" class="form-label">{{ __("Role") }}</label>
            <select class="form-control" id="role_id" name="role_id" required>
                <option value="">{{ __("Select Role") }}</option>
                @foreach($roles as $role)
                    <option value="{{ $role->id }}" {{ (isset($user) && $user->role_id == $role->id) || old("role_id") == $role->id ? "selected" : "" }}>
                        {{ $role->name_ar }} ({{ $role->name_en }})
                    </option>
                @endforeach
            </select>
        </div>
        
        <div class="mb-3">
            <label for="language" class="form-label">{{ __("Preferred Language") }}</label>
            <select class="form-control" id="language" name="language">
                <option value="ar" {{ (isset($user) && $user->language == "ar") || old("language") == "ar" ? "selected" : "" }}>{{ __("Arabic") }}</option>
                <option value="en" {{ (isset($user) && $user->language == "en") || old("language") == "en" ? "selected" : "" }}>{{ __("English") }}</option>
            </select>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ (isset($user) && $user->is_active) || old("is_active", 1) ? "checked" : "" }}>
            <label class="form-check-label" for="is_active">{{ __("Is Active") }}</label>
        </div>

        <button type="submit" class="btn btn-success">{{ isset($user) ? __("Update User") : __("Save User") }}</button>
        <a href="{{ route("admin.users.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

