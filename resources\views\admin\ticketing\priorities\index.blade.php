@extends("layouts.admin")

@section("content")
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ __("Ticket Priorities") }}</h1>
        <a href="{{ route("admin.ticketing.ticket_priorities.create") }}" class="btn btn-primary">{{ __("Create New Priority") }}</a>
    </div>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>{{ __("ID") }}</th>
                <th>{{ __("Name") }}</th>
                <th>{{ __("Level") }}</th>
                <th>{{ __("Color") }}</th>
                <th>{{ __("Active") }}</th>
                <th>{{ __("Actions") }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($priorities as $priority)
                <tr>
                    <td>{{ $priority->id }}</td>
                    <td>{{ $priority->name }}</td>
                    <td>{{ $priority->level }}</td>
                    <td>
                        <span style="display:inline-block; width: 20px; height: 20px; background-color: {{ $priority->color ?? '#FFFFFF' }}; border: 1px solid #ccc;"></span>
                        {{ $priority->color }}
                    </td>
                    <td>
                        @if($priority->is_active)
                            <span class="badge badge-success">{{ __("Yes") }}</span>
                        @else
                            <span class="badge badge-danger">{{ __("No") }}</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route("admin.ticketing.ticket_priorities.show", $priority->id) }}" class="btn btn-sm btn-info">{{ __("View") }}</a>
                        <a href="{{ route("admin.ticketing.ticket_priorities.edit", $priority->id) }}" class="btn btn-sm btn-warning">{{ __("Edit") }}</a>
                        <form action="{{ route("admin.ticketing.ticket_priorities.destroy", $priority->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("{{ __("Are you sure you want to delete this priority?") }}")">{{ __("Delete") }}</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">{{ __("No ticket priorities found.") }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $priorities->links() }}
</div>
@endsection

