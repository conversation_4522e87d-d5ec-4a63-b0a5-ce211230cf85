<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class MfgBom extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_boms';

    protected $fillable = [
        'bom_code',
        'name',
        'description',
        'mfg_product_id',
        'quantity_to_produce',
        'unit_of_measure',
        'version',
        'is_active',
        'is_default',
        'valid_from',
        'valid_to',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'quantity_to_produce' => 'decimal:4',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'valid_from' => 'date',
        'valid_to' => 'date',
    ];

    /**
     * Get the manufacturing product this BOM is for.
     */
    public function mfgProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'mfg_product_id');
    }

    /**
     * Get the items (components) in this BOM.
     */
    public function bomItems()
    {
        return $this->hasMany(MfgBomItem::class, 'mfg_bom_id');
    }

    /**
     * Get the work orders that use this BOM.
     */
    public function workOrders()
    {
        return $this->hasMany(MfgWorkOrder::class, 'mfg_bom_id');
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

