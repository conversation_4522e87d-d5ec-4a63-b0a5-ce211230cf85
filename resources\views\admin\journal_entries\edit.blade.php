@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>تعديل القيد المحاسبي: {{ $journalEntry->entry_number }}</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.journal_entries.update", $journalEntry->id) }}" method="POST" id="journalEntryForm">
            @csrf
            @method("PUT")
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="entry_number">رقم القيد:</label>
                        <input type="text" class="form-control" id="entry_number" name="entry_number" value="{{ old("entry_number", $journalEntry->entry_number) }}" readonly>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="entry_date">تاريخ القيد:</label>
                        <input type="date" class="form-control" id="entry_date" name="entry_date" value="{{ old("entry_date", $journalEntry->entry_date->format("Y-m-d")) }}" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="branch_id">الفرع (اختياري):</label>
                <select class="form-control" id="branch_id" name="branch_id">
                    <option value="">اختر الفرع</option>
                    @foreach ($branches as $branch)
                        <option value="{{ $branch->id }}" {{ old("branch_id", $journalEntry->branch_id) == $branch->id ? "selected" : "" }}>
                            {{ $branch->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group">
                <label for="description">الوصف (اختياري):</label>
                <textarea class="form-control" id="description" name="description">{{ old("description", $journalEntry->description) }}</textarea>
            </div>

            <hr>
            <h4>بنود القيد المحاسبي</h4>
            <table class="table table-bordered" id="journalItemsTable">
                <thead>
                    <tr>
                        <th>الحساب</th>
                        <th>المبلغ المدين</th>
                        <th>المبلغ الدائن</th>
                        <th>الوصف</th>
                        <th>الإجراء</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (old("items", $journalEntry->items->toArray()) as $index => $item)
                    <tr data-index="{{ $index }}">
                        <td>
                            <select name="items[{{ $index }}][account_id]" class="form-control account-select" required>
                                <option value="">Select Account</option>
                                @foreach ($accounts as $acc)
                                    <option value="{{ $acc->id }}" {{ (isset($item["account_id"]) && $item["account_id"] == $acc->id) || (isset($item["account"]) && $item["account"]["id"] == $acc->id) ? "selected" : "" }}>
                                        {{ $acc->name }} ({{ $acc->code }})
                                    </option>
                                @endforeach
                            </select>
                        </td>
                        <td><input type="number" name="items[{{ $index }}][debit_amount]" class="form-control debit-amount" step="0.01" min="0" value="{{ $item["debit_amount"] ?? "0.00" }}"></td>
                        <td><input type="number" name="items[{{ $index }}][credit_amount]" class="form-control credit-amount" step="0.01" min="0" value="{{ $item["credit_amount"] ?? "0.00" }}"></td>
                        <td><input type="text" name="items[{{ $index }}][description]" class="form-control" value="{{ $item["description"] ?? "" }}"></td>
                        <td><button type="button" class="btn btn-danger btn-sm removeItemRow">حذف</button></td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="1" class="text-right"><strong>الإجمالي:</strong></td>
                        <td><input type="text" id="total_debit" class="form-control" readonly></td>
                        <td><input type="text" id="total_credit" class="form-control" readonly></td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
            <button type="button" class="btn btn-info mb-3" id="addItemRow">إضافة بند</button>

            <div class="alert alert-danger" id="balanceWarning" style="display:none;">
                يجب أن تتساوى المبالغ المدينة والدائنة.
            </div>

            <button type="submit" class="btn btn-success">تحديث القيد المحاسبي</button>
            <a href="{{ route("admin.journal_entries.index") }}" class="btn btn-secondary">إلغاء</a>
        </form>
    </div>

@push("scripts")
<script>
    document.addEventListener("DOMContentLoaded", function() {
        let itemIndex = {{ count(old("items", $journalEntry->items->toArray())) }};
        const addItemRowButton = document.getElementById("addItemRow");
        const journalItemsTableBody = document.querySelector("#journalItemsTable tbody");
        const totalDebitInput = document.getElementById("total_debit");
        const totalCreditInput = document.getElementById("total_credit");
        const balanceWarning = document.getElementById("balanceWarning");
        const journalEntryForm = document.getElementById("journalEntryForm");

        const accounts = @json($accounts);

        function createItemRow() {
            itemIndex++;
            const row = document.createElement("tr");
            row.dataset.index = itemIndex;
            row.innerHTML = `
                <td>
                    <select name="items[${itemIndex}][account_id]" class="form-control account-select" required>
                        <option value="">Select Account</option>
                        ${accounts.map(acc => `<option value="${acc.id}">${acc.name} (${acc.code})</option>`).join("")}
                    </select>
                </td>
                <td><input type="number" name="items[${itemIndex}][debit_amount]" class="form-control debit-amount" step="0.01" min="0" value="0.00"></td>
                <td><input type="number" name="items[${itemIndex}][credit_amount]" class="form-control credit-amount" step="0.01" min="0" value="0.00"></td>
                <td><input type="text" name="items[${itemIndex}][description]" class="form-control"></td>
                <td><button type="button" class="btn btn-danger btn-sm removeItemRow">Remove</button></td>
            `;
            journalItemsTableBody.appendChild(row);
            attachRowEventListeners(row);
        }

        function attachRowEventListeners(row) {
            row.querySelector(".removeItemRow").addEventListener("click", function() {
                row.remove();
                updateTotals();
            });
            row.querySelectorAll(".debit-amount, .credit-amount").forEach(input => {
                input.addEventListener("change", updateTotals);
            });
        }

        function updateTotals() {
            let totalDebit = 0;
            let totalCredit = 0;
            journalItemsTableBody.querySelectorAll("tr").forEach(row => {
                const debitInput = row.querySelector(".debit-amount");
                const creditInput = row.querySelector(".credit-amount");
                if (debitInput) totalDebit += parseFloat(debitInput.value) || 0;
                if (creditInput) totalCredit += parseFloat(creditInput.value) || 0;
            });
            totalDebitInput.value = totalDebit.toFixed(2);
            totalCreditInput.value = totalCredit.toFixed(2);

            if (totalDebit.toFixed(2) !== totalCredit.toFixed(2)) {
                balanceWarning.style.display = "block";
            } else {
                balanceWarning.style.display = "none";
            }
        }

        addItemRowButton.addEventListener("click", createItemRow);
        document.querySelectorAll(".removeItemRow").forEach(button => {
            button.addEventListener("click", function() {
                this.closest("tr").remove();
                updateTotals();
            });
        });
         document.querySelectorAll(".debit-amount, .credit-amount").forEach(input => {
            input.addEventListener("change", updateTotals);
        });

        // Initial calculation
        updateTotals();

        journalEntryForm.addEventListener("submit", function(event) {
            if (totalDebitInput.value !== totalCreditInput.value) {
                event.preventDefault();
                balanceWarning.style.display = "block";
                alert("القيد المحاسبي غير متوازن. يجب أن تتساوى المبالغ المدينة والدائنة.");
            }
             if (journalItemsTableBody.rows.length === 0) {
                 event.preventDefault();
                 alert("الرجاء إضافة بند واحد على الأقل للقيد المحاسبي.");
            }
        });
    });
</script>
@endpush

