<li class="tree-node tree-level-{{ $level ?? 2 }}">
    <div class="tree-content">
        <div class="tree-item @if($account->is_control_account) has-children @endif account-type-{{ $account->accountType->slug }}">
            @if($account->children && $account->children->count() > 0)
                <span class="tree-toggle" data-toggle="collapse" data-target="#account-{{ $account->id }}" aria-expanded="false">
                    <i class="fas fa-chevron-right"></i>
                </span>
            @else
                <span class="tree-toggle-placeholder"></span>
            @endif
            
            <div class="account-info">
                <div class="account-code">{{ $account->code }}</div>
                <div class="account-name">
                    @if($account->is_control_account)
                        <i class="fas fa-folder mr-1" style="color: #f6c23e;"></i>
                    @else
                        <i class="fas fa-file-alt mr-1" style="color: #4e73df;"></i>
                    @endif
                    <span class="@if($account->is_control_account) font-weight-bold @endif">{{ $account->name_ar }}</span>
                    
                    @if($account->children && $account->children->count() > 0)
                        <span class="badge badge-light badge-pill ml-2" title="عدد الحسابات الفرعية">
                            {{ $account->children->count() }}
                        </span>
                    @endif
                </div>
                
                @php
                    $balance = $account->opening_balance_debit - $account->opening_balance_credit;
                    $balanceClass = $balance < 0 ? 'negative' : '';
                @endphp
                
                <div class="account-balance {{ $balanceClass }}">
                    {{ number_format(abs($balance), 2) }}
                    <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                </div>
                
                <div class="account-actions">
                    <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-info" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    @if(!$account->children || $account->children->count() == 0)
                        <form action="{{ route('admin.accounts.destroy', $account->id) }}" method="POST" style="display:inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    @if($account->children && $account->children->count() > 0)
        <div class="collapse" id="account-{{ $account->id }}">
            <ul class="tree">
                @foreach($account->children as $child)
                    @php
                        $childLevel = ($level ?? 2) + 1;
                        // Limit to max level of 2 for styling purposes
                        $childDisplayLevel = min($childLevel, 2);
                    @endphp
                    @include('admin.accounts._tree_node_child', ['account' => $child, 'level' => $childDisplayLevel])
                @endforeach
            </ul>
        </div>
    @endif
</li>
