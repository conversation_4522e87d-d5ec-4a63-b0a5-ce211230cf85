@extends('layouts.admin')

@section('title', 'إدارة مرتجعات المشتريات')

@push('styles')
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">مرتجعات المشتريات</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.purchases.returns.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة مرتجع شراء جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في مرتجعات المشتريات...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>المورد</th>
                                    <th>رقم الفاتورة الأصلية</th>
                                    <th>تاريخ المرتجع</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد مرتجعات مشتريات مسجلة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة مرتجعات المشتريات
        console.log('تم تعطيل DataTables في صفحة مرتجعات المشتريات');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة مرتجعات المشتريات');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة مرتجعات المشتريات');
                return this;
            };
        }
        
        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });
</script>
@endpush
