@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($messageTemplate) ? "Edit" : "Add New" }} WhatsApp Message Template</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <strong>Whoops!</strong> There were some problems with your input.<br><br>
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($messageTemplate) ? route(\'admin.whatsapp_integration.message_templates.update\', $messageTemplate->id) : route(\'admin.whatsapp_integration.message_templates.store\') }}" method="POST">
        @csrf
        @if(isset($messageTemplate))
            @method(\'PUT\')
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Template Name:</strong>
                    <input type="text" name="name" value="{{ old(\'name\', $messageTemplate->name ?? \'\') }}" class="form-control" placeholder="Unique template name (e.g., order_confirmation)">
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Namespace (Optional):</strong>
                    <input type="text" name="namespace" value="{{ old(\'namespace\', $messageTemplate->namespace ?? \'\') }}" class="form-control" placeholder="Template namespace if required by provider">
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Language Code:</strong>
                    <input type="text" name="language_code" value="{{ old(\'language_code\', $messageTemplate->language_code ?? \'en_US\') }}" class="form-control" placeholder="e.g., en_US, ar">
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Category (Optional):</strong>
                    <input type="text" name="category" value="{{ old(\'category\', $messageTemplate->category ?? \'TRANSACTIONAL\') }}" class="form-control" placeholder="e.g., TRANSACTIONAL, MARKETING">
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Components JSON:</strong>
                    <textarea name="components_json" class="form-control" rows="5" placeholder=\'Paste JSON for template components (header, body, footer, buttons)\'">{{ old(\'components_json\', isset($messageTemplate) ? json_encode($messageTemplate->components_json, JSON_PRETTY_PRINT) : \'{\n    "header": {
        "type": "TEXT",
        "text": "Your Order {{1}} is Confirmed!"
    },
    "body": {
        "text": "Hello {{2}}, thank you for your order. Your items will be shipped soon. Total amount: {{3}}."
    },
    "footer": {
        "text": "Thank you for shopping with us!"
    }
}\'
) }}</textarea>
                    <small class="form-text text-muted">Refer to WhatsApp API documentation for component structure.</small>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Status:</strong>
                    <select name="status" class="form-control">
                        @foreach(['PENDING', 'APPROVED', 'REJECTED', 'PAUSED', 'DISABLED'] as $status)
                            <option value="{{ $status }}" {{ (isset($messageTemplate) && $messageTemplate->status == $status) || old(\'status\') == $status ? \'selected\' : \'\' }}>{{ $status }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Provider Template ID (Optional):</strong>
                    <input type="text" name="provider_template_id" value="{{ old(\'provider_template_id\', $messageTemplate->provider_template_id ?? \'\') }}" class="form-control" placeholder="Template ID from provider, if any">
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12 text-center">
                <button type="submit" class="btn btn-primary">Submit</button>
                <a class="btn btn-secondary" href="{{ route(\'admin.whatsapp_integration.message_templates.index\') }}"> Back</a>
            </div>
        </div>
    </form>
</div>
@endsection

