<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_routings", function (Blueprint $table) {
            $table->id();
            $table->string("routing_code")->unique();
            $table->string("name");
            $table->text("description")->nullable();
            $table->unsignedBigInteger("mfg_product_id")->nullable(); // Optional: if this routing is specific to one product
            $table->foreign("mfg_product_id")->references("id")->on("mfg_products")->onDelete("set null");
            $table->integer("version")->default(1);
            $table->boolean("is_active")->default(true);
            $table->boolean("is_default")->default(false); // Is this the default routing for the product?
            $table->decimal("total_standard_time_hours", 10, 4)->nullable(); // Calculated sum of operation times
            $table->text("notes")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->foreign("created_by")->references("id")->on("users")->onDelete("set null");
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->foreign("updated_by")->references("id")->on("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes();

            $table->unique(["mfg_product_id", "version"], "mfg_product_routing_version_unique");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_routings");
    }
};

