<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BnplTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'provider_name',
        'provider_session_id',
        'provider_order_id',
        'provider_capture_id',
        'provider_refund_id',
        'status',
        'amount',
        'currency',
        'payment_type',
        'instalments',
        'webhook_payload',
        'error_message',
    ];

    protected $casts = [
        'webhook_payload' => 'array',
        'amount' => 'decimal:2',
        'instalments' => 'integer',
    ];

    /**
     * Get the order that owns the BNPL transaction.
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}

