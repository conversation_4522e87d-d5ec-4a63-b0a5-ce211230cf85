<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الباقة
            $table->string('slug')->unique(); // معرف الباقة
            $table->text('description')->nullable(); // وصف الباقة
            $table->decimal('price', 10, 2); // سعر الباقة
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly'])->default('monthly'); // دورة الفوترة
            $table->integer('max_branches')->default(1); // الحد الأقصى للفروع
            $table->integer('max_users')->default(5); // الحد الأقصى للمستخدمين
            $table->integer('max_customers')->default(100); // الحد الأقصى للعملاء
            $table->integer('max_products')->default(500); // الحد الأقصى للمنتجات
            $table->integer('max_invoices_per_month')->default(100); // الحد الأقصى للفواتير شهرياً
            $table->integer('max_sales_representatives')->default(5); // الحد الأقصى للمناديب
            $table->json('features'); // المميزات المتاحة
            $table->json('modules'); // الوحدات المتاحة
            $table->boolean('has_restaurant_module')->default(false); // وحدة المطاعم
            $table->boolean('has_sales_reps_module')->default(false); // وحدة المناديب
            $table->boolean('has_inventory_module')->default(false); // وحدة المخزون
            $table->boolean('has_hr_module')->default(false); // وحدة الموارد البشرية
            $table->boolean('has_crm_module')->default(false); // وحدة إدارة العملاء
            $table->boolean('has_reports_module')->default(true); // وحدة التقارير
            $table->boolean('has_api_access')->default(false); // الوصول للـ API
            $table->boolean('has_custom_branding')->default(false); // العلامة التجارية المخصصة
            $table->boolean('has_priority_support')->default(false); // الدعم المتقدم
            $table->boolean('has_data_backup')->default(true); // النسخ الاحتياطي
            $table->integer('storage_limit_gb')->default(1); // حد التخزين بالجيجابايت
            $table->boolean('is_popular')->default(false); // باقة شائعة
            $table->boolean('is_active')->default(true); // نشطة
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->json('restrictions')->nullable(); // قيود إضافية
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
