<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Customer;

class SalesRoute extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'route_type',
        'schedule_days',
        'start_time',
        'end_time',
        'estimated_duration',
        'estimated_distance',
        'fuel_allowance',
        'max_customers',
        'priority_level',
        'route_points',
        'special_instructions',
        'requires_vehicle',
        'is_active',
        'sales_representative_id',
        'sales_area_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'schedule_days' => 'array',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'estimated_duration' => 'integer',
        'estimated_distance' => 'decimal:2',
        'fuel_allowance' => 'decimal:2',
        'max_customers' => 'integer',
        'priority_level' => 'integer',
        'route_points' => 'array',
        'requires_vehicle' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the sales representative assigned to this route.
     */
    public function salesRepresentative(): BelongsTo
    {
        return $this->belongsTo(SalesRepresentative::class);
    }

    /**
     * Get the sales area for this route.
     */
    public function salesArea(): BelongsTo
    {
        return $this->belongsTo(SalesArea::class);
    }

    /**
     * Get the branch that owns the route.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the route.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the sales visits for this route.
     */
    public function salesVisits(): HasMany
    {
        return $this->hasMany(SalesVisit::class);
    }

    /**
     * Get the customers on this route.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'sales_route_id');
    }

    /**
     * Get route type color.
     */
    public function getRouteTypeColorAttribute(): string
    {
        return match($this->route_type) {
            'daily' => '#28a745',
            'weekly' => '#007bff',
            'monthly' => '#ffc107',
            'custom' => '#6f42c1',
            default => '#6c757d'
        };
    }

    /**
     * Get route type text.
     */
    public function getRouteTypeTextAttribute(): string
    {
        return match($this->route_type) {
            'daily' => 'يومي',
            'weekly' => 'أسبوعي',
            'monthly' => 'شهري',
            'custom' => 'مخصص',
            default => 'غير محدد'
        };
    }

    /**
     * Get priority level badge.
     */
    public function getPriorityBadgeAttribute(): string
    {
        return match($this->priority_level) {
            1 => 'badge-danger',
            2 => 'badge-warning',
            3 => 'badge-info',
            default => 'badge-secondary'
        };
    }

    /**
     * Get estimated duration in hours and minutes.
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->estimated_duration / 60);
        $minutes = $this->estimated_duration % 60;
        
        if ($hours > 0) {
            return $hours . ' ساعة ' . ($minutes > 0 ? $minutes . ' دقيقة' : '');
        }
        
        return $minutes . ' دقيقة';
    }

    /**
     * Check if route is scheduled for today.
     */
    public function isScheduledForToday(): bool
    {
        if (!$this->schedule_days) {
            return false;
        }

        $today = strtolower(now()->format('l')); // Get day name in English
        $todayArabic = match($today) {
            'sunday' => 'الأحد',
            'monday' => 'الاثنين',
            'tuesday' => 'الثلاثاء',
            'wednesday' => 'الأربعاء',
            'thursday' => 'الخميس',
            'friday' => 'الجمعة',
            'saturday' => 'السبت',
            default => $today
        };

        return in_array($todayArabic, $this->schedule_days) || in_array($today, $this->schedule_days);
    }

    /**
     * Get next scheduled date.
     */
    public function getNextScheduledDate(): ?string
    {
        if (!$this->schedule_days) {
            return null;
        }

        // Implementation for calculating next scheduled date based on route type and schedule days
        return now()->addDay()->format('Y-m-d');
    }

    /**
     * Calculate total visits for a period.
     */
    public function calculateVisits($startDate, $endDate): int
    {
        return $this->salesVisits()
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->count();
    }

    /**
     * Calculate completion rate for a period.
     */
    public function calculateCompletionRate($startDate, $endDate): float
    {
        $totalVisits = $this->salesVisits()
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->count();

        if ($totalVisits === 0) {
            return 0;
        }

        $completedVisits = $this->salesVisits()
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->where('visit_status', 'completed')
            ->count();

        return ($completedVisits / $totalVisits) * 100;
    }

    /**
     * Scope a query to only include active routes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by priority and name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('priority_level')->orderBy('name');
    }

    /**
     * Scope a query to filter by route type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('route_type', $type);
    }

    /**
     * Scope a query to filter by sales representative.
     */
    public function scopeByRepresentative($query, $representativeId)
    {
        return $query->where('sales_representative_id', $representativeId);
    }
}
