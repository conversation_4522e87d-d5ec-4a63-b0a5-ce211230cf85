@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("admin_views.view_bom") }}: {{ $bom->name_ar }} ({{ $bom->name_en }})</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ __("admin_views.bom_details") }}</h4>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.id") }}:</strong></div>
                <div class="col-md-9">{{ $bom->id }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.name_ar") }}:</strong></div>
                <div class="col-md-9">{{ $bom->name_ar }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.name_en") }}:</strong></div>
                <div class="col-md-9">{{ $bom->name_en }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.bom_code") }}:</strong></div>
                <div class="col-md-9">{{ $bom->bom_code ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.item_to_produce") }}:</strong></div>
                <div class="col-md-9">{{ $bom->item ? $bom->item->name_ar . " (" . $bom->item->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.quantity_to_produce") }}:</strong></div>
                <div class="col-md-9">{{ number_format($bom->quantity_to_produce, 4) }} {{ $bom->unit_of_measure_ar }}</div>
            </div>
             <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.branch") }}:</strong></div>
                <div class="col-md-9">{{ $bom->branch ? $bom->branch->name_ar . " (" . $bom->branch->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.description_ar") }}:</strong></div>
                <div class="col-md-9">{{ $bom->description_ar ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.description_en") }}:</strong></div>
                <div class="col-md-9">{{ $bom->description_en ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.is_default_bom") }}:</strong></div>
                <div class="col-md-9">{{ $bom->is_default ? __("admin_views.yes") : __("admin_views.no") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.status") }}:</strong></div>
                <div class="col-md-9">{{ $bom->is_active ? __("admin_views.active") : __("admin_views.inactive") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.valid_from") }}:</strong></div>
                <div class="col-md-9">{{ $bom->valid_from ? $bom->valid_from->format("Y-m-d") : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.valid_to") }}:</strong></div>
                <div class="col-md-9">{{ $bom->valid_to ? $bom->valid_to->format("Y-m-d") : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.created_at") }}:</strong></div>
                <div class="col-md-9">{{ $bom->created_at }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.updated_at") }}:</strong></div>
                <div class="col-md-9">{{ $bom->updated_at }}</div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h4>{{ __("admin_views.bom_items") }}</h4>
        </div>
        <div class="card-body">
            @if($bom->bomItems && $bom->bomItems->count() > 0)
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>{{ __("admin_views.component_item") }}</th>
                            <th>{{ __("admin_views.quantity_required") }}</th>
                            <th>{{ __("admin_views.unit_of_measure_ar") }}</th>
                            <th>{{ __("admin_views.scrap_percentage") }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($bom->bomItems as $bomItem)
                            <tr>
                                <td>{{ $bomItem->item ? $bomItem->item->name_ar : "-" }}</td>
                                <td>{{ number_format($bomItem->quantity_required, 4) }}</td>
                                <td>{{ $bomItem->unit_of_measure_ar ?? "-" }}</td>
                                <td>{{ number_format($bomItem->scrap_percentage, 4) }}%</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <p>{{ __("admin_views.no_bom_items_found") }}</p>
            @endif
        </div>
        <div class="card-footer">
            <a href="{{ route("admin.boms.edit", $bom->id) }}" class="btn btn-warning">{{ __("admin_views.edit") }}</a>
            <a href="{{ route("admin.boms.index") }}" class="btn btn-secondary">{{ __("admin_views.back_to_list") }}</a>
        </div>
    </div>
</div>
@endsection

