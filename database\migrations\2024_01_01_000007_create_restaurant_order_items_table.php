<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('restaurant_orders')->onDelete('cascade');
            $table->foreignId('menu_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->integer('quantity'); // الكمية
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('total_price', 10, 2); // السعر الإجمالي
            $table->text('notes')->nullable(); // ملاحظات على الصنف
            $table->text('special_instructions')->nullable(); // تعليمات خاصة
            $table->enum('status', ['pending', 'preparing', 'ready', 'served', 'cancelled'])->default('pending'); // حالة الصنف
            $table->timestamp('preparation_started_at')->nullable(); // بداية التحضير
            $table->timestamp('ready_at')->nullable(); // وقت الجاهزية
            $table->timestamp('served_at')->nullable(); // وقت التقديم
            $table->foreignId('kitchen_station_id')->nullable()->constrained('kitchen_stations')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_order_items');
    }
};
