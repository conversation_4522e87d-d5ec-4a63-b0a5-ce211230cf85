<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_work_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('work_order_id')->constrained('manufacturing_work_orders')->onDelete('cascade');
            $table->foreignId('item_id')->constrained('manufacturing_items')->comment('The specific component item used or output item produced');
            $table->enum('item_direction', ['input', 'output'])->comment('Indicates if the item is consumed (input) or produced (output)');
            $table->decimal('quantity_planned', 15, 4)->nullable()->comment('Quantity planned as per BOM or estimate');
            $table->decimal('quantity_actual', 15, 4)->default(0.0000)->comment('Actual quantity consumed or produced');
            $table->string('unit_of_measure');
            $table->text('notes')->nullable();
            // Link to a specific operation within the work order if applicable
            // $table->foreignId('work_order_operation_id')->nullable()->constrained('manufacturing_work_order_operations'); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_work_order_items');
    }
};
