<?php

namespace App\Models\Modules\Subscriptions;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subscription extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'tenant_id',
        'subscription_plan_id',
        'subscription_number',
        'start_date',
        'end_date',
        'status',
        'notes',
        'auto_renew',
        'current_users_count',
        'current_branches_count',
        'price_paid',
        'payment_method',
        'payment_reference',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'auto_renew' => 'boolean',
        'current_users_count' => 'integer',
        'current_branches_count' => 'integer',
        'price_paid' => 'decimal:2',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            if (empty($subscription->subscription_number)) {
                $subscription->subscription_number = 'SUB-' . date('Ymd') . '-' . strtoupper(uniqid());
            }
        });
    }

    /**
     * Get the tenant (main customer user) for this subscription.
     */
    public function tenant()
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the subscription plan for this subscription.
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the payments for this subscription.
     */
    public function payments()
    {
        return $this->hasMany(SubscriptionPayment::class);
    }



    /**
     * Get the changes for this subscription.
     */
    public function changes()
    {
        return $this->hasMany(SubscriptionChange::class);
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive()
    {
        return $this->status === 'active' && $this->end_date >= now();
    }

    /**
     * Check if the subscription has expired.
     */
    public function hasExpired()
    {
        return $this->end_date < now();
    }

    /**
     * Get the formatted price paid.
     */
    public function getFormattedPricePaidAttribute()
    {
        return number_format($this->price_paid, 2) . ' ريال';
    }

    /**
     * Get the status in Arabic.
     */
    public function getStatusArabicAttribute()
    {
        return match($this->status) {
            'active' => 'نشط',
            'expired' => 'منتهي',
            'cancelled' => 'ملغي',
            'pending' => 'قيد الانتظار',
            default => $this->status,
        };
    }

    /**
     * Get the remaining days for this subscription.
     */
    public function getRemainingDaysAttribute()
    {
        if ($this->hasExpired()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }
}
