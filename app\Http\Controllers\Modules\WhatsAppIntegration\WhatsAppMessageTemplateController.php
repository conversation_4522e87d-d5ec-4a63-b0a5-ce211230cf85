<?php

namespace App\Http\Controllers\Modules\WhatsAppIntegration;

use App\Http\Controllers\Controller;
use App\Models\Modules\WhatsAppIntegration\WhatsAppMessageTemplate;
use Illuminate\Http\Request;

class WhatsAppMessageTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(WhatsAppMessageTemplate $whatsAppMessageTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WhatsAppMessageTemplate $whatsAppMessageTemplate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WhatsAppMessageTemplate $whatsAppMessageTemplate)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WhatsAppMessageTemplate $whatsAppMessageTemplate)
    {
        //
    }
}
