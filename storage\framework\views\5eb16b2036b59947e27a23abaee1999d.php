<?php $__env->startSection('title', 'إدارة حدود الفواتير'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة حدود الفواتير للباقات</h3>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('admin.super_admin.invoice_limits.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>الباقة</th>
                                        <th>السعر</th>
                                        <th>دورة الفوترة</th>
                                        <th>حد الفواتير الشهري</th>
                                        <th>حد الفواتير الإجمالي</th>
                                        <th>عدد المشتركين</th>
                                        <th>نشط</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($plan->name); ?></td>
                                            <td><?php echo e($plan->price); ?> ريال</td>
                                            <td>
                                                <?php switch($plan->billing_cycle):
                                                    case ('monthly'): ?>
                                                        شهري
                                                        <?php break; ?>
                                                    <?php case ('quarterly'): ?>
                                                        ربع سنوي
                                                        <?php break; ?>
                                                    <?php case ('semi_annually'): ?>
                                                        نصف سنوي
                                                        <?php break; ?>
                                                    <?php case ('annually'): ?>
                                                        سنوي
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <?php echo e($plan->billing_cycle); ?>

                                                <?php endswitch; ?>
                                            </td>
                                            <td>
                                                <input type="hidden" name="limits[<?php echo e($loop->index); ?>][subscription_plan_id]" value="<?php echo e($plan->id); ?>">
                                                <input type="number" class="form-control" name="limits[<?php echo e($loop->index); ?>][monthly_invoice_limit]" value="<?php echo e($invoiceLimits->where('subscription_plan_id', $plan->id)->first()->monthly_invoice_limit ?? 100); ?>" min="1" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" name="limits[<?php echo e($loop->index); ?>][total_invoice_limit]" value="<?php echo e($invoiceLimits->where('subscription_plan_id', $plan->id)->first()->total_invoice_limit ?? ''); ?>" min="0">
                                            </td>
                                            <td><?php echo e($plan->subscriptions->count()); ?></td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" name="limits[<?php echo e($loop->index); ?>][is_active]" value="1" <?php echo e(($invoiceLimits->where('subscription_plan_id', $plan->id)->first()->is_active ?? true) ? 'checked' : ''); ?>>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">لا توجد باقات</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">معلومات حول حدود الفواتير</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle"></i> كيفية عمل حدود الفواتير</h5>
                        <ul>
                            <li><strong>حد الفواتير الشهري:</strong> هو الحد الأقصى لعدد الفواتير التي يمكن للعميل إنشاؤها في الشهر الواحد.</li>
                            <li><strong>حد الفواتير الإجمالي:</strong> هو الحد الأقصى لعدد الفواتير التي يمكن للعميل إنشاؤها طوال فترة الاشتراك. اتركه فارغاً لعدم وجود حد.</li>
                            <li><strong>نشط:</strong> إذا كان غير نشط، فلن يتم تطبيق حدود الفواتير على هذه الباقة.</li>
                        </ul>
                        <p>يتم إعادة تعيين حد الفواتير الشهري في بداية كل شهر تلقائياً.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/super_admin/invoice_limits/index.blade.php ENDPATH**/ ?>