<?php

namespace App\Models\Modules\Branches;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\TenantScoped;

class Branch extends Model
{
    use HasFactory, TenantScoped;

    protected $fillable = [
        "name",
        "code",
        "address",
        "phone",
        "email",
        "parent_id",
        "is_active",
        "tenant_id",
    ];

    /**
     * Get the parent branch of this branch.
     */
    public function parent()
    {
        return $this->belongsTo(Branch::class, "parent_id");
    }

    /**
     * Get the child branches of this branch.
     */
    public function children()
    {
        return $this->hasMany(Branch::class, "parent_id");
    }

    /**
     * Get the users associated with this branch.
     */
    public function users()
    {
        return $this->hasMany(\App\Models\User::class); // Corrected namespace for User model
    }
}

