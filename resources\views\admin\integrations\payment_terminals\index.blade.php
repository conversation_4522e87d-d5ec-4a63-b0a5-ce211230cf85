@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Payment Terminal Integrations</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <p>Manage and configure integrations with payment terminal providers (e.g., mada, Visa, Mastercard through local bank terminals).</p>

    <div class="card mb-3">
        <div class="card-header">Default Payment Terminal</div>
        <div class="card-body">
            <p>Provider: {{-- $settings->default_terminal_provider ?? "Not Set" --}} Local Bank POS</p>
            <p>Status: {{-- $settings->default_terminal_enabled ? "Active" : "Inactive" --}} Active</p>
            <p>Model: {{-- $settings->default_terminal_model ?? "N/A" --}} Ingenico ICT250</p>
            <a href="{{ route("admin.integrations.payment_terminals.edit", ["terminal_id" => "default"]) }}" class="btn btn-warning btn-sm">Configure Default Terminal</a>
        </div>
    </div>

    {{-- Add more cards if multiple terminal configurations are supported --}}

    <a href="{{ route("admin.integrations.payment_terminals.show", ["terminal_id" => "default"]) }}" class="btn btn-info">View Transaction Logs/Details</a>

</div>
@endsection

