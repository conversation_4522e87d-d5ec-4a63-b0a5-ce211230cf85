<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('items', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('code')->unique()->nullable();
            $table->enum('type', ['raw_material', 'finished_good', 'semi_finished_good', 'service', 'other'])->default('raw_material');
            $table->string('unit_of_measure_ar')->nullable();
            $table->string('unit_of_measure_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();

            $table->decimal('standard_cost', 15, 4)->default(0.0000); // Standard cost for valuation
            $table->decimal('last_purchase_price', 15, 4)->nullable();
            $table->decimal('selling_price', 15, 4)->nullable();

            // Inventory related fields
            $table->decimal('quantity_on_hand', 15, 4)->default(0.0000);
            $table->decimal('quantity_committed', 15, 4)->default(0.0000); // Committed to sales orders or work orders
            $table->decimal('quantity_on_order', 15, 4)->default(0.0000); // On purchase orders
            $table->decimal('reorder_level', 15, 4)->nullable();
            $table->decimal('minimum_stock_level', 15, 4)->nullable();
            $table->decimal('maximum_stock_level', 15, 4)->nullable();

            // Accounting links (can be more detailed, e.g., separate accounts for inventory, COGS, sales)
            $table->foreignId('inventory_account_id')->nullable()->constrained('accounts')->onDelete('set null');
            $table->foreignId('cogs_account_id')->nullable()->constrained('accounts')->onDelete('set null'); // Cost of Goods Sold
            $table->foreignId('sales_revenue_account_id')->nullable()->constrained('accounts')->onDelete('set null');
            
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade'); // Item might be specific to a branch or global
            $table->boolean('is_active')->default(true);
            $table->boolean('is_manufactured')->default(false); // True if this item is produced internally
            $table->boolean('is_purchased')->default(true); // True if this item can be purchased
            $table->boolean('is_sold')->default(true); // True if this item can be sold
            
            $table->foreignId('created_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by_user_id')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();
            $table->softDeletes(); // Optional: for soft deleting items
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('items');
    }
};

