<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\MenuItemModifier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MenuItemModifierController extends Controller
{
    public function index()
    {
        $modifiers = MenuItemModifier::with(['options'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.restaurant.modifiers.index', compact('modifiers'));
    }

    public function create()
    {
        return view('admin.restaurant.modifiers.form');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'type' => 'required|in:single,multiple',
            'is_required' => 'boolean',
            'min_selections' => 'nullable|integer|min:0',
            'max_selections' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_required'] = $request->has('is_required');
        $validatedData['is_active'] = $request->has('is_active');

        MenuItemModifier::create($validatedData);

        return redirect()->route('admin.restaurant.modifiers.index')
            ->with('success', __('Modifier created successfully.'));
    }

    public function show(MenuItemModifier $modifier)
    {
        $modifier->load(['options', 'menuItems']);
        return view('admin.restaurant.modifiers.show', compact('modifier'));
    }

    public function edit(MenuItemModifier $modifier)
    {
        return view('admin.restaurant.modifiers.form', compact('modifier'));
    }

    public function update(Request $request, MenuItemModifier $modifier)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'type' => 'required|in:single,multiple',
            'is_required' => 'boolean',
            'min_selections' => 'nullable|integer|min:0',
            'max_selections' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_required'] = $request->has('is_required');
        $validatedData['is_active'] = $request->has('is_active');

        $modifier->update($validatedData);

        return redirect()->route('admin.restaurant.modifiers.index')
            ->with('success', __('Modifier updated successfully.'));
    }

    public function destroy(MenuItemModifier $modifier)
    {
        // Check if modifier has options
        if ($modifier->options()->count() > 0) {
            return redirect()->route('admin.restaurant.modifiers.index')
                ->with('error', __('Cannot delete modifier that has options.'));
        }

        $modifier->delete();

        return redirect()->route('admin.restaurant.modifiers.index')
            ->with('success', __('Modifier deleted successfully.'));
    }
}
