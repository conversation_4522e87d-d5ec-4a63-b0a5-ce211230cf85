<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Modules\Sales\InvoiceItem;
use App\Traits\TenantScoped;

class Product extends Model
{
    use HasFactory, TenantScoped;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'sku',
        'barcode',
        'price',
        'cost_price',
        'tax_rate',
        'category_id',
        'stock_quantity',
        'min_stock_quantity',
        'is_active',
        'image',
        'tenant_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'float',
        'cost_price' => 'float',
        'tax_rate' => 'float',
        'stock_quantity' => 'float',
        'min_stock_quantity' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the category that owns the product.
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the invoice items for the product.
     */
    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get the formatted price.
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2);
    }

    /**
     * Get the formatted cost price.
     *
     * @return string
     */
    public function getFormattedCostPriceAttribute()
    {
        return number_format($this->cost_price, 2);
    }

    /**
     * Check if the product is low on stock.
     *
     * @return bool
     */
    public function isLowOnStock()
    {
        return $this->stock_quantity <= $this->min_stock_quantity;
    }

    /**
     * Check if the product is out of stock.
     *
     * @return bool
     */
    public function isOutOfStock()
    {
        return $this->stock_quantity <= 0;
    }

    /**
     * Scope a query to only include active products.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
