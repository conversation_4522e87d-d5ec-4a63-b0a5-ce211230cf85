<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenant_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('users')->onDelete('cascade'); // المستأجر
            $table->foreignId('subscription_plan_id')->constrained('subscription_plans')->onDelete('cascade'); // الباقة
            $table->string('subscription_code')->unique(); // كود الاشتراك
            $table->enum('status', ['active', 'expired', 'cancelled', 'suspended', 'trial'])->default('trial'); // حالة الاشتراك
            $table->date('start_date'); // تاريخ البداية
            $table->date('end_date'); // تاريخ النهاية
            $table->date('trial_end_date')->nullable(); // نهاية الفترة التجريبية
            $table->decimal('amount_paid', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->string('payment_method')->nullable(); // طريقة الدفع
            $table->string('payment_reference')->nullable(); // مرجع الدفع
            $table->json('current_usage')->nullable(); // الاستخدام الحالي
            $table->json('custom_limits')->nullable(); // حدود مخصصة
            $table->boolean('auto_renew')->default(true); // التجديد التلقائي
            $table->date('next_billing_date')->nullable(); // تاريخ الفوترة القادم
            $table->text('cancellation_reason')->nullable(); // سبب الإلغاء
            $table->timestamp('cancelled_at')->nullable(); // تاريخ الإلغاء
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenant_subscriptions');
    }
};
