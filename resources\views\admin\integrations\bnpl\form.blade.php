@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Configure BNPL Provider: {{ ucfirst($provider) }}</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ route("admin.integrations.bnpl.update", ["provider" => $provider]) }}" method="POST">
        @csrf
        @method("PUT") {{-- Or POST --}}

        <div class="form-group">
            <label for="enabled">Enable {{ ucfirst($provider) }} Integration</label>
            <select name="enabled" id="enabled" class="form-control">
                <option value="1" {{-- old("enabled", $settings->{$provider."_enabled"} ?? false) ? "selected" : "" --}}>Enabled</option>
                <option value="0" {{-- !old("enabled", $settings->{$provider."_enabled"} ?? false) ? "selected" : "" --}}>Disabled</option>
            </select>
        </div>

        @if($provider == "tabby")
            <div class="form-group">
                <label for="public_key">Public Key</label>
                <input type="text" name="public_key" id="public_key" class="form-control" value="{{-- old("public_key", $settings->tabby_public_key ?? "") --}}">
            </div>
            <div class="form-group">
                <label for="secret_key">Secret Key</label>
                <input type="password" name="secret_key" id="secret_key" class="form-control" value="{{-- old("secret_key", $settings->tabby_secret_key ?? "") --}}">
            </div>
            <div class="form-group">
                <label for="merchant_id">Merchant ID / Merchant Code</label>
                <input type="text" name="merchant_id" id="merchant_id" class="form-control" value="{{-- old("merchant_id", $settings->tabby_merchant_id ?? "") --}}">
            </div>
        @elseif($provider == "tamara")
            <div class="form-group">
                <label for="api_token">API Token</label>
                <input type="password" name="api_token" id="api_token" class="form-control" value="{{-- old("api_token", $settings->tamara_api_token ?? "") --}}">
            </div>
            <div class="form-group">
                <label for="notification_token">Notification Token</label>
                <input type="password" name="notification_token" id="notification_token" class="form-control" value="{{-- old("notification_token", $settings->tamara_notification_token ?? "") --}}">
            </div>
        @endif

        <div class="form-group">
            <label for="environment">Environment</label>
            <select name="environment" id="environment" class="form-control">
                <option value="sandbox" {{-- old("environment", $settings->{$provider."_environment"} ?? "sandbox") == "sandbox" ? "selected" : "" --}}>Sandbox</option>
                <option value="production" {{-- old("environment", $settings->{$provider."_environment"} ?? "") == "production" ? "selected" : "" --}}>Production</option>
            </select>
        </div>

        {{-- Add other common or provider-specific settings like min/max order value --}}

        <button type="submit" class="btn btn-success mt-3">Save Configuration</button>
        <a href="{{ route("admin.integrations.bnpl.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

