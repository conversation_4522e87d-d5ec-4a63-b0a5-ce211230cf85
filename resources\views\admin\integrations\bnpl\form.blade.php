@extends('layouts.admin')

@section('title', 'إعدادات ' . ucfirst($provider))

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-credit-card-2-front me-2"></i>
                        إعدادات {{ $provider == 'tabby' ? 'تابي' : 'تمارا' }}
                    </h4>
                    <a href="{{ route('admin.integrations.bnpl.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        العودة
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.integrations.bnpl.update', $provider) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="environment" class="form-label">البيئة</label>
                                    <select class="form-select" id="environment" name="environment" required>
                                        <option value="sandbox">تجريبي (Sandbox)</option>
                                        <option value="production">إنتاج (Production)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1">
                                        <label class="form-check-label" for="is_active">
                                            تفعيل {{ $provider == 'tabby' ? 'تابي' : 'تمارا' }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($provider == 'tabby')
                            <!-- إعدادات تابي -->
                            <div class="card border-primary mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">إعدادات تابي (Tabby)</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="merchant_code" class="form-label">كود التاجر</label>
                                                <input type="text" class="form-control" id="merchant_code" name="merchant_code"
                                                       placeholder="أدخل كود التاجر من تابي">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="public_key" class="form-label">المفتاح العام</label>
                                                <input type="text" class="form-control" id="public_key" name="public_key"
                                                       placeholder="أدخل المفتاح العام">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="secret_key" class="form-label">المفتاح السري</label>
                                        <textarea class="form-control" id="secret_key" name="secret_key" rows="3"
                                                  placeholder="أدخل المفتاح السري من تابي"></textarea>
                                    </div>
                                </div>
                            </div>
                        @else
                            <!-- إعدادات تمارا -->
                            <div class="card border-success mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إعدادات تمارا (Tamara)</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="merchant_id" class="form-label">معرف التاجر</label>
                                                <input type="text" class="form-control" id="merchant_id" name="merchant_id"
                                                       placeholder="أدخل معرف التاجر من تمارا">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="api_token" class="form-label">رمز API</label>
                                                <input type="text" class="form-control" id="api_token" name="api_token"
                                                       placeholder="أدخل رمز API">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="notification_key" class="form-label">مفتاح الإشعارات</label>
                                        <textarea class="form-control" id="notification_key" name="notification_key" rows="3"
                                                  placeholder="أدخل مفتاح الإشعارات من تمارا"></textarea>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.integrations.bnpl.index') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-1"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

