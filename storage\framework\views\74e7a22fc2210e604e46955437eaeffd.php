<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0"><?php echo e(__("Kitchen Stations")); ?></h1>
                <a href="<?php echo e(route('admin.restaurant.kitchen-stations.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__("Add New Station")); ?>

                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Kitchen Stations List")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($kitchenStations->count() > 0): ?>
                        <div class="row">
                            <?php $__currentLoopData = $kitchenStations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $station): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card station-card h-100">
                                        <div class="card-header" style="background-color: <?php echo e($station->color ?? '#007bff'); ?>20; border-bottom: 3px solid <?php echo e($station->color ?? '#007bff'); ?>;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0" style="color: <?php echo e($station->color ?? '#007bff'); ?>">
                                                    <i class="fas fa-gear me-2"></i><?php echo e($station->name); ?>

                                                </h6>
                                                <?php if($station->is_active): ?>
                                                    <span class="badge bg-success"><?php echo e(__("Active")); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger"><?php echo e(__("Inactive")); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="card-body">
                                            <?php if($station->description): ?>
                                                <p class="card-text text-muted"><?php echo e($station->description); ?></p>
                                            <?php endif; ?>
                                            
                                            <div class="row text-center mb-3">
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-primary mb-0"><?php echo e($station->menuItems->count()); ?></h5>
                                                        <small class="text-muted"><?php echo e(__("Menu Items")); ?></small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-success mb-0"><?php echo e($station->printers->count()); ?></h5>
                                                        <small class="text-muted"><?php echo e(__("Printers")); ?></small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-info mb-0"><?php echo e($station->sort_order); ?></h5>
                                                        <small class="text-muted"><?php echo e(__("Order")); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <strong class="text-muted"><?php echo e(__("Branch")); ?>:</strong>
                                                <span class="badge bg-secondary"><?php echo e($station->branch->name ?? '-'); ?></span>
                                            </div>
                                            
                                            <?php if($station->printers->count() > 0): ?>
                                                <div class="mb-3">
                                                    <strong class="text-muted"><?php echo e(__("Printers")); ?>:</strong>
                                                    <div class="mt-1">
                                                        <?php $__currentLoopData = $station->printers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $printer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span class="badge <?php echo e($printer->is_active ? 'bg-success' : 'bg-danger'); ?> me-1">
                                                                <?php echo e($printer->name); ?>

                                                            </span>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <a href="<?php echo e(route('admin.restaurant.kitchen-stations.show', $station)); ?>" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> <?php echo e(__("View")); ?>

                                                </a>
                                                <a href="<?php echo e(route('admin.restaurant.kitchen-stations.edit', $station)); ?>" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i> <?php echo e(__("Edit")); ?>

                                                </a>
                                                <form action="<?php echo e(route('admin.restaurant.kitchen-stations.destroy', $station)); ?>" 
                                                      method="POST" style="display: inline-block;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            title="<?php echo e(__('Delete')); ?>"
                                                            onclick="return confirm('<?php echo e(__('Are you sure you want to delete this kitchen station?')); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($kitchenStations->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-gear fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo e(__("No kitchen stations found")); ?></h5>
                            <p class="text-muted"><?php echo e(__("Start by creating your first kitchen station.")); ?></p>
                            <a href="<?php echo e(route('admin.restaurant.kitchen-stations.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> <?php echo e(__("Add First Station")); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.station-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
}

.station-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.btn-group .btn {
    flex: 1;
}

.card-header {
    transition: all 0.3s;
}

.station-card:hover .card-header {
    transform: scale(1.02);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/restaurant/kitchen-stations/index.blade.php ENDPATH**/ ?>