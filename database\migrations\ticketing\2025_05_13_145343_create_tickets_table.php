<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number')->unique()->comment('Unique, system-generated ticket number');
            $table->foreignId('user_id')->constrained('users')->comment('The user (customer) who opened the ticket');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->comment('Branch associated with the ticket, if any');
            $table->foreignId('ticket_category_id')->constrained('ticket_categories');
            $table->foreignId('ticket_priority_id')->constrained('ticket_priorities');
            $table->foreignId('ticket_status_id')->constrained('ticket_statuses');
            $table->foreignId('assigned_to_user_id')->nullable()->constrained('users')->comment('Support agent assigned to the ticket');
            $table->string('subject');
            $table->text('description');
            $table->timestamp('last_reply_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            $table->string('created_by_type')->default('user')->comment('Indicates if ticket was created by a user or an agent'); // E.g., 'user', 'agent'
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
