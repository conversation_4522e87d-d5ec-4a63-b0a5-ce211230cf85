<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create("ticket_priorities", function (Blueprint $table) {
            $table->id();
            $table->string("name_ar");
            $table->string("name_en");
            $table->string("color_code")->nullable()->comment("Hex color code for UI representation");
            $table->boolean("is_default")->default(false);
            $table->boolean("is_active")->default(true);
            $table->integer("sort_order")->default(0);
            $table->foreignId("created_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("updated_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("ticket_priorities");
    }
};

