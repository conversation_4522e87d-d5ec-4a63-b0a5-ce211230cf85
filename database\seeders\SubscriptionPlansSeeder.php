<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subscription\SubscriptionPlan;

class SubscriptionPlansSeeder extends Seeder
{
    public function run(): void
    {
        $plans = [
            // الباقة الأساسية - شهري
            [
                'name' => 'الباقة الأساسية',
                'code' => 'basic-monthly',
                'description' => 'مثالية للشركات الصغيرة والمشاريع الناشئة',
                'price' => 299.00,
                'billing_cycle' => 'monthly',
                'max_branches' => 1,
                'max_users' => 3,
                'has_pos' => true,
                'has_inventory' => false,
                'has_accounting' => true,
                'has_manufacturing' => false,
                'has_hr' => false,
                'has_crm' => false,
                'has_purchases' => true,
                'has_sales' => true,
                'has_reports' => true,
                'has_api_access' => false,
                'storage_space_gb' => 1,
                'is_active' => true,
            ],

            // الباقة المتقدمة - شهري
            [
                'name' => 'الباقة المتقدمة',
                'code' => 'advanced-monthly',
                'description' => 'للشركات المتوسطة التي تحتاج مميزات أكثر',
                'price' => 599.00,
                'billing_cycle' => 'monthly',
                'max_branches' => 3,
                'max_users' => 10,
                'has_pos' => true,
                'has_inventory' => true,
                'has_accounting' => true,
                'has_manufacturing' => false,
                'has_hr' => false,
                'has_crm' => true,
                'has_purchases' => true,
                'has_sales' => true,
                'has_reports' => true,
                'has_api_access' => true,
                'storage_space_gb' => 5,
                'is_active' => true,
            ],

            // الباقة الاحترافية - شهري
            [
                'name' => 'الباقة الاحترافية',
                'code' => 'professional-monthly',
                'description' => 'للشركات الكبيرة والمؤسسات',
                'price' => 1199.00,
                'billing_cycle' => 'monthly',
                'max_branches' => 10,
                'max_users' => 50,
                'has_pos' => true,
                'has_inventory' => true,
                'has_accounting' => true,
                'has_manufacturing' => true,
                'has_hr' => true,
                'has_crm' => true,
                'has_purchases' => true,
                'has_sales' => true,
                'has_reports' => true,
                'has_api_access' => true,
                'storage_space_gb' => 20,
                'is_active' => true,
            ],

            // الباقة المؤسسية - شهري
            [
                'name' => 'الباقة المؤسسية',
                'code' => 'enterprise-monthly',
                'description' => 'حلول مخصصة للمؤسسات الكبرى',
                'price' => 2499.00,
                'billing_cycle' => 'monthly',
                'max_branches' => 999,
                'max_users' => 999,
                'has_pos' => true,
                'has_inventory' => true,
                'has_accounting' => true,
                'has_manufacturing' => true,
                'has_hr' => true,
                'has_crm' => true,
                'has_purchases' => true,
                'has_sales' => true,
                'has_reports' => true,
                'has_api_access' => true,
                'storage_space_gb' => 100,
                'is_active' => true,
            ],
        ];

        // إنشاء الباقات
        foreach ($plans as $planData) {
            SubscriptionPlan::create($planData);
        }

        $this->command->info('Subscription plans seeded successfully!');
        $this->command->info('Created 4 subscription plans');
    }
}
