@extends("layouts.admin")

@section("title", "تفاصيل الحساب")

@section("header", "تفاصيل الحساب")

@section("content")
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="admin-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-journal-text text-primary me-2"></i>تفاصيل الحساب: {{ $account->name_ar }}</h5>
                        <div>
                            <a href="{{ route("admin.accounts.edit", $account->id) }}" class="btn btn-warning">
                                <i class="bi bi-pencil me-1"></i> تعديل
                            </a>
                            <a href="{{ route("admin.accounts.index") }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-1"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="admin-card mb-4">
                    <h5 class="mb-3">معلومات الحساب الأساسية</h5>
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 30%">الرقم التعريفي</th>
                                <td>{{ $account->id }}</td>
                            </tr>
                            <tr>
                                <th>الاسم (عربي)</th>
                                <td>{{ $account->name_ar }}</td>
                            </tr>
                            <tr>
                                <th>الاسم (إنجليزي)</th>
                                <td>{{ $account->name_en }}</td>
                            </tr>
                            <tr>
                                <th>الكود</th>
                                <td>{{ $account->code ?? "غير محدد" }}</td>
                            </tr>
                            <tr>
                                <th>نوع الحساب</th>
                                <td>
                                    @if($account->accountType)
                                        <span class="badge type-badge-{{ $account->accountType->slug }}">
                                            {{ $account->accountType->name_ar }}
                                        </span>
                                    @else
                                        غير محدد
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>الحساب الرئيسي</th>
                                <td>{{ $account->parent->name_ar ?? "حساب رئيسي" }}</td>
                            </tr>
                            <tr>
                                <th>الفرع</th>
                                <td>{{ $account->branch->name_ar ?? "جميع الفروع" }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <div class="admin-card mb-4">
                    <h5 class="mb-3">معلومات الرصيد والإعدادات</h5>
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 30%">رصيد افتتاحي (مدين)</th>
                                <td>{{ number_format($account->opening_balance_debit, 2) }}</td>
                            </tr>
                            <tr>
                                <th>رصيد افتتاحي (دائن)</th>
                                <td>{{ number_format($account->opening_balance_credit, 2) }}</td>
                            </tr>
                            <tr>
                                <th>صافي الرصيد الافتتاحي</th>
                                @php
                                    $balance = $account->opening_balance_debit - $account->opening_balance_credit;
                                    $balanceClass = $balance < 0 ? 'text-danger' : 'text-success';
                                @endphp
                                <td class="{{ $balanceClass }}">
                                    {{ number_format(abs($balance), 2) }}
                                    <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                                </td>
                            </tr>
                            <tr>
                                <th>تاريخ الرصيد الافتتاحي</th>
                                <td>{{ $account->opening_balance_date ? $account->opening_balance_date->format('Y-m-d') : 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <th>حساب تحكم</th>
                                <td>
                                    @if($account->is_control_account)
                                        <span class="badge bg-success">نعم</span>
                                    @else
                                        <span class="badge bg-secondary">لا</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>يقبل القيود</th>
                                <td>
                                    @if($account->accepts_entries)
                                        <span class="badge bg-success">نعم</span>
                                    @else
                                        <span class="badge bg-secondary">لا</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>الحالة</th>
                                <td>
                                    @if($account->is_active)
                                        <span class="badge bg-success">نشط</span>
                                    @else
                                        <span class="badge bg-danger">غير نشط</span>
                                    @endif
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        @if($account->children && $account->children->count() > 0)
        <div class="row">
            <div class="col-12">
                <div class="admin-card mb-4">
                    <h5 class="mb-3">الحسابات الفرعية</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>الاسم</th>
                                    <th>الرصيد</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($account->children as $child)
                                <tr>
                                    <td>{{ $child->code }}</td>
                                    <td>{{ $child->name_ar }}</td>
                                    @php
                                        $childBalance = $child->opening_balance_debit - $child->opening_balance_credit;
                                        $childBalanceClass = $childBalance < 0 ? 'text-danger' : 'text-success';
                                    @endphp
                                    <td class="{{ $childBalanceClass }}">
                                        {{ number_format(abs($childBalance), 2) }}
                                        <small>{{ $childBalance < 0 ? 'دائن' : 'مدين' }}</small>
                                    </td>
                                    <td>
                                        @if($child->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.accounts.show', $child->id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.accounts.edit', $child->id) }}" class="btn btn-sm btn-warning">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="row">
            <div class="col-12">
                <div class="admin-card mb-4">
                    <h5 class="mb-3">معلومات إضافية</h5>
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 30%">الوصف (عربي)</th>
                                <td>{{ $account->description_ar ?? "غير متوفر" }}</td>
                            </tr>
                            <tr>
                                <th>الوصف (إنجليزي)</th>
                                <td>{{ $account->description_en ?? "غير متوفر" }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء</th>
                                <td>{{ $account->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ آخر تحديث</th>
                                <td>{{ $account->updated_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

