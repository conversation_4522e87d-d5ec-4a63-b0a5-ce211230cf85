<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
// Assuming Location and CostCenter models might exist or be created later
// use App\Models\Location;
// use App\Models\Finance\CostCenter;

class MfgWorkCenter extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_work_centers';

    protected $fillable = [
        'wc_code',
        'name',
        'description',
        'type',
        'location_id',
        'capacity_hours_per_day',
        'number_of_machines',
        'machine_efficiency_percentage',
        'labor_efficiency_percentage',
        'standard_machine_hour_rate',
        'standard_labor_hour_rate',
        'standard_overhead_rate_per_hour',
        'cost_center_id',
        'is_active',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'capacity_hours_per_day' => 'decimal:2',
        'machine_efficiency_percentage' => 'decimal:2',
        'labor_efficiency_percentage' => 'decimal:2',
        'standard_machine_hour_rate' => 'decimal:4',
        'standard_labor_hour_rate' => 'decimal:4',
        'standard_overhead_rate_per_hour' => 'decimal:4',
        'is_active' => 'boolean',
    ];

    /**
     * Get the operations that can be performed at this work center (as default).
     */
    public function defaultOperations()
    {
        return $this->hasMany(MfgOperation::class, 'default_work_center_id');
    }

    /**
     * Get the routing steps that are performed at this work center.
     */
    public function routingOperations()
    {
        return $this->hasMany(MfgRoutingOperation::class, 'work_center_id');
    }

    /**
     * Get the production transactions recorded at this work center.
     */
    public function productionTransactions()
    {
        return $this->hasMany(MfgProductionTransaction::class, 'mfg_work_center_id');
    }

    // /**
    //  * Get the location of this work center.
    //  */
    // public function location()
    // {
    //     return $this->belongsTo(Location::class, 'location_id');
    // }

    // /**
    //  * Get the cost center associated with this work center.
    //  */
    // public function costCenter()
    // {
    //     return $this->belongsTo(CostCenter::class, 'cost_center_id');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

