@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>WhatsApp Message Templates</h1>
    <a href="{{ route('admin.whatsapp_integration.message_templates.create') }}" class="btn btn-primary mb-3">Add New Template</a>

    @if ($message = Session::get('success'))
        <div class="alert alert-success">
            <p>{{ $message }}</p>
        </div>
    @endif

    <table class="table table-bordered">
        <tr>
            <th>No</th>
            <th>Name</th>
            <th>Language Code</th>
            <th>Status</th>
            <th>Provider Template ID</th>
            <th width="280px">Action</th>
        </tr>
        @php $i = 0; @endphp
        @foreach ($messageTemplates as $template)
        <tr>
            <td>{{ ++$i }}</td>
            <td>{{ $template->name }}</td>
            <td>{{ $template->language_code }}</td>
            <td>{{ $template->status }}</td>
            <td>{{ $template->provider_template_id ?? 'N/A' }}</td>
            <td>
                <form action="{{ route('admin.whatsapp_integration.message_templates.destroy',$template->id) }}" method="POST">
                    <a class="btn btn-info" href="{{ route('admin.whatsapp_integration.message_templates.show',$template->id) }}">Show</a>
                    <a class="btn btn-primary" href="{{ route('admin.whatsapp_integration.message_templates.edit',$template->id) }}">Edit</a>
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </td>
        </tr>
        @endforeach
    </table>
    {{-- {!! $messageTemplates->links() !!} --}}
</div>
@endsection

