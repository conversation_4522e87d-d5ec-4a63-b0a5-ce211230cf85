<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string format(float $amount, string|null $currency = null, bool $showSymbol = true)
 * @method static string formatSAR(float $amount, bool $showSymbol = true)
 * @method static string getSymbol(string|null $currency = null)
 * @method static string getName(string|null $currency = null, string $locale = 'ar')
 * @method static array getSupportedCurrencies(string $locale = 'ar')
 * @method static float parseAmount(string $amount, string|null $currency = null)
 * @method static bool isValidCurrency(string $currency)
 * @method static string getDefaultCurrency()
 * @method static string formatForTable(float $amount, string|null $currency = null)
 * @method static string formatForInput(float $amount, string|null $currency = null)
 * @method static string toWords(float $amount, string|null $currency = null)
 */
class Currency extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'currency';
    }
}
