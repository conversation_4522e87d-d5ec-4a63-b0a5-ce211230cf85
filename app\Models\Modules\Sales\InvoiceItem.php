<?php

namespace App\Models\Modules\Sales;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Product;

class InvoiceItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'invoice_id',
        'product_id',
        'quantity',
        'price',
        'tax_rate',
        'discount',
        'line_total',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'float',
        'price' => 'float',
        'tax_rate' => 'float',
        'discount' => 'float',
        'line_total' => 'float',
    ];

    /**
     * Get the invoice that owns the item.
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the product that owns the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the formatted price.
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2);
    }

    /**
     * Get the formatted line total.
     *
     * @return string
     */
    public function getFormattedLineTotalAttribute()
    {
        return number_format($this->line_total, 2);
    }

    /**
     * Get the subtotal (price * quantity).
     *
     * @return float
     */
    public function getSubtotalAttribute()
    {
        return $this->price * $this->quantity;
    }

    /**
     * Get the tax amount.
     *
     * @return float
     */
    public function getTaxAmountAttribute()
    {
        return ($this->subtotal - $this->discount) * ($this->tax_rate / 100);
    }

    /**
     * Get the total (subtotal - discount + tax).
     *
     * @return float
     */
    public function getTotalAttribute()
    {
        return $this->subtotal - $this->discount + $this->tax_amount;
    }
}
