<?php

namespace App\Events\Bnpl;

use App\Models\Order;
use App\Models\BnplTransaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BnplPaymentCapturedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Order $order;
    public BnplTransaction $bnplTransaction;
    public array $captureDetails;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param BnplTransaction $bnplTransaction
     * @param array $captureDetails
     */
    public function __construct(Order $order, BnplTransaction $bnplTransaction, array $captureDetails = [])
    {
        $this->order = $order;
        $this->bnplTransaction = $bnplTransaction;
        $this->captureDetails = $captureDetails;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel(\'channel-name\'),
        ];
    }
}

