<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Permission;
use App\Models\Tenant;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_system',
    ];

    protected $casts = [
        'is_system' => 'boolean',
    ];

    /**
     * العلاقة مع الصلاحيات
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class);
    }

    /**
     * العلاقة مع المستخدمين
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * العلاقة مع المستأجرين
     */
    public function tenants()
    {
        return $this->belongsToMany(Tenant::class, "role_user", "role_id", "tenant_id");
    }

    /**
     * التحقق من وجود صلاحية معينة
     */
    public function hasPermission($permission)
    {
        if (is_string($permission)) {
            return $this->permissions->contains('name', $permission);
        }

        return $permission->intersect($this->permissions)->count() > 0;
    }

    /**
     * إضافة صلاحيات
     */
    public function givePermissionTo(...$permissions)
    {
        $permissions = collect($permissions)
            ->flatten()
            ->map(function ($permission) {
                if (is_string($permission)) {
                    return Permission::whereName($permission)->firstOrFail();
                }

                return $permission;
            });

        $this->permissions()->syncWithoutDetaching($permissions->pluck('id')->toArray());

        return $this;
    }

    /**
     * سحب صلاحيات
     */
    public function revokePermissionTo(...$permissions)
    {
        $permissions = collect($permissions)
            ->flatten()
            ->map(function ($permission) {
                if (is_string($permission)) {
                    return Permission::whereName($permission)->firstOrFail();
                }

                return $permission;
            });

        $this->permissions()->detach($permissions->pluck('id')->toArray());

        return $this;
    }

    /**
     * تحديث صلاحيات
     */
    public function syncPermissions(...$permissions)
    {
        $this->permissions()->detach();

        return $this->givePermissionTo($permissions);
    }
}
