<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\PermissionGroup;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'permission_group_id',
        'name',
        'display_name',
        'description',
    ];

    /**
     * العلاقة مع مجموعة الصلاحيات
     */
    public function group()
    {
        return $this->belongsTo(PermissionGroup::class, 'permission_group_id');
    }

    /**
     * العلاقة مع الأدوار
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     * العلاقة مع المستخدمين
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
