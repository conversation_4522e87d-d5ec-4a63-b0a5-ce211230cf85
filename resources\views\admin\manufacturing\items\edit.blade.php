@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Edit Manufacturing Item: {{-- $item->name --}}</h1>
        <form action="{{-- route("admin.manufacturing.items.update", $item->id) --}}" method="POST">
            @csrf
            @method("PUT")
            <div class="form-group">
                <label for="name">Item Name</label>
                <input type="text" name="name" id="name" class="form-control" value="{{-- $item->name --}}" required>
            </div>
            <div class="form-group">
                <label for="item_code">Item Code</label>
                <input type="text" name="item_code" id="item_code" class="form-control" value="{{-- $item->item_code --}}" required>
            </div>
            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" id="description" class="form-control">{{-- $item->description --}}</textarea>
            </div>
            <div class="form-group">
                <label for="item_type">Item Type</label>
                <select name="item_type" id="item_type" class="form-control">
                    <option value="raw_material" {{-- $item->item_type == 'raw_material' ? 'selected' : '' --}}>Raw Material</option>
                    <option value="semi_finished_good" {{-- $item->item_type == 'semi_finished_good' ? 'selected' : '' --}}>Semi-Finished Good</option>
                    <option value="finished_good" {{-- $item->item_type == 'finished_good' ? 'selected' : '' --}}>Finished Good</option>
                    <option value="service" {{-- $item->item_type == 'service' ? 'selected' : '' --}}>Service</option>
                    <option value="asset" {{-- $item->item_type == 'asset' ? 'selected' : '' --}}>Asset</option>
                    <option value="expense" {{-- $item->item_type == 'expense' ? 'selected' : '' --}}>Expense</option>
                    <option value="other" {{-- $item->item_type == 'other' ? 'selected' : '' --}}>Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="unit_of_measure">Unit of Measure</label>
                <input type="text" name="unit_of_measure" id="unit_of_measure" class="form-control" value="{{-- $item->unit_of_measure --}}" required>
            </div>
            <div class="form-group">
                <label for="standard_cost">Standard Cost</label>
                <input type="number" step="0.0001" name="standard_cost" id="standard_cost" class="form-control" value="{{-- $item->standard_cost --}}">
            </div>
            <div class="form-group">
                <label for="branch_id">Branch</label>
                <select name="branch_id" id="branch_id" class="form-control">
                    {{-- @foreach($branches as $branch) --}}
                    {{-- <option value="{{ $branch->id }}" {{ $item->branch_id == $branch->id ? 'selected' : '' }}>{{ $branch->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Branch (Optional)</option> 
                </select>
            </div>
             <div class="form-group">
                <label for="account_id">Default Account</label>
                <select name="account_id" id="account_id" class="form-control">
                    {{-- @foreach($accounts as $account) --}}
                    {{-- <option value="{{ $account->id }}" {{ $item->account_id == $account->id ? 'selected' : '' }}>{{ $account->name }}</option> --}}
                    {{-- @endforeach --}}
                     <option value="">Select Account (Optional)</option>
                </select>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_manufactured" id="is_manufactured" class="form-check-input" value="1" {{-- $item->is_manufactured ? 'checked' : '' --}}>
                <label for="is_manufactured" class="form-check-label">Is Manufactured</label>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_purchased" id="is_purchased" class="form-check-input" value="1" {{-- $item->is_purchased ? 'checked' : '' --}}>
                <label for="is_purchased" class="form-check-label">Is Purchased</label>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_sold" id="is_sold" class="form-check-input" value="1" {{-- $item->is_sold ? 'checked' : '' --}}>
                <label for="is_sold" class="form-check-label">Is Sold</label>
            </div>
            <div class="form-group">
                <label for="min_stock_level">Min Stock Level</label>
                <input type="number" step="0.0001" name="min_stock_level" id="min_stock_level" class="form-control" value="{{-- $item->min_stock_level --}}">
            </div>
            <div class="form-group">
                <label for="max_stock_level">Max Stock Level</label>
                <input type="number" step="0.0001" name="max_stock_level" id="max_stock_level" class="form-control" value="{{-- $item->max_stock_level --}}">
            </div>

            <button type="submit" class="btn btn-success mt-3">Update Item</button>
        </form>
    </div>
@endsection
