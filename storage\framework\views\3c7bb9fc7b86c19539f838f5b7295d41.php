<?php $__env->startSection("content"); ?>
<div class="container">
    <h1>Buy Now, Pay Later (BNPL) Integrations</h1>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <p>Manage and configure integrations with BNPL providers like Tabby and Tamara.</p>

    <div class="card mb-3">
        <div class="card-header">Tabby Integration</div>
        <div class="card-body">
            <p>Status:  Active</p>
            <p>Merchant ID:  TB12345</p>
            <a href="<?php echo e(route("admin.integrations.bnpl.edit", ["provider" => "tabby"])); ?>" class="btn btn-warning btn-sm">Configure Tabby</a>
            <a href="<?php echo e(route("admin.integrations.bnpl.show", ["provider" => "tabby"])); ?>" class="btn btn-info btn-sm">View Details/Logs</a>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">Tamara Integration</div>
        <div class="card-body">
            <p>Status:  Inactive</p>
            <p>API Token:  Not Set</p>
            <a href="<?php echo e(route("admin.integrations.bnpl.edit", ["provider" => "tamara"])); ?>" class="btn btn-warning btn-sm">Configure Tamara</a>
            <a href="<?php echo e(route("admin.integrations.bnpl.show", ["provider" => "tamara"])); ?>" class="btn btn-info btn-sm">View Details/Logs</a>
        </div>
    </div>

    

</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/bnpl/index.blade.php ENDPATH**/ ?>