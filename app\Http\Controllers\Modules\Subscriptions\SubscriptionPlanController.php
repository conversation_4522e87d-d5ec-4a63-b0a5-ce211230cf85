<?php

namespace App\Http\Controllers\Modules\Subscriptions;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Config;

class SubscriptionPlanController extends Controller
{
    /**
     * إنشاء مثيل جديد من وحدة التحكم.
     */
    public function __construct()
    {
        // التحقق من أن المستخدم هو سوبر أدمن إذا كان الإعداد يتطلب ذلك
        if (Config::get('subscriptions.super_admin_only', true)) {
            $this->middleware(function ($request, $next) {
                if (!Auth::user() || !Auth::user()->hasRole('super_admin')) {
                    abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
                }
                return $next($request);
            });
        }
    }
    /**
     * Display a listing of the subscription plans.
     */
    public function index()
    {
        $plans = SubscriptionPlan::latest()->paginate(10);
        return view('admin.subscriptions.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new subscription plan.
     */
    public function create()
    {
        return view('admin.subscriptions.plans.form');
    }

    /**
     * Store a newly created subscription plan in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:subscription_plans,code',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,semi_annually,annually',
            'max_users' => 'required|integer|min:1',
            'max_branches' => 'required|integer|min:1',
            'has_pos' => 'boolean',
            'has_inventory' => 'boolean',
            'has_accounting' => 'boolean',
            'has_manufacturing' => 'boolean',
            'has_hr' => 'boolean',
            'has_crm' => 'boolean',
            'has_purchases' => 'boolean',
            'has_sales' => 'boolean',
            'has_reports' => 'boolean',
            'has_api_access' => 'boolean',
            'storage_space_gb' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        // Set boolean fields
        $validatedData['has_pos'] = $request->has('has_pos');
        $validatedData['has_inventory'] = $request->has('has_inventory');
        $validatedData['has_accounting'] = $request->has('has_accounting');
        $validatedData['has_manufacturing'] = $request->has('has_manufacturing');
        $validatedData['has_hr'] = $request->has('has_hr');
        $validatedData['has_crm'] = $request->has('has_crm');
        $validatedData['has_purchases'] = $request->has('has_purchases');
        $validatedData['has_sales'] = $request->has('has_sales');
        $validatedData['has_reports'] = $request->has('has_reports');
        $validatedData['has_api_access'] = $request->has('has_api_access');
        $validatedData['is_active'] = $request->has('is_active');

        // Generate code if not provided
        if (empty($validatedData['code'])) {
            $validatedData['code'] = Str::slug($validatedData['name']) . '-' . Str::random(5);
        }

        SubscriptionPlan::create($validatedData);

        return redirect()->route('admin.subscriptions.plans.index')
            ->with('success', 'تم إنشاء خطة الاشتراك بنجاح');
    }

    /**
     * Display the specified subscription plan.
     */
    public function show(SubscriptionPlan $plan)
    {
        return view('admin.subscriptions.plans.show', compact('plan'));
    }

    /**
     * Show the form for editing the specified subscription plan.
     */
    public function edit(SubscriptionPlan $plan)
    {
        return view('admin.subscriptions.plans.form', compact('plan'));
    }

    /**
     * Update the specified subscription plan in storage.
     */
    public function update(Request $request, SubscriptionPlan $plan)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:subscription_plans,code,' . $plan->id,
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,semi_annually,annually',
            'max_users' => 'required|integer|min:1',
            'max_branches' => 'required|integer|min:1',
            'has_pos' => 'boolean',
            'has_inventory' => 'boolean',
            'has_accounting' => 'boolean',
            'has_manufacturing' => 'boolean',
            'has_hr' => 'boolean',
            'has_crm' => 'boolean',
            'has_purchases' => 'boolean',
            'has_sales' => 'boolean',
            'has_reports' => 'boolean',
            'has_api_access' => 'boolean',
            'storage_space_gb' => 'required|integer|min:1',
            'is_active' => 'boolean',
        ]);

        // Set boolean fields
        $validatedData['has_pos'] = $request->has('has_pos');
        $validatedData['has_inventory'] = $request->has('has_inventory');
        $validatedData['has_accounting'] = $request->has('has_accounting');
        $validatedData['has_manufacturing'] = $request->has('has_manufacturing');
        $validatedData['has_hr'] = $request->has('has_hr');
        $validatedData['has_crm'] = $request->has('has_crm');
        $validatedData['has_purchases'] = $request->has('has_purchases');
        $validatedData['has_sales'] = $request->has('has_sales');
        $validatedData['has_reports'] = $request->has('has_reports');
        $validatedData['has_api_access'] = $request->has('has_api_access');
        $validatedData['is_active'] = $request->has('is_active');

        $plan->update($validatedData);

        return redirect()->route('admin.subscriptions.plans.index')
            ->with('success', 'تم تحديث خطة الاشتراك بنجاح');
    }

    /**
     * Remove the specified subscription plan from storage.
     */
    public function destroy(SubscriptionPlan $plan)
    {
        // Check if the plan has active subscriptions
        if ($plan->subscriptions()->where('status', 'active')->count() > 0) {
            return redirect()->route('admin.subscriptions.plans.index')
                ->with('error', 'لا يمكن حذف خطة الاشتراك لأنها مرتبطة باشتراكات نشطة');
        }

        $plan->delete();

        return redirect()->route('admin.subscriptions.plans.index')
            ->with('success', 'تم حذف خطة الاشتراك بنجاح');
    }
}
