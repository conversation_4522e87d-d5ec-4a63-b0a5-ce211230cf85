@extends('layouts.admin')

@section('title', 'إعدادات النظام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إعدادات النظام</h3>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- معلومات الشركة -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>معلومات الشركة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="company_name" class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control @error('company_name') is-invalid @enderror"
                                                id="company_name" name="company_name" value="{{ old('company_name', $settings['company_name'] ?? '') }}">
                                            @error('company_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="company_email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control @error('company_email') is-invalid @enderror"
                                                id="company_email" name="company_email" value="{{ old('company_email', $settings['company_email'] ?? '') }}">
                                            @error('company_email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="company_phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control @error('company_phone') is-invalid @enderror"
                                                id="company_phone" name="company_phone" value="{{ old('company_phone', $settings['company_phone'] ?? '') }}">
                                            @error('company_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="company_address" class="form-label">العنوان</label>
                                            <textarea class="form-control @error('company_address') is-invalid @enderror"
                                                id="company_address" name="company_address" rows="3">{{ old('company_address', $settings['company_address'] ?? '') }}</textarea>
                                            @error('company_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control @error('tax_number') is-invalid @enderror"
                                                id="tax_number" name="tax_number" value="{{ old('tax_number', $settings['tax_number'] ?? '') }}">
                                            @error('tax_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات النظام -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>إعدادات النظام</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="currency" class="form-label">العملة</label>
                                            <select class="form-select @error('currency') is-invalid @enderror" id="currency" name="currency">
                                                @foreach($supportedCurrencies as $code => $currency)
                                                    <option value="{{ $code }}" {{ (old('currency', $settings['currency'] ?? '') == $code) ? 'selected' : '' }}>
                                                        {{ $currency['name'] }} ({{ $code }}) {{ $currency['symbol'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('currency')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="language" class="form-label">اللغة</label>
                                            <select class="form-select @error('language') is-invalid @enderror" id="language" name="language">
                                                <option value="ar" {{ (old('language', $settings['language'] ?? '') == 'ar') ? 'selected' : '' }}>العربية</option>
                                                <option value="en" {{ (old('language', $settings['language'] ?? '') == 'en') ? 'selected' : '' }}>English</option>
                                            </select>
                                            @error('language')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select @error('timezone') is-invalid @enderror" id="timezone" name="timezone">
                                                <option value="Asia/Riyadh" {{ (old('timezone', $settings['timezone'] ?? '') == 'Asia/Riyadh') ? 'selected' : '' }}>الرياض (GMT+3)</option>
                                                <option value="Asia/Dubai" {{ (old('timezone', $settings['timezone'] ?? '') == 'Asia/Dubai') ? 'selected' : '' }}>دبي (GMT+4)</option>
                                                <option value="Europe/London" {{ (old('timezone', $settings['timezone'] ?? '') == 'Europe/London') ? 'selected' : '' }}>لندن (GMT+0)</option>
                                            </select>
                                            @error('timezone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="logo" class="form-label">شعار الشركة</label>
                                            <input type="file" class="form-control @error('logo') is-invalid @enderror" id="logo" name="logo">
                                            @error('logo')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            @if(!empty($settings['logo'] ?? ''))
                                                <div class="mt-2">
                                                    <img src="{{ asset('storage/' . $settings['logo']) }}" alt="Company Logo" class="img-thumbnail" style="max-height: 100px;">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // يمكن إضافة أي سكربتات خاصة بصفحة الإعدادات هنا
</script>
@endpush
