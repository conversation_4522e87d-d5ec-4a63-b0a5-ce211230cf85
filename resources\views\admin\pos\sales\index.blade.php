@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Sales</h1>
    {{-- POS Sales will likely be initiated from a dedicated POS interface rather than a generic admin "create" button --}}
    {{-- <a href="{{ route("admin.pos.sales.create") }}" class="btn btn-primary mb-3">Create New Sale</a> --}}

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    {{-- Add search/filter form here if needed --}}

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Invoice No</th>
                <th>Customer</th>
                <th>Total Amount</th>
                <th>Status</th>
                <th>Sale Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {{-- @foreach($sales as $sale) --}}
            {{-- Replace with actual data when available --}}
            <tr>
                <td>1</td>
                <td>INV-2023-001</td>
                <td>Walk-in Customer</td>
                <td>150.75</td>
                <td>Completed</td>
                <td>2023-05-13 10:15:00</td>
                <td>
                    <a href="{{-- route("admin.pos.sales.show", $sale->id) --}}" class="btn btn-info btn-sm">View</a>
                    {{-- Edit might be for returns/adjustments --}}
                    <a href="{{-- route("admin.pos.sales.edit", $sale->id) --}}" class="btn btn-warning btn-sm">Adjust/Return</a>
                    <a href="{{-- route("admin.pos.sales.printReceipt", $sale->id) --}}" class="btn btn-secondary btn-sm">Print Receipt</a>
                    {{-- Delete/Void action --}}
                    {{-- <form action="{{ route("admin.pos.sales.destroy", $sale->id) }}" method="POST" style="display:inline-block;">
                        @csrf
                        @method("DELETE")
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure you want to void this sale?")">Void</button>
                    </form> --}}
                </td>
            </tr>
            {{-- @endforeach --}}
        </tbody>
    </table>
    {{-- Pagination links --}}
    {{-- {{ $sales->links() }} --}}
</div>
@endsection

