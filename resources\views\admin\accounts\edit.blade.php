@extends("layouts.admin")

@section("main-content")
    <div class="container">
        <h2>Edit Account: {{ $account->name }}</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.accounts.update", $account->id) }}" method="POST">
            @csrf
            @method("PUT")
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name", $account->name) }}" required>
            </div>
            <div class="form-group">
                <label for="code">Code:</label>
                <input type="text" class="form-control" id="code" name="code" value="{{ old("code", $account->code) }}" required>
            </div>
            <div class="form-group">
                <label for="account_type_id">Account Type:</label>
                <select class="form-control" id="account_type_id" name="account_type_id" required>
                    <option value="">Select Account Type</option>
                    @foreach ($accountTypes as $type)
                        <option value="{{ $type->id }}" {{ old("account_type_id", $account->account_type_id) == $type->id ? "selected" : "" }}>
                            {{ $type->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label for="branch_id">Branch (Optional):</label>
                <select class="form-control" id="branch_id" name="branch_id">
                    <option value="">Select Branch (if applicable)</option>
                    @foreach ($branches as $branch)
                        <option value="{{ $branch->id }}" {{ old("branch_id", $account->branch_id) == $branch->id ? "selected" : "" }}>
                            {{ $branch->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label for="opening_balance">Opening Balance:</label>
                <input type="number" step="0.01" class="form-control" id="opening_balance" name="opening_balance" value="{{ old("opening_balance", $account->opening_balance) }}" required>
            </div>
            <div class="form-group">
                <label for="is_active">Is Active?</label>
                <select class="form-control" id="is_active" name="is_active">
                    <option value="1" {{ old("is_active", $account->is_active) == "1" ? "selected" : "" }}>Yes</option>
                    <option value="0" {{ old("is_active", $account->is_active) == "0" ? "selected" : "" }}>No</option>
                </select>
            </div>
            <div class="form-group">
                <label for="description">Description (Optional):</label>
                <textarea class="form-control" id="description" name="description">{{ old("description", $account->description) }}</textarea>
            </div>
            <button type="submit" class="btn btn-success">Update Account</button>
            <a href="{{ route("admin.accounts.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

