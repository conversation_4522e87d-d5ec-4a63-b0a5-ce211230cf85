<?php

namespace App\Services\Delivery;

interface DeliveryServiceInterface
{
    public function createShipment(array $data);

    public function trackShipment(string $trackingNumber);

    public function getShipmentRates(array $data);

    public function schedulePickup(array $data);

    public function printLabel(string $trackingNumber);

    public function cancelShipment(string $trackingNumber);
}

