@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Session Details</h1>

    {{-- Assuming $session variable is passed to this view --}}
    {{-- @if(!$session)
        <div class="alert alert-danger">Session not found.</div>
        <a href="{{ route("admin.pos.sessions.index") }}" class="btn btn-primary">Back to List</a>
    @else --}}
        <table class="table table-bordered">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $session->id --}}1</td>
                </tr>
                <tr>
                    <th>POS Device</th>
                    <td>{{-- $session->device->name ?? 'N/A' --}}Main Counter POS</td>
                </tr>
                <tr>
                    <th>User (Cashier)</th>
                    <td>{{-- $session->user->name ?? 'N/A' --}}Cashier One</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>{{-- ucfirst($session->status) --}}Open</td>
                </tr>
                <tr>
                    <th>Opening Balance</th>
                    <td>{{-- number_format($session->opening_balance, 2) --}}100.00</td>
                </tr>
                <tr>
                    <th>Closing Balance</th>
                    <td>{{-- $session->closing_balance ? number_format($session->closing_balance, 2) : 'N/A' --}}N/A</td>
                </tr>
                <tr>
                    <th>Calculated Sales</th>
                    <td>{{-- number_format($session->calculated_sales, 2) --}} (To be calculated)</td>
                </tr>
                 <tr>
                    <th>Difference</th>
                    <td>{{-- $session->closing_balance ? number_format($session->closing_balance - ($session->opening_balance + $session->calculated_sales), 2) : 'N/A' --}} (To be calculated)</td>
                </tr>
                <tr>
                    <th>Opened At</th>
                    <td>{{-- $session->opened_at->format('Y-m-d H:i:s') --}}2023-05-13 09:00:00</td>
                </tr>
                <tr>
                    <th>Closed At</th>
                    <td>{{-- $session->closed_at ? $session->closed_at->format('Y-m-d H:i:s') : 'N/A' --}}N/A</td>
                </tr>
                <tr>
                    <th>Notes</th>
                    <td>{{-- $session->notes ?? 'N/A' --}}Initial cash float.</td>
                </tr>
            </tbody>
        </table>

        {{-- @if($session->status == 'open') --}}
            <a href="{{-- route("admin.pos.sessions.edit", $session->id) --}}" class="btn btn-warning">Manage Session</a> {{-- Or link to POS interface --}}
            <form action="{{-- route("admin.pos.sessions.close", $session->id) --}}" method="POST" style="display:inline-block; margin-left: 5px;">
                @csrf
                <button type="submit" class="btn btn-danger" onclick="return confirm("Are you sure you want to close this session? This will finalize sales and calculate totals.")">Close Session</button>
            </form>
        {{-- @endif --}}
        <a href="{{ route("admin.pos.sessions.index") }}" class="btn btn-secondary" style="margin-left: 5px;">Back to List</a>
    {{-- @endif --}}
</div>
@endsection

