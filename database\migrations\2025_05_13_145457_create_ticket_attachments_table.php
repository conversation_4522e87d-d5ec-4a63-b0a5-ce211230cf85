<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("ticket_attachments")) {
            Schema::create("ticket_attachments", function (Blueprint $table) {
                $table->id();
                // Add any other columns for ticket_attachments here if they were intended
                // For example:
                // $table->foreignId("ticket_id")->constrained("tickets");
                // $table->string("file_path");
                // $table->string("file_name");
                // $table->string("file_type")->nullable();
                // $table->unsignedBigInteger("file_size")->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("ticket_attachments");
    }
};

