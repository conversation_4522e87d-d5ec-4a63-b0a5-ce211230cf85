<?php $__env->startSection('title', 'لوحة التحكم الرئيسية'); ?>

<?php $__env->startSection('header', 'لوحة التحكم الرئيسية'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.main-accounts-list {
    padding-right: 0;
    list-style-type: none;
}

.main-accounts-list li {
    margin-bottom: 15px;
}

.account-item {
    padding: 12px 15px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s;
    border: 1px solid #eaeaea;
}

.account-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transform: translateY(-2px);
    border-color: #d1d1d1;
}

.account-code {
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.account-name {
    flex-grow: 1;
}

.list-group-item {
    border-right: none;
    border-left: none;
    padding: 15px 20px;
}

.bg-gradient-blue {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.bg-gradient-green {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.bg-gradient-purple {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.card-icon-lg {
    width: 50px;
    height: 50px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-people-fill text-primary" style="font-size: 2.5rem;"></i>
            </div>
            <h5>المستخدمين</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\User::count()); ?></h3>
            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-sm btn-outline-primary mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-building text-success" style="font-size: 2.5rem;"></i>
            </div>
            <h5>الفروع</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\Modules\Branches\Branch::count() ?? 0); ?></h3>
            <a href="<?php echo e(route('admin.branches.index')); ?>" class="btn btn-sm btn-outline-success mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-journal-text text-info" style="font-size: 2.5rem;"></i>
            </div>
            <h5>الحسابات</h5>
            <h3 class="mb-0"><?php echo e($totalAccounts); ?></h3>
            <a href="<?php echo e(route('admin.accounts.index')); ?>" class="btn btn-sm btn-outline-info mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-journal-plus text-warning" style="font-size: 2.5rem;"></i>
            </div>
            <h5>أنواع الحسابات</h5>
            <h3 class="mb-0"><?php echo e($accountTypes); ?></h3>
            <a href="<?php echo e(route('admin.account_types.index')); ?>" class="btn btn-sm btn-outline-warning mt-3">عرض الكل</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="admin-card">
            <h5 class="mb-3">روابط سريعة</h5>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-outline-primary w-100 py-3">
                        <i class="bi bi-person-plus-fill mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة مستخدم جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.branches.create')); ?>" class="btn btn-outline-success w-100 py-3">
                        <i class="bi bi-building-add mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة فرع جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.accounts.create')); ?>" class="btn btn-outline-info w-100 py-3">
                        <i class="bi bi-journal-plus mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة حساب جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.journal_entries.create')); ?>" class="btn btn-outline-warning w-100 py-3">
                        <i class="bi bi-file-earmark-plus mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة قيد محاسبي</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="admin-card">
            <h5 class="mb-3"><i class="bi bi-diagram-3 me-2"></i> شجرة الحسابات الرئيسية</h5>
            <div class="account-tree">
                <?php if(isset($mainAccounts) && count($mainAccounts) > 0): ?>
                    <ul class="main-accounts-list">
                        <?php $__currentLoopData = $mainAccounts->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <div class="account-item">
                                    <div class="d-flex align-items-center">
                                        <span class="account-code badge bg-light text-dark me-2"><?php echo e($account->code); ?></span>
                                        <span class="account-name"><?php echo e($account->name_ar); ?></span>
                                    </div>
                                    <a href="<?php echo e(route('admin.accounts.show', $account->id)); ?>" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> لا توجد حسابات مضافة بعد.
                    </div>
                <?php endif; ?>
            </div>
            <div class="text-center mt-3">
                <a href="<?php echo e(route('admin.accounts.index')); ?>" class="btn btn-sm btn-primary">عرض كل الحسابات</a>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="admin-card">
            <h5 class="mb-3"><i class="bi bi-clock-history me-2"></i> آخر النشاطات</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>النشاط</th>
                            <th>التفاصيل</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>إضافة حساب جديد</td>
                            <td>تم إضافة حساب "المصروفات التشغيلية" بواسطة المدير</td>
                            <td>منذ 3 ساعات</td>
                        </tr>
                        <tr>
                            <td>تعديل حساب</td>
                            <td>تم تعديل حساب "الإيرادات" بواسطة المدير</td>
                            <td>منذ 5 ساعات</td>
                        </tr>
                        <tr>
                            <td>إضافة قيد محاسبي</td>
                            <td>تم إضافة قيد محاسبي جديد بقيمة 5000 ريال</td>
                            <td>منذ يوم</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <a href="#" class="btn btn-sm btn-primary">عرض كل النشاطات</a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/dashboard.blade.php ENDPATH**/ ?>