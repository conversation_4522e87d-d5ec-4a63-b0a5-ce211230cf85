<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\KitchenPrinter;
use App\Models\Modules\Restaurant\KitchenStation;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class KitchenPrinterController extends Controller
{
    public function index()
    {
        $kitchenPrinters = KitchenPrinter::with(['kitchenStation', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->orderBy('name')
            ->paginate(15);

        return view('admin.restaurant.kitchen-printers.index', compact('kitchenPrinters'));
    }

    public function create()
    {
        $kitchenStations = KitchenStation::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.kitchen-printers.form', compact('kitchenStations', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'ip_address' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
            'type' => 'required|in:thermal,impact,laser',
            'connection_type' => 'required|in:network,usb,serial',
            'driver' => 'nullable|string|max:100',
            'paper_width' => 'nullable|integer|min:58|max:80',
            'auto_cut' => 'boolean',
            'kitchen_station_id' => 'required|exists:kitchen_stations,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['auto_cut'] = $request->has('auto_cut');
        $validatedData['is_active'] = $request->has('is_active');

        KitchenPrinter::create($validatedData);

        return redirect()->route('admin.restaurant.kitchen-printers.index')
            ->with('success', __('Kitchen printer created successfully.'));
    }

    public function show(KitchenPrinter $kitchenPrinter)
    {
        $kitchenPrinter->load(['kitchenStation', 'branch']);
        return view('admin.restaurant.kitchen-printers.show', compact('kitchenPrinter'));
    }

    public function edit(KitchenPrinter $kitchenPrinter)
    {
        $kitchenStations = KitchenStation::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.kitchen-printers.form', compact('kitchenPrinter', 'kitchenStations', 'branches'));
    }

    public function update(Request $request, KitchenPrinter $kitchenPrinter)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'ip_address' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
            'type' => 'required|in:thermal,impact,laser',
            'connection_type' => 'required|in:network,usb,serial',
            'driver' => 'nullable|string|max:100',
            'paper_width' => 'nullable|integer|min:58|max:80',
            'auto_cut' => 'boolean',
            'kitchen_station_id' => 'required|exists:kitchen_stations,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['auto_cut'] = $request->has('auto_cut');
        $validatedData['is_active'] = $request->has('is_active');

        $kitchenPrinter->update($validatedData);

        return redirect()->route('admin.restaurant.kitchen-printers.index')
            ->with('success', __('Kitchen printer updated successfully.'));
    }

    public function destroy(KitchenPrinter $kitchenPrinter)
    {
        $kitchenPrinter->delete();

        return redirect()->route('admin.restaurant.kitchen-printers.index')
            ->with('success', __('Kitchen printer deleted successfully.'));
    }

    public function testConnection(KitchenPrinter $kitchenPrinter)
    {
        $isConnected = $kitchenPrinter->testConnection();

        return response()->json([
            'success' => $isConnected,
            'message' => $isConnected 
                ? __('Printer connection successful.') 
                : __('Printer connection failed.'),
            'status' => $isConnected ? 'connected' : 'disconnected'
        ]);
    }
}
