@extends('layouts.admin')

@section('title', 'تحويلات المخزون')

@push('styles')
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تحويلات المخزون</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.inventory.transfers.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة تحويل جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في التحويلات...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="in_transit">في الطريق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter" placeholder="تاريخ التحويل">
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم التحويل</th>
                                    <th>التاريخ</th>
                                    <th>من مستودع</th>
                                    <th>إلى مستودع</th>
                                    <th>عدد الأصناف</th>
                                    <th>القيمة الإجمالية</th>
                                    <th>الحالة</th>
                                    <th>المسؤول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>TRF-2024-001</td>
                                    <td>2024-03-15</td>
                                    <td>المستودع الرئيسي</td>
                                    <td>مستودع الفرع الثاني</td>
                                    <td>3</td>
                                    <td>15,000 ريال</td>
                                    <td><span class="badge badge-warning">في الطريق</span></td>
                                    <td>أحمد محمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.inventory.transfers.show', 1) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory.transfers.edit', 1) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.inventory.transfers.destroy', 1) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>TRF-2024-002</td>
                                    <td>2024-03-14</td>
                                    <td>مستودع الفرع الثاني</td>
                                    <td>المستودع الرئيسي</td>
                                    <td>2</td>
                                    <td>8,500 ريال</td>
                                    <td><span class="badge badge-success">مكتمل</span></td>
                                    <td>فاطمة أحمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.inventory.transfers.show', 2) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory.transfers.edit', 2) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.inventory.transfers.destroy', 2) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>TRF-2024-003</td>
                                    <td>2024-03-13</td>
                                    <td>المستودع الرئيسي</td>
                                    <td>مستودع المواد الخام</td>
                                    <td>5</td>
                                    <td>12,300 ريال</td>
                                    <td><span class="badge badge-secondary">معلق</span></td>
                                    <td>محمد علي</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.inventory.transfers.show', 3) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory.transfers.edit', 3) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.inventory.transfers.destroy', 3) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة تحويلات المخزون
        console.log('تم تعطيل DataTables في صفحة تحويلات المخزون');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة تحويلات المخزون');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة تحويلات المخزون');
                return this;
            };
        }
        
        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });

        // فلترة حسب الحالة
        $('#statusFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                }
            });
        });

        // فلترة حسب التاريخ
        $('#dateFilter').on('change', function() {
            var value = $(this).val();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().indexOf(value) > -1);
                }
            });
        });
    });
</script>
@endpush
