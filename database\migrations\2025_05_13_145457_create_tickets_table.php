<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("tickets")) {
            Schema::create("tickets", function (Blueprint $table) {
                $table->id();
                // Add any other columns for tickets here if they were intended
                // For example:
                // $table->string("subject");
                // $table->text("description");
                // $table->foreignId("user_id")->constrained("users"); // or customer_id
                // $table->foreignId("assigned_to_admin_id")->nullable()->constrained("admins");
                // $table->foreignId("ticket_category_id")->constrained("ticket_categories");
                // $table->foreignId("ticket_priority_id")->constrained("ticket_priorities");
                // $table->foreignId("ticket_status_id")->constrained("ticket_statuses");
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("tickets");
    }
};

