<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mfg_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id'); // Foreign key to existing products table
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->enum('type', ['manufactured', 'semi_manufactured', 'raw_material']); // Type of product in manufacturing context
            $table->string('mfg_code')->unique()->nullable(); // Specific manufacturing code, if different from general product code
            $table->text('description')->nullable();
            $table->unsignedBigInteger('default_bom_id')->nullable(); // Foreign key to mfg_boms table (defined later)
            $table->unsignedBigInteger('default_routing_id')->nullable(); // Foreign key to mfg_routings table (defined later)
            $table->decimal('standard_cost', 15, 4)->default(0.0000);
            $table->string('costing_method', 50)->default('standard'); // e.g., standard, actual
            $table->boolean('can_be_manufactured')->default(false);
            $table->boolean('can_be_purchased')->default(true);
            $table->decimal('lead_time_days', 8, 2)->nullable(); // Manufacturing lead time
            $table->decimal('safety_stock_quantity', 15, 4)->default(0.0000);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mfg_products');
    }
};

