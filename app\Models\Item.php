<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Modules\Branches\Branch;
use App\Models\Modules\GeneralLedger\Account;
use App\Models\User;

class Item extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'type',
        'unit_of_measure_ar',
        'unit_of_measure_en',
        'description_ar',
        'description_en',
        'standard_cost',
        'last_purchase_price',
        'selling_price',
        'branch_id',
        'inventory_account_id',
        'cogs_account_id',
        'sales_revenue_account_id',
        'is_active',
        'is_manufactured',
        'is_purchased',
        'is_sold',
        'created_by_user_id',
        'updated_by_user_id'
    ];

    protected $casts = [
        'standard_cost' => 'decimal:4',
        'last_purchase_price' => 'decimal:4',
        'selling_price' => 'decimal:4',
        'is_active' => 'boolean',
        'is_manufactured' => 'boolean',
        'is_purchased' => 'boolean',
        'is_sold' => 'boolean',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function inventoryAccount()
    {
        return $this->belongsTo(Account::class, 'inventory_account_id');
    }

    public function cogsAccount()
    {
        return $this->belongsTo(Account::class, 'cogs_account_id');
    }

    public function salesRevenueAccount()
    {
        return $this->belongsTo(Account::class, 'sales_revenue_account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by_user_id');
    }
}
