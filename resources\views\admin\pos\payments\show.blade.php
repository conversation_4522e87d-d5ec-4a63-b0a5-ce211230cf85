@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Payment Details</h1>

    {{-- Assuming $payment variable is passed to this view --}}
    {{-- @if(!$payment)
        <div class="alert alert-danger">Payment not found.</div>
        <a href="{{ route("admin.pos.payments.index") }}" class="btn btn-primary">Back to List</a>
    @else --}}
        <table class="table table-bordered">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $payment->id --}}1</td>
                </tr>
                <tr>
                    <th>Sale ID</th>
                    <td><a href="#{{-- route("admin.pos.sales.show", $payment->sale_id) --}}">{{-- $payment->sale_id --}}S1001</a></td>
                </tr>
                <tr>
                    <th>Amount</th>
                    <td>{{-- number_format($payment->amount, 2) --}}50.00</td>
                </tr>
                <tr>
                    <th>Payment Method</th>
                    <td>{{-- ucfirst(str_replace("_", " ", $payment->payment_method)) --}}Cash</td>
                </tr>
                <tr>
                    <th>Payment Date</th>
                    <td>{{-- $payment->payment_date->format("Y-m-d H:i:s") --}}2023-05-13 11:00:00</td>
                </tr>
                <tr>
                    <th>Reference</th>
                    <td>{{-- $payment->reference ?? "N/A" --}}N/A</td>
                </tr>
                <tr>
                    <th>Notes</th>
                    <td>{{-- $payment->notes ?? "N/A" --}}Payment for order S1001.</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{-- $payment->created_at->format("Y-m-d H:i:s") --}}2023-05-13 11:00:05</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{-- $payment->updated_at->format("Y-m-d H:i:s") --}}2023-05-13 11:00:05</td>
                </tr>
            </tbody>
        </table>

        <a href="{{-- route("admin.pos.payments.edit", $payment->id) --}}" class="btn btn-warning">Edit Payment</a>
        <a href="{{ route("admin.pos.payments.index") }}" class="btn btn-secondary">Back to Payments List</a>
        {{-- @if($payment->sale_id)
        <a href="{{ route("admin.pos.sales.show", $payment->sale_id) }}" class="btn btn-info">View Associated Sale</a>
        @endif --}}
    {{-- @endif --}}
</div>
@endsection

