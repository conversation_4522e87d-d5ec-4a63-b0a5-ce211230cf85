<?php

namespace App\Http\Controllers;

use App\Models\Modules\GeneralLedger\Account;
use App\Models\Modules\GeneralLedger\AccountType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index()
    {
        $user = Auth::user();

        // التحقق من دور المستخدم وتوجيهه إلى لوحة التحكم المناسبة
        if ($user && $user->hasRole('admin')) {
            // إحصائيات الحسابات للمدير
            $totalAccounts = Account::count();
            $activeAccounts = Account::where('is_active', true)->count();
            $accountTypes = AccountType::count();

            // الحسابات الرئيسية
            $mainAccounts = Account::whereNull('parent_id')->with('children')->get();

            return view('dashboard', compact('totalAccounts', 'activeAccounts', 'accountTypes', 'mainAccounts'));
        } elseif ($user && $user->hasRole('tenant')) {
            return redirect()->route('tenant.dashboard');
        } else {
            // المستخدمين العاديين (موظفي المستأجرين)
            if ($user && $user->tenant_id) {
                return redirect()->route('tenant.dashboard');
            }

            // إحصائيات الحسابات للمستخدم العادي
            $totalAccounts = Account::count();
            $activeAccounts = Account::where('is_active', true)->count();
            $accountTypes = AccountType::count();

            // الحسابات الرئيسية
            $mainAccounts = Account::whereNull('parent_id')->with('children')->get();

            return view('dashboard', compact('totalAccounts', 'activeAccounts', 'accountTypes', 'mainAccounts'));
        }
    }
}
