<?php

namespace App\Models\Modules\Users;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Modules\Permissions\Permission;
use App\Models\User; // Corrected namespace for User model

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        "name",
        "slug",
        "description",
    ];

    /**
     * The permissions that belong to the role.
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, "permission_role");
    }

    /**
     * The users that belong to the role.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, "role_user");
    }
}

