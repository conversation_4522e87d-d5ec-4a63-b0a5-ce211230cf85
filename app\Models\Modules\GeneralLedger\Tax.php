<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tax extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'rate',
        'is_default',
        'is_active',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'rate' => 'float',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the formatted rate as a percentage.
     *
     * @return string
     */
    public function getFormattedRateAttribute()
    {
        return number_format($this->rate, 2) . '%';
    }

    /**
     * Get the status text.
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'نشط' : 'غير نشط';
    }

    /**
     * Get the default status text.
     *
     * @return string
     */
    public function getDefaultTextAttribute()
    {
        return $this->is_default ? 'افتراضي' : 'غير افتراضي';
    }

    /**
     * Scope a query to only include active taxes.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the default tax.
     *
     * @return \App\Models\Modules\GeneralLedger\Tax|null
     */
    public static function getDefault()
    {
        return self::where('is_default', true)->first();
    }
}
