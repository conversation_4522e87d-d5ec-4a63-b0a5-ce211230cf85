<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * عرض قائمة المستخدمين التابعين للمستأجر
     */
    public function index()
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على المستخدمين التابعين للمستأجر
        $users = $user->tenantUsers()->with('roles')->get();
        
        // الحصول على معلومات الاشتراك لمعرفة الحد الأقصى للمستخدمين
        $subscription = Subscription::where('tenant_id', $user->id)
            ->where('status', 'active')
            ->orderBy('end_date', 'desc')
            ->first();
        
        $maxUsers = $subscription ? $subscription->plan->max_users : 0;
        
        return view('tenant.users', compact('user', 'users', 'subscription', 'maxUsers'));
    }

    /**
     * عرض نموذج إنشاء مستخدم جديد
     */
    public function create()
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على معلومات الاشتراك لمعرفة الحد الأقصى للمستخدمين
        $subscription = Subscription::where('tenant_id', $user->id)
            ->where('status', 'active')
            ->orderBy('end_date', 'desc')
            ->first();
        
        if (!$subscription) {
            return redirect()->route('tenant.users')->with('error', 'لا يوجد اشتراك نشط. يرجى الاشتراك أولاً.');
        }
        
        // التحقق من عدم تجاوز الحد الأقصى للمستخدمين
        $currentUsersCount = $user->tenantUsers()->count();
        if ($currentUsersCount >= $subscription->plan->max_users) {
            return redirect()->route('tenant.users')->with('error', 'لقد وصلت إلى الحد الأقصى لعدد المستخدمين المسموح به في خطة اشتراكك.');
        }
        
        // الحصول على الأدوار المتاحة للمستخدمين التابعين للمستأجر
        $roles = Role::where('name', '!=', 'admin')
                    ->where('name', '!=', 'tenant')
                    ->get();
        
        return view('tenant.users.create', compact('roles'));
    }

    /**
     * تخزين مستخدم جديد
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => ['nullable', 'array'],
            'roles.*' => ['exists:roles,id'],
        ]);
        
        // الحصول على معلومات الاشتراك لمعرفة الحد الأقصى للمستخدمين
        $subscription = Subscription::where('tenant_id', $user->id)
            ->where('status', 'active')
            ->orderBy('end_date', 'desc')
            ->first();
        
        if (!$subscription) {
            return redirect()->route('tenant.users')->with('error', 'لا يوجد اشتراك نشط. يرجى الاشتراك أولاً.');
        }
        
        // التحقق من عدم تجاوز الحد الأقصى للمستخدمين
        $currentUsersCount = $user->tenantUsers()->count();
        if ($currentUsersCount >= $subscription->plan->max_users) {
            return redirect()->route('tenant.users')->with('error', 'لقد وصلت إلى الحد الأقصى لعدد المستخدمين المسموح به في خطة اشتراكك.');
        }
        
        // إنشاء المستخدم الجديد
        $newUser = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'tenant_id' => $user->id,
            'is_active' => true,
        ]);
        
        // تعيين الأدوار للمستخدم
        if ($request->has('roles')) {
            // التأكد من أن الأدوار لا تتضمن أدوار المدير أو المستأجر
            $allowedRoles = Role::whereIn('id', $request->roles)
                                ->where('name', '!=', 'admin')
                                ->where('name', '!=', 'tenant')
                                ->pluck('id')
                                ->toArray();
            
            $newUser->roles()->sync($allowedRoles);
        }
        
        // تحديث عدد المستخدمين في الاشتراك
        $subscription->current_users_count = $currentUsersCount + 1;
        $subscription->save();
        
        return redirect()->route('tenant.users')->with('success', 'تم إنشاء المستخدم بنجاح.');
    }

    /**
     * عرض نموذج تعديل مستخدم
     */
    public function edit($id)
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على المستخدم المطلوب تعديله
        $editUser = $user->tenantUsers()->with('roles')->findOrFail($id);
        
        // الحصول على الأدوار المتاحة للمستخدمين التابعين للمستأجر
        $roles = Role::where('name', '!=', 'admin')
                    ->where('name', '!=', 'tenant')
                    ->get();
        
        return view('tenant.users.edit', compact('editUser', 'roles'));
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على المستخدم المطلوب تعديله
        $editUser = $user->tenantUsers()->findOrFail($id);
        
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $editUser->id],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'roles' => ['nullable', 'array'],
            'roles.*' => ['exists:roles,id'],
        ]);
        
        // تحديث بيانات المستخدم
        $editUser->name = $request->name;
        $editUser->email = $request->email;
        
        if ($request->filled('password')) {
            $editUser->password = Hash::make($request->password);
        }
        
        $editUser->save();
        
        // تعيين الأدوار للمستخدم
        if ($request->has('roles')) {
            // التأكد من أن الأدوار لا تتضمن أدوار المدير أو المستأجر
            $allowedRoles = Role::whereIn('id', $request->roles)
                                ->where('name', '!=', 'admin')
                                ->where('name', '!=', 'tenant')
                                ->pluck('id')
                                ->toArray();
            
            $editUser->roles()->sync($allowedRoles);
        } else {
            $editUser->roles()->detach();
        }
        
        return redirect()->route('tenant.users')->with('success', 'تم تحديث بيانات المستخدم بنجاح.');
    }

    /**
     * تغيير حالة المستخدم (تفعيل/تعطيل)
     */
    public function toggleStatus($id)
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على المستخدم المطلوب تغيير حالته
        $editUser = $user->tenantUsers()->findOrFail($id);
        
        // تغيير حالة المستخدم
        $editUser->is_active = !$editUser->is_active;
        $editUser->save();
        
        $status = $editUser->is_active ? 'تفعيل' : 'تعطيل';
        
        return redirect()->route('tenant.users')->with('success', "تم {$status} المستخدم بنجاح.");
    }
}
