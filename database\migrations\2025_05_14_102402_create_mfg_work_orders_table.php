<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_work_orders", function (Blueprint $table) {
            $table->id();
            $table->string("wo_number")->unique();
            $table->string("description")->nullable();
            $table->unsignedBigInteger("mfg_product_id");
            $table->foreign("mfg_product_id")->references("id")->on("mfg_products")->onDelete("cascade");
            $table->decimal("quantity_to_produce", 15, 4);
            $table->string("unit_of_measure");
            $table->unsignedBigInteger("mfg_bom_id")->nullable(); // BOM to be used, can be null if product has default or if manually specified
            $table->foreign("mfg_bom_id")->references("id")->on("mfg_boms")->onDelete("set null");
            $table->unsignedBigInteger("mfg_routing_id")->nullable(); // Routing to be used
            $table->foreign("mfg_routing_id")->references("id")->on("mfg_routings")->onDelete("set null");
            $table->timestamp("scheduled_start_date")->nullable();
            $table->timestamp("scheduled_end_date")->nullable();
            $table->timestamp("actual_start_date")->nullable();
            $table->timestamp("actual_end_date")->nullable();
            $table->string("status"); // e.g., Planned, Released, In Progress, On Hold, Completed, Cancelled
            $table->integer("priority")->default(0);
            $table->decimal("quantity_produced", 15, 4)->default(0);
            $table->decimal("quantity_scrapped", 15, 4)->default(0);
            $table->unsignedBigInteger("source_sales_order_id")->nullable(); // Link to sales order if WO is for a specific order
            // $table->foreign("source_sales_order_id")->references("id")->on("sales_orders")->onDelete("set null");
            $table->unsignedBigInteger("source_forecast_id")->nullable(); // Link to forecast if WO is for forecast
            // $table->foreign("source_forecast_id")->references("id")->on("forecasts")->onDelete("set null");
            $table->text("notes")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->foreign("created_by")->references("id")->on("users")->onDelete("set null");
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->foreign("updated_by")->references("id")->on("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_work_orders");
    }
};

