<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("work_orders", function (Blueprint $table) {
            $table->id();
            $table->string("work_order_number")->unique();
            $table->foreignId("item_id")->constrained("items")->comment("Finished good to be produced");
            $table->foreignId("bom_id")->nullable()->constrained("boms")->comment("BOM used for this work order, if applicable");
            $table->decimal("quantity_to_produce", 15, 4);
            $table->decimal("quantity_produced", 15, 4)->default(0.0000);
            $table->decimal("quantity_scrapped", 15, 4)->default(0.0000);
            $table->string("unit_of_measure_ar")->nullable();
            $table->string("unit_of_measure_en")->nullable();
            $table->enum("status", ["draft", "planned", "in_progress", "on_hold", "completed", "closed", "cancelled"])->default("draft");
            $table->date("planned_start_date");
            $table->date("planned_end_date");
            $table->dateTime("actual_start_date")->nullable();
            $table->dateTime("actual_end_date")->nullable();
            $table->text("notes_ar")->nullable();
            $table->text("notes_en")->nullable();
            $table->integer("priority")->default(0); // e.g., 0-Normal, 1-High, 2-Urgent
            $table->foreignId("branch_id")->constrained("branches");
            // Link to sales order or other source if applicable
            // $table->unsignedBigInteger("source_document_id")->nullable(); 
            // $table->string("source_document_type")->nullable(); // e.g., SalesOrder, StockReplenishment
            $table->foreignId("created_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("updated_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("assigned_to_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("work_orders");
    }
};

