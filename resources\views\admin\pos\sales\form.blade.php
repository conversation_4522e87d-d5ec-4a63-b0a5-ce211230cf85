@extends("layouts.admin")

@section("content")
<div class="container">
    {{-- This form might be more for adjusting/returning a sale rather than creating a new one from scratch in admin --}}
    <h1>{{ isset($sale) ? "Adjust/Return POS Sale" : "Create New POS Sale (Admin Override)" }}</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ isset($sale) ? route("admin.pos.sales.update", $sale->id) : route("admin.pos.sales.store") }}" method="POST">
        @csrf
        @if(isset($sale))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="customer_id">Customer</label>
                    <select name="customer_id" id="customer_id" class="form-control">
                        <option value="">Walk-in Customer</option>
                        {{-- @foreach($customers as $customer) --}}
                        {{-- <option value="{{ $customer->id }}" {{ old("customer_id", isset($sale) && $sale->customer_id == $customer->id ? "selected" : "") }}>{{ $customer->name }}</option> --}}
                        <option value="1">Registered Customer A (ID:1)</option>
                        {{-- @endforeach --}}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sale_date">Sale Date</label>
                    <input type="datetime-local" name="sale_date" id="sale_date" class="form-control" value="{{ old("sale_date", isset($sale) ? optional($sale->sale_date)->format("Y-m-d\TH:i") : now()->format("Y-m-d\TH:i")) }}" required>
                </div>
            </div>
        </div>

        <h3 class="mt-4">Sale Items</h3>
        <div id="sale-items-container">
            {{-- Item rows will be added here dynamically or pre-filled if editing --}}
            {{-- Example of a single item row (to be repeated) --}}
            <div class="row sale-item-row mb-2">
                <div class="col-md-4">
                    <label>Product/Service</label>
                    <select name="items[0][product_id]" class="form-control product-select">
                        {{-- @foreach($products as $product) --}}
                        <option value="1">Product A (Price: 10.00)</option>
                        <option value="2">Service B (Price: 25.50)</option>
                        {{-- @endforeach --}}
                    </select>
                </div>
                <div class="col-md-2">
                    <label>Quantity</label>
                    <input type="number" name="items[0][quantity]" class="form-control quantity-input" value="1" min="1">
                </div>
                <div class="col-md-2">
                    <label>Unit Price</label>
                    <input type="number" name="items[0][unit_price]" class="form-control price-input" step="0.01" value="10.00">
                </div>
                <div class="col-md-2">
                    <label>Subtotal</label>
                    <input type="text" class="form-control subtotal-display" readonly value="10.00">
                </div>
                <div class="col-md-2">
                    <label>&nbsp;</label>
                    <button type="button" class="btn btn-danger btn-sm remove-item-btn">Remove</button>
                </div>
            </div>
        </div>
        <button type="button" id="add-sale-item" class="btn btn-info btn-sm mt-2">Add Item</button>

        <div class="row mt-4">
            <div class="col-md-6 offset-md-6">
                <div class="form-group row">
                    <label for="sub_total_amount" class="col-sm-4 col-form-label">Subtotal</label>
                    <div class="col-sm-8">
                        <input type="text" readonly class="form-control-plaintext" id="sub_total_amount" value="{{ old("sub_total_amount", isset($sale) ? $sale->sub_total_amount : "0.00") }}">
                    </div>
                </div>
                <div class="form-group row">
                    <label for="discount_amount" class="col-sm-4 col-form-label">Discount</label>
                    <div class="col-sm-8">
                        <input type="number" name="discount_amount" id="discount_amount" class="form-control" step="0.01" value="{{ old("discount_amount", isset($sale) ? $sale->discount_amount : "0.00") }}">
                    </div>
                </div>
                <div class="form-group row">
                    <label for="tax_amount" class="col-sm-4 col-form-label">Tax (VAT)</label>
                    <div class="col-sm-8">
                        <input type="text" readonly class="form-control-plaintext" id="tax_amount" value="{{ old("tax_amount", isset($sale) ? $sale->tax_amount : "0.00") }}">
                    </div>
                </div>
                <div class="form-group row">
                    <label for="total_amount" class="col-sm-4 col-form-label font-weight-bold">Total Amount</label>
                    <div class="col-sm-8">
                        <input type="text" readonly class="form-control-plaintext font-weight-bold" id="total_amount" value="{{ old("total_amount", isset($sale) ? $sale->total_amount : "0.00") }}">
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group mt-3">
            <label for="notes">Notes</label>
            <textarea name="notes" id="notes" class="form-control">{{ old("notes", isset($sale) ? $sale->notes : "") }}</textarea>
        </div>

        <button type="submit" class="btn btn-success mt-3">{{ isset($sale) ? "Update Sale" : "Save Sale" }}</button>
        <a href="{{ route("admin.pos.sales.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>

@push("scripts")
<script>
// Basic script for adding/removing items and calculating totals - needs refinement
document.addEventListener("DOMContentLoaded", function() {
    let itemIndex = 1;
    const container = document.getElementById("sale-items-container");

    document.getElementById("add-sale-item").addEventListener("click", function() {
        const newItemRow = `
            <div class="row sale-item-row mb-2">
                <div class="col-md-4">
                    <select name="items[${itemIndex}][product_id]" class="form-control product-select">
                        <option value="1">Product A (Price: 10.00)</option>
                        <option value="2">Service B (Price: 25.50)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" name="items[${itemIndex}][quantity]" class="form-control quantity-input" value="1" min="1">
                </div>
                <div class="col-md-2">
                    <input type="number" name="items[${itemIndex}][unit_price]" class="form-control price-input" step="0.01" value="0.00">
                </div>
                <div class="col-md-2">
                    <input type="text" class="form-control subtotal-display" readonly value="0.00">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-item-btn">Remove</button>
                </div>
            </div>`;
        container.insertAdjacentHTML("beforeend", newItemRow);
        itemIndex++;
        attachEventListeners();
    });

    function attachEventListeners() {
        document.querySelectorAll(".remove-item-btn").forEach(btn => {
            btn.removeEventListener("click", handleRemoveItem); // Prevent multiple listeners
            btn.addEventListener("click", handleRemoveItem);
        });
        document.querySelectorAll(".product-select, .quantity-input, .price-input, #discount_amount").forEach(input => {
            input.removeEventListener("change", calculateTotals);
            input.removeEventListener("input", calculateTotals);
            input.addEventListener("change", calculateTotals);
            input.addEventListener("input", calculateTotals);
        });
         // Update price when product changes
        document.querySelectorAll(".product-select").forEach(select => {
            select.removeEventListener("change", updatePriceOnProductChange);
            select.addEventListener("change", updatePriceOnProductChange);
        });
    }

    function handleRemoveItem(event) {
        event.target.closest(".sale-item-row").remove();
        calculateTotals();
    }
    
    function updatePriceOnProductChange(event) {
        const selectedOption = event.target.options[event.target.selectedIndex];
        const price = parseFloat(selectedOption.text.split("(Price: ")[1].replace(")", "")) || 0;
        const row = event.target.closest(".sale-item-row");
        row.querySelector(".price-input").value = price.toFixed(2);
        calculateTotals(); // Recalculate after price update
    }

    function calculateTotals() {
        let overallSubtotal = 0;
        document.querySelectorAll(".sale-item-row").forEach(row => {
            const quantity = parseFloat(row.querySelector(".quantity-input").value) || 0;
            const price = parseFloat(row.querySelector(".price-input").value) || 0;
            const itemSubtotal = quantity * price;
            row.querySelector(".subtotal-display").value = itemSubtotal.toFixed(2);
            overallSubtotal += itemSubtotal;
        });

        document.getElementById("sub_total_amount").value = overallSubtotal.toFixed(2);
        const discount = parseFloat(document.getElementById("discount_amount").value) || 0;
        // Assuming VAT is 15% of (subtotal - discount)
        const taxableAmount = overallSubtotal - discount;
        const tax = taxableAmount > 0 ? taxableAmount * 0.15 : 0;
        document.getElementById("tax_amount").value = tax.toFixed(2);
        const total = taxableAmount + tax;
        document.getElementById("total_amount").value = total.toFixed(2);
    }

    attachEventListeners(); // Initial attachment
    calculateTotals(); // Initial calculation
});
</script>
@endpush

