@extends("layouts.admin")

@section("title", "إدارة الفروع")

@section("header", "إدارة الفروع")

@push('styles')
<!-- إضافة مكتبات DataTables مباشرة في الصفحة -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
@endpush

@section("content")
<div class="row">
    <div class="col-12 mb-4">
        <div class="admin-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">
                    <i class="bi bi-building-fill text-primary me-2"></i>
                    قائمة الفروع
                </h5>
                <a href="{{ route("admin.branches.create") }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    إضافة فرع جديد
                </a>
            </div>

            <div class="mb-3">
                <input type="text" id="search-input" class="form-control" placeholder="ابحث...">
            </div>
            <div class="table-responsive">
                <table id="branches-table" class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>الاسم</th>
                            <th>الكود</th>
                            <th>الفرع الرئيسي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($branches as $branch)
                            <tr>
                                <td>{{ $branch->id }}</td>
                                <td>{{ $branch->name }}</td>
                                <td>{{ $branch->code }}</td>
                                <td>{{ $branch->parent->name ?? "لا يوجد" }}</td>
                                <td>
                                    @if ($branch->is_active)
                                        <span class="badge bg-success">نشط</span>
                                    @else
                                        <span class="badge bg-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route("admin.branches.show", $branch->id) }}" class="btn btn-info btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ route("admin.branches.edit", $branch->id) }}" class="btn btn-warning btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form action="{{ route("admin.branches.destroy", $branch->id) }}" method="POST" style="display:inline-block;">
                                            @csrf
                                            @method("DELETE")
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">لا توجد فروع مسجلة.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="admin-card">
            <h5 class="mb-3">روابط سريعة</h5>
            <div class="row">
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="{{ route('admin.branches.create') }}" class="btn btn-outline-primary w-100 py-3">
                        <i class="bi bi-building-add mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة فرع جديد</div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-info w-100 py-3">
                        <i class="bi bi-speedometer2 mb-2" style="font-size: 1.5rem;"></i>
                        <div>العودة للوحة التحكم</div>
                    </a>
                </div>
                <div class="col-md-4 col-sm-6 mb-3">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-success w-100 py-3">
                        <i class="bi bi-people-fill mb-2" style="font-size: 1.5rem;"></i>
                        <div>إدارة المستخدمين</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- إضافة مكتبات DataTables مباشرة في الصفحة -->
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // تعريف اللغة العربية مباشرة بدلاً من استخدام ملف خارجي
        var arabicLanguage = {
            "sProcessing": "جارٍ التحميل...",
            "sLengthMenu": "أظهر _MENU_ مدخلات",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sInfoPostFix": "",
            "sSearch": "ابحث:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        };

        // استخدام jQuery العادي بدلاً من DataTables
        $('#branches-table').addClass('table-striped');

        // إضافة وظيفة البحث البسيطة
        $('#search-input').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#branches-table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>
@endpush
