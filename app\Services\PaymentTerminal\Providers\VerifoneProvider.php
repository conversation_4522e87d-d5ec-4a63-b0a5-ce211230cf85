<?php

namespace App\Services\PaymentTerminal\Providers;

use App\Services\PaymentTerminal\PaymentTerminalServiceInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VerifoneProvider implements PaymentTerminalServiceInterface
{
    protected array $config;
    protected string $baseUrl;

    public function setConfig(array $config): void
    {
        $this->config = $config;
        // Determine base URL based on environment (sandbox/production)
        $this->baseUrl = $this->config["environment"] === "sandbox" 
            ? "https://cst.checkout.vficloud.net" // Sandbox URL from documentation
            : "https://checkout.vficloud.com"; // Production URL (assumption, verify from docs)
    }

    public function initiatePayment(float $amount, string $currency, array $options = []): array
    {
        if (empty($this->config["entity_id"]) || empty($this->config["api_key"])) {
            return ['status' => 'error', 'message' => 'Verifone provider not configured. Missing entity_id or api_key.'];
        }

        $endpoint = $this->baseUrl . "/v2/checkout";
        $payload = [
            "entity_id" => $this->config["entity_id"],
            "currency_code" => strtoupper($currency),
            "amount" => (int)($amount * 100), // Amount in smallest currency unit (e.g., cents)
            "merchant_reference" => $options["invoice_id"] ?? uniqid("vf_txn_"),
            "payment_method_config" => [
                // This section would typically define allowed payment methods or specific terminal configurations
                // For a direct terminal integration, this might involve different parameters
                // For now, assuming a generic checkout API call
            ],
            "interaction_type" => "HPP", // Hosted Payment Page, might need adjustment for direct terminal
            "return_url" => $options["return_url"] ?? route("home"), // Placeholder
            // Add other necessary parameters based on Verifone's documentation for terminal integration
        ];

        try {
            $response = Http::withBasicAuth($this->config["api_key"], '') // API key as username, empty password
                ->timeout(30)
                ->post($endpoint, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                // The actual response structure for initiating a payment to a terminal might differ.
                // This is based on the Checkout API for online payments.
                // For terminal, it might return a transaction ID and expect a separate status check or webhook.
                return [
                    'status' => 'success',
                    'provider' => 'verifone',
                    'transaction_id' => $responseData['id'] ?? null, // Example, adjust based on actual API response
                    'checkout_url' => $responseData['links']['self']['href'] ?? null, // For HPP
                    'message' => 'Payment initiated successfully with Verifone.',
                    'raw_response' => $responseData
                ];
            } else {
                Log::error("Verifone API Error: ", ['status' => $response->status(), 'body' => $response->body()]);
                return [
                    'status' => 'error',
                    'provider' => 'verifone',
                    'message' => 'Failed to initiate payment with Verifone: ' . $response->status(),
                    'details' => $response->json() ?? $response->body()
                ];
            }
        } catch (\Exception $e) {
            Log::error("Verifone Connection Error: " . $e->getMessage());
            return [
                'status' => 'error',
                'provider' => 'verifone',
                'message' => 'Connection error with Verifone: ' . $e->getMessage()
            ];
        }
    }

    public function checkPaymentStatus(string $transactionId): array
    {
        if (empty($this->config["entity_id"]) || empty($this->config["api_key"])) {
            return ['status' => 'error', 'message' => 'Verifone provider not configured.'];
        }

        // The endpoint to check transaction status might be different.
        // This is an assumption based on common API patterns.
        $endpoint = $this->baseUrl . "/v2/checkout/" . $transactionId;

        try {
            $response = Http::withBasicAuth($this->config["api_key"], '')
                ->timeout(30)
                ->get($endpoint);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    'status' => 'success',
                    'provider' => 'verifone',
                    'transaction_status' => $responseData['status'] ?? 'unknown', // e.g., AUTHORIZED, CAPTURED, FAILED
                    'details' => $responseData
                ];
            } else {
                return [
                    'status' => 'error',
                    'provider' => 'verifone',
                    'message' => 'Failed to check payment status with Verifone: ' . $response->status(),
                    'details' => $response->json() ?? $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'provider' => 'verifone',
                'message' => 'Connection error while checking status: ' . $e->getMessage()
            ];
        }
    }

    public function refundPayment(string $transactionId, float $amount, string $currency, array $options = []): array
    {
        // Refund logic would require a specific endpoint and payload structure from Verifone documentation.
        // Placeholder implementation:
        return [
            'status' => 'pending_implementation',
            'provider' => 'verifone',
            'message' => 'Refund functionality for Verifone is not yet implemented.'
        ];
    }

    public function testConnection(): array
    {
        if (empty($this->config["entity_id"]) || empty($this->config["api_key"])) {
            return ['status' => 'error', 'message' => 'Verifone provider not configured. Missing entity_id or api_key.'];
        }
        // A simple test could be to fetch a list of transactions or a specific non-sensitive endpoint.
        // For now, we can try to get a checkout session with a dummy ID or a known one if available.
        // This is a basic check, a more robust one would use a dedicated health check endpoint if Verifone provides one.
        $testTransactionId = "dummy-test-id"; // This will likely fail, but tests connectivity and auth
        $status = $this->checkPaymentStatus($testTransactionId);

        if (isset($status['status']) && $status['status'] === 'error' && str_contains($status['message'], '404')) {
             // A 404 for a dummy ID might indicate successful connection and auth but non-existent resource, which is a good sign for a test.
            return ['status' => 'success', 'provider' => 'verifone', 'message' => 'Connection test to Verifone successful (dummy ID check resulted in 404, auth likely worked).'];
        } elseif (isset($status['status']) && $status['status'] === 'success') {
            return ['status' => 'success', 'provider' => 'verifone', 'message' => 'Connection test to Verifone successful.'];
        }
        
        return ['status' => 'error', 'provider' => 'verifone', 'message' => 'Connection test to Verifone failed.', 'details' => $status];
    }
}

