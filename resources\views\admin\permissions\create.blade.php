@extends("layouts.admin")

@section("title", "إضافة صلاحية جديدة")

@section("header", "إضافة صلاحية جديدة")

@section("content")
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="admin-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-key-fill text-primary me-2"></i>إضافة صلاحية جديدة</h5>
                        <a href="{{ route("admin.permissions_system.permissions.index") }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right me-1"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @if ($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                    <div>
                        <ul class="mb-0 list-unstyled">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-12">
                <div class="admin-card">
                    <form action="{{ route("admin.permissions_system.permissions.store") }}" method="POST">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">اسم الصلاحية (مثال: إنشاء مستخدمين، تعديل فواتير):</label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ old("name") }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="slug" class="form-label">معرف الصلاحية (مثال: create-users, edit-invoices):</label>
                                    <input type="text" class="form-control" id="slug" name="slug" value="{{ old("slug") }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="group_name" class="form-label">اسم المجموعة (مثال: إدارة المستخدمين، إدارة الفواتير):</label>
                                    <input type="text" class="form-control" id="group_name" name="group_name" value="{{ old("group_name") }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">الوصف:</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ old("description") }}</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-group text-end">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-1"></i> حفظ الصلاحية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // توليد معرف الصلاحية تلقائيًا من الاسم
    $(document).ready(function() {
        $('#name').on('input', function() {
            let slug = $(this).val()
                .toLowerCase()
                .replace(/\s+/g, '-')           // استبدال المسافات بشرطات
                .replace(/[^\w\-]+/g, '')       // إزالة الأحرف غير اللاتينية
                .replace(/\-\-+/g, '-')         // استبدال الشرطات المتعددة بشرطة واحدة
                .replace(/^-+/, '')             // إزالة الشرطات من بداية النص
                .replace(/-+$/, '');            // إزالة الشرطات من نهاية النص

            $('#slug').val(slug);
        });
    });
</script>
@endpush

