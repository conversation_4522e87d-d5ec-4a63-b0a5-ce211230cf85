<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->enum('type', ['open', 'pending', 'closed'])->comment('To facilitate grouping and reporting');
            $table->string('color')->nullable()->comment('Hex color code for UI distinction');
            $table->boolean('is_default_new')->default(false)->comment('Is this the default status for new tickets? Only one should be true.');
            $table->boolean('is_default_closed')->default(false)->comment('Is this the default status for closed tickets? Only one should be true.');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_statuses');
    }
};
