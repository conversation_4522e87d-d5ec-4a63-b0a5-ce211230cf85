<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_boms', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Name of the BOM, e.g., "Standard BOM for Product X"');
            $table->text('description')->nullable();
            $table->foreignId('item_id')->constrained('manufacturing_items')->onDelete('cascade')->comment('The finished or semi-finished good this BOM defines');
            $table->string('version')->default('1.0')->comment('BOM version for tracking changes');
            $table->boolean('is_active')->default(true)->comment('Is this BOM version currently active?');
            $table->date('valid_from')->nullable()->comment('Date from which this BOM version is valid');
            $table->date('valid_to')->nullable()->comment('Date until which this BOM version is valid');
            $table->decimal('quantity_produced', 15, 4)->default(1.0000)->comment('Default quantity of the item_id produced by this BOM');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null');
            $table->foreignId('created_by_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['item_id', 'version', 'branch_id'], 'bom_item_version_branch_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_boms');
    }
};
