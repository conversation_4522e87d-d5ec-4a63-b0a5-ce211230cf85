<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mfg_material_allocations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('work_order_id');
            $table->foreign('work_order_id')->references('id')->on('mfg_work_orders')->onDelete('cascade');
            
            $table->unsignedBigInteger('product_id'); // This should be an mfg_product_id
            $table->foreign('product_id')->references('id')->on('mfg_products')->onDelete('cascade');
            
            $table->decimal('quantity_required', 15, 4);
            $table->decimal('quantity_allocated', 15, 4)->default(0.0000);
            $table->decimal('quantity_issued', 15, 4)->default(0.0000);
            
            $table->unsignedBigInteger('warehouse_id')->nullable(); // Assuming a 'warehouses' table exists or will exist
            // $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('set null'); // Uncomment if 'warehouses' table is confirmed
            
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mfg_material_allocations');
    }
};

