<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Modules\Branches\Branch;
use App\Models\Modules\GeneralLedger\Account;

class ManufacturingItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'manufacturing_items';

    protected $fillable = [
        'name',
        'description',
        'item_code',
        'item_type',
        'unit_of_measure',
        'standard_cost',
        'branch_id',
        'account_id',
        'is_manufactured',
        'is_purchased',
        'is_sold',
        'min_stock_level',
        'max_stock_level',
    ];

    protected $casts = [
        'is_manufactured' => 'boolean',
        'is_purchased' => 'boolean',
        'is_sold' => 'boolean',
        'standard_cost' => 'decimal:4',
        'min_stock_level' => 'decimal:4',
        'max_stock_level' => 'decimal:4',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function bomsThisProduces()
    {
        return $this->hasMany(ManufacturingBom::class, 'item_id');
    }

    public function bomComponents()
    {
        return $this->hasMany(ManufacturingBomItem::class, 'item_id');
    }

    public function workOrdersThisProduces()
    {
        return $this->hasMany(ManufacturingWorkOrder::class, 'item_id');
    }

    public function workOrderItems()
    {
        return $this->hasMany(ManufacturingWorkOrderItem::class, 'item_id');
    }

    public function workOrderConsumedItems()
    {
        return $this->hasMany(ManufacturingWorkOrderConsumedItem::class, 'item_id');
    }

    public function productionCosts()
    {
        return $this->hasMany(ManufacturingProductionCost::class, 'item_id');
    }
}

