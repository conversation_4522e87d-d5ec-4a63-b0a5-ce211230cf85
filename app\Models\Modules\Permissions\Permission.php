<?php

namespace App\Models\Modules\Permissions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Modules\Users\Role;
use App\Models\PermissionGroup;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        "name",
        "display_name",
        "slug",
        "group_name",
        "description",
        "permission_group_id",
        "module",
    ];

    /**
     * The roles that belong to the permission.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, "permission_role");
    }

    /**
     * Get the permission group that owns the permission.
     */
    public function permissionGroup()
    {
        return $this->belongsTo(PermissionGroup::class);
    }
}

