<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class SalesTarget extends Model
{
    use HasFactory;

    protected $fillable = [
        'sales_representative_id',
        'target_type',
        'target_year',
        'target_month',
        'target_quarter',
        'sales_target',
        'visits_target',
        'new_customers_target',
        'collection_target',
        'achieved_sales',
        'achieved_visits',
        'achieved_new_customers',
        'achieved_collection',
        'sales_achievement_percentage',
        'visits_achievement_percentage',
        'collection_achievement_percentage',
        'overall_achievement_percentage',
        'bonus_amount',
        'status',
        'target_notes',
        'start_date',
        'end_date',
        'set_by',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'target_year' => 'integer',
        'target_month' => 'integer',
        'target_quarter' => 'integer',
        'sales_target' => 'decimal:2',
        'visits_target' => 'integer',
        'new_customers_target' => 'integer',
        'collection_target' => 'decimal:2',
        'achieved_sales' => 'decimal:2',
        'achieved_visits' => 'integer',
        'achieved_new_customers' => 'integer',
        'achieved_collection' => 'decimal:2',
        'sales_achievement_percentage' => 'decimal:2',
        'visits_achievement_percentage' => 'decimal:2',
        'collection_achievement_percentage' => 'decimal:2',
        'overall_achievement_percentage' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the sales representative for this target.
     */
    public function salesRepresentative(): BelongsTo
    {
        return $this->belongsTo(SalesRepresentative::class);
    }

    /**
     * Get the user who set this target.
     */
    public function setBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'set_by');
    }

    /**
     * Get the branch that owns the target.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the target.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Update achievement percentages.
     */
    public function updateAchievements(): void
    {
        $salesPercentage = $this->sales_target > 0 ? ($this->achieved_sales / $this->sales_target) * 100 : 0;
        $visitsPercentage = $this->visits_target > 0 ? ($this->achieved_visits / $this->visits_target) * 100 : 0;
        $collectionPercentage = $this->collection_target > 0 ? ($this->achieved_collection / $this->collection_target) * 100 : 0;
        
        $overallPercentage = ($salesPercentage + $visitsPercentage + $collectionPercentage) / 3;

        $this->update([
            'sales_achievement_percentage' => $salesPercentage,
            'visits_achievement_percentage' => $visitsPercentage,
            'collection_achievement_percentage' => $collectionPercentage,
            'overall_achievement_percentage' => $overallPercentage,
        ]);
    }

    /**
     * Get target type text in Arabic.
     */
    public function getTargetTypeTextAttribute(): string
    {
        return match($this->target_type) {
            'monthly' => 'شهري',
            'quarterly' => 'ربع سنوي',
            'yearly' => 'سنوي',
            'custom' => 'مخصص',
            default => 'غير محدد'
        };
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => '#28a745',
            'completed' => '#17a2b8',
            'cancelled' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Get overall achievement color based on percentage.
     */
    public function getAchievementColorAttribute(): string
    {
        if ($this->overall_achievement_percentage >= 100) {
            return '#28a745'; // Green
        } elseif ($this->overall_achievement_percentage >= 80) {
            return '#ffc107'; // Yellow
        } elseif ($this->overall_achievement_percentage >= 60) {
            return '#fd7e14'; // Orange
        } else {
            return '#dc3545'; // Red
        }
    }

    /**
     * Check if target is achieved.
     */
    public function isAchieved(): bool
    {
        return $this->overall_achievement_percentage >= 100;
    }

    /**
     * Check if target period is current.
     */
    public function isCurrent(): bool
    {
        return now()->between($this->start_date, $this->end_date);
    }

    /**
     * Scope a query to only include active targets.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include current targets.
     */
    public function scopeCurrent($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope a query to filter by target type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('target_type', $type);
    }

    /**
     * Scope a query to filter by sales representative.
     */
    public function scopeByRepresentative($query, $representativeId)
    {
        return $query->where('sales_representative_id', $representativeId);
    }
}
