<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Invoice;

class SalesCommission extends Model
{
    use HasFactory;

    protected $fillable = [
        'commission_code',
        'sales_representative_id',
        'order_id',
        'invoice_id',
        'customer_id',
        'commission_type',
        'transaction_date',
        'base_amount',
        'commission_rate',
        'commission_amount',
        'additional_bonus',
        'deductions',
        'net_commission',
        'calculation_method',
        'calculation_details',
        'payment_status',
        'payment_date',
        'payment_reference',
        'commission_notes',
        'is_recurring',
        'period_month',
        'period_year',
        'approved_by',
        'approved_at',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'base_amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'additional_bonus' => 'decimal:2',
        'deductions' => 'decimal:2',
        'net_commission' => 'decimal:2',
        'calculation_details' => 'array',
        'payment_date' => 'date',
        'is_recurring' => 'boolean',
        'period_month' => 'integer',
        'period_year' => 'integer',
        'approved_at' => 'datetime',
    ];

    /**
     * Generate unique commission code.
     */
    public static function generateCommissionCode(): string
    {
        $prefix = 'COM';
        $date = now()->format('Ymd');
        $lastCommission = static::whereDate('created_at', now())->latest()->first();
        $sequence = $lastCommission ? (int)substr($lastCommission->commission_code, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the sales representative for this commission.
     */
    public function salesRepresentative(): BelongsTo
    {
        return $this->belongsTo(SalesRepresentative::class);
    }

    /**
     * Get the order for this commission.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the invoice for this commission.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the customer for this commission.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who approved this commission.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the branch that owns the commission.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the commission.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get commission type color.
     */
    public function getCommissionTypeColorAttribute(): string
    {
        return match($this->commission_type) {
            'sales' => '#28a745',
            'collection' => '#17a2b8',
            'target_bonus' => '#ffc107',
            'special_bonus' => '#6f42c1',
            default => '#6c757d'
        };
    }

    /**
     * Get payment status color.
     */
    public function getPaymentStatusColorAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => '#ffc107',
            'approved' => '#17a2b8',
            'paid' => '#28a745',
            'cancelled' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Get commission type text in Arabic.
     */
    public function getCommissionTypeTextAttribute(): string
    {
        return match($this->commission_type) {
            'sales' => 'عمولة مبيعات',
            'collection' => 'عمولة تحصيل',
            'target_bonus' => 'مكافأة هدف',
            'special_bonus' => 'مكافأة خاصة',
            default => 'غير محدد'
        };
    }

    /**
     * Get payment status text in Arabic.
     */
    public function getPaymentStatusTextAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'في الانتظار',
            'approved' => 'معتمدة',
            'paid' => 'مدفوعة',
            'cancelled' => 'ملغية',
            default => 'غير محدد'
        };
    }

    /**
     * Calculate commission based on representative's settings.
     */
    public static function calculateCommission(SalesRepresentative $representative, float $baseAmount, string $type = 'sales'): array
    {
        $commissionRate = $representative->commission_rate;
        $commissionAmount = 0;
        $calculationDetails = [];

        switch ($representative->commission_type) {
            case 'percentage':
                $commissionAmount = ($baseAmount * $commissionRate) / 100;
                $calculationDetails = [
                    'method' => 'percentage',
                    'rate' => $commissionRate,
                    'base_amount' => $baseAmount,
                ];
                break;

            case 'fixed':
                $commissionAmount = $commissionRate;
                $calculationDetails = [
                    'method' => 'fixed',
                    'amount' => $commissionRate,
                ];
                break;

            case 'tiered':
                if ($representative->commission_tiers) {
                    foreach ($representative->commission_tiers as $tier) {
                        if ($baseAmount >= $tier['min_amount'] && ($tier['max_amount'] === null || $baseAmount <= $tier['max_amount'])) {
                            $commissionAmount = ($baseAmount * $tier['rate']) / 100;
                            $calculationDetails = [
                                'method' => 'tiered',
                                'tier' => $tier,
                                'base_amount' => $baseAmount,
                            ];
                            break;
                        }
                    }
                }
                break;
        }

        return [
            'commission_rate' => $commissionRate,
            'commission_amount' => $commissionAmount,
            'calculation_details' => $calculationDetails,
        ];
    }

    /**
     * Approve the commission.
     */
    public function approve(User $user): void
    {
        $this->update([
            'payment_status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
    }

    /**
     * Mark as paid.
     */
    public function markAsPaid(string $paymentReference): void
    {
        $this->update([
            'payment_status' => 'paid',
            'payment_date' => now(),
            'payment_reference' => $paymentReference,
        ]);
    }

    /**
     * Cancel the commission.
     */
    public function cancel(string $reason): void
    {
        $this->update([
            'payment_status' => 'cancelled',
            'commission_notes' => $this->commission_notes . "\n\nسبب الإلغاء: " . $reason,
        ]);
    }

    /**
     * Scope a query to only include pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope a query to only include approved commissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('payment_status', 'approved');
    }

    /**
     * Scope a query to only include paid commissions.
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Scope a query to filter by commission type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('commission_type', $type);
    }

    /**
     * Scope a query to filter by sales representative.
     */
    public function scopeByRepresentative($query, $representativeId)
    {
        return $query->where('sales_representative_id', $representativeId);
    }

    /**
     * Scope a query to filter by period.
     */
    public function scopeByPeriod($query, $year, $month = null)
    {
        $query->where('period_year', $year);
        
        if ($month) {
            $query->where('period_month', $month);
        }
        
        return $query;
    }

    /**
     * Scope a query to order by transaction date.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('transaction_date', 'desc');
    }
}
