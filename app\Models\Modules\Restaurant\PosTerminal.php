<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class PosTerminal extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'terminal_id',
        'location',
        'type',
        'is_active',
        'settings',
        'receipt_printer_ip',
        'receipt_printer_port',
        'default_table_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
        'receipt_printer_port' => 'integer',
    ];

    /**
     * Get the branch that owns the terminal.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the terminal.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the default table for the terminal.
     */
    public function defaultTable(): BelongsTo
    {
        return $this->belongsTo(RestaurantTable::class, 'default_table_id');
    }

    /**
     * Test receipt printer connection.
     */
    public function testReceiptPrinter(): bool
    {
        try {
            if ($this->receipt_printer_ip) {
                $socket = @fsockopen($this->receipt_printer_ip, $this->receipt_printer_port, $errno, $errstr, 5);
                if ($socket) {
                    fclose($socket);
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Print receipt.
     */
    public function printReceipt($receiptData): bool
    {
        try {
            // Implementation for printing receipt
            // This would format the receipt data and send to printer
            return true;
        } catch (\Exception $e) {
            \Log::error('Receipt printer error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Scope a query to only include active terminals.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get terminal status color.
     */
    public function getStatusColorAttribute(): string
    {
        return $this->is_active ? '#28a745' : '#dc3545';
    }
}
