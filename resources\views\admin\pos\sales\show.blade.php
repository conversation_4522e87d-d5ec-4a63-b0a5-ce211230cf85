@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Sale Details</h1>

    {{-- Assuming $sale variable is passed to this view --}}
    {{-- @if(!$sale)
        <div class="alert alert-danger">Sale not found.</div>
        <a href="{{ route("admin.pos.sales.index") }}" class="btn btn-primary">Back to List</a>
    @else --}}
        <div class="row mb-4">
            <div class="col-md-6">
                <h4>Sale Information</h4>
                <table class="table table-sm table-bordered">
                    <tbody>
                        <tr><th>ID</th><td>{{-- $sale->id --}}1</td></tr>
                        <tr><th>Invoice Number</th><td>{{-- $sale->invoice_number --}}INV-2023-001</td></tr>
                        <tr><th>Customer</th><td>{{-- $sale->customer->name ?? "Walk-in Customer" --}}Walk-in Customer</td></tr>
                        <tr><th>Sale Date</th><td>{{-- $sale->sale_date->format("Y-m-d H:i:s") --}}2023-05-13 10:15:00</td></tr>
                        <tr><th>Status</th><td>{{-- ucfirst($sale->status) --}}Completed</td></tr>
                        <tr><th>POS Device</th><td>{{-- $sale->pos_session->device->name ?? "N/A" --}}Main Counter POS</td></tr>
                        <tr><th>Cashier</th><td>{{-- $sale->pos_session->user->name ?? "N/A" --}}Cashier 1</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="col-md-6">
                <h4>Financial Summary</h4>
                <table class="table table-sm table-bordered">
                    <tbody>
                        <tr><th>Subtotal</th><td>{{-- number_format($sale->sub_total_amount, 2) --}}131.09</td></tr>
                        <tr><th>Discount</th><td>{{-- number_format($sale->discount_amount, 2) --}}0.00</td></tr>
                        <tr><th>Taxable Amount</th><td>{{-- number_format($sale->sub_total_amount - $sale->discount_amount, 2) --}}131.09</td></tr>
                        <tr><th>Tax (VAT 15%)</th><td>{{-- number_format($sale->tax_amount, 2) --}}19.66</td></tr>
                        <tr><th>Total Amount</th><td><strong>{{-- number_format($sale->total_amount, 2) --}}150.75</strong></td></tr>
                        <tr><th>Amount Paid</th><td>{{-- number_format($sale->amount_paid, 2) --}}150.75</td></tr>
                        <tr><th>Amount Due</th><td><strong>{{-- number_format($sale->total_amount - $sale->amount_paid, 2) --}}0.00</strong></td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <h4>Sale Items</h4>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Product/Service</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Subtotal</th>
                </tr>
            </thead>
            <tbody>
                {{-- @forelse($sale->items as $item) --}}
                {{-- Replace with actual data --}}
                <tr>
                    <td>1</td>
                    <td>Product A</td>
                    <td>2</td>
                    <td>10.00</td>
                    <td>20.00</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Service B</td>
                    <td>1</td>
                    <td>25.50</td>
                    <td>25.50</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Product C</td>
                    <td>3</td>
                    <td>28.53</td>
                    <td>85.59</td>
                </tr>
                {{-- @empty
                <tr>
                    <td colspan="5" class="text-center">No items found for this sale.</td>
                </tr>
                @endforelse --}}
            </tbody>
        </table>

        <h4>Payments</h4>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Payment Method</th>
                    <th>Amount</th>
                    <th>Payment Date</th>
                    <th>Reference</th>
                </tr>
            </thead>
            <tbody>
                {{-- @forelse($sale->payments as $payment) --}}
                {{-- Replace with actual data --}}
                <tr>
                    <td>1</td>
                    <td>Cash</td>
                    <td>100.00</td>
                    <td>2023-05-13 10:15:30</td>
                    <td>N/A</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Card (Visa **** 1234)</td>
                    <td>50.75</td>
                    <td>2023-05-13 10:15:45</td>
                    <td>TXN_56789</td>
                </tr>
                {{-- @empty
                <tr>
                    <td colspan="5" class="text-center">No payments recorded for this sale.</td>
                </tr>
                @endforelse --}}
            </tbody>
        </table>
        {{-- @if($sale->status !== "returned" && $sale->status !== "voided") --}}
        {{-- <a href="{{ route("admin.pos.payments.create", ["sale_id" => $sale->id]) }}" class="btn btn-success mb-3">Add Payment</a> --}}
        {{-- @endif --}}

        <div class="mt-4">
            <a href="{{-- route("admin.pos.sales.edit", $sale->id) --}}" class="btn btn-warning">Adjust/Return Sale</a>
            <a href="{{-- route("admin.pos.sales.printReceipt", $sale->id) --}}" class="btn btn-secondary">Print Receipt</a>
            <a href="{{ route("admin.pos.sales.index") }}" class="btn btn-primary">Back to Sales List</a>
        </div>

        @if(!empty($sale->notes))
        <div class="mt-4">
            <h4>Notes:</h4>
            <p>{{-- nl2br(e($sale->notes)) --}}Customer requested extra napkins.</p>
        </div>
        @endif
    {{-- @endif --}}
</div>
@endsection

