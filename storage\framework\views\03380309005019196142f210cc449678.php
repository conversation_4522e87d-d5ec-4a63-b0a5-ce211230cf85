<?php $__env->startSection('title', 'باقات الاشتراك'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">اختر الباقة المناسبة لك</h1>
        <p class="lead text-muted">باقات مرنة تناسب جميع أحجام الأعمال</p>
        
        <?php if($currentSubscription): ?>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                لديك اشتراك حالي في <strong><?php echo e($currentSubscription->subscriptionPlan->name); ?></strong>
                (<?php echo e($currentSubscription->status_text); ?>)
                <a href="<?php echo e(route('subscription.current')); ?>" class="alert-link">عرض التفاصيل</a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Billing Cycle Selector -->
    <div class="text-center mb-4">
        <div class="btn-group" role="group" aria-label="Billing Cycle">
            <input type="radio" class="btn-check" name="billing_cycle" id="monthly" value="monthly" checked>
            <label class="btn btn-outline-primary" for="monthly">شهري</label>

            <input type="radio" class="btn-check" name="billing_cycle" id="quarterly" value="quarterly">
            <label class="btn btn-outline-primary" for="quarterly">
                ربع سنوي 
                <span class="badge bg-success ms-1">وفر 10%</span>
            </label>

            <input type="radio" class="btn-check" name="billing_cycle" id="yearly" value="yearly">
            <label class="btn btn-outline-primary" for="yearly">
                سنوي 
                <span class="badge bg-success ms-1">وفر 20%</span>
            </label>
        </div>
    </div>

    <!-- Plans Grid -->
    <?php $__currentLoopData = ['monthly', 'quarterly', 'yearly']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cycle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="plans-container" id="<?php echo e($cycle); ?>-plans" style="<?php echo e($cycle !== 'monthly' ? 'display: none;' : ''); ?>">
            <?php if(isset($plans[$cycle]) && $plans[$cycle]->count() > 0): ?>
                <div class="row g-4 justify-content-center">
                    <?php $__currentLoopData = $plans[$cycle]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-3 col-md-6">
                            <div class="card h-100 <?php echo e($plan->is_popular ? 'border-primary shadow-lg' : 'border-light'); ?> position-relative">
                                <?php if($plan->is_popular): ?>
                                    <div class="position-absolute top-0 start-50 translate-middle">
                                        <span class="badge bg-primary px-3 py-2">الأكثر شعبية</span>
                                    </div>
                                <?php endif; ?>

                                <div class="card-header text-center bg-transparent border-0 pt-4">
                                    <h3 class="card-title h4 mb-3"><?php echo e($plan->name); ?></h3>
                                    <div class="mb-3">
                                        <span class="display-4 fw-bold text-primary"><?php echo e(number_format($plan->price, 0)); ?></span>
                                        <span class="text-muted">ريال</span>
                                    </div>
                                    <p class="text-muted"><?php echo e($plan->billing_cycle_text); ?></p>
                                    
                                    <?php if($plan->billing_cycle !== 'monthly'): ?>
                                        <div class="text-success small">
                                            <i class="bi bi-check-circle me-1"></i>
                                            وفر <?php echo e(number_format($plan->getSavingsAmount(), 0)); ?> ريال
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-body">
                                    <p class="text-muted mb-4"><?php echo e($plan->description); ?></p>

                                    <!-- Plan Limits -->
                                    <ul class="list-unstyled mb-4">
                                        <li class="mb-2">
                                            <i class="bi bi-building text-primary me-2"></i>
                                            <?php echo e($plan->max_branches == -1 ? 'فروع غير محدودة' : $plan->max_branches . ' فرع'); ?>

                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-people text-primary me-2"></i>
                                            <?php echo e($plan->max_users == -1 ? 'مستخدمين غير محدودين' : $plan->max_users . ' مستخدم'); ?>

                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-person-badge text-primary me-2"></i>
                                            <?php echo e($plan->max_sales_representatives == -1 ? 'مناديب غير محدودين' : $plan->max_sales_representatives . ' مندوب'); ?>

                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-receipt text-primary me-2"></i>
                                            <?php echo e($plan->max_invoices_per_month == -1 ? 'فواتير غير محدودة' : $plan->max_invoices_per_month . ' فاتورة شهرياً'); ?>

                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-hdd text-primary me-2"></i>
                                            <?php echo e($plan->storage_limit_gb == -1 ? 'تخزين غير محدود' : $plan->storage_limit_gb . ' جيجابايت تخزين'); ?>

                                        </li>
                                    </ul>

                                    <!-- Features -->
                                    <h6 class="fw-bold mb-3">المميزات المتاحة:</h6>
                                    <ul class="list-unstyled mb-4">
                                        <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="mb-2">
                                                <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                <?php echo e($feature); ?>

                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>

                                    <!-- Modules -->
                                    <h6 class="fw-bold mb-3">الوحدات المتاحة:</h6>
                                    <div class="row g-2 mb-4">
                                        <?php if($plan->has_sales_reps_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">المناديب</span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($plan->has_restaurant_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">المطاعم</span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($plan->has_inventory_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">المخزون</span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($plan->has_hr_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">الموارد البشرية</span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($plan->has_crm_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">إدارة العملاء</span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($plan->has_reports_module): ?>
                                            <div class="col-6">
                                                <span class="badge bg-success w-100">التقارير</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent border-0 text-center pb-4">
                                    <?php if($currentSubscription && $currentSubscription->subscriptionPlan->id === $plan->id): ?>
                                        <button class="btn btn-outline-secondary w-100" disabled>
                                            <i class="bi bi-check-circle me-2"></i>
                                            الباقة الحالية
                                        </button>
                                    <?php elseif($currentSubscription): ?>
                                        <?php if($plan->price > $currentSubscription->subscriptionPlan->price): ?>
                                            <a href="<?php echo e(route('subscription.upgrade')); ?>" class="btn btn-primary w-100">
                                                <i class="bi bi-arrow-up-circle me-2"></i>
                                                ترقية الباقة
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-outline-secondary w-100" disabled>
                                                ترقية غير متاحة
                                            </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <form method="POST" action="<?php echo e(route('subscription.subscribe', $plan)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn <?php echo e($plan->is_popular ? 'btn-primary' : 'btn-outline-primary'); ?> w-100">
                                                <i class="bi bi-rocket-takeoff me-2"></i>
                                                ابدأ الفترة التجريبية
                                            </button>
                                        </form>
                                        <small class="text-muted d-block mt-2">14 يوم مجاناً</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-triangle display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">لا توجد باقات متاحة لهذه الفترة</h5>
                </div>
            <?php endif; ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- Features Comparison -->
    <div class="mt-5">
        <h2 class="text-center mb-4">مقارنة المميزات</h2>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>المميزة</th>
                        <th class="text-center">الأساسية</th>
                        <th class="text-center">المتقدمة</th>
                        <th class="text-center">الاحترافية</th>
                        <th class="text-center">المؤسسية</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>عدد الفروع</td>
                        <td class="text-center">1</td>
                        <td class="text-center">3</td>
                        <td class="text-center">10</td>
                        <td class="text-center">غير محدود</td>
                    </tr>
                    <tr>
                        <td>عدد المستخدمين</td>
                        <td class="text-center">3</td>
                        <td class="text-center">10</td>
                        <td class="text-center">50</td>
                        <td class="text-center">غير محدود</td>
                    </tr>
                    <tr>
                        <td>وحدة المطاعم</td>
                        <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                    </tr>
                    <tr>
                        <td>وحدة المناديب</td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                    </tr>
                    <tr>
                        <td>وحدة المخزون</td>
                        <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                    </tr>
                    <tr>
                        <td>الدعم الفني المتقدم</td>
                        <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                    </tr>
                    <tr>
                        <td>API Access</td>
                        <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                        <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-5">
        <h2 class="text-center mb-4">الأسئلة الشائعة</h2>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                هل يمكنني تغيير الباقة لاحقاً؟
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، يمكنك ترقية أو تغيير باقتك في أي وقت. سيتم احتساب الفرق في السعر بشكل تناسبي.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                ما هي مدة الفترة التجريبية؟
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نوفر فترة تجريبية مجانية لمدة 14 يوم لجميع الباقات بدون الحاجة لبطاقة ائتمان.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                هل البيانات آمنة؟
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، نستخدم أحدث تقنيات الأمان والتشفير لحماية بياناتك. كما نقوم بعمل نسخ احتياطية يومية.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle billing cycle change
    const billingCycleInputs = document.querySelectorAll('input[name="billing_cycle"]');
    const plansContainers = document.querySelectorAll('.plans-container');

    billingCycleInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Hide all plan containers
            plansContainers.forEach(container => {
                container.style.display = 'none';
            });

            // Show selected plan container
            const selectedContainer = document.getElementById(this.value + '-plans');
            if (selectedContainer) {
                selectedContainer.style.display = 'block';
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/subscription/plans.blade.php ENDPATH**/ ?>