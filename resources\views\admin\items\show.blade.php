@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("admin_views.view_item") }}: {{ $item->name_ar }} ({{ $item->name_en }})</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ __("admin_views.item_details") }}</h4>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.id") }}:</strong></div>
                <div class="col-md-9">{{ $item->id }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.name_ar") }}:</strong></div>
                <div class="col-md-9">{{ $item->name_ar }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.name_en") }}:</strong></div>
                <div class="col-md-9">{{ $item->name_en }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.code") }}:</strong></div>
                <div class="col-md-9">{{ $item->code ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.type") }}:</strong></div>
                <div class="col-md-9">{{ __("admin_views." . $item->type) }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.unit_of_measure_ar") }}:</strong></div>
                <div class="col-md-9">{{ $item->unit_of_measure_ar ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.unit_of_measure_en") }}:</strong></div>
                <div class="col-md-9">{{ $item->unit_of_measure_en ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.description_ar") }}:</strong></div>
                <div class="col-md-9">{{ $item->description_ar ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.description_en") }}:</strong></div>
                <div class="col-md-9">{{ $item->description_en ?? "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.standard_cost") }}:</strong></div>
                <div class="col-md-9">{{ number_format($item->standard_cost, 4) }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.last_purchase_price") }}:</strong></div>
                <div class="col-md-9">{{ $item->last_purchase_price ? number_format($item->last_purchase_price, 4) : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.selling_price") }}:</strong></div>
                <div class="col-md-9">{{ $item->selling_price ? number_format($item->selling_price, 4) : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.branch") }}:</strong></div>
                <div class="col-md-9">{{ $item->branch ? $item->branch->name_ar . " (" . $item->branch->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.inventory_account") }}:</strong></div>
                <div class="col-md-9">{{ $item->inventoryAccount ? $item->inventoryAccount->name_ar . " (" . $item->inventoryAccount->account_number . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.cogs_account") }}:</strong></div>
                <div class="col-md-9">{{ $item->cogsAccount ? $item->cogsAccount->name_ar . " (" . $item->cogsAccount->account_number . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.sales_revenue_account") }}:</strong></div>
                <div class="col-md-9">{{ $item->salesRevenueAccount ? $item->salesRevenueAccount->name_ar . " (" . $item->salesRevenueAccount->account_number . ")" : "-" }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.status") }}:</strong></div>
                <div class="col-md-9">{{ $item->is_active ? __("admin_views.active") : __("admin_views.inactive") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.is_manufactured") }}:</strong></div>
                <div class="col-md-9">{{ $item->is_manufactured ? __("admin_views.yes") : __("admin_views.no") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.is_purchased") }}:</strong></div>
                <div class="col-md-9">{{ $item->is_purchased ? __("admin_views.yes") : __("admin_views.no") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.is_sold") }}:</strong></div>
                <div class="col-md-9">{{ $item->is_sold ? __("admin_views.yes") : __("admin_views.no") }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.created_at") }}:</strong></div>
                <div class="col-md-9">{{ $item->created_at }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3"><strong>{{ __("admin_views.updated_at") }}:</strong></div>
                <div class="col-md-9">{{ $item->updated_at }}</div>
            </div>
        </div>
        <div class="card-footer">
            <a href="{{ route("admin.items.edit", $item->id) }}" class="btn btn-warning">{{ __("admin_views.edit") }}</a>
            <a href="{{ route("admin.items.index") }}" class="btn btn-secondary">{{ __("admin_views.back_to_list") }}</a>
        </div>
    </div>
</div>
@endsection

