<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_bom_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bom_id')->constrained('manufacturing_boms')->onDelete('cascade');
            $table->foreignId('item_id')->constrained('manufacturing_items')->onDelete('cascade')->comment('Component item (raw material or semi-finished good)');
            $table->decimal('quantity', 15, 4)->comment('Quantity of the component item required');
            $table->string('unit_of_measure')->comment('Unit of measure for the component item quantity');
            $table->text('notes')->nullable();
            $table->integer('operation_sequence')->nullable()->comment('Sequence number for the operation where this item is consumed');
            // Add other fields like scrap percentage, etc. if needed
            $table->timestamps();

            $table->unique(['bom_id', 'item_id'], 'bom_item_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_bom_items');
    }
};
