<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_attachments', function (Blueprint $table) {
            $table->id();
            $table->morphs('attachable'); // For ticket_id or ticket_reply_id
            $table->foreignId('user_id')->constrained('users')->comment('User who uploaded the attachment');
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_mime_type')->nullable();
            $table->unsignedInteger('file_size')->nullable()->comment('File size in bytes');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_attachments');
    }
};
