<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_areas', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المنطقة
            $table->text('description')->nullable(); // وصف المنطقة
            $table->string('color', 7)->default('#007bff'); // لون المنطقة
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->boolean('is_active')->default(true);
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_areas');
    }
};
