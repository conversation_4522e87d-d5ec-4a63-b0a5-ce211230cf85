<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display the system settings page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // استرجاع جميع الإعدادات وتنظيمها في مصفوفة
        $settingsCollection = Setting::all();
        $settings = [];

        foreach ($settingsCollection as $setting) {
            $settings[$setting->key] = $setting->typed_value;
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the system settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        // التحقق من صحة البيانات
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|max:255',
            'company_phone' => 'required|string|max:20',
            'company_address' => 'required|string|max:500',
            'tax_number' => 'nullable|string|max:50',
            'currency' => 'required|string|max:10',
            'language' => 'required|string|max:10',
            'timezone' => 'required|string|max:50',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            // إضافة المزيد من الحقول حسب الحاجة
        ]);

        // حفظ الإعدادات في قاعدة البيانات
        foreach ($validated as $key => $value) {
            // تخطي حقل الشعار لأنه سيتم معالجته بشكل منفصل
            if ($key === 'logo') {
                continue;
            }

            // تحديد نوع البيانات المناسب
            $type = 'string';
            if (is_numeric($value) && strpos($value, '.') !== false) {
                $type = 'float';
            } elseif (is_numeric($value)) {
                $type = 'integer';
            }

            // تحديد المجموعة بناءً على اسم الحقل
            $group = 'general';
            if (strpos($key, 'company_') === 0) {
                $group = 'company';
            } elseif (in_array($key, ['currency', 'language', 'timezone'])) {
                $group = 'system';
            }

            // حفظ الإعداد
            Setting::setByKey($key, $value, $type, $group);
        }

        // معالجة ملف الشعار إذا تم تحميله
        if ($request->hasFile('logo')) {
            // حذف الشعار القديم إذا وجد
            $oldLogo = Setting::getByKey('logo');
            if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                Storage::disk('public')->delete($oldLogo);
            }

            // تخزين الشعار الجديد
            $logoPath = $request->file('logo')->store('logos', 'public');
            Setting::setByKey('logo', $logoPath, 'string', 'company', 'شعار الشركة');
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'تم تحديث إعدادات النظام بنجاح');
    }
}
