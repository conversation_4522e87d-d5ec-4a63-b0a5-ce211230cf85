@extends('layouts.admin')

@section('title', isset($subscription) ? 'تعديل اشتراك' : 'إضافة اشتراك جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($subscription) ? 'تعديل اشتراك' : 'إضافة اشتراك جديد' }}</h3>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ isset($subscription) ? route('admin.subscriptions.subscriptions.update', $subscription) : route('admin.subscriptions.subscriptions.store') }}" method="POST">
                        @csrf
                        @if(isset($subscription))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tenant_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                    <select class="form-select @error('tenant_id') is-invalid @enderror" id="tenant_id" name="tenant_id" required>
                                        <option value="">اختر العميل</option>
                                        @foreach($tenants as $tenant)
                                            <option value="{{ $tenant->id }}" {{ old('tenant_id', $subscription->tenant_id ?? '') == $tenant->id ? 'selected' : '' }}>
                                                {{ $tenant->name }} ({{ $tenant->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('tenant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subscription_plan_id" class="form-label">خطة الاشتراك <span class="text-danger">*</span></label>
                                    <select class="form-select @error('subscription_plan_id') is-invalid @enderror" id="subscription_plan_id" name="subscription_plan_id" required>
                                        <option value="">اختر الخطة</option>
                                        @foreach($plans as $plan)
                                            <option value="{{ $plan->id }}" {{ old('subscription_plan_id', $subscription->subscription_plan_id ?? '') == $plan->id ? 'selected' : '' }}>
                                                {{ $plan->name }} ({{ $plan->formatted_price }} - {{ $plan->billing_cycle_arabic }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('subscription_plan_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" id="start_date" name="start_date" value="{{ old('start_date', isset($subscription) ? $subscription->start_date->format('Y-m-d') : date('Y-m-d')) }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">تاريخ الانتهاء <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" id="end_date" name="end_date" value="{{ old('end_date', isset($subscription) ? $subscription->end_date->format('Y-m-d') : date('Y-m-d', strtotime('+1 month'))) }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="active" {{ old('status', $subscription->status ?? '') == 'active' ? 'selected' : '' }}>نشط</option>
                                        <option value="pending" {{ old('status', $subscription->status ?? '') == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                                        <option value="expired" {{ old('status', $subscription->status ?? '') == 'expired' ? 'selected' : '' }}>منتهي</option>
                                        <option value="cancelled" {{ old('status', $subscription->status ?? '') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price_paid" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" class="form-control @error('price_paid') is-invalid @enderror" id="price_paid" name="price_paid" value="{{ old('price_paid', $subscription->price_paid ?? '') }}" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    @error('price_paid')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                                    <select class="form-select @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method">
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash" {{ old('payment_method', $subscription->payment_method ?? '') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                        <option value="bank_transfer" {{ old('payment_method', $subscription->payment_method ?? '') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                                        <option value="credit_card" {{ old('payment_method', $subscription->payment_method ?? '') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                        <option value="other" {{ old('payment_method', $subscription->payment_method ?? '') == 'other' ? 'selected' : '' }}>أخرى</option>
                                    </select>
                                    @error('payment_method')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_reference" class="form-label">مرجع الدفع</label>
                                    <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" id="payment_reference" name="payment_reference" value="{{ old('payment_reference', $subscription->payment_reference ?? '') }}">
                                    @error('payment_reference')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes', $subscription->notes ?? '') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="auto_renew" name="auto_renew" {{ old('auto_renew', $subscription->auto_renew ?? false) ? 'checked' : '' }}>
                            <label class="form-check-label" for="auto_renew">تجديد تلقائي</label>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.subscriptions.subscriptions.index') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">{{ isset($subscription) ? 'تحديث' : 'حفظ' }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عند تغيير خطة الاشتراك، قم بتحديث تاريخ الانتهاء تلقائيًا
        const planSelect = document.getElementById('subscription_plan_id');
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        function updateEndDate() {
            const planId = planSelect.value;
            if (!planId) return;
            
            const selectedOption = planSelect.options[planSelect.selectedIndex];
            const billingCycle = selectedOption.textContent.includes('شهري') ? 'monthly' :
                                selectedOption.textContent.includes('ربع سنوي') ? 'quarterly' :
                                selectedOption.textContent.includes('نصف سنوي') ? 'semi_annually' :
                                selectedOption.textContent.includes('سنوي') ? 'annually' : 'monthly';
            
            const startDate = new Date(startDateInput.value);
            let endDate = new Date(startDate);
            
            switch(billingCycle) {
                case 'monthly':
                    endDate.setMonth(endDate.getMonth() + 1);
                    break;
                case 'quarterly':
                    endDate.setMonth(endDate.getMonth() + 3);
                    break;
                case 'semi_annually':
                    endDate.setMonth(endDate.getMonth() + 6);
                    break;
                case 'annually':
                    endDate.setFullYear(endDate.getFullYear() + 1);
                    break;
            }
            
            // تنسيق التاريخ بصيغة YYYY-MM-DD
            const formattedDate = endDate.toISOString().split('T')[0];
            endDateInput.value = formattedDate;
        }
        
        planSelect.addEventListener('change', updateEndDate);
        startDateInput.addEventListener('change', updateEndDate);
    });
</script>
@endpush
