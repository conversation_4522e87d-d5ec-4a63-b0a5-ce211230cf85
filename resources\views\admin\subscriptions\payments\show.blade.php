@extends('layouts.admin')

@section('title', 'تفاصيل المدفوعة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل المدفوعة: {{ $payment->receipt_number }}</h3>
                    <div>
                        <a href="{{ route('admin.subscriptions.payments.edit', $payment) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.subscriptions.payments.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات المدفوعة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">رقم الإيصال</th>
                                            <td>{{ $payment->receipt_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>المبلغ</th>
                                            <td>{{ $payment->formatted_amount }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الدفع</th>
                                            <td>{{ $payment->payment_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>طريقة الدفع</th>
                                            <td>{{ $payment->payment_method }}</td>
                                        </tr>
                                        <tr>
                                            <th>رقم العملية</th>
                                            <td>{{ $payment->transaction_id ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if ($payment->status == 'paid')
                                                    <span class="badge bg-success">{{ $payment->status_arabic }}</span>
                                                @elseif ($payment->status == 'pending')
                                                    <span class="badge bg-warning">{{ $payment->status_arabic }}</span>
                                                @elseif ($payment->status == 'failed')
                                                    <span class="badge bg-danger">{{ $payment->status_arabic }}</span>
                                                @elseif ($payment->status == 'refunded')
                                                    <span class="badge bg-info">{{ $payment->status_arabic }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $payment->status }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $payment->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $payment->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات الاشتراك</h5>
                                </div>
                                <div class="card-body">
                                    @if($payment->subscription)
                                        <table class="table table-bordered">
                                            <tr>
                                                <th style="width: 30%">رقم الاشتراك</th>
                                                <td>
                                                    <a href="{{ route('admin.subscriptions.subscriptions.show', $payment->subscription) }}">
                                                        {{ $payment->subscription->subscription_number }}
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>العميل</th>
                                                <td>{{ $payment->subscription->tenant->name ?? 'غير محدد' }}</td>
                                            </tr>
                                            <tr>
                                                <th>خطة الاشتراك</th>
                                                <td>{{ $payment->subscription->plan->name ?? 'غير محدد' }}</td>
                                            </tr>
                                            <tr>
                                                <th>تاريخ البدء</th>
                                                <td>{{ $payment->subscription->start_date->format('Y-m-d') }}</td>
                                            </tr>
                                            <tr>
                                                <th>تاريخ الانتهاء</th>
                                                <td>{{ $payment->subscription->end_date->format('Y-m-d') }}</td>
                                            </tr>
                                            <tr>
                                                <th>الحالة</th>
                                                <td>
                                                    @if ($payment->subscription->status == 'active')
                                                        <span class="badge bg-success">{{ $payment->subscription->status_arabic }}</span>
                                                    @elseif ($payment->subscription->status == 'pending')
                                                        <span class="badge bg-warning">{{ $payment->subscription->status_arabic }}</span>
                                                    @elseif ($payment->subscription->status == 'expired')
                                                        <span class="badge bg-danger">{{ $payment->subscription->status_arabic }}</span>
                                                    @else
                                                        <span class="badge bg-secondary">{{ $payment->subscription->status_arabic }}</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                        
                                        <a href="{{ route('admin.subscriptions.subscriptions.show', $payment->subscription) }}" class="btn btn-sm btn-primary mt-3">
                                            عرض تفاصيل الاشتراك
                                        </a>
                                    @else
                                        <p class="text-center">لا توجد معلومات عن الاشتراك</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($payment->notes)
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>ملاحظات</h5>
                                    </div>
                                    <div class="card-body">
                                        {{ $payment->notes }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>إجراءات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <a href="{{ route('admin.subscriptions.payments.edit', $payment) }}" class="btn btn-warning w-100 mb-2">
                                                <i class="bi bi-pencil"></i> تعديل المدفوعة
                                            </a>
                                        </div>
                                        <div class="col-md-6">
                                            <form action="{{ route('admin.subscriptions.payments.destroy', $payment) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذه المدفوعة؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger w-100">
                                                    <i class="bi bi-trash"></i> حذف المدفوعة
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
