<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class ManufacturingWorkOrderConsumedItem extends Model
{
    use HasFactory;

    protected $table = 'manufacturing_work_order_consumed_items';

    protected $fillable = [
        'work_order_id',
        'item_id',
        'quantity_consumed',
        'unit_of_measure',
        'consumption_date',
        'issued_by_id',
        'notes',
        // 'work_order_item_id', // If linking to a specific input line from work_order_items
    ];

    protected $casts = [
        'quantity_consumed' => 'decimal:4',
        'consumption_date' => 'datetime',
    ];

    public function workOrder()
    {
        return $this->belongsTo(ManufacturingWorkOrder::class, 'work_order_id');
    }

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }

    public function issuedBy()
    {
        return $this->belongsTo(User::class, 'issued_by_id');
    }

    // public function workOrderItem()
    // {
    //     return $this->belongsTo(ManufacturingWorkOrderItem::class, 'work_order_item_id');
    // }
}

