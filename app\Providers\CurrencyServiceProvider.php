<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\CurrencyHelper;

class CurrencyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل CurrencyHelper كـ singleton
        $this->app->singleton('currency', function ($app) {
            return new CurrencyHelper();
        });

        // تسجيل الـ Helper في الـ container
        $this->app->bind(CurrencyHelper::class, function ($app) {
            return new CurrencyHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تسجيل Blade directives للعملة
        $this->registerBladeDirectives();

        // تسجيل view composers
        $this->registerViewComposers();
    }

    /**
     * تسجيل Blade directives
     */
    protected function registerBladeDirectives(): void
    {
        // @currency(1000) - تنسيق المبلغ مع رمز العملة
        Blade::directive('currency', function ($expression) {
            return "<?php echo \\App\\Helpers\\CurrencyHelper::format({$expression}); ?>";
        });

        // @currencysar(1000) - تنسيق المبلغ بالريال السعودي
        Blade::directive('currencysar', function ($expression) {
            return "<?php echo \\App\\Helpers\\CurrencyHelper::formatSAR({$expression}); ?>";
        });

        // @currencysymbol('SAR') - عرض رمز العملة فقط
        Blade::directive('currencysymbol', function ($expression = null) {
            if ($expression) {
                return "<?php echo \\App\\Helpers\\CurrencyHelper::getSymbol({$expression}); ?>";
            } else {
                return "<?php echo \\App\\Helpers\\CurrencyHelper::getSymbol(); ?>";
            }
        });

        // @currencyname('SAR') - عرض اسم العملة
        Blade::directive('currencyname', function ($expression = null) {
            if ($expression) {
                return "<?php echo \\App\\Helpers\\CurrencyHelper::getName({$expression}); ?>";
            } else {
                return "<?php echo \\App\\Helpers\\CurrencyHelper::getName(); ?>";
            }
        });

        // @currencyinput(1000) - تنسيق المبلغ للإدخال (بدون رمز)
        Blade::directive('currencyinput', function ($expression) {
            return "<?php echo \\App\\Helpers\\CurrencyHelper::formatForInput({$expression}); ?>";
        });

        // @currencywords(1000) - تحويل المبلغ إلى كلمات
        Blade::directive('currencywords', function ($expression) {
            return "<?php echo \\App\\Helpers\\CurrencyHelper::toWords({$expression}); ?>";
        });
    }

    /**
     * تسجيل view composers
     */
    protected function registerViewComposers(): void
    {
        // إضافة متغيرات العملة لجميع الـ views
        view()->composer('*', function ($view) {
            $view->with([
                'defaultCurrency' => CurrencyHelper::getDefaultCurrency(),
                'defaultCurrencySymbol' => CurrencyHelper::getSymbol(),
                'supportedCurrencies' => CurrencyHelper::getSupportedCurrencies(),
            ]);
        });
    }
}
