@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>WhatsApp Opt-Ins</h1>
    {{-- No create button for Opt-Ins as they are typically managed via API or user actions --}}
    {{-- <a href="{{ route('admin.whatsapp_integration.opt_ins.create') }}" class="btn btn-primary mb-3">Add New Opt-In</a> --}}

    @if ($message = Session::get('success'))
        <div class="alert alert-success">
            <p>{{ $message }}</p>
        </div>
    @endif

    <table class="table table-bordered">
        <tr>
            <th>No</th>
            <th>User</th>
            <th>Phone Number</th>
            <th>Status</th>
            <th>Opt-In Source</th>
            <th>Opt-In Date</th>
            <th>Opt-Out Date</th>
            <th width="280px">Action</th>
        </tr>
        @php $i = 0; @endphp
        @foreach ($optIns as $optIn)
        <tr>
            <td>{{ ++$i }}</td>
            <td>{{ $optIn->user->name ?? 'N/A' }} (ID: {{ $optIn->user_id }})</td>
            <td>{{ $optIn->phone_number }}</td>
            <td>{{ $optIn->opt_in_status ? 'Opted-In' : 'Opted-Out' }}</td>
            <td>{{ $optIn->opt_in_source ?? 'N/A' }}</td>
            <td>{{ $optIn->opt_in_at }}</td>
            <td>{{ $optIn->opt_out_at ?? 'N/A' }}</td>
            <td>
                <form action="{{ route('admin.whatsapp_integration.opt_ins.destroy',$optIn->id) }}" method="POST">
                    <a class="btn btn-info" href="{{ route('admin.whatsapp_integration.opt_ins.show',$optIn->id) }}">Show</a>
                    <a class="btn btn-primary" href="{{ route('admin.whatsapp_integration.opt_ins.edit',$optIn->id) }}">Edit Status</a>
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </td>
        </tr>
        @endforeach
    </table>
    {{-- {!! $optIns->links() !!} --}}
</div>
@endsection

