<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get account types
        $accountTypes = AccountType::active()
                                  ->orderBy('name_ar')
                                  ->get();

        // Get root accounts (accounts with no parent) grouped by account type
        $rootAccounts = [];

        foreach ($accountTypes as $type) {
            $accounts = Account::with(['children'])
                              ->where('account_type_id', $type->id)
                              ->whereNull('parent_id')
                              ->orderBy('code')
                              ->get();

            if ($accounts->count() > 0) {
                $rootAccounts[$type->id] = [
                    'type' => $type,
                    'accounts' => $accounts
                ];
            }
        }

        // Get all accounts for the table view
        $allAccounts = Account::with(["accountType"])
                            ->orderBy("account_type_id")
                            ->orderBy("code")
                            ->get();

        return view("admin.accounts.new", compact("accountTypes", "rootAccounts", "allAccounts"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accountTypes = AccountType::active()->orderBy("name_ar")->get();
        $parentAccounts = Account::active()->orderBy("name_ar")->get();
        return view("admin.accounts.form", compact("accountTypes", "parentAccounts"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "nullable|string|max:255",
            "code" => ["nullable", "string", "max:255", Rule::unique("accounts", "code")],
            "account_type_id" => "required|exists:account_types,id",
            "parent_id" => "nullable|exists:accounts,id",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "is_control_account" => "boolean",
            "accepts_entries" => "boolean",
            "opening_balance_debit" => "nullable|numeric|min:0",
            "opening_balance_credit" => "nullable|numeric|min:0",
            "opening_balance_date" => "nullable|date",
            "branch_id" => "nullable|exists:branches,id",
            "is_active" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");
        $validatedData["is_control_account"] = $request->has("is_control_account");
        $validatedData["accepts_entries"] = $request->has("accepts_entries");

        // If code is not provided, generate one
        if (empty($validatedData["code"])) {
            $validatedData["code"] = strtoupper(Str::random(3)) . time() % 10000;
        }

        Account::create($validatedData);

        return redirect()->route("admin.accounts.index")->with("success", "تم إنشاء الحساب بنجاح");
    }

    /**
     * Display the specified resource.
     */
    public function show(Account $account)
    {
        $account->load(["accountType", "parent", "children"]);
        return view("admin.accounts.show", compact("account"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Account $account)
    {
        $accountTypes = AccountType::active()->orderBy("name_ar")->get();
        $parentAccounts = Account::active()
                                ->where("id", "!=", $account->id)
                                ->orderBy("name_ar")->get();
        return view("admin.accounts.form", compact("account", "accountTypes", "parentAccounts"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Account $account)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "nullable|string|max:255",
            "code" => ["nullable", "string", "max:255", Rule::unique("accounts", "code")->ignore($account->id)],
            "account_type_id" => "required|exists:account_types,id",
            "parent_id" => "nullable|exists:accounts,id",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "is_control_account" => "boolean",
            "accepts_entries" => "boolean",
            "opening_balance_debit" => "nullable|numeric|min:0",
            "opening_balance_credit" => "nullable|numeric|min:0",
            "opening_balance_date" => "nullable|date",
            "branch_id" => "nullable|exists:branches,id",
            "is_active" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");
        $validatedData["is_control_account"] = $request->has("is_control_account");
        $validatedData["accepts_entries"] = $request->has("accepts_entries");

        // Prevent setting parent_id to itself or a child to prevent infinite loops
        if ($request->parent_id == $account->id) {
            return redirect()->back()->withErrors(["parent_id" => "لا يمكن تعيين الحساب كوالد لنفسه"])->withInput();
        }

        $account->update($validatedData);

        return redirect()->route("admin.accounts.index")->with("success", "تم تحديث الحساب بنجاح");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Account $account)
    {
        // Check if the account has child accounts
        if ($account->children()->count() > 0) {
            return redirect()->route("admin.accounts.index")->with("error", "لا يمكن حذف هذا الحساب لأنه يحتوي على حسابات فرعية");
        }

        try {
            $account->delete();
            return redirect()->route("admin.accounts.index")->with("success", "تم حذف الحساب بنجاح");
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.accounts.index")->with("error", "لا يمكن حذف الحساب. قد يكون مرتبط ببيانات أخرى");
        }
    }
}

