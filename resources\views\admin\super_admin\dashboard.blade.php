@extends('layouts.admin')

@section('title', 'لوحة تحكم السوبر أدمن')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">لوحة تحكم السوبر أدمن</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h1 class="display-4 text-primary">{{ $totalPlans }}</h1>
                                    <h5>إجمالي الباقات</h5>
                                    <p class="text-muted">{{ $activePlans }} باقة نشطة</p>
                                    <a href="{{ route('admin.super_admin.subscription_plans.index') }}" class="btn btn-sm btn-primary">إدارة الباقات</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h1 class="display-4 text-success">{{ $totalSubscriptions }}</h1>
                                    <h5>إجمالي الاشتراكات</h5>
                                    <p class="text-muted">{{ $activeSubscriptions }} اشتراك نشط</p>
                                    <a href="{{ route('admin.subscriptions.subscriptions.index') }}" class="btn btn-sm btn-success">إدارة الاشتراكات</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h1 class="display-4 text-info">{{ $totalTenants }}</h1>
                                    <h5>إجمالي العملاء</h5>
                                    <p class="text-muted">المستأجرين النشطين</p>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-info">إدارة العملاء</a>
                                        <a href="{{ route('admin.super_admin.tenant_switch.index') }}" class="btn btn-sm btn-primary">تبديل الحسابات</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h1 class="display-4 text-warning"><i class="bi bi-gear-fill"></i></h1>
                                    <h5>إعدادات النظام</h5>
                                    <p class="text-muted">حدود الفواتير والإعدادات</p>
                                    <a href="{{ route('admin.super_admin.invoice_limits.index') }}" class="btn btn-sm btn-warning">إدارة الإعدادات</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">الباقات النشطة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الباقة</th>
                                    <th>السعر</th>
                                    <th>عدد المشتركين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($plans as $plan)
                                <tr>
                                    <td>{{ $plan->name }}</td>
                                    <td>{{ $plan->price }} ريال</td>
                                    <td>{{ $plan->subscriptions_count }}</td>
                                    <td>
                                        <a href="{{ route('admin.super_admin.subscription_plans.show', $plan) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد باقات نشطة</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">آخر الاشتراكات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>الباقة</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($latestSubscriptions as $subscription)
                                <tr>
                                    <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                    <td>{{ $subscription->plan->name ?? 'غير محدد' }}</td>
                                    <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                    <td>
                                        <span class="badge {{ $subscription->status == 'active' ? 'bg-success' : 'bg-warning' }}">
                                            {{ $subscription->status_arabic }}
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد اشتراكات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
