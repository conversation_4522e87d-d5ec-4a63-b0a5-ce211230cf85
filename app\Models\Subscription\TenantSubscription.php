<?php

namespace App\Models\Subscription;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;
use Carbon\Carbon;

class TenantSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'subscription_plan_id',
        'subscription_code',
        'status',
        'start_date',
        'end_date',
        'trial_end_date',
        'amount_paid',
        'discount_amount',
        'payment_method',
        'payment_reference',
        'current_usage',
        'custom_limits',
        'auto_renew',
        'next_billing_date',
        'cancellation_reason',
        'cancelled_at',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'trial_end_date' => 'date',
        'amount_paid' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'current_usage' => 'array',
        'custom_limits' => 'array',
        'auto_renew' => 'boolean',
        'next_billing_date' => 'date',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Generate unique subscription code.
     */
    public static function generateSubscriptionCode(): string
    {
        $prefix = 'SUB';
        $date = now()->format('Ymd');
        $lastSubscription = static::whereDate('created_at', now())->latest()->first();
        $sequence = $lastSubscription ? (int)substr($lastSubscription->subscription_code, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the tenant that owns the subscription.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the subscription invoices.
     */
    public function subscriptionInvoices(): HasMany
    {
        return $this->hasMany(SubscriptionInvoice::class);
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => '#28a745',
            'trial' => '#17a2b8',
            'expired' => '#ffc107',
            'cancelled' => '#dc3545',
            'suspended' => '#6c757d',
            default => '#6c757d'
        };
    }

    /**
     * Get status text in Arabic.
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'active' => 'نشط',
            'trial' => 'تجريبي',
            'expired' => 'منتهي',
            'cancelled' => 'ملغي',
            'suspended' => 'معلق',
            default => $this->status
        };
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->end_date->isFuture();
    }

    /**
     * Check if subscription is in trial.
     */
    public function isTrial(): bool
    {
        return $this->status === 'trial' && 
               $this->trial_end_date && 
               $this->trial_end_date->isFuture();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->end_date->isPast() || $this->status === 'expired';
    }

    /**
     * Check if subscription is expiring soon.
     */
    public function isExpiringSoon($days = 7): bool
    {
        return $this->end_date->diffInDays(now()) <= $days;
    }

    /**
     * Get days remaining.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return max(0, $this->end_date->diffInDays(now()));
    }

    /**
     * Get current usage for a resource.
     */
    public function getCurrentUsage(string $resource): int
    {
        return $this->current_usage[$resource] ?? 0;
    }

    /**
     * Get limit for a resource.
     */
    public function getLimit(string $resource): int
    {
        // Check custom limits first
        if ($this->custom_limits && isset($this->custom_limits[$resource])) {
            return $this->custom_limits[$resource];
        }

        // Fall back to plan limits
        $planLimits = $this->subscriptionPlan->getLimits();
        return $planLimits[$resource] ?? 0;
    }

    /**
     * Check if resource limit is exceeded.
     */
    public function isLimitExceeded(string $resource): bool
    {
        $usage = $this->getCurrentUsage($resource);
        $limit = $this->getLimit($resource);
        
        return $limit > 0 && $usage >= $limit;
    }

    /**
     * Get usage percentage for a resource.
     */
    public function getUsagePercentage(string $resource): float
    {
        $usage = $this->getCurrentUsage($resource);
        $limit = $this->getLimit($resource);
        
        if ($limit <= 0) {
            return 0;
        }

        return min(100, ($usage / $limit) * 100);
    }

    /**
     * Update usage for a resource.
     */
    public function updateUsage(string $resource, int $count = 1): void
    {
        $currentUsage = $this->current_usage ?? [];
        $currentUsage[$resource] = ($currentUsage[$resource] ?? 0) + $count;
        
        $this->update(['current_usage' => $currentUsage]);
    }

    /**
     * Reset monthly usage.
     */
    public function resetMonthlyUsage(): void
    {
        $currentUsage = $this->current_usage ?? [];
        
        // Reset monthly counters
        $monthlyResources = ['invoices_per_month'];
        foreach ($monthlyResources as $resource) {
            $currentUsage[$resource] = 0;
        }
        
        $this->update(['current_usage' => $currentUsage]);
    }

    /**
     * Check if tenant has access to module.
     */
    public function hasModuleAccess(string $module): bool
    {
        return $this->subscriptionPlan->hasModule($module);
    }

    /**
     * Check if tenant has access to feature.
     */
    public function hasFeatureAccess(string $feature): bool
    {
        return $this->subscriptionPlan->hasFeature($feature);
    }

    /**
     * Renew subscription.
     */
    public function renew(): void
    {
        $plan = $this->subscriptionPlan;
        
        $newEndDate = match($plan->billing_cycle) {
            'monthly' => $this->end_date->addMonth(),
            'quarterly' => $this->end_date->addMonths(3),
            'yearly' => $this->end_date->addYear(),
            default => $this->end_date->addMonth()
        };

        $this->update([
            'end_date' => $newEndDate,
            'next_billing_date' => $newEndDate,
            'status' => 'active'
        ]);

        // Reset monthly usage if needed
        if ($plan->billing_cycle === 'monthly') {
            $this->resetMonthlyUsage();
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'auto_renew' => false,
            'cancellation_reason' => $reason,
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Suspend subscription.
     */
    public function suspend(): void
    {
        $this->update(['status' => 'suspended']);
    }

    /**
     * Reactivate subscription.
     */
    public function reactivate(): void
    {
        $this->update(['status' => 'active']);
    }

    /**
     * Scope to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to only include trial subscriptions.
     */
    public function scopeTrial($query)
    {
        return $query->where('status', 'trial');
    }

    /**
     * Scope to only include expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere('end_date', '<', now());
    }

    /**
     * Scope to only include expiring soon subscriptions.
     */
    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('end_date', '<=', now()->addDays($days))
                    ->where('end_date', '>', now())
                    ->where('status', 'active');
    }
}
