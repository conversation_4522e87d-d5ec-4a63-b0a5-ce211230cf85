<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User; // Assuming User model exists for created_by/updated_by if needed in future

class MfgBomItem extends Model
{
    use HasFactory;

    protected $table = 'mfg_bom_items';

    protected $fillable = [
        'mfg_bom_id',
        'component_product_id',
        'quantity_per_parent',
        'unit_of_measure',
        'scrap_percentage',
        'notes',
        'sequence',
        'operation_id',
    ];

    protected $casts = [
        'quantity_per_parent' => 'decimal:4',
        'scrap_percentage' => 'decimal:2',
    ];

    /**
     * Get the BOM this item belongs to.
     */
    public function mfgBom()
    {
        return $this->belongsTo(MfgBom::class, 'mfg_bom_id');
    }

    /**
     * Get the component product (an MfgProduct).
     */
    public function componentProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'component_product_id');
    }

    /**
     * Get the operation where this item is consumed (optional).
     */
    public function operation()
    {
        return $this->belongsTo(MfgOperation::class, 'operation_id');
    }
}

