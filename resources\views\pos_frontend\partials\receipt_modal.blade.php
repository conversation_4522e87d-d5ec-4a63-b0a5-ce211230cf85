{{-- Receipt Modal --}}
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiptModalLabel"><i class="bi bi-receipt-cutoff me-2"></i>إيصال الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="receipt-content-area">
                {{-- Receipt content will be injected here by JavaScript --}}
                <p class="text-center">يتم الآن تحميل الإيصال...</p>
            </div>
            <div class="modal-footer justify-content-between">
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendReceiptByEmail()"><i class="bi bi-envelope-fill me-1"></i> إرسال بريد</button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="sendReceiptByWhatsApp()"><i class="bi bi-whatsapp me-1"></i> إرسال واتساب</button>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x-lg me-1"></i>إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printReceipt()"><i class="bi bi-printer-fill me-1"></i>طباعة الإيصال</button>
                </div>
            </div>
        </div>
    </div>
</div>

@push("styles")
<style>
    #receipt-content-area .receipt-preview {
        font-family: "Tajawal", "Helvetica Neue", Helvetica, Arial, sans-serif; /* Modern Arabic font */
        font-size: 10pt; /* Standard for 80mm, adjust for 58mm */
        width: 100%; /* Modal will control width, this is for content within */
        margin: auto;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #fff;
        direction: rtl; /* Ensure RTL for Arabic */
    }
    #receipt-content-area .receipt-preview hr {
        border: 0;
        border-top: 1px dashed #333;
        margin: 8px 0;
    }
    #receipt-content-area .receipt-preview table {
        width: 100%;
        border-collapse: collapse;
        font-size: 9pt;
    }
    #receipt-content-area .receipt-preview th, 
    #receipt-content-area .receipt-preview td {
        padding: 3px;
        text-align: right; /* Default to right for Arabic */
    }
    #receipt-content-area .receipt-preview .text-center { text-align: center; }
    #receipt-content-area .receipt-preview .text-start { text-align: left; } /* For LTR numbers/prices */
    #receipt-content-area .receipt-preview .item-name { width: 50%; }
    #receipt-content-area .receipt-preview .item-qty { width: 15%; text-align: center;}
    #receipt-content-area .receipt-preview .item-price, 
    #receipt-content-area .receipt-preview .item-total {
        width: 17.5%;
        text-align: left; /* Prices often LTR */
    }
    #receipt-content-area .receipt-preview .totals-label { width: 70%; }
    #receipt-content-area .receipt-preview .totals-value { width: 30%; text-align: left; }
    #receipt-content-area .receipt-preview .company-logo {
        max-width: 150px; max-height: 60px; margin-bottom: 5px;
    }
    #receipt-content-area .receipt-preview .qr-code-container {
        text-align: center;
        margin: 10px 0;
    }
    #receipt-content-area .receipt-preview .qr-code-placeholder {
        width: 120px; /* Adjust as needed */
        height: 120px;
        border: 1px solid #eee;
        display: inline-block;
        line-height: 120px;
        color: #aaa;
        font-size: 0.8em;
    }

    /* Styles for 58mm printer preview (can be toggled via JS or separate template) */
    .receipt-preview.printer-58mm {
        font-size: 8pt;
    }
    .receipt-preview.printer-58mm .item-name { width: 45%; }
    .receipt-preview.printer-58mm .item-qty { width: 15%; }
    .receipt-preview.printer-58mm .item-price, 
    .receipt-preview.printer-58mm .item-total {
        width: 20%;
    }
    .receipt-preview.printer-58mm .qr-code-placeholder {
        width: 90px; height: 90px; line-height: 90px;
    }

</style>
@endpush

@push("scripts")
<script>
    // saleData is passed from processPayment in payment_modal.blade.php
    function generateReceiptHTML(saleData, printerType = "80mm") { 
        const companyName = "{{ config("app.name", "اسم المتجر الافتراضي") }}";
        const companyAddress = "عنوان المتجر، المدينة"; // Replace with actual config or data
        const companyPhone = "0123456789"; // Replace
        const companyVatNo = "300000000000003"; // Replace
        const companyLogoUrl = "{{ asset("images/company_logo_receipt.png") }}"; // Placeholder, ensure image exists

        let itemsHTML = saleData.cart.map(item => `
            <tr>
                <td class="item-name">${item.name}</td>
                <td class="item-qty">${item.quantity}</td>
                <td class="item-price">${parseFloat(item.price).toFixed(2)}</td>
                <td class="item-total">${(parseFloat(item.price) * parseInt(item.quantity)).toFixed(2)}</td>
            </tr>
        `).join("");

        const subtotal = parseFloat(saleData.subTotal) || saleData.cart.reduce((sum, item) => sum + (parseFloat(item.price) * parseInt(item.quantity)), 0);
        const tax = parseFloat(saleData.taxAmount) || (subtotal * 0.15);
        const deliveryCharge = parseFloat(saleData.deliveryCharge) || 0;
        const total = parseFloat(saleData.totalAmount) || (subtotal + tax + deliveryCharge);
        const amountTendered = parseFloat(saleData.amountTendered) || 0;
        const changeDue = parseFloat(saleData.changeDue) || 0;

        let paymentMethodDisplay = "غير محدد";
        let networkPaymentDetailsHTML = "";

        if (saleData.paymentMethod === "cash") paymentMethodDisplay = "نقداً";
        else if (saleData.paymentMethod === "card") paymentMethodDisplay = "بطاقة"; // Generic card, might be manual entry
        else if (saleData.paymentMethod === "tabby") paymentMethodDisplay = "تابي";
        else if (saleData.paymentMethod === "tamara") paymentMethodDisplay = "تمارا";
        else if (saleData.paymentMethod === "network_pos") {
            paymentMethodDisplay = "شبكة (جهاز نقاط البيع)";
            if (saleData.networkPaymentDetails) {
                networkPaymentDetailsHTML += `<p><small><strong>نوع البطاقة:</strong> ${saleData.networkPaymentDetails.card_type || "N/A"}</small></p>`;
                networkPaymentDetailsHTML += `<p><small><strong>رقم البطاقة (مقنع):</strong> ${saleData.networkPaymentDetails.masked_pan || "N/A"}</small></p>`;
                networkPaymentDetailsHTML += `<p><small><strong>معرف عملية الجهاز:</strong> ${saleData.networkPaymentDetails.terminal_transaction_id || "N/A"}</small></p>`;
                networkPaymentDetailsHTML += `<p><small><strong>رمز الموافقة:</strong> ${saleData.networkPaymentDetails.approval_code || "N/A"}</small></p>`;
            }
        }

        // ZATCA QR Code - Placeholder for actual generation logic
        const zatcaQRString = `Seller: ${companyName}, VAT: ${companyVatNo}, Timestamp: ${new Date(saleData.timestamp).toISOString()}, Total: ${total.toFixed(2)}, VAT Amount: ${tax.toFixed(2)}`;
        const qrCodeHTML = `<div class="qr-code-container"><div id="zatca-qr-code-placeholder" class="qr-code-placeholder">رمز QR هنا</div></div>`;

        return `
            <div class="receipt-preview ${printerType === "58mm" ? "printer-58mm" : ""}" id="printable-receipt-area">
                <div class="text-center mb-2">
                    ${ companyLogoUrl ? `<img src="${companyLogoUrl}" alt="${companyName}" class="company-logo"><br>` : ``}
                    <strong>${companyName}</strong><br>
                    <small>${companyAddress}</small><br>
                    <small>الهاتف: ${companyPhone}</small><br>
                    <small>الرقم الضريبي: ${companyVatNo}</small>
                </div>
                <hr>
                <p><small>فاتورة ضريبية مبسطة</small></p>
                <p><small><strong>رقم الفاتورة:</strong> ${saleData.invoiceId}</small></p>
                <p><small><strong>التاريخ:</strong> ${new Date(saleData.timestamp).toLocaleString("ar-SA", {dateStyle: "short", timeStyle: "short"})}</small></p>
                <p><small><strong>الكاشير:</strong> ${saleData.cashierName || "غير محدد"}</small></p>
                ${saleData.customer && saleData.customer.name ? `<p><small><strong>العميل:</strong> ${saleData.customer.name} ${saleData.customer.vat ? `(ضريبي: ${saleData.customer.vat})` : ""}</small></p>` : ""}
                <hr>
                <table>
                    <thead>
                        <tr>
                            <th class="item-name">الصنف</th>
                            <th class="item-qty">الكمية</th>
                            <th class="item-price">السعر</th>
                            <th class="item-total">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>${itemsHTML}</tbody>
                </table>
                <hr>
                <table>
                    <tr><td class="totals-label">الإجمالي الفرعي:</td><td class="totals-value">${subtotal.toFixed(2)} ريال</td></tr>
                    ${deliveryCharge > 0 ? `<tr><td class="totals-label">رسوم التوصيل:</td><td class="totals-value">${deliveryCharge.toFixed(2)} ريال</td></tr>` : ""}
                    <tr><td class="totals-label">ضريبة القيمة المضافة (15%):</td><td class="totals-value">${tax.toFixed(2)} ريال</td></tr>
                    <tr><td class="totals-label fw-bold">الإجمالي الكلي:</td><td class="totals-value fw-bold">${total.toFixed(2)} ريال</td></tr>
                </table>
                <hr>
                <p><small><strong>طريقة الدفع:</strong> ${paymentMethodDisplay}</small></p>
                ${networkPaymentDetailsHTML}
                ${saleData.paymentMethod === "cash" && amountTendered > 0 ? 
                    `<p><small><strong>المبلغ المستلم:</strong> ${amountTendered.toFixed(2)} ريال</small></p>
                     <p><small><strong>الباقي:</strong> ${changeDue.toFixed(2)} ريال</small></p>` : ""}
                <hr>
                ${qrCodeHTML}
                <hr>
                <p class="text-center"><small>شكراً لتعاملكم معنا!</small></p>
                <p class="text-center"><small>www.example.com</small></p>
            </div>
        `;
    }

    function showReceiptModal(saleData) {
        const receiptArea = document.getElementById("receipt-content-area");
        const printerType = "80mm"; 
        receiptArea.innerHTML = generateReceiptHTML(saleData, printerType);
        
        // if (typeof QRCode !== "undefined") { // Placeholder for QR code generation
        //    new QRCode(document.getElementById("zatca-qr-code-placeholder"), {
        //        text: `Seller: ${companyName}, VAT: ${companyVatNo}, Timestamp: ${new Date(saleData.timestamp).toISOString()}, Total: ${parseFloat(saleData.totalAmount).toFixed(2)}, VAT Amount: ${parseFloat(saleData.taxAmount).toFixed(2)}`,
        //        width: printerType === "58mm" ? 80 : 110,
        //        height: printerType === "58mm" ? 80 : 110,
        //        correctLevel : QRCode.CorrectLevel.M
        //    });
        //    document.getElementById("zatca-qr-code-placeholder").innerHTML = ""; 
        // }

        var receiptModalInstance = bootstrap.Modal.getInstance(document.getElementById("receiptModal"));
        if (!receiptModalInstance) {
            receiptModalInstance = new bootstrap.Modal(document.getElementById("receiptModal"));
        }
        receiptModalInstance.show();
    }

    function printReceipt() {
        const printableArea = document.getElementById("printable-receipt-area");
        if (!printableArea) {
            alert("محتوى الإيصال غير جاهز للطباعة.");
            return;
        }
        const receiptHTML = printableArea.innerHTML;
        
        const iframe = document.createElement("iframe");
        iframe.style.position = "fixed";
        iframe.style.width = "0";
        iframe.style.height = "0";
        iframe.style.border = "0";
        document.body.appendChild(iframe);

        const iframeDoc = iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(`
            <html>
            <head>
                <title>إيصال</title>
                <style>
                    @media print {
                        @page { margin: 0mm; size: ${printableArea.classList.contains("printer-58mm") ? "58mm" : "80mm"} auto; }
                        body { margin: 0; padding: 0; font-family: "Tajawal", "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: ${printableArea.classList.contains("printer-58mm") ? "8pt" : "10pt"}; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; font-size: inherit; }
                        th, td { padding: 2px; text-align: right; }
                        hr { border: 0; border-top: 1px dashed #333; margin: 5px 0; }
                        .text-center { text-align: center; }
                        .text-start { text-align: left; }
                        .company-logo { max-width: 100%; height: auto; max-height: 40px; margin-bottom: 3px; }
                        .qr-code-container { text-align: center; margin: 5px 0; }
                        .qr-code-placeholder { width: ${printableArea.classList.contains("printer-58mm") ? "80px" : "110px"}; height: ${printableArea.classList.contains("printer-58mm") ? "80px" : "110px"}; border: 1px solid #eee; display: inline-block; line-height: ${printableArea.classList.contains("printer-58mm") ? "80px" : "110px"}; color: #aaa; font-size: 0.7em; }
                    }
                </style>
            </head>
            <body>${receiptHTML}</body>
            </html>
        `);
        iframeDoc.close();

        iframe.contentWindow.focus();
        setTimeout(() => { 
            try {
                iframe.contentWindow.print(); 
            } catch (e) {
                console.error("Printing failed:", e);
                alert("فشلت عملية الطباعة. يرجى المحاولة مرة أخرى أو التحقق من إعدادات الطابعة.");
            }
            // setTimeout(() => { document.body.removeChild(iframe); }, 1000); 
        }, 500); 
    }

    function sendReceiptByEmail(){
        const email = saleDataForReceipt.receiptOptions.email || prompt("أدخل البريد الإلكتروني لإرسال الإيصال:");
        if (email) {
            alert(`سيتم إرسال الإيصال إلى ${email} (محاكاة).`);
            console.log("Simulating sending email to:", email, "with data:", saleDataForReceipt);
        }
    }

    function sendReceiptByWhatsApp(){
        const whatsappNumber = saleDataForReceipt.receiptOptions.whatsapp || prompt("أدخل رقم الواتساب لإرسال الإيصال (مع رمز الدولة):");
        if(whatsappNumber){
            alert(`سيتم إرسال الإيصال إلى ${whatsappNumber} عبر واتساب (محاكاة).`);
            console.log("Simulating sending WhatsApp to:", whatsappNumber, "with data:", saleDataForReceipt);
        }
    }

    let saleDataForReceipt; 

</script>
@endpush


