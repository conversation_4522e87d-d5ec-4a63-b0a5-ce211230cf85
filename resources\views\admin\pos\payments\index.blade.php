@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Payments</h1>

    {{-- Payments are typically associated with a sale. A general "create" button here might not be standard. --}}
    {{-- It's more common to add payments from a specific sale's view. --}}
    {{-- <a href="{{ route("admin.pos.payments.create") }}" class="btn btn-primary mb-3">Add New Payment</a> --}}

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    {{-- Add search/filter form here if needed (e.g., by Sale ID, Date, Payment Method) --}}
    <form method="GET" action="{{ route("admin.pos.payments.index") }}" class="mb-3">
        <div class="row">
            <div class="col-md-3">
                <input type="text" name="sale_id" class="form-control" placeholder="Sale ID" value="{{ request("sale_id") }}">
            </div>
            <div class="col-md-3">
                <input type="date" name="payment_date" class="form-control" value="{{ request("payment_date") }}">
            </div>
            <div class="col-md-3">
                <select name="payment_method" class="form-control">
                    <option value="">All Payment Methods</option>
                    <option value="cash" {{ request("payment_method") == "cash" ? "selected" : "" }}>Cash</option>
                    <option value="card" {{ request("payment_method") == "card" ? "selected" : "" }}>Card</option>
                    <option value="bnpl" {{ request("payment_method") == "bnpl" ? "selected" : "" }}>BNPL</option>
                    {{-- Add other payment methods as needed --}}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-info">Filter</button>
                <a href="{{ route("admin.pos.payments.index") }}" class="btn btn-secondary">Clear</a>
            </div>
        </div>
    </form>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Sale ID</th>
                <th>Amount</th>
                <th>Payment Method</th>
                <th>Payment Date</th>
                <th>Reference</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {{-- @forelse($payments as $payment) --}}
            {{-- Replace with actual data when available --}}
            <tr>
                <td>1</td>
                <td><a href="#{{-- route("admin.pos.sales.show", $payment->sale_id) --}}">S1001</a></td>
                <td>50.00</td>
                <td>Cash</td>
                <td>2023-05-13 11:00:00</td>
                <td>N/A</td>
                <td>
                    <a href="{{-- route("admin.pos.payments.show", $payment->id) --}}" class="btn btn-info btn-sm">View</a>
                    {{-- Edit might be for corrections if allowed --}}
                    <a href="{{-- route("admin.pos.payments.edit", $payment->id) --}}" class="btn btn-warning btn-sm">Edit</a>
                    {{-- Delete/Refund action --}}
                    {{-- <form action="{{ route("admin.pos.payments.destroy", $payment->id) }}" method="POST" style="display:inline-block;">
                        @csrf
                        @method("DELETE")
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure you want to delete/refund this payment?")">Delete/Refund</button>
                    </form> --}}
                </td>
            </tr>
            <tr>
                <td>2</td>
                <td><a href="#{{-- route("admin.pos.sales.show", $payment->sale_id) --}}">S1002</a></td>
                <td>120.50</td>
                <td>Card</td>
                <td>2023-05-13 12:30:00</td>
                <td>TXN_ABC123</td>
                <td>
                    <a href="#{{-- route("admin.pos.payments.show", $payment->id) --}}" class="btn btn-info btn-sm">View</a>
                    <a href="#{{-- route("admin.pos.payments.edit", $payment->id) --}}" class="btn btn-warning btn-sm">Edit</a>
                </td>
            </tr>
            {{-- @empty
            <tr>
                <td colspan="7" class="text-center">No payments found.</td>
            </tr>
            @endforelse --}}
        </tbody>
    </table>
    {{-- Pagination links --}}
    {{-- {{ $payments->links() }} --}}
</div>
@endsection

