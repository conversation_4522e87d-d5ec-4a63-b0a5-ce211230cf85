@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">{{ __("Ticketing Reports") }}</h1>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $totalTickets }}</h4>
                            <p class="mb-0">{{ __("Total Tickets") }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $openTickets }}</h4>
                            <p class="mb-0">{{ __("Open Tickets") }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $pendingTickets }}</h4>
                            <p class="mb-0">{{ __("Pending Tickets") }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $closedTickets }}</h4>
                            <p class="mb-0">{{ __("Closed Tickets") }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- التذاكر حسب الفئة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __("Tickets by Category") }}</h5>
                </div>
                <div class="card-body">
                    @if($ticketsByCategory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __("Category") }}</th>
                                        <th>{{ __("Count") }}</th>
                                        <th>{{ __("Percentage") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ticketsByCategory as $item)
                                        <tr>
                                            <td>{{ $item->category->name ?? __("Unknown") }}</td>
                                            <td>{{ $item->count }}</td>
                                            <td>{{ $totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0 }}%</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">{{ __("No data available") }}</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- التذاكر حسب الأولوية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __("Tickets by Priority") }}</h5>
                </div>
                <div class="card-body">
                    @if($ticketsByPriority->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __("Priority") }}</th>
                                        <th>{{ __("Count") }}</th>
                                        <th>{{ __("Percentage") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ticketsByPriority as $item)
                                        <tr>
                                            <td>
                                                @if($item->priority && $item->priority->color)
                                                    <span style="display:inline-block; width: 12px; height: 12px; background-color: {{ $item->priority->color }}; border-radius: 50%; margin-right: 5px;"></span>
                                                @endif
                                                {{ $item->priority->name ?? __("Unknown") }}
                                            </td>
                                            <td>{{ $item->count }}</td>
                                            <td>{{ $totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0 }}%</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">{{ __("No data available") }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- التذاكر حسب الحالة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __("Tickets by Status") }}</h5>
                </div>
                <div class="card-body">
                    @if($ticketsByStatus->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __("Status") }}</th>
                                        <th>{{ __("Count") }}</th>
                                        <th>{{ __("Percentage") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ticketsByStatus as $item)
                                        <tr>
                                            <td>
                                                @if($item->status && $item->status->color)
                                                    <span style="display:inline-block; width: 12px; height: 12px; background-color: {{ $item->status->color }}; border-radius: 50%; margin-right: 5px;"></span>
                                                @endif
                                                {{ $item->status->name ?? __("Unknown") }}
                                            </td>
                                            <td>{{ $item->count }}</td>
                                            <td>{{ $totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0 }}%</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">{{ __("No data available") }}</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- التذاكر حسب الوكيل -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __("Tickets by Agent") }}</h5>
                </div>
                <div class="card-body">
                    @if($ticketsByAgent->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __("Agent") }}</th>
                                        <th>{{ __("Count") }}</th>
                                        <th>{{ __("Percentage") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ticketsByAgent as $item)
                                        <tr>
                                            <td>{{ $item->assignedTo->name ?? __("Unknown") }}</td>
                                            <td>{{ $item->count }}</td>
                                            <td>{{ $totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0 }}%</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">{{ __("No assigned tickets") }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني للتذاكر الحديثة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __("Recent Tickets (Last 30 Days)") }}</h5>
                </div>
                <div class="card-body">
                    @if($recentTickets->count() > 0)
                        <canvas id="recentTicketsChart" width="400" height="100"></canvas>
                    @else
                        <p class="text-muted">{{ __("No recent tickets") }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if($recentTickets->count() > 0)
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('recentTicketsChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {!! json_encode($recentTickets->pluck('date')) !!},
            datasets: [{
                label: '{{ __("Tickets Created") }}',
                data: {!! json_encode($recentTickets->pluck('count')) !!},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
@endif
@endsection
