<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bnpl_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->string('provider_name'); // e.g., "tabby", "tamara"
            $table->string('provider_session_id')->nullable(); // e.g., Tabby Session ID, Tamara Checkout ID
            $table->string('provider_order_id')->nullable(); // e.g., Tabby Payment ID, Tamara Order ID
            $table->string('provider_capture_id')->nullable();
            $table->string('provider_refund_id')->nullable();
            $table->string('status'); // e.g., PENDING_CUSTOMER_APPROVAL, AUTHORIZED, CAPTURED, CANCELED, FAILED, EXPIRED, REFUND_INITIATED, REFUNDED, PARTIALLY_REFUNDED
            $table->decimal('amount', 15, 2);
            $table->string('currency', 3);
            $table->string('payment_type')->nullable(); // e.g., "PAY_BY_INSTALMENTS", "PAY_LATER"
            $table->integer('instalments')->nullable();
            $table->json('webhook_payload')->nullable(); // for storing last relevant webhook data
            $table->text('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bnpl_transactions');
    }
};

