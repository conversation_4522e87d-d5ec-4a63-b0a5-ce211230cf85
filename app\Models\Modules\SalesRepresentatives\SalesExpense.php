<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class SalesExpense extends Model
{
    use HasFactory;

    protected $fillable = [
        'expense_code',
        'sales_representative_id',
        'sales_visit_id',
        'expense_type',
        'expense_date',
        'amount',
        'currency',
        'description',
        'receipt_number',
        'vendor_name',
        'receipt_images',
        'approval_status',
        'approval_notes',
        'approved_by',
        'approved_at',
        'payment_date',
        'payment_reference',
        'is_reimbursable',
        'reimbursed_amount',
        'expense_categories',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2',
        'receipt_images' => 'array',
        'approved_at' => 'datetime',
        'payment_date' => 'date',
        'is_reimbursable' => 'boolean',
        'reimbursed_amount' => 'decimal:2',
        'expense_categories' => 'array',
    ];

    /**
     * Generate unique expense code.
     */
    public static function generateExpenseCode(): string
    {
        $prefix = 'EXP';
        $date = now()->format('Ymd');
        $lastExpense = static::whereDate('created_at', now())->latest()->first();
        $sequence = $lastExpense ? (int)substr($lastExpense->expense_code, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the sales representative for this expense.
     */
    public function salesRepresentative(): BelongsTo
    {
        return $this->belongsTo(SalesRepresentative::class);
    }

    /**
     * Get the sales visit for this expense.
     */
    public function salesVisit(): BelongsTo
    {
        return $this->belongsTo(SalesVisit::class);
    }

    /**
     * Get the user who approved this expense.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the branch that owns the expense.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the expense.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get expense type color.
     */
    public function getExpenseTypeColorAttribute(): string
    {
        return match($this->expense_type) {
            'fuel' => '#dc3545',
            'meals' => '#28a745',
            'accommodation' => '#17a2b8',
            'transportation' => '#ffc107',
            'communication' => '#6f42c1',
            'entertainment' => '#fd7e14',
            'other' => '#6c757d',
            default => '#6c757d'
        };
    }

    /**
     * Get approval status color.
     */
    public function getApprovalStatusColorAttribute(): string
    {
        return match($this->approval_status) {
            'pending' => '#ffc107',
            'approved' => '#28a745',
            'rejected' => '#dc3545',
            'paid' => '#17a2b8',
            default => '#6c757d'
        };
    }

    /**
     * Get expense type text in Arabic.
     */
    public function getExpenseTypeTextAttribute(): string
    {
        return match($this->expense_type) {
            'fuel' => 'وقود',
            'meals' => 'وجبات',
            'accommodation' => 'إقامة',
            'transportation' => 'مواصلات',
            'communication' => 'اتصالات',
            'entertainment' => 'ضيافة',
            'other' => 'أخرى',
            default => 'غير محدد'
        };
    }

    /**
     * Get approval status text in Arabic.
     */
    public function getApprovalStatusTextAttribute(): string
    {
        return match($this->approval_status) {
            'pending' => 'في الانتظار',
            'approved' => 'معتمد',
            'rejected' => 'مرفوض',
            'paid' => 'مدفوع',
            default => 'غير محدد'
        };
    }

    /**
     * Approve the expense.
     */
    public function approve(User $user, string $notes = null): void
    {
        $this->update([
            'approval_status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    /**
     * Reject the expense.
     */
    public function reject(User $user, string $reason): void
    {
        $this->update([
            'approval_status' => 'rejected',
            'approved_by' => $user->id,
            'approved_at' => now(),
            'approval_notes' => $reason,
        ]);
    }

    /**
     * Mark as paid.
     */
    public function markAsPaid(string $paymentReference, float $amount = null): void
    {
        $this->update([
            'approval_status' => 'paid',
            'payment_date' => now(),
            'payment_reference' => $paymentReference,
            'reimbursed_amount' => $amount ?? $this->amount,
        ]);
    }

    /**
     * Scope a query to only include pending expenses.
     */
    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    /**
     * Scope a query to only include approved expenses.
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope a query to only include reimbursable expenses.
     */
    public function scopeReimbursable($query)
    {
        return $query->where('is_reimbursable', true);
    }

    /**
     * Scope a query to filter by expense type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('expense_type', $type);
    }

    /**
     * Scope a query to filter by sales representative.
     */
    public function scopeByRepresentative($query, $representativeId)
    {
        return $query->where('sales_representative_id', $representativeId);
    }

    /**
     * Scope a query to order by expense date.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('expense_date', 'desc');
    }
}
