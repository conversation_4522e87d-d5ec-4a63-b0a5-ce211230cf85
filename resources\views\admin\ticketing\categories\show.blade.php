@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("Ticket Category Details") }}</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ $category->name }}</h4>
        </div>
        <div class="card-body">
            <p><strong>{{ __("ID:") }}</strong> {{ $category->id }}</p>
            <p><strong>{{ __("Name:") }}</strong> {{ $category->name }}</p>
            <p><strong>{{ __("Description:") }}</strong> {{ $category->description ?? __("N/A") }}</p>
            <p><strong>{{ __("Active:") }}</strong> 
                @if($category->is_active)
                    <span class="badge badge-success">{{ __("Yes") }}</span>
                @else
                    <span class="badge badge-danger">{{ __("No") }}</span>
                @endif
            </p>
            <p><strong>{{ __("Created At:") }}</strong> {{ $category->created_at->format("Y-m-d H:i:s") }}</p>
            <p><strong>{{ __("Updated At:") }}</strong> {{ $category->updated_at->format("Y-m-d H:i:s") }}</p>
        </div>
        <div class="card-footer">
            <a href="{{ route("admin.ticketing.categories.edit", $category->id) }}" class="btn btn-warning">{{ __("Edit") }}</a>
            <a href="{{ route("admin.ticketing.categories.index") }}" class="btn btn-secondary">{{ __("Back to List") }}</a>
        </div>
    </div>
</div>
@endsection

