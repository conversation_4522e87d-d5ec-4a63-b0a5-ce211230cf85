@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>BOM Details: {{-- $bom->name --}}</h1>
        <table class="table table-bordered mt-3">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $bom->id --}}</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{-- $bom->name --}}</td>
                </tr>
                <tr>
                    <th>Description</th>
                    <td>{{-- $bom->description --}}</td>
                </tr>
                <tr>
                    <th>Item (Produces)</th>
                    <td>{{-- $bom->item ? $bom->item->name . " (" . $bom->item->item_code . ")" : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Version</th>
                    <td>{{-- $bom->version --}}</td>
                </tr>
                <tr>
                    <th>Quantity Produced</th>
                    <td>{{-- number_format($bom->quantity_produced, 4) --}}</td>
                </tr>
                <tr>
                    <th>Is Active?</th>
                    <td>{{-- $bom->is_active ? "Yes" : "No" --}}</td>
                </tr>
                <tr>
                    <th>Valid From</th>
                    <td>{{-- $bom->valid_from ? $bom->valid_from->format("Y-m-d") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Valid To</th>
                    <td>{{-- $bom->valid_to ? $bom->valid_to->format("Y-m-d") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Branch</th>
                    <td>{{-- $bom->branch ? $bom->branch->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{-- $bom->created_at --}}</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{-- $bom->updated_at --}}</td>
                </tr>
            </tbody>
        </table>

        <h3 class="mt-4">BOM Items (Components)</h3>
        @if(isset($bom) && $bom->bomItems && $bom->bomItems->count() > 0)
            <table class="table table-sm table-striped mt-2">
                <thead>
                    <tr>
                        <th>Component Item</th>
                        <th>Quantity</th>
                        <th>Unit of Measure</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- @foreach($bom->bomItems as $bomItem) --}}
                    <tr>
                        <td>{{-- $bomItem->item ? $bomItem->item->name . " (" . $bomItem->item->item_code . ")" : "N/A" --}}</td>
                        <td>{{-- number_format($bomItem->quantity, 4) --}}</td>
                        <td>{{-- $bomItem->unit_of_measure --}}</td>
                    </tr>
                    {{-- @endforeach --}}
                </tbody>
            </table>
        @else
            <p>No component items found for this BOM.</p>
        @endif

        <a href="{{-- route("admin.manufacturing.boms.index") --}}" class="btn btn-secondary mt-3">Back to List</a>
        <a href="{{-- route("admin.manufacturing.boms.edit", $bom->id) --}}" class="btn btn-warning mt-3">Edit</a>
    </div>
@endsection
