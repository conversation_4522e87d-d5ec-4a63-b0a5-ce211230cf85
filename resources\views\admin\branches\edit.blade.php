@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>Edit Branch: {{ $branch->name }}</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.branches.update", $branch->id) }}" method="POST">
            @csrf
            @method("PUT")
            <div class="form-group">
                <label for="name">Branch Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name", $branch->name) }}" required>
            </div>
            <div class="form-group">
                <label for="code">Branch Code:</label>
                <input type="text" class="form-control" id="code" name="code" value="{{ old("code", $branch->code) }}">
            </div>
            <div class="form-group">
                <label for="address">Address:</label>
                <textarea class="form-control" id="address" name="address">{{ old("address", $branch->address) }}</textarea>
            </div>
            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="text" class="form-control" id="phone" name="phone" value="{{ old("phone", $branch->phone) }}">
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" class="form-control" id="email" name="email" value="{{ old("email", $branch->email) }}">
            </div>
            <div class="form-group">
                <label for="parent_id">Parent Branch:</label>
                <select class="form-control" id="parent_id" name="parent_id">
                    <option value="">None</option>
                    @foreach ($allBranches as $parentBranch)
                        <option value="{{ $parentBranch->id }}" {{ old("parent_id", $branch->parent_id) == $parentBranch->id ? "selected" : "" }} @if($parentBranch->id == $branch->id) disabled @endif>{{ $parentBranch->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label for="is_active">Active:</label>
                <select class="form-control" id="is_active" name="is_active">
                    <option value="1" {{ old("is_active", $branch->is_active) == 1 ? "selected" : "" }}>Yes</option>
                    <option value="0" {{ old("is_active", $branch->is_active) == 0 ? "selected" : "" }}>No</option>
                </select>
            </div>
            <button type="submit" class="btn btn-success">Update Branch</button>
            <a href="{{ route("admin.branches.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

