<?php

namespace App\Http\Controllers\Modules\Ticketing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Ticketing\TicketStatus;
use Illuminate\Http\Request;

class TicketStatusController extends Controller
{
    public function index()
    {
        $statuses = TicketStatus::latest()->paginate(15);
        return view("admin.ticketing.statuses.index", compact("statuses"));
    }

    public function create()
    {
        return view("admin.ticketing.statuses.form");
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_statuses,name",
            "type" => "required|in:open,pending,closed",
            "color" => "nullable|string|max:7",
            "is_default_new" => "boolean",
            "is_default_closed" => "boolean",
            "is_active" => "boolean",
        ]);
        $validatedData["is_default_new"] = $request->has("is_default_new");
        $validatedData["is_default_closed"] = $request->has("is_default_closed");
        $validatedData["is_active"] = $request->has("is_active");

        // Ensure only one default_new and one default_closed status exists
        if ($validatedData["is_default_new"]) {
            TicketStatus::where("is_default_new", true)->update(["is_default_new" => false]);
        }
        if ($validatedData["is_default_closed"]) {
            TicketStatus::where("is_default_closed", true)->update(["is_default_closed" => false]);
        }

        TicketStatus::create($validatedData);
        return redirect()->route("admin.ticketing.statuses.index")->with("success", __("Ticket status created successfully."));
    }

    public function show(TicketStatus $status)
    {
        return view("admin.ticketing.statuses.show", compact("status"));
    }

    public function edit(TicketStatus $status)
    {
        return view("admin.ticketing.statuses.form", compact("status"));
    }

    public function update(Request $request, TicketStatus $status)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_statuses,name," . $status->id,
            "type" => "required|in:open,pending,closed",
            "color" => "nullable|string|max:7",
            "is_default_new" => "boolean",
            "is_default_closed" => "boolean",
            "is_active" => "boolean",
        ]);
        $validatedData["is_default_new"] = $request->has("is_default_new");
        $validatedData["is_default_closed"] = $request->has("is_default_closed");
        $validatedData["is_active"] = $request->has("is_active");

        if ($validatedData["is_default_new"]) {
            TicketStatus::where("is_default_new", true)->where("id", "!=", $status->id)->update(["is_default_new" => false]);
        }
        if ($validatedData["is_default_closed"]) {
            TicketStatus::where("is_default_closed", true)->where("id", "!=", $status->id)->update(["is_default_closed" => false]);
        }

        $status->update($validatedData);
        return redirect()->route("admin.ticketing.statuses.index")->with("success", __("Ticket status updated successfully."));
    }

    public function destroy(TicketStatus $status)
    {
        if ($status->tickets()->count() > 0) {
            return redirect()->route("admin.ticketing.statuses.index")->with("error", __("Cannot delete status with associated tickets."));
        }
        if ($status->is_default_new || $status->is_default_closed) {
             return redirect()->route("admin.ticketing.statuses.index")->with("error", __("Cannot delete a default status. Please set another status as default first."));
        }
        $status->delete();
        return redirect()->route("admin.ticketing.statuses.index")->with("success", __("Ticket status deleted successfully."));
    }
}

