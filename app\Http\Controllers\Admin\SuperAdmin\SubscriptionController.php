<?php

namespace App\Http\Controllers\Admin\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    /**
     * عرض قائمة الاشتراكات
     */
    public function index()
    {
        $subscriptions = Subscription::with(['tenant', 'plan'])->latest()->get();
        return view('admin.super_admin.subscriptions.index', compact('subscriptions'));
    }

    /**
     * عرض نموذج إنشاء اشتراك جديد
     */
    public function create()
    {
        $plans = SubscriptionPlan::where('is_active', true)->get();
        $tenants = User::whereHas('roles', function ($query) {
            $query->where('name', 'tenant');
        })->get();

        return view('admin.super_admin.subscriptions.form', compact('plans', 'tenants'));
    }

    /**
     * حفظ اشتراك جديد
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,cancelled,pending',
            'notes' => 'nullable|string',
            'auto_renew' => 'boolean',
            'price_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        // تحويل القيم البوليانية
        $validatedData['auto_renew'] = isset($validatedData['auto_renew']) ? true : false;

        DB::beginTransaction();

        try {
            // إنشاء الاشتراك
            $subscription = Subscription::create($validatedData);

            // إنشاء سجل تغيير الاشتراك
            $subscription->changes()->create([
                'user_id' => Auth::id(),
                'change_type' => 'new',
                'change_details' => 'إنشاء اشتراك جديد',
                'new_plan_id' => $validatedData['subscription_plan_id'],
                'effective_date' => $validatedData['start_date'],
                'price_difference' => $validatedData['price_paid'],
            ]);

            DB::commit();

            return redirect()->route('admin.super_admin.subscriptions.index')
                ->with('success', 'تم إنشاء الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء الاشتراك: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * عرض تفاصيل الاشتراك
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['tenant', 'plan', 'payments', 'changes.user', 'changes.oldPlan', 'changes.newPlan']);
        return view('admin.super_admin.subscriptions.show', compact('subscription'));
    }

    /**
     * عرض نموذج تعديل الاشتراك
     */
    public function edit(Subscription $subscription)
    {
        $plans = SubscriptionPlan::where('is_active', true)->get();
        $tenants = User::whereHas('roles', function ($query) {
            $query->where('name', 'tenant');
        })->get();

        return view('admin.super_admin.subscriptions.form', compact('subscription', 'plans', 'tenants'));
    }

    /**
     * تحديث الاشتراك
     */
    public function update(Request $request, Subscription $subscription)
    {
        $validatedData = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,cancelled,pending',
            'notes' => 'nullable|string',
            'auto_renew' => 'boolean',
            'price_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        // تحويل القيم البوليانية
        $validatedData['auto_renew'] = isset($validatedData['auto_renew']) ? true : false;

        DB::beginTransaction();

        try {
            // تحديث الاشتراك
            $subscription->update($validatedData);

            // إنشاء سجل تغيير الاشتراك
            $subscription->changes()->create([
                'user_id' => Auth::id(),
                'change_type' => 'update',
                'change_details' => 'تحديث الاشتراك',
                'old_plan_id' => $subscription->getOriginal('subscription_plan_id'),
                'new_plan_id' => $validatedData['subscription_plan_id'],
                'effective_date' => now(),
                'price_difference' => $validatedData['price_paid'],
            ]);

            DB::commit();

            return redirect()->route('admin.super_admin.subscriptions.index')
                ->with('success', 'تم تحديث الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الاشتراك: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * حذف الاشتراك
     */
    public function destroy(Subscription $subscription)
    {
        try {
            $subscription->delete();
            return redirect()->route('admin.super_admin.subscriptions.index')
                ->with('success', 'تم حذف الاشتراك بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('admin.super_admin.subscriptions.index')
                ->with('error', 'حدث خطأ أثناء حذف الاشتراك: ' . $e->getMessage());
        }
    }
}
