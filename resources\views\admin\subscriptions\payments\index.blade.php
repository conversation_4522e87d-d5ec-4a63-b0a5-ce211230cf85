@extends('layouts.admin')

@section('title', 'إدارة مدفوعات الاشتراكات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">مدفوعات الاشتراكات</h3>
                    <a href="{{ route('admin.subscriptions.payments.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة مدفوعة جديدة
                    </a>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الإيصال</th>
                                    <th>الاشتراك</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الدفع</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($payments as $payment)
                                    <tr>
                                        <td>{{ $payment->id }}</td>
                                        <td>{{ $payment->receipt_number }}</td>
                                        <td>
                                            @if($payment->subscription)
                                                <a href="{{ route('admin.subscriptions.subscriptions.show', $payment->subscription) }}">
                                                    {{ $payment->subscription->subscription_number }}
                                                </a>
                                            @else
                                                غير محدد
                                            @endif
                                        </td>
                                        <td>
                                            @if($payment->subscription && $payment->subscription->tenant)
                                                {{ $payment->subscription->tenant->name }}
                                            @else
                                                غير محدد
                                            @endif
                                        </td>
                                        <td>{{ $payment->formatted_amount }}</td>
                                        <td>{{ $payment->payment_date->format('Y-m-d') }}</td>
                                        <td>{{ $payment->payment_method }}</td>
                                        <td>
                                            @if ($payment->status == 'paid')
                                                <span class="badge bg-success">{{ $payment->status_arabic }}</span>
                                            @elseif ($payment->status == 'pending')
                                                <span class="badge bg-warning">{{ $payment->status_arabic }}</span>
                                            @elseif ($payment->status == 'failed')
                                                <span class="badge bg-danger">{{ $payment->status_arabic }}</span>
                                            @elseif ($payment->status == 'refunded')
                                                <span class="badge bg-info">{{ $payment->status_arabic }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $payment->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.subscriptions.payments.show', $payment) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.subscriptions.payments.edit', $payment) }}" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route('admin.subscriptions.payments.destroy', $payment) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المدفوعة؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد مدفوعات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $payments->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
