@extends('layouts.admin')

@section('title', isset($subscription) ? 'تعديل اشتراك' : 'إضافة اشتراك جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($subscription) ? 'تعديل اشتراك' : 'إضافة اشتراك جديد' }}</h3>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ isset($subscription) ? route('admin.super_admin.subscriptions.update', $subscription->id) : route('admin.super_admin.subscriptions.store') }}" method="POST">
                        @csrf
                        @if(isset($subscription))
                            @method('PUT')
                        @endif

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tenant_id" class="form-label">المستأجر <span class="text-danger">*</span></label>
                                    <select name="tenant_id" id="tenant_id" class="form-select @error('tenant_id') is-invalid @enderror" required>
                                        <option value="">اختر المستأجر</option>
                                        @foreach($tenants as $tenant)
                                            <option value="{{ $tenant->id }}" {{ (old('tenant_id', $subscription->tenant_id ?? '') == $tenant->id) ? 'selected' : '' }}>
                                                {{ $tenant->name }} ({{ $tenant->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('tenant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="subscription_plan_id" class="form-label">الباقة <span class="text-danger">*</span></label>
                                    <select name="subscription_plan_id" id="subscription_plan_id" class="form-select @error('subscription_plan_id') is-invalid @enderror" required>
                                        <option value="">اختر الباقة</option>
                                        @foreach($plans as $plan)
                                            <option value="{{ $plan->id }}" {{ (old('subscription_plan_id', $subscription->subscription_plan_id ?? '') == $plan->id) ? 'selected' : '' }}>
                                                {{ $plan->name }} ({{ $plan->price }} ريال)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('subscription_plan_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date" class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                                    <input type="date" name="start_date" id="start_date" class="form-control @error('start_date') is-invalid @enderror" value="{{ old('start_date', isset($subscription) ? $subscription->start_date->format('Y-m-d') : date('Y-m-d')) }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date" class="form-label">تاريخ الانتهاء <span class="text-danger">*</span></label>
                                    <input type="date" name="end_date" id="end_date" class="form-control @error('end_date') is-invalid @enderror" value="{{ old('end_date', isset($subscription) ? $subscription->end_date->format('Y-m-d') : date('Y-m-d', strtotime('+1 month'))) }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                    <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                        <option value="active" {{ (old('status', $subscription->status ?? '') == 'active') ? 'selected' : '' }}>نشط</option>
                                        <option value="pending" {{ (old('status', $subscription->status ?? '') == 'pending') ? 'selected' : '' }}>قيد الانتظار</option>
                                        <option value="expired" {{ (old('status', $subscription->status ?? '') == 'expired') ? 'selected' : '' }}>منتهي</option>
                                        <option value="cancelled" {{ (old('status', $subscription->status ?? '') == 'cancelled') ? 'selected' : '' }}>ملغي</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price_paid" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                    <input type="number" name="price_paid" id="price_paid" class="form-control @error('price_paid') is-invalid @enderror" value="{{ old('price_paid', $subscription->price_paid ?? 0) }}" step="0.01" min="0" required>
                                    @error('price_paid')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                                    <input type="text" name="payment_method" id="payment_method" class="form-control @error('payment_method') is-invalid @enderror" value="{{ old('payment_method', $subscription->payment_method ?? '') }}">
                                    @error('payment_method')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_reference" class="form-label">مرجع الدفع</label>
                                    <input type="text" name="payment_reference" id="payment_reference" class="form-control @error('payment_reference') is-invalid @enderror" value="{{ old('payment_reference', $subscription->payment_reference ?? '') }}">
                                    @error('payment_reference')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" rows="3">{{ old('notes', $subscription->notes ?? '') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input type="checkbox" name="auto_renew" id="auto_renew" class="form-check-input @error('auto_renew') is-invalid @enderror" value="1" {{ old('auto_renew', $subscription->auto_renew ?? false) ? 'checked' : '' }}>
                                    <label for="auto_renew" class="form-check-label">تجديد تلقائي</label>
                                    @error('auto_renew')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> {{ isset($subscription) ? 'تحديث' : 'حفظ' }}
                                </button>
                                <a href="{{ route('admin.super_admin.subscriptions.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
