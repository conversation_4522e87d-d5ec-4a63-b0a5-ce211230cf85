<?php

namespace App\Http\Controllers\Modules\WhatsAppIntegration;

use App\Http\Controllers\Controller;
use App\Models\Modules\WhatsAppIntegration\WhatsAppConfiguration;
use Illuminate\Http\Request;

class WhatsAppConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $configurations = WhatsAppConfiguration::all();
        return view('admin.whatsapp.settings.index', compact('configurations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'provider_name' => 'required|string|max:255',
            'phone_number_id' => 'required|string|max:255',
            'access_token' => 'required|string',
            'business_account_id' => 'nullable|string|max:255',
            'app_id' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // إذا كان الإعداد الجديد مفعل، قم بإلغاء تفعيل الإعدادات الأخرى
        if ($request->is_active) {
            WhatsAppConfiguration::where('is_active', true)->update(['is_active' => false]);
        }

        WhatsAppConfiguration::create([
            'provider_name' => $request->provider_name,
            'phone_number_id' => $request->phone_number_id,
            'access_token' => $request->access_token,
            'business_account_id' => $request->business_account_id,
            'app_id' => $request->app_id,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.whatsapp.settings.index')
                        ->with('success', 'تم إضافة إعداد واتساب بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }
}
