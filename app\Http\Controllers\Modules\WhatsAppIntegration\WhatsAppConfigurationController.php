<?php

namespace App\Http\Controllers\Modules\WhatsAppIntegration;

use App\Http\Controllers\Controller;
use App\Models\Modules\WhatsAppIntegration\WhatsAppConfiguration;
use Illuminate\Http\Request;

class WhatsAppConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WhatsAppConfiguration $whatsAppConfiguration)
    {
        //
    }
}
