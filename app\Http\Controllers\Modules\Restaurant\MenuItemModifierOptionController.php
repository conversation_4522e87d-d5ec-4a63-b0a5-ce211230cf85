<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\MenuItemModifierOption;
use App\Models\Modules\Restaurant\MenuItemModifier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MenuItemModifierOptionController extends Controller
{
    public function index()
    {
        $modifierOptions = MenuItemModifierOption::with(['modifier'])
            ->whereHas('modifier', function($query) {
                $query->where('tenant_id', Auth::user()->tenant_id ?? Auth::id());
            })
            ->ordered()
            ->paginate(20);

        return view('admin.restaurant.modifier-options.index', compact('modifierOptions'));
    }

    public function create()
    {
        $modifiers = MenuItemModifier::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        return view('admin.restaurant.modifier-options.form', compact('modifiers'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'modifier_id' => 'required|exists:menu_item_modifiers,id',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'price_adjustment' => 'required|numeric',
            'sort_order' => 'nullable|integer|min:0',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_default'] = $request->has('is_default');
        $validatedData['is_active'] = $request->has('is_active');

        MenuItemModifierOption::create($validatedData);

        return redirect()->route('admin.restaurant.modifier-options.index')
            ->with('success', __('Modifier option created successfully.'));
    }

    public function show(MenuItemModifierOption $modifierOption)
    {
        $modifierOption->load(['modifier']);
        return view('admin.restaurant.modifier-options.show', compact('modifierOption'));
    }

    public function edit(MenuItemModifierOption $modifierOption)
    {
        $modifiers = MenuItemModifier::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        return view('admin.restaurant.modifier-options.form', compact('modifierOption', 'modifiers'));
    }

    public function update(Request $request, MenuItemModifierOption $modifierOption)
    {
        $validatedData = $request->validate([
            'modifier_id' => 'required|exists:menu_item_modifiers,id',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'price_adjustment' => 'required|numeric',
            'sort_order' => 'nullable|integer|min:0',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_default'] = $request->has('is_default');
        $validatedData['is_active'] = $request->has('is_active');

        $modifierOption->update($validatedData);

        return redirect()->route('admin.restaurant.modifier-options.index')
            ->with('success', __('Modifier option updated successfully.'));
    }

    public function destroy(MenuItemModifierOption $modifierOption)
    {
        $modifierOption->delete();

        return redirect()->route('admin.restaurant.modifier-options.index')
            ->with('success', __('Modifier option deleted successfully.'));
    }
}
