<?php

namespace App\Http\Controllers\Modules\GeneralLedger;

use App\Http\Controllers\Controller;
use App\Models\Modules\GeneralLedger\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB; // For more complex queries if needed

class ReportController extends Controller
{
    /**
     * Display the trial balance report.
     */
    public function trialBalance(Request $request)
    {
        // Fetch all active accounts with their balances
        // Accounts should have a balance field that is updated upon posting journal entries
        $accounts = Account::where("is_active", true)
                            ->with("accountType") // Eager load account type for debit/credit nature
                            ->orderBy("code")
                            ->get();

        $totalDebit = 0;
        $totalCredit = 0;

        foreach ($accounts as $account) {
            // Assuming positive balance for debit-nature accounts is debit
            // and positive balance for credit-nature accounts is credit.
            // This logic depends on how `balance` is stored and `is_debit_balance` on AccountType.
            if ($account->accountType) {
                if ($account->accountType->is_debit_balance) {
                    if ($account->balance >= 0) {
                        $totalDebit += $account->balance;
                    } else {
                        // Negative balance for a debit account means it has a credit balance
                        $totalCredit += abs($account->balance);
                    }
                } else {
                    if ($account->balance >= 0) {
                        $totalCredit += $account->balance;
                    } else {
                        // Negative balance for a credit account means it has a debit balance
                        $totalDebit += abs($account->balance);
                    }
                }
            }
        }
        
        // For a simpler trial balance, directly sum debit and credit columns if they exist separately
        // Or sum based on account type and balance sign as done above.

        return view("admin.reports.trial_balance", compact("accounts", "totalDebit", "totalCredit"));
    }

    // Placeholder for other reports like Balance Sheet, Income Statement
    // public function balanceSheet(Request $request) { /* ... */ }
    // public function incomeStatement(Request $request) { /* ... */ }
}

