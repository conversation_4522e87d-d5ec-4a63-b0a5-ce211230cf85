<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("boms", function (Blueprint $table) {
            $table->id();
            $table->string("name_ar");
            $table->string("name_en");
            $table->string("bom_code")->unique()->nullable(); // Bill of Materials code
            $table->foreignId("item_id")->constrained("items")->comment("The finished good this BOM is for");
            $table->decimal("quantity_to_produce", 15, 4)->default(1.0000);
            $table->string("unit_of_measure_ar")->nullable(); // Unit of measure for the quantity_to_produce
            $table->string("unit_of_measure_en")->nullable();
            $table->text("description_ar")->nullable();
            $table->text("description_en")->nullable();
            $table->boolean("is_default")->default(false)->comment("Is this the default BOM for the item?");
            $table->boolean("is_active")->default(true);
            $table->date("valid_from")->nullable();
            $table->date("valid_to")->nullable();
            $table->foreignId("branch_id")->nullable()->constrained("branches")->onDelete("cascade");
            $table->foreignId("created_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("updated_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("boms");
    }
};

