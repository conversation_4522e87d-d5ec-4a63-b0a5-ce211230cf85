<?php

namespace App\Services\Bnpl;

use App\Models\BnplProviderSetting; // Assuming settings are passed as this model or an array derived from it
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TabbyService implements BnplServiceInterface
{
    private string $apiKey;
    private string $baseUrl;
    private ?string $merchantCode;
    private ?string $webhookSecret;
    private bool $isProduction;

    public function initialize(array $settings): void
    {
        $this->isProduction = ($settings["environment"] ?? "sandbox") === "production";
        
        $this->apiKey = $this->isProduction ? ($settings["api_key_production"] ?? "") : ($settings["api_key_sandbox"] ?? "");
        $this->baseUrl = $this->isProduction ? "https://api.tabby.ai" : "https://api.tabby.dev";
        $this->merchantCode = $this->isProduction ? ($settings["merchant_code_production"] ?? null) : ($settings["merchant_code_sandbox"] ?? null);
        $this->webhookSecret = $this->isProduction ? ($settings["webhook_secret_production"] ?? null) : ($settings["webhook_secret_sandbox"] ?? null);

        if (empty($this->apiKey)) {
            Log::error("TabbyService: API key is not configured for the current environment.", ["environment" => $settings["environment"] ?? "sandbox"]);
            // Optionally throw an exception or handle this state as an error
        }
    }

    public function createCheckoutSession(array $orderDetails): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tabby API key not configured."];
        }
        if (empty($this->merchantCode)) {
             Log::warning("Tabby createCheckoutSession: Merchant code not configured in settings, attempting to use from orderDetails or default.");
        }

        // OrderDetails should contain the 'payment' structure and 'lang', 'merchant_urls'
        // The 'merchant_code' will now primarily come from $this->merchantCode
        $payload = $orderDetails["payment"];
        $payload["lang"] = $orderDetails["lang"] ?? "ar";
        $payload["merchant_code"] = $this->merchantCode ?? $orderDetails["merchant_code"] ?? null; // Prioritize initialized merchant code
        $payload["merchant_urls"] = $orderDetails["merchant_urls"];

        if (empty($payload["merchant_code"])){
            Log::error("Tabby createCheckoutSession: Merchant code is missing.");
            return ["success" => false, "error_message" => "Tabby merchant code is missing."];
        }

        try {
            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json",
                // "X-Merchant-Code" => $payload["merchant_code"] // Tabby API v2 for checkout session creation might not need X-Merchant-Code header if it's in body
            ])->post($this->baseUrl . "/v2/checkout", $payload); // Ensure $payload matches Tabby's expected structure for v2/checkout

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "checkout_url" => $responseData["configuration"]["available_products"]["installments"]["web_url"] ?? $responseData["checkout_url"] ?? null, 
                    "session_id" => $responseData["id"] ?? null
                ];
            } else {
                Log::error("Tabby createCheckoutSession failed: ", ["status" => $response->status(), "body" => $response->body(), "payload_sent" => $payload]);
                return ["success" => false, "error_message" => "Failed to create Tabby checkout session. Status: " . $response->status()];
            }
        } catch (\Exception $e) {
            Log::error("Tabby createCheckoutSession exception: " . $e->getMessage(), ["payload_sent" => $payload]);
            return ["success" => false, "error_message" => "Exception during Tabby checkout session creation: " . $e->getMessage()];
        }
    }

    public function authorizeOrder(string $sessionId, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tabby API key not configured."];
        }
        return $this->getOrderStatus($sessionId); 
    }

    public function capturePayment(string $paymentId, float $amount, string $currency, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tabby API key not configured."];
        }
        try {
            $payload = [
                "amount" => (string)number_format($amount, 2, ".", ""),
                "tax_amount" => $params["tax_amount"] ?? "0.00",
                "shipping_amount" => $params["shipping_amount"] ?? "0.00",
                "discount_amount" => $params["discount_amount"] ?? "0.00",
                "created_at" => now()->toIso8601String(),
                "items" => $params["items"] ?? []
            ];

            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json"
            ])->post($this->baseUrl . "/v1/payments/" . $paymentId . "/captures", $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "capture_id" => $responseData["id"] ?? null,
                    "status" => $responseData["status"] ?? null
                ];
            } else {
                Log::error("Tabby capturePayment failed: ", ["status" => $response->status(), "body" => $response->body()]);
                return ["success" => false, "error_message" => "Failed to capture Tabby payment. Status: " . $response->status()];
            }
        } catch (\Exception $e) {
            Log::error("Tabby capturePayment exception: " . $e->getMessage());
            return ["success" => false, "error_message" => "Exception during Tabby payment capture: " . $e->getMessage()];
        }
    }

    public function refundPayment(string $paymentId, ?string $captureId, float $amount, string $currency, string $reason, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tabby API key not configured."];
        }
        try {
            $payload = [
                "amount" => (string)number_format($amount, 2, ".", ""),
                "reason" => $reason,
            ];
            
            $refundUrl = $this->baseUrl . "/v1/payments/" . ($captureId ?? $paymentId) . "/refunds";
            if ($captureId) { // If captureId is provided, Tabby might have a different endpoint or logic
                // Adjust refundUrl or payload if Tabby's API for refunding specific captures differs.
                // For now, assuming paymentId is the primary identifier for refunds.
            }

            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json"
            ])->post($refundUrl, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "refund_id" => $responseData["id"] ?? null,
                    "status" => $responseData["status"] ?? null
                ];
            } else {
                Log::error("Tabby refundPayment failed: ", ["status" => $response->status(), "body" => $response->body()]);
                return ["success" => false, "error_message" => "Failed to process Tabby refund. Status: " . $response->status()];
            }
        } catch (\Exception $e) {
            Log::error("Tabby refundPayment exception: " . $e->getMessage());
            return ["success" => false, "error_message" => "Exception during Tabby refund processing: " . $e->getMessage()];
        }
    }

    public function getOrderStatus(string $paymentId): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tabby API key not configured."];
        }
        try {
            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey
            ])->get($this->baseUrl . "/v1/payments/" . $paymentId);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "status" => $responseData["status"] ?? null,
                    "details" => $responseData
                ];
            } else {
                Log::error("Tabby getOrderStatus failed: ", ["status" => $response->status(), "body" => $response->body()]);
                return ["success" => false, "error_message" => "Failed to get Tabby order status. Status: " . $response->status()];
            }
        } catch (\Exception $e) {
            Log::error("Tabby getOrderStatus exception: " . $e->getMessage());
            return ["success" => false, "error_message" => "Exception during Tabby order status retrieval: " . $e->getMessage()];
        }
    }

    public function verifyWebhookSignature(array $requestHeaders, string $requestBody): bool
    {
        $signature = $requestHeaders["x-tabby-signature"] ?? $requestHeaders["X-Tabby-Signature"] ?? null;
        if (!$signature) {
            Log::warning("Tabby webhook verification: X-Tabby-Signature header missing.");
            return false;
        }

        if (empty($this->webhookSecret)) {
            Log::error("Tabby webhook verification: Webhook secret key not configured in TabbyService.");
            return false;
        }

        $computedSignature = hash_hmac("sha256", $requestBody, $this->webhookSecret);

        if (hash_equals($computedSignature, $signature)) {
            return true;
        }

        Log::warning("Tabby webhook verification failed: Signatures do not match.", [
            "received_signature" => $signature,
            "computed_signature" => $computedSignature
        ]);
        return false;
    }

    public function handleWebhook(array $webhookData): array
    {
        $eventType = $webhookData["event"] ?? $webhookData["type"] ?? null; // Tabby uses 'type' in some webhook docs
        $paymentId = $webhookData["payment_id"] ?? $webhookData["id"] ?? null;
        $status = $webhookData["status"] ?? null;

        Log::info("Tabby webhook received: ", $webhookData);

        if (!$paymentId) {
            Log::error("Tabby webhook handling: Payment ID missing.", $webhookData);
            return ["success" => false, "error_message" => "Payment ID missing in webhook data."];
        }
        
        // Further processing based on eventType and status, e.g., updating order in DB
        // event(new TabbyPaymentUpdated($paymentId, $status, $webhookData));

        return ["success" => true, "message" => "Tabby webhook processed for event: " . $eventType . " and payment ID: " . $paymentId];
    }
}

