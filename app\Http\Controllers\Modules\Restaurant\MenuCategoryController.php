<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\MenuCategory;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MenuCategoryController extends Controller
{
    public function index()
    {
        $categories = MenuCategory::with(['parent', 'branch', 'children'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.restaurant.menu-categories.index', compact('categories'));
    }

    public function create()
    {
        $parentCategories = MenuCategory::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.menu-categories.form', compact('parentCategories', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id' => 'nullable|exists:menu_categories,id',
            'branch_id' => 'nullable|exists:branches,id',
            'is_active' => 'boolean',
            'is_available' => 'boolean',
        ]);

        if ($request->hasFile('image')) {
            $validatedData['image'] = $request->file('image')->store('menu-categories', 'public');
        }

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');
        $validatedData['is_available'] = $request->has('is_available');

        MenuCategory::create($validatedData);

        return redirect()->route('admin.restaurant.menu-categories.index')
            ->with('success', __('Menu category created successfully.'));
    }

    public function show(MenuCategory $menuCategory)
    {
        $menuCategory->load(['parent', 'children', 'branch', 'menuItems']);
        return view('admin.restaurant.menu-categories.show', compact('menuCategory'));
    }

    public function edit(MenuCategory $menuCategory)
    {
        $parentCategories = MenuCategory::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->where('id', '!=', $menuCategory->id)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.menu-categories.form', compact('menuCategory', 'parentCategories', 'branches'));
    }

    public function update(Request $request, MenuCategory $menuCategory)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id' => 'nullable|exists:menu_categories,id',
            'branch_id' => 'nullable|exists:branches,id',
            'is_active' => 'boolean',
            'is_available' => 'boolean',
        ]);

        if ($request->hasFile('image')) {
            // Delete old image
            if ($menuCategory->image) {
                Storage::disk('public')->delete($menuCategory->image);
            }
            $validatedData['image'] = $request->file('image')->store('menu-categories', 'public');
        }

        $validatedData['is_active'] = $request->has('is_active');
        $validatedData['is_available'] = $request->has('is_available');

        $menuCategory->update($validatedData);

        return redirect()->route('admin.restaurant.menu-categories.index')
            ->with('success', __('Menu category updated successfully.'));
    }

    public function destroy(MenuCategory $menuCategory)
    {
        // Check if category has menu items
        if ($menuCategory->menuItems()->count() > 0) {
            return redirect()->route('admin.restaurant.menu-categories.index')
                ->with('error', __('Cannot delete category that has menu items.'));
        }

        // Check if category has child categories
        if ($menuCategory->children()->count() > 0) {
            return redirect()->route('admin.restaurant.menu-categories.index')
                ->with('error', __('Cannot delete category that has subcategories.'));
        }

        // Delete image
        if ($menuCategory->image) {
            Storage::disk('public')->delete($menuCategory->image);
        }

        $menuCategory->delete();

        return redirect()->route('admin.restaurant.menu-categories.index')
            ->with('success', __('Menu category deleted successfully.'));
    }
}
