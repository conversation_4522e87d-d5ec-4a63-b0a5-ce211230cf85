<?php

namespace App\Services\Zatca;

use Saleh7\Zatca\Invoice;
use Saleh7\Zatca\InvoiceType;
use Saleh7\Zatca\Party;
use Saleh7\Zatca\TaxScheme;
use Saleh7\Zatca\LegalMonetaryTotal;
use Saleh7\Zatca\TaxTotal;
use Saleh7\Zatca\InvoiceLine;
use Saleh7\Zatca\Item;
use Saleh7\Zatca\Price;
use Saleh7\Zatca\AllowanceCharge;
use Saleh7\Zatca\Delivery;
use Saleh7\Zatca\PaymentMeans;
use Saleh7\Zatca\GeneratorInvoice;
use Saleh7\Zatca\Tag; // For QR Code generation
use Saleh7\Zatca\CertificateBuilder;
use Saleh7\Zatca\ZatcaAPI;
use Saleh7\Zatca\Exceptions\CertificateBuilderException;
use Saleh7\Zatca\Exceptions\ZatcaApiException;
use Saleh7\Zatca\BillingReference;
use Saleh7\Zatca\Address; // Added for Party
use Saleh7\Zatca\LegalEntity; // Added for Party
use Saleh7\Zatca\PartyTaxScheme; // Added for Party
use Saleh7\Zatca\TaxSubTotal;
use Saleh7\Zatca\TaxCategory;
use Saleh7\Zatca\Api\ComplianceCertificateResult; // Added for compliance certificate
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ZatcaInvoiceService
{
    protected string $sellerName;
    protected string $vatRegistrationNumber;
    protected string $sellerStreet;
    protected string $sellerBuildingNumber;
    protected string $sellerPlotIdentification;
    protected string $sellerCitySubdivision;
    protected string $sellerCity;
    protected string $sellerPostalZone;
    protected string $sellerCountryCode;

    private string $csrFilePath;
    private string $csrPrivateKeyPath;
    private string $csidCertificatePath;
    private string $csidPrivateKeyPath;
    private string $csrPrivateKeyPassword;
    private string $csidPrivateKeyPassword;

    private string $zatcaApiEnvironment;
    private string $complianceCsidOtp;

    public function __construct()
    {
        $this->sellerName = config("zatca.seller_name", "Test Seller Name");
        $this->vatRegistrationNumber = config("zatca.vat_registration_number", "310123456789013");
        $this->sellerStreet = config("zatca.seller_street", "Test Street");
        $this->sellerBuildingNumber = config("zatca.seller_building_number", "1234");
        $this->sellerPlotIdentification = config("zatca.seller_plot_identification", "1234");
        $this->sellerCitySubdivision = config("zatca.seller_city_subdivision", "District");
        $this->sellerCity = config("zatca.seller_city", "Riyadh");
        $this->sellerPostalZone = config("zatca.seller_postal_zone", "12345");
        $this->sellerCountryCode = config("zatca.seller_country_code", "SA");

        if (!Storage::disk("local")->exists("zatca")) {
            Storage::disk("local")->makeDirectory("zatca");
        }

        $this->csrFilePath = storage_path("app/zatca/csr.pem");
        $this->csrPrivateKeyPath = storage_path("app/zatca/csr_private_key.pem");
        $this->csidCertificatePath = storage_path("app/zatca/csid_certificate.pem");
        $this->csidPrivateKeyPath = storage_path("app/zatca/csid_private_key.pem");
        $this->csrPrivateKeyPassword = config("zatca.csr_private_key_password", "");
        $this->csidPrivateKeyPassword = config("zatca.csid_private_key_password", "");

        $this->zatcaApiEnvironment = config("zatca.environment", "sandbox");
        $this->complianceCsidOtp = (string) config("zatca.compliance_otp", "");
    }

    private function populateSellerParty(): Party
    {
        $seller = new Party();

        $address = (new Address())
            ->setStreetName($this->sellerStreet)
            ->setBuildingNumber($this->sellerBuildingNumber)
            ->setPlotIdentification($this->sellerPlotIdentification)
            ->setCitySubdivisionName($this->sellerCitySubdivision)
            ->setCityName($this->sellerCity)
            ->setPostalZone($this->sellerPostalZone)
            ->setCountry($this->sellerCountryCode);
        $seller->setPostalAddress($address);

        $legalEntity = (new LegalEntity())
            ->setRegistrationName($this->sellerName);
        $seller->setLegalEntity($legalEntity);

        $taxScheme = (new TaxScheme())->setId("VAT");
        $partyTaxScheme = (new PartyTaxScheme())
            ->setCompanyId($this->vatRegistrationNumber)
            ->setTaxScheme($taxScheme);
        $seller->setPartyTaxScheme($partyTaxScheme);

        return $seller;
    }

    public function generateSimplifiedInvoiceXml(array $invoiceData, string $previousInvoiceHash = null): string
    {
        $issueDateTime = Carbon::now();
        $invoice = new Invoice();
        
        $invoiceType = (new InvoiceType())
                        ->setInvoice("simplified")
                        ->setInvoiceType("invoice");
        $invoice->setInvoiceType($invoiceType);

        $invoice->setId($invoiceData["id"] ?? Str::uuid()->toString());
        $invoice->setIssueDate($issueDateTime);
        $invoice->setIssueTime($issueDateTime);
        
        $pihToUse = $previousInvoiceHash ?? "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==";
        $billingReference = (new BillingReference())->setId($pihToUse);
        $invoice->setBillingReferences([$billingReference]);

        $invoice->setAccountingSupplierParty($this->populateSellerParty());
        
        $buyer = new Party();
        if (!empty($invoiceData["buyer_name"])) {
            $buyerLegalEntity = (new LegalEntity())->setRegistrationName($invoiceData["buyer_name"]);
            $buyer->setLegalEntity($buyerLegalEntity);
        } else {
            // For simplified invoices, buyer details can be minimal or placeholder
            $buyerLegalEntity = (new LegalEntity())->setRegistrationName("-"); // Placeholder if name is mandatory
            $buyer->setLegalEntity($buyerLegalEntity);
        }
        $invoice->setAccountingCustomerParty($buyer);

        $totalTaxableAmount = 0;
        $totalVatAmount = 0;
        $invoiceLines = [];
        foreach ($invoiceData["items"] as $itemData) {
            $line = new InvoiceLine();
            $line->setId((string)$itemData["id"]);
            $line->setInvoicedQuantity((string)number_format($itemData["quantity"], 4, ".", ""));
            $priceBeforeVat = (float)$itemData["price_before_vat"];
            $vatRate = (float)$itemData["vat_rate"];
            $lineExtensionAmount = $priceBeforeVat * $itemData["quantity"];
            $lineVatAmount = $lineExtensionAmount * $vatRate;
            $line->setLineExtensionAmount((string)number_format($lineExtensionAmount, 2, ".", ""));
            
            $item = new Item();
            $item->setName($itemData["name"]);
            $classifiedTaxCategory = (new \Saleh7\Zatca\ClassifiedTaxCategory())
                ->setId("S") // Standard VAT rate
                ->setPercent((string)number_format($vatRate * 100, 2, ".", ""))
                ->setTaxScheme((new TaxScheme())->setId("VAT"));
            $item->setClassifiedTaxCategory($classifiedTaxCategory);
            $line->setItem($item);

            $price = new Price();
            $price->setPriceAmount((string)number_format($priceBeforeVat, 2, ".", ""));
            $line->setPrice($price);

            $taxTotalLine = new TaxTotal();
            $taxTotalLine->setTaxAmount((string)number_format($lineVatAmount, 2, ".", ""));
            // Note: The library might automatically add TaxSubTotal based on Item's ClassifiedTaxCategory
            // or it might need to be added explicitly if there are multiple tax categories per line.
            // For simple VAT, this might be sufficient.
            $line->setTaxTotal($taxTotalLine);
            
            $invoiceLines[] = $line;
            $totalTaxableAmount += $lineExtensionAmount;
            $totalVatAmount += $lineVatAmount;
        }
        $invoice->setInvoiceLines($invoiceLines);

        $totalAmountWithVat = $totalTaxableAmount + $totalVatAmount;
        $taxTotal = new TaxTotal();
        $taxTotal->setTaxAmount((string)number_format($totalVatAmount, 2, ".", ""));

        $taxCategoryForSimplifiedTotal = (new TaxCategory())
            ->setId('S') // Standard VAT rate category
            ->setPercent((string)number_format(($totalTaxableAmount > 0 ? ($totalVatAmount / $totalTaxableAmount) * 100 : 0), 2, ".", "")) // Calculate overall percentage
            ->setTaxScheme((new TaxScheme())->setId('VAT'));

        $taxSubTotalSimplifiedInstance = (new TaxSubTotal())
            ->setTaxableAmount((float)number_format($totalTaxableAmount, 2, ".", ""))
            ->setTaxAmount((float)number_format($totalVatAmount, 2, ".", ""))
            ->setTaxCategory($taxCategoryForSimplifiedTotal);

        $taxTotal->addTaxSubTotal($taxSubTotalSimplifiedInstance);
        $invoice->setTaxTotal($taxTotal);

        $legalMonetaryTotal = new LegalMonetaryTotal();
        $legalMonetaryTotal->setLineExtensionAmount((string)number_format($totalTaxableAmount, 2, ".", ""));
        $legalMonetaryTotal->setTaxExclusiveAmount((string)number_format($totalTaxableAmount, 2, ".", ""));
        $legalMonetaryTotal->setTaxInclusiveAmount((string)number_format($totalAmountWithVat, 2, ".", ""));
        $legalMonetaryTotal->setAllowanceTotalAmount("0.00");
        $legalMonetaryTotal->setPayableAmount((string)number_format($totalAmountWithVat, 2, ".", ""));
        $invoice->setLegalMonetaryTotal($legalMonetaryTotal);

        $paymentMeans = new PaymentMeans();
        $paymentMeans->setPaymentMeansCode("10"); // Cash
        $invoice->setPaymentMeans($paymentMeans);

        $generator = new GeneratorInvoice();
        return $generator->generate($invoice);
    }

    public function generateStandardInvoiceXml(array $invoiceData, string $previousInvoiceHash = null): string
    {
        $issueDateTime = Carbon::now();
        $invoice = new Invoice();

        $invoiceType = (new InvoiceType())
                        ->setInvoice("standard")
                        ->setInvoiceType("invoice");
        $invoice->setInvoiceType($invoiceType);

        $invoice->setId($invoiceData["id"] ?? Str::uuid()->toString());
        $invoice->setIssueDate($issueDateTime);
        $invoice->setIssueTime($issueDateTime);

        $pihToUse = $previousInvoiceHash ?? "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==";
        $billingReference = (new BillingReference())->setId($pihToUse);
        $invoice->setBillingReferences([$billingReference]);

        $invoice->setAccountingSupplierParty($this->populateSellerParty());
        
        $buyerData = $invoiceData["buyer"];
        $buyer = new Party();
        $buyerAddress = (new Address())
            ->setStreetName($buyerData["street"])
            ->setBuildingNumber($buyerData["building_number"])
            ->setPlotIdentification($buyerData["plot_identification"])
            ->setCitySubdivisionName($buyerData["city_subdivision"])
            ->setCityName($buyerData["city"])
            ->setPostalZone($buyerData["postal_zone"])
            ->setCountry($buyerData["country_code"]);
        $buyer->setPostalAddress($buyerAddress);

        $buyerLegalEntity = (new LegalEntity())
            ->setRegistrationName($buyerData["name"]);
        $buyer->setLegalEntity($buyerLegalEntity);

        if (!empty($buyerData["vat_id"])) {
            $buyerTaxScheme = (new TaxScheme())->setId("VAT");
            $buyerPartyTaxScheme = (new PartyTaxScheme())
                ->setCompanyId($buyerData["vat_id"])
                ->setTaxScheme($buyerTaxScheme);
            $buyer->setPartyTaxScheme($buyerPartyTaxScheme);
        }
        $invoice->setAccountingCustomerParty($buyer);

        if (!empty($invoiceData["delivery_date"])) {
            $delivery = new Delivery();
            $delivery->setActualDeliveryDate(Carbon::parse($invoiceData["delivery_date"])); 
            $invoice->setDelivery($delivery);
        }

        $paymentMeans = new PaymentMeans();
        $paymentMeans->setPaymentMeansCode($invoiceData["payment_means_code"] ?? "30"); 
        if(!empty($invoiceData["payment_account_id"])){
            $paymentMeans->setInstructionNote($invoiceData["payment_account_id"]);
        }
        $invoice->setPaymentMeans($paymentMeans);

        $totalTaxableAmount = 0;
        $totalVatAmount = 0;
        $invoiceLines = [];
        foreach ($invoiceData["items"] as $itemData) {
            $line = new InvoiceLine();
            $line->setId((string)$itemData["id"]);
            $line->setInvoicedQuantity((string)number_format($itemData["quantity"], 4, ".", ""));
            $priceBeforeVat = (float)$itemData["price_before_vat"];
            $vatRate = (float)$itemData["vat_rate"];
            $lineExtensionAmount = $priceBeforeVat * $itemData["quantity"];
            $lineVatAmount = $lineExtensionAmount * $vatRate;
            $line->setLineExtensionAmount((string)number_format($lineExtensionAmount, 2, ".", ""));

            $item = new Item();
            $item->setName($itemData["name"]);
            $classifiedTaxCategory = (new \Saleh7\Zatca\ClassifiedTaxCategory())
                ->setId("S")
                ->setPercent((string)number_format($vatRate * 100, 2, ".", ""))
                ->setTaxScheme((new TaxScheme())->setId("VAT"));
            $item->setClassifiedTaxCategory($classifiedTaxCategory);
            $line->setItem($item);

            $price = new Price();
            $price->setPriceAmount((string)number_format($priceBeforeVat, 2, ".", ""));
            $line->setPrice($price);

            $taxTotalLine = new TaxTotal();
            $taxTotalLine->setTaxAmount((string)number_format($lineVatAmount, 2, ".", ""));
            $line->setTaxTotal($taxTotalLine);

            $invoiceLines[] = $line;
            $totalTaxableAmount += $lineExtensionAmount;
            $totalVatAmount += $lineVatAmount;
        }
        $invoice->setInvoiceLines($invoiceLines);

        $totalAmountWithVat = $totalTaxableAmount + $totalVatAmount;
        $taxTotal = new TaxTotal();
        $taxTotal->setTaxAmount((string)number_format($totalVatAmount, 2, ".", ""));
        
        $taxCategoryForStandardTotal = (new TaxCategory())
            ->setId('S') // Standard VAT rate category
            ->setPercent((string)number_format(($totalTaxableAmount > 0 ? ($totalVatAmount / $totalTaxableAmount) * 100 : 0), 2, ".", "")) // Calculate overall percentage
            ->setTaxScheme((new TaxScheme())->setId('VAT'));

        $taxSubTotalStandardInstance = (new TaxSubTotal())
            ->setTaxableAmount((float)number_format($totalTaxableAmount, 2, ".", ""))
            ->setTaxAmount((float)number_format($totalVatAmount, 2, ".", ""))
            ->setTaxCategory($taxCategoryForStandardTotal);
            
        $taxTotal->addTaxSubTotal($taxSubTotalStandardInstance);
        $invoice->setTaxTotal($taxTotal);

        $legalMonetaryTotal = new LegalMonetaryTotal();
        $legalMonetaryTotal->setLineExtensionAmount((string)number_format($totalTaxableAmount, 2, ".", ""));
        $legalMonetaryTotal->setTaxExclusiveAmount((string)number_format($totalTaxableAmount, 2, ".", ""));
        $legalMonetaryTotal->setTaxInclusiveAmount((string)number_format($totalAmountWithVat, 2, ".", ""));
        $legalMonetaryTotal->setAllowanceTotalAmount("0.00");
        $legalMonetaryTotal->setPayableAmount((string)number_format($totalAmountWithVat, 2, ".", ""));
        $invoice->setLegalMonetaryTotal($legalMonetaryTotal);

        $generator = new GeneratorInvoice();
        return $generator->generate($invoice);
    }

    public function generateCsr(
        string $commonName, 
        string $vatNumber, 
        string $organizationName, 
        string $organizationUnitName, 
        string $countryCode, 
        string $invoiceType, // This is for CSR field, usually "1100"
        string $locationAddress, 
        string $industryBusinessCategory,
        string $egsSolutionName = "DefaultSolution",
        string $egsModel = "DefaultModel",
        string $egsSerialNumber = "DefaultEGS123"
    ): array
    {
        try {
            (new CertificateBuilder())
                ->setOrganizationIdentifier($vatNumber) 
                ->setCommonName($commonName)
                ->setOrganizationName($organizationName)
                ->setOrganizationalUnitName($organizationUnitName)
                ->setCountryName($countryCode)
                ->setInvoiceType($invoiceType) // This is a string for CSR, e.g., "1100"
                ->setAddress($locationAddress) 
                ->setBusinessCategory($industryBusinessCategory)
                ->setProduction($this->zatcaApiEnvironment === 'production')
                ->setSerialNumber($egsSolutionName, $egsModel, $egsSerialNumber) 
                ->generateAndSave($this->csrFilePath, $this->csrPrivateKeyPath);
            
            Log::info("CSR and Private Key generated and saved.", ["csr_path" => $this->csrFilePath, "private_key_path" => $this->csrPrivateKeyPath]);
            
            $csrContent = Storage::disk("local")->get(str_replace(storage_path("app/"), "", $this->csrFilePath));
            return ["csr_path" => $this->csrFilePath, "private_key_path" => $this->csrPrivateKeyPath, "csr_content" => $csrContent];
        } catch (CertificateBuilderException $e) {
            Log::error("Error generating CSR: " . $e->getMessage());
            throw $e;
        }
    }

    public function requestComplianceCSID(string $otp): array
    {
        if (!file_exists($this->csrFilePath)) {
            throw new \Exception("CSR file not found. Please generate CSR first.");
        }
        $csrContent = file_get_contents($this->csrFilePath);

        $api = new ZatcaAPI($this->zatcaApiEnvironment);
        try {
            $complianceResult = $api->requestComplianceCertificate($csrContent, $otp);

            $certificateContent = $complianceResult->getCertificate(); // Already decoded by the library
            $secret = $complianceResult->getSecret();
            $requestId = $complianceResult->getRequestId();

            if (!empty($certificateContent) && !empty($secret)) {
                Storage::disk('local')->put('zatca/csid_certificate.pem', $certificateContent);
                Storage::disk('local')->put('zatca/csid_private_key.pem', $secret);
                Log::info("Compliance CSID obtained and saved.", ["certificate_path" => $this->csidCertificatePath, "secret_path" => $this->csidPrivateKeyPath]);
                return [
                    "certificate_path" => $this->csidCertificatePath,
                    "private_key_path" => $this->csidPrivateKeyPath,
                    "request_id" => $requestId
                ];
            } else {
                Log::error("Failed to obtain Compliance CSID. Empty certificate or secret from ComplianceCertificateResult.", ['request_id' => $requestId]);
                throw new \Exception("Failed to obtain Compliance CSID. Empty certificate or secret from ComplianceCertificateResult.");
            }
        } catch (ZatcaApiException $e) {
            Log::error("ZATCA API Error during compliance CSID request: " . $e->getMessage());
            throw $e;
        }
    }

    public function signInvoice(string $invoiceXml): string
    {
        if (!file_exists($this->csidCertificatePath) || !file_exists($this->csidPrivateKeyPath)) {
            throw new \Exception("CSID certificate or private key not found. Please obtain CSID first.");
        }

        $certificateContent = file_get_contents($this->csidCertificatePath);
        $privateKeyContent = file_get_contents($this->csidPrivateKeyPath);

        $generator = new GeneratorInvoice();
        return $generator->sign($invoiceXml, $certificateContent, $privateKeyContent, $this->csidPrivateKeyPassword);
    }

    public function validateInvoice(string $signedInvoiceXml): array
    {
        $api = new ZatcaAPI($this->zatcaApiEnvironment);
        try {
            return $api->validateInvoice($signedInvoiceXml, $this->csidCertificatePath, $this->csidPrivateKeyPath);
        } catch (ZatcaApiException $e) {
            Log::error("ZATCA API Error during invoice validation: " . $e->getMessage(), ['errors' => $e->getErrors()]);
            throw $e;
        }
    }

    public function reportInvoice(string $signedInvoiceXml, string $invoiceHash, string $invoiceUuid): array
    {
        $api = new ZatcaAPI($this->zatcaApiEnvironment);
        try {
            return $api->reportInvoice($signedInvoiceXml, $invoiceHash, $invoiceUuid, $this->csidCertificatePath, $this->csidPrivateKeyPath);
        } catch (ZatcaApiException $e) {
            Log::error("ZATCA API Error during invoice reporting: " . $e->getMessage(), ['errors' => $e->getErrors()]);
            throw $e;
        }
    }

    public function clearInvoice(string $signedInvoiceXml, string $invoiceHash, string $invoiceUuid): array
    {
        $api = new ZatcaAPI($this->zatcaApiEnvironment);
        try {
            return $api->clearInvoice($signedInvoiceXml, $invoiceHash, $invoiceUuid, $this->csidCertificatePath, $this->csidPrivateKeyPath);
        } catch (ZatcaApiException $e) {
            Log::error("ZATCA API Error during invoice clearance: " . $e->getMessage(), ['errors' => $e->getErrors()]);
            throw $e;
        }
    }

    public function getCsidCertificatePath(): string
    {
        return $this->csidCertificatePath;
    }

    public function getCsidPrivateKeyPath(): string
    {
        return $this->csidPrivateKeyPath;
    }
}

