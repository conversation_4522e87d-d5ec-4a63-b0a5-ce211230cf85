<?php

namespace App\Models;

use App\Models\Modules\Subscriptions\SubscriptionPlan;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceLimit extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_plan_id',
        'monthly_invoice_limit',
        'total_invoice_limit',
        'is_active',
    ];

    protected $casts = [
        'monthly_invoice_limit' => 'integer',
        'total_invoice_limit' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * العلاقة مع خطة الاشتراك
     */
    public function subscriptionPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }
}
