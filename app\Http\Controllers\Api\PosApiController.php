<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PaymentTerminalService;
use App\Models\PaymentTransaction; // Assuming you have this model
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PosApiController extends Controller
{
    protected $paymentTerminalService;

    public function __construct(PaymentTerminalService $paymentTerminalService)
    {
        $this->paymentTerminalService = $paymentTerminalService;
    }

    /**
     * Initiates a payment transaction with the physical payment terminal.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function initiateNetworkPayment(Request $request)
    {
        Log::info("PosApiController: Received request for initiateNetworkPayment", $request->all());

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'order_id' => 'required|string|max:255', // Changed from transaction_id to match table
            'currency' => 'sometimes|string|size:3',
            // Add any other necessary validation rules here, e.g., customer_id if applicable
        ]);

        if ($validator->fails()) {
            Log::warning("PosApiController: Validation failed for initiateNetworkPayment", $validator->errors()->toArray());
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $validator->errors()], 422);
        }

        $amount = (float) $request->input('amount');
        $orderId = $request->input('order_id');
        $currency = $request->input('currency', 'SAR');
        $userId = Auth::id(); // Get authenticated user ID if applicable

        // Create an initial transaction record
        $transaction = PaymentTransaction::create([
            'order_id' => $orderId,
            'payment_method' => 'network_pos',
            'amount' => $amount,
            'currency' => $currency,
            'status' => 'PENDING_TERMINAL',
            'user_id' => $userId,
            // 'customer_id' => $request->input('customer_id'), // If provided
        ]);

        try {
            $result = $this->paymentTerminalService->initiatePayment($amount, $transaction->id); // Pass internal transaction ID
            
            Log::info("PosApiController: PaymentTerminalService result for order {$orderId}, transaction {$transaction->id}", $result);

            if ($result['success'] && isset($result['data'])) {
                $transaction->status = $result['data']['status'] ?? 'UNKNOWN'; // e.g., APPROVED, DECLINED
                $transaction->terminal_transaction_id = $result['data']['terminal_transaction_id'] ?? null;
                $transaction->approval_code = $result['data']['approval_code'] ?? null;
                $transaction->masked_pan = $result['data']['masked_pan'] ?? null;
                $transaction->card_type = $result['data']['card_type'] ?? null;
                $transaction->terminal_id = $result['data']['terminal_id'] ?? null;
                $transaction->response_data = json_encode($result['data']);
                $transaction->save();

                return response()->json(['success' => true, 'message' => $result['message'] ?? 'Transaction processed.', 'data' => $result['data']]);
            } else {
                $transaction->status = 'ERROR_TERMINAL';
                $transaction->error_message = $result['message'] ?? 'Terminal communication error or payment declined.';
                $transaction->response_data = json_encode($result); // Save the full error response
                $transaction->save();
                Log::error("PosApiController: Payment failed or terminal error for order {$orderId}, transaction {$transaction->id}", $result);
                return response()->json(['success' => false, 'message' => $transaction->error_message, 'data' => $result], 400);
            }
        } catch (\Exception $e) {
            Log::error("PosApiController: Exception during initiateNetworkPayment for order {$orderId}, transaction {$transaction->id}: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            $transaction->status = 'EXCEPTION';
            $transaction->error_message = 'System error: ' . $e->getMessage();
            $transaction->save();
            return response()->json(['success' => false, 'message' => 'An unexpected system error occurred while processing the payment.'], 500);
        }
    }
}

