@extends('layouts.admin')

@section('title', isset($representative) ? 'تعديل المندوب' : 'إضافة مندوب جديد')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">
            <i class="bi bi-person-badge-fill text-primary me-2"></i>
            {{ isset($representative) ? 'تعديل المندوب' : 'إضافة مندوب جديد' }}
        </h2>
        <a href="{{ route('admin.sales-representatives.representatives.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            العودة للقائمة
        </a>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <form method="POST" 
          action="{{ isset($representative) ? route('admin.sales-representatives.representatives.update', $representative) : route('admin.sales-representatives.representatives.store') }}" 
          enctype="multipart/form-data">
        @csrf
        @if(isset($representative))
            @method('PUT')
        @endif

        <div class="row">
            <!-- المعلومات الأساسية -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-fill me-2"></i>
                            المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="employee_code" class="form-label">كود الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('employee_code') is-invalid @enderror" 
                                       id="employee_code" name="employee_code" 
                                       value="{{ old('employee_code', $representative->employee_code ?? '') }}" required>
                                @error('employee_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" 
                                       value="{{ old('name', $representative->name ?? '') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" 
                                       value="{{ old('phone', $representative->phone ?? '') }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" 
                                       value="{{ old('email', $representative->email ?? '') }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="national_id" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                                       id="national_id" name="national_id" 
                                       value="{{ old('national_id', $representative->national_id ?? '') }}" required>
                                @error('national_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                                       id="birth_date" name="birth_date" 
                                       value="{{ old('birth_date', isset($representative) ? $representative->birth_date?->format('Y-m-d') : '') }}">
                                @error('birth_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="3">{{ old('address', $representative->address ?? '') }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-briefcase-fill me-2"></i>
                            معلومات العمل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="hire_date" class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('hire_date') is-invalid @enderror" 
                                       id="hire_date" name="hire_date" 
                                       value="{{ old('hire_date', isset($representative) ? $representative->hire_date?->format('Y-m-d') : '') }}" required>
                                @error('hire_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                <select class="form-select @error('branch_id') is-invalid @enderror" 
                                        id="branch_id" name="branch_id" required>
                                    <option value="">اختر الفرع</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}" 
                                                {{ old('branch_id', $representative->branch_id ?? '') == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('branch_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="manager_id" class="form-label">المدير المباشر</label>
                                <select class="form-select @error('manager_id') is-invalid @enderror" 
                                        id="manager_id" name="manager_id">
                                    <option value="">اختر المدير</option>
                                    @foreach($managers as $manager)
                                        <option value="{{ $manager->id }}" 
                                                {{ old('manager_id', $representative->manager_id ?? '') == $manager->id ? 'selected' : '' }}>
                                            {{ $manager->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('manager_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="active" {{ old('status', $representative->status ?? 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status', $representative->status ?? '') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                    <option value="suspended" {{ old('status', $representative->status ?? '') == 'suspended' ? 'selected' : '' }}>معلق</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="base_salary" class="form-label">الراتب الأساسي</label>
                                <div class="input-group">
                                    <input type="number" class="form-control @error('base_salary') is-invalid @enderror" 
                                           id="base_salary" name="base_salary" step="0.01" min="0"
                                           value="{{ old('base_salary', $representative->base_salary ?? '') }}">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                @error('base_salary')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="target_amount" class="form-label">الهدف الشهري</label>
                                <div class="input-group">
                                    <input type="number" class="form-control @error('target_amount') is-invalid @enderror" 
                                           id="target_amount" name="target_amount" step="0.01" min="0"
                                           value="{{ old('target_amount', $representative->target_amount ?? '') }}">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                @error('target_amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمولة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-cash-coin me-2"></i>
                            معلومات العمولة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="commission_type" class="form-label">نوع العمولة <span class="text-danger">*</span></label>
                                <select class="form-select @error('commission_type') is-invalid @enderror" 
                                        id="commission_type" name="commission_type" required>
                                    <option value="percentage" {{ old('commission_type', $representative->commission_type ?? 'percentage') == 'percentage' ? 'selected' : '' }}>نسبة مئوية</option>
                                    <option value="fixed" {{ old('commission_type', $representative->commission_type ?? '') == 'fixed' ? 'selected' : '' }}>مبلغ ثابت</option>
                                    <option value="tiered" {{ old('commission_type', $representative->commission_type ?? '') == 'tiered' ? 'selected' : '' }}>شرائح</option>
                                </select>
                                @error('commission_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6" id="commission_rate_field">
                                <label for="commission_rate" class="form-label">نسبة/مبلغ العمولة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control @error('commission_rate') is-invalid @enderror" 
                                           id="commission_rate" name="commission_rate" step="0.01" min="0"
                                           value="{{ old('commission_rate', $representative->commission_rate ?? '') }}">
                                    <span class="input-group-text" id="commission_unit">%</span>
                                </div>
                                @error('commission_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12" id="commission_tiers_field" style="display: none;">
                                <label class="form-label">شرائح العمولة</label>
                                <div id="tiers_container">
                                    <!-- سيتم إضافة الشرائح هنا بواسطة JavaScript -->
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTier()">
                                    <i class="bi bi-plus me-1"></i>إضافة شريحة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الصورة والمعلومات الإضافية -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-image me-2"></i>
                            الصورة الشخصية
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            @if(isset($representative) && $representative->profile_image)
                                <img src="{{ asset('storage/' . $representative->profile_image) }}" 
                                     alt="{{ $representative->name }}" 
                                     class="rounded-circle mb-3" 
                                     width="120" height="120" id="preview_image">
                            @else
                                <div class="bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                     style="width: 120px; height: 120px;" id="preview_placeholder">
                                    <i class="bi bi-person-fill text-muted" style="font-size: 3rem;"></i>
                                </div>
                            @endif
                        </div>
                        <input type="file" class="form-control @error('profile_image') is-invalid @enderror" 
                               id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)">
                        @error('profile_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">الحد الأقصى: 2 ميجابايت</small>
                    </div>
                </div>

                <!-- معلومات المركبة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-car-front me-2"></i>
                            معلومات المركبة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="vehicle_type" class="form-label">نوع المركبة</label>
                            <input type="text" class="form-control @error('vehicle_type') is-invalid @enderror" 
                                   id="vehicle_type" name="vehicle_type" 
                                   value="{{ old('vehicle_type', $representative->vehicle_type ?? '') }}">
                            @error('vehicle_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="vehicle_number" class="form-label">رقم المركبة</label>
                            <input type="text" class="form-control @error('vehicle_number') is-invalid @enderror" 
                                   id="vehicle_number" name="vehicle_number" 
                                   value="{{ old('vehicle_number', $representative->vehicle_number ?? '') }}">
                            @error('vehicle_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="license_number" class="form-label">رقم الرخصة</label>
                            <input type="text" class="form-control @error('license_number') is-invalid @enderror" 
                                   id="license_number" name="license_number" 
                                   value="{{ old('license_number', $representative->license_number ?? '') }}">
                            @error('license_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="license_expiry" class="form-label">انتهاء الرخصة</label>
                            <input type="date" class="form-control @error('license_expiry') is-invalid @enderror" 
                                   id="license_expiry" name="license_expiry" 
                                   value="{{ old('license_expiry', isset($representative) ? $representative->license_expiry?->format('Y-m-d') : '') }}">
                            @error('license_expiry')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-chat-text me-2"></i>
                            ملاحظات
                        </h5>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                  id="notes" name="notes" rows="4" 
                                  placeholder="أي ملاحظات إضافية...">{{ old('notes', $representative->notes ?? '') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- حالة النشاط -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ old('is_active', $representative->is_active ?? true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <strong>مندوب نشط</strong>
                                <br><small class="text-muted">يمكن للمندوب تسجيل الدخول وأداء المهام</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.sales-representatives.representatives.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>
                                {{ isset($representative) ? 'تحديث المندوب' : 'إضافة المندوب' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
// Preview uploaded image
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('preview_image');
            const placeholder = document.getElementById('preview_placeholder');
            
            if (preview) {
                preview.src = e.target.result;
            } else if (placeholder) {
                placeholder.innerHTML = `<img src="${e.target.result}" class="rounded-circle" width="120" height="120" id="preview_image">`;
            }
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Handle commission type change
document.getElementById('commission_type').addEventListener('change', function() {
    const commissionType = this.value;
    const rateField = document.getElementById('commission_rate_field');
    const tiersField = document.getElementById('commission_tiers_field');
    const unit = document.getElementById('commission_unit');
    
    if (commissionType === 'tiered') {
        rateField.style.display = 'none';
        tiersField.style.display = 'block';
    } else {
        rateField.style.display = 'block';
        tiersField.style.display = 'none';
        
        if (commissionType === 'percentage') {
            unit.textContent = '%';
        } else {
            unit.textContent = 'ريال';
        }
    }
});

// Add commission tier
let tierIndex = 0;
function addTier() {
    const container = document.getElementById('tiers_container');
    const tierHtml = `
        <div class="tier-row mb-3 p-3 border rounded" id="tier_${tierIndex}">
            <div class="row g-2">
                <div class="col-md-3">
                    <label class="form-label">الحد الأدنى</label>
                    <input type="number" class="form-control" name="tiers[${tierIndex}][min_amount]" step="0.01" min="0">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحد الأقصى</label>
                    <input type="number" class="form-control" name="tiers[${tierIndex}][max_amount]" step="0.01" min="0">
                </div>
                <div class="col-md-3">
                    <label class="form-label">النسبة (%)</label>
                    <input type="number" class="form-control" name="tiers[${tierIndex}][rate]" step="0.01" min="0" max="100">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeTier(${tierIndex})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', tierHtml);
    tierIndex++;
}

function removeTier(index) {
    document.getElementById(`tier_${index}`).remove();
}

// Initialize commission type on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('commission_type').dispatchEvent(new Event('change'));
    
    // Load existing tiers if editing
    @if(isset($representative) && $representative->commission_tiers)
        const existingTiers = @json($representative->commission_tiers);
        existingTiers.forEach(tier => {
            addTier();
            const currentTier = document.getElementById(`tier_${tierIndex - 1}`);
            currentTier.querySelector('input[name*="[min_amount]"]').value = tier.min_amount || '';
            currentTier.querySelector('input[name*="[max_amount]"]').value = tier.max_amount || '';
            currentTier.querySelector('input[name*="[rate]"]').value = tier.rate || '';
        });
    @endif
});
</script>
@endpush
