@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("admin_views.work_orders_list") }}</h1>
    <a href="{{ route("admin.work_orders.create") }}" class="btn btn-primary mb-3">{{ __("admin_views.add_new_work_order") }}</a>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif
    @if(session("error"))
        <div class="alert alert-danger">
            {{ session("error") }}
        </div>
    @endif

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>{{ __("admin_views.work_order_code") }}</th>
                <th>{{ __("admin_views.item_to_produce") }}</th>
                <th>{{ __("admin_views.bom") }}</th>
                <th>{{ __("admin_views.quantity_to_produce") }}</th>
                <th>{{ __("admin_views.status") }}</th>
                <th>{{ __("admin_views.branch") }}</th>
                <th>{{ __("admin_views.start_date") }}</th>
                <th>{{ __("admin_views.end_date") }}</th>
                <th>{{ __("admin_views.actions") }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($workOrders as $workOrder)
                <tr>
                    <td>{{ $workOrder->id }}</td>
                    <td>{{ $workOrder->work_order_code }}</td>
                    <td>{{ $workOrder->item ? $workOrder->item->name_ar : "-" }}</td>
                    <td>{{ $workOrder->bom ? $workOrder->bom->name_ar : "-" }}</td>
                    <td>{{ number_format($workOrder->quantity_to_produce, 4) }}</td>
                    <td>{{ __("admin_views.work_order_status_" . $workOrder->status) }}</td>
                    <td>{{ $workOrder->branch ? $workOrder->branch->name_ar : "-" }}</td>
                    <td>{{ $workOrder->start_date ? $workOrder->start_date->format("Y-m-d") : "-" }}</td>
                    <td>{{ $workOrder->end_date ? $workOrder->end_date->format("Y-m-d") : "-" }}</td>
                    <td>
                        <a href="{{ route("admin.work_orders.show", $workOrder->id) }}" class="btn btn-info btn-sm">{{ __("admin_views.view") }}</a>
                        @if($workOrder->status == "planned" || $workOrder->status == "in_progress")
                        <a href="{{ route("admin.work_orders.edit", $workOrder->id) }}" class="btn btn-warning btn-sm">{{ __("admin_views.edit") }}</a>
                        @endif
                        @if($workOrder->status == "planned")
                        <form action="{{ route("admin.work_orders.destroy", $workOrder->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("{{ __("admin_views.confirm_delete") }}")">{{ __("admin_views.delete") }}</button>
                        </form>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">{{ __("admin_views.no_work_orders_found") }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $workOrders->links() }}
</div>
@endsection

