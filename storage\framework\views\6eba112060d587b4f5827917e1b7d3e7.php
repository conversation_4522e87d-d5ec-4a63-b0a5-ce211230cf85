<?php $__env->startSection('title', 'إدارة مدفوعات الاشتراكات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">مدفوعات الاشتراكات</h3>
                    <a href="<?php echo e(route('admin.subscriptions.payments.create')); ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة مدفوعة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الإيصال</th>
                                    <th>الاشتراك</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الدفع</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($payment->id); ?></td>
                                        <td><?php echo e($payment->receipt_number); ?></td>
                                        <td>
                                            <?php if($payment->subscription): ?>
                                                <a href="<?php echo e(route('admin.subscriptions.subscriptions.show', $payment->subscription)); ?>">
                                                    <?php echo e($payment->subscription->subscription_number); ?>

                                                </a>
                                            <?php else: ?>
                                                غير محدد
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($payment->subscription && $payment->subscription->tenant): ?>
                                                <?php echo e($payment->subscription->tenant->name); ?>

                                            <?php else: ?>
                                                غير محدد
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($payment->formatted_amount); ?></td>
                                        <td><?php echo e($payment->payment_date->format('Y-m-d')); ?></td>
                                        <td><?php echo e($payment->payment_method); ?></td>
                                        <td>
                                            <?php if($payment->status == 'paid'): ?>
                                                <span class="badge bg-success"><?php echo e($payment->status_arabic); ?></span>
                                            <?php elseif($payment->status == 'pending'): ?>
                                                <span class="badge bg-warning"><?php echo e($payment->status_arabic); ?></span>
                                            <?php elseif($payment->status == 'failed'): ?>
                                                <span class="badge bg-danger"><?php echo e($payment->status_arabic); ?></span>
                                            <?php elseif($payment->status == 'refunded'): ?>
                                                <span class="badge bg-info"><?php echo e($payment->status_arabic); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo e($payment->status); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.subscriptions.payments.show', $payment)); ?>" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.subscriptions.payments.edit', $payment)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.subscriptions.payments.destroy', $payment)); ?>" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المدفوعة؟')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد مدفوعات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <?php echo e($payments->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/subscriptions/payments/index.blade.php ENDPATH**/ ?>