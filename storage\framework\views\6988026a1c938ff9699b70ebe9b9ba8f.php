<?php $__env->startSection('title', 'إدارة الاشتراكات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">الاشتراكات</h3>
                    <a href="<?php echo e(route('admin.subscriptions.subscriptions.create')); ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة اشتراك جديد
                    </a>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الاشتراك</th>
                                    <th>العميل</th>
                                    <th>الخطة</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($subscription->id); ?></td>
                                        <td><?php echo e($subscription->subscription_number); ?></td>
                                        <td><?php echo e($subscription->tenant->name ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($subscription->plan->name ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($subscription->start_date->format('Y-m-d')); ?></td>
                                        <td><?php echo e($subscription->end_date->format('Y-m-d')); ?></td>
                                        <td>
                                            <?php if($subscription->status == 'active'): ?>
                                                <span class="badge bg-success"><?php echo e($subscription->status_arabic); ?></span>
                                            <?php elseif($subscription->status == 'pending'): ?>
                                                <span class="badge bg-warning"><?php echo e($subscription->status_arabic); ?></span>
                                            <?php elseif($subscription->status == 'expired'): ?>
                                                <span class="badge bg-danger"><?php echo e($subscription->status_arabic); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo e($subscription->status_arabic); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($subscription->formatted_price_paid); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.subscriptions.subscriptions.show', $subscription)); ?>" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.subscriptions.subscriptions.edit', $subscription)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.subscriptions.subscriptions.destroy', $subscription)); ?>" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد اشتراكات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <?php echo e($subscriptions->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/subscriptions/index.blade.php ENDPATH**/ ?>