<?php

use App\Http\Controllers\Tenant\DashboardController;
use App\Http\Controllers\Tenant\RegisterController;
use App\Http\Controllers\Tenant\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| هنا يتم تعريف المسارات الخاصة بالمستأجرين (العملاء)
|
*/

// مسارات التسجيل للمستأجرين
Route::middleware('guest')->group(function () {
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('tenant.register');
    Route::post('/register', [RegisterController::class, 'register'])->name('tenant.register.submit');
});

// مسارات المستأجرين المسجلين
Route::middleware(['auth', 'tenant'])->prefix('tenant')->group(function () {
    // لوحة التحكم
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('tenant.dashboard');

    // إدارة الاشتراك
    Route::get('/subscription', [DashboardController::class, 'subscription'])->name('tenant.subscription');

    // إدارة المستخدمين
    Route::get('/users', [DashboardController::class, 'users'])->name('tenant.users');
    Route::get('/users/create', [UserController::class, 'create'])->name('tenant.users.create');
    Route::post('/users', [UserController::class, 'store'])->name('tenant.users.store');
    Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('tenant.users.edit');
    Route::put('/users/{user}', [UserController::class, 'update'])->name('tenant.users.update');
    Route::patch('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('tenant.users.toggle-status');

    // يمكن إضافة المزيد من المسارات هنا حسب احتياجات النظام
});
