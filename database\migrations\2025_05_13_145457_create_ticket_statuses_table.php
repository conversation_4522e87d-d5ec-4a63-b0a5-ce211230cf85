<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("ticket_statuses")) {
            Schema::create("ticket_statuses", function (Blueprint $table) {
                $table->id();
                // Add any other columns for ticket_statuses here if they were intended
                // For example:
                // $table->string("name")->unique();
                // $table->string("color")->nullable(); // e.g., for UI display
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("ticket_statuses");
    }
};

