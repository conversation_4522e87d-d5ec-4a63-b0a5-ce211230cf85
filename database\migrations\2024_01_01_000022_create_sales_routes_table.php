<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_routes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم خط السير
            $table->string('code')->unique(); // كود خط السير
            $table->text('description')->nullable(); // وصف خط السير
            $table->enum('route_type', ['daily', 'weekly', 'monthly', 'custom'])->default('daily'); // نوع خط السير
            $table->json('schedule_days')->nullable(); // أيام الجدولة
            $table->time('start_time')->nullable(); // وقت البداية
            $table->time('end_time')->nullable(); // وقت النهاية
            $table->integer('estimated_duration')->default(480); // المدة المقدرة (بالدقائق)
            $table->decimal('estimated_distance', 8, 2)->default(0); // المسافة المقدرة (كم)
            $table->decimal('fuel_allowance', 8, 2)->default(0); // بدل الوقود
            $table->integer('max_customers')->default(20); // الحد الأقصى للعملاء
            $table->integer('priority_level')->default(1); // مستوى الأولوية
            $table->json('route_points')->nullable(); // نقاط خط السير
            $table->text('special_instructions')->nullable(); // تعليمات خاصة
            $table->boolean('requires_vehicle')->default(true); // يتطلب مركبة
            $table->boolean('is_active')->default(true);
            $table->foreignId('sales_representative_id')->nullable()->constrained('sales_representatives')->onDelete('set null');
            $table->foreignId('sales_area_id')->constrained('sales_areas')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_routes');
    }
};
