@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Payment Terminal Details & Logs: {{-- ucfirst($terminal_id) --}} Default Terminal</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <div class="card mb-3">
        <div class="card-header">Configuration Summary</div>
        <div class="card-body">
            <p><strong>Provider:</strong> {{-- $settings->{$terminal_id."_provider_name"} ?? "Local Bank POS" --}} Local Bank POS</p>
            <p><strong>Model:</strong> {{-- $settings->{$terminal_id."_terminal_model"} ?? "Ingenico ICT250" --}} Ingenico ICT250</p>
            <p><strong>Status:</strong> {{-- ($settings->{$terminal_id."_enabled"} ?? false) ? "Enabled" : "Disabled" --}} Enabled</p>
            <p><strong>Connection Type:</strong> {{-- ucfirst($settings->{$terminal_id."_connection_type"} ?? "ip") --}} IP/Network</p>
            <p><strong>IP Address:</strong> {{-- $settings->{$terminal_id."_ip_address"} ?? "*************" --}} *************</p>
            <p><strong>Port:</strong> {{-- $settings->{$terminal_id."_port"} ?? "9100" --}} 9100</p>
            <a href="{{-- route("admin.integrations.payment_terminals.edit", ["terminal_id" => $terminal_id]) --}}" class="btn btn-sm btn-warning">Edit Configuration</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Transaction Logs (Last 50 Entries for this Terminal)</div>
        <div class="card-body">
            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Sale ID</th>
                        <th>Amount</th>
                        <th>Card Type</th>
                        <th>Transaction ID</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- @forelse($logs as $log) --}}
                    {{-- Replace with actual log data --}}
                    <tr>
                        <td>2023-05-13 12:30:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->sale_id) --}}">S1002</a></td>
                        <td>120.50</td>
                        <td>Visa **** 1234</td>
                        <td>TXN_ABC123</td>
                        <td><span class="badge bg-success">Approved</span></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 11:15:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->sale_id) --}}">S1001</a></td>
                        <td>75.00</td>
                        <td>Mada **** 5678</td>
                        <td>TXN_DEF456</td>
                        <td><span class="badge bg-danger">Declined</span></td>
                    </tr>
                    {{-- @empty
                    <tr>
                        <td colspan="6" class="text-center">No transaction logs found for this terminal.</td>
                    </tr>
                    @endforelse --}}
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-3">
        <a href="{{ route("admin.integrations.payment_terminals.index") }}" class="btn btn-secondary">Back to Payment Terminals</a>
    </div>
</div>
@endsection

