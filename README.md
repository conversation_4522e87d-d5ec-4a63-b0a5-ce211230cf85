<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

# نظام المحاسبة

## إعداد المشروع

### متطلبات النظام
- PHP >= 8.1
- MySQL >= 5.7
- Composer
- Node.js & NPM

### خطوات التثبيت

1. قم بنسخ المشروع:
```bash
git clone https://github.com/yourusername/accounting_project.git
cd accounting_project
```

2. قم بتثبيت اعتماديات PHP:
```bash
composer install
```

3. قم بتثبيت اعتماديات JavaScript:
```bash
npm install
npm run build
```

4. قم بنسخ ملف .env.example إلى .env:
```bash
cp .env.example .env
```

5. قم بإنشاء مفتاح التطبيق:
```bash
php artisan key:generate
```

### إعداد قاعدة البيانات MySQL

1. تأكد من تثبيت MySQL على جهازك:
   - للويندوز: يمكنك تثبيت [XAMPP](https://www.apachefriends.org/download.html) أو [WAMP](https://www.wampserver.com/en/) أو [MySQL Community Server](https://dev.mysql.com/downloads/mysql/)
   - للماك: يمكنك تثبيت [MAMP](https://www.mamp.info/en/downloads/) أو استخدام Homebrew `brew install mysql`
   - للينكس: `sudo apt install mysql-server` (أوبونتو) أو `sudo yum install mysql-server` (سينتوس)

2. قم بإنشاء قاعدة بيانات MySQL جديدة:
```sql
CREATE DATABASE accounting_project CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. قم بإنشاء مستخدم لقاعدة البيانات (اختياري):
```sql
CREATE USER 'accounting_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON accounting_project.* TO 'accounting_user'@'localhost';
FLUSH PRIVILEGES;
```

4. قم بتعديل ملف .env لإعداد اتصال MySQL:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=accounting_project
DB_USERNAME=root  # أو accounting_user إذا أنشأت مستخدم جديد
DB_PASSWORD=your_password
```

5. قم بنسخ ملف تكوين MySQL (اختياري):
```bash
# لنظام ويندوز
copy mysql-config\my.cnf C:\ProgramData\MySQL\MySQL Server 8.0\

# لنظام لينكس/ماك
sudo cp mysql-config/my.cnf /etc/mysql/conf.d/accounting-project.cnf
sudo systemctl restart mysql  # إعادة تشغيل MySQL
```

6. قم بتشغيل الترحيلات وزراعة البيانات:
```bash
php artisan migrate:fresh --seed
```

7. إذا واجهت مشكلة في الترحيلات، جرب:
```bash
# إعادة تعيين قاعدة البيانات
php artisan db:wipe

# تشغيل الترحيلات مع تجاهل الأخطاء
php artisan migrate --force

# زراعة البيانات
php artisan db:seed
```

### تشغيل المشروع

```bash
php artisan serve
```

يمكنك الآن الوصول إلى المشروع على العنوان: http://localhost:8000

### حسابات المستخدمين

- **السوبر أدمن**:
  - البريد الإلكتروني: <EMAIL>
  - كلمة المرور: password

## نظام السوبر أدمن

يتيح نظام السوبر أدمن التحكم في:
- إدارة الباقات والاشتراكات
- تحديد حدود الفواتير لكل باقة
- إدارة المستخدمين والصلاحيات

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
