@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($configuration) ? "Edit" : "Add New" }} WhatsApp Configuration</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <strong>Whoops!</strong> There were some problems with your input.<br><br>
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($configuration) ? route('admin.whatsapp_integration.configurations.update', $configuration->id) : route('admin.whatsapp_integration.configurations.store') }}" method="POST">
        @csrf
        @if(isset($configuration))
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Provider Name:</strong>
                    <input type="text" name="provider_name" value="{{ old('provider_name', $configuration->provider_name ?? '') }}" class="form-control" placeholder="e.g., Twilio, Meta">
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Phone Number ID:</strong>
                    <input type="text" name="phone_number_id" value="{{ old('phone_number_id', $configuration->phone_number_id ?? '') }}" class="form-control" placeholder="Registered Phone Number ID">
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Access Token:</strong>
                    <input type="password" name="access_token" class="form-control" placeholder="API Access Token">
                    @if(isset($configuration))
                        <small class="form-text text-muted">Leave blank to keep the current token.</small>
                    @endif
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Business Account ID (Optional):</strong>
                    <input type="text" name="business_account_id" value="{{ old('business_account_id', $configuration->business_account_id ?? '') }}" class="form-control" placeholder="WhatsApp Business Account ID">
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>App ID (Optional):</strong>
                    <input type="text" name="app_id" value="{{ old('app_id', $configuration->app_id ?? '') }}" class="form-control" placeholder="App ID if required">
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Is Active:</strong>
                    <select name="is_active" class="form-control">
                        <option value="1" {{ (isset($configuration) && $configuration->is_active) || old('is_active') == '1' ? 'selected' : '' }}>Yes</option>
                        <option value="0" {{ (isset($configuration) && !$configuration->is_active) || old('is_active') == '0' ? 'selected' : '' }}>No</option>
                    </select>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-12 text-center">
                <button type="submit" class="btn btn-primary">Submit</button>
                <a class="btn btn-secondary" href="{{ route('admin.whatsapp_integration.configurations.index') }}"> Back</a>
            </div>
        </div>
    </form>
</div>
@endsection

