@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>ZATCA E-Invoicing Integration</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <p>Manage and monitor the integration with ZATCA (Zakat, Tax and Customs Authority) for e-invoicing compliance.</p>

    <div class="card mb-3">
        <div class="card-header">Integration Status & Configuration</div>
        <div class="card-body">
            <p><strong>Current Phase:</strong> {{-- $settings->zatca_phase ?? "Phase 2: Integration" --}} Phase 2: Integration</p>
            <p><strong>Onboarding Status:</strong> {{-- $settings->zatca_onboarding_status ?? "Completed" --}} Completed</p>
            <p><strong>CSID (Cryptographic Stamp ID):</strong> {{-- $settings->zatca_csid ? substr($settings->zatca_csid, 0, 10) . "..." : "Not Set" --}} ABC123XYZ789...</p>
            <p><strong>Last Successful Submission:</strong> {{-- $settings->zatca_last_submission_at ?? "N/A" --}} 2023-05-13 16:00:00</p>
            <a href="{{ route("admin.integrations.zatca.edit") }}" class="btn btn-warning btn-sm">Configure ZATCA Settings</a>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">Recent Activity</div>
        <div class="card-body">
            <ul>
                <li>Standard Invoice #INV-2023-005 submitted successfully. (Clearance ID: ZATCA-CLR-001)</li>
                <li>Simplified Invoice #SMI-2023-010 reported successfully.</li>
                <li>Credit Note #CRN-2023-002 for INV-2023-003 submitted successfully.</li>
            </ul>
            <a href="{{ route("admin.integrations.zatca.show") }}" class="btn btn-info btn-sm">View All Invoice Logs</a>
        </div>
    </div>

    {{-- Add more sections as needed, e.g., for testing connection, generating reports for ZATCA --}}

</div>
@endsection

