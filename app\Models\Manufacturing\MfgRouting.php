<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class MfgRouting extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_routings';

    protected $fillable = [
        'routing_code',
        'name',
        'description',
        'mfg_product_id',
        'version',
        'is_active',
        'is_default',
        'total_standard_time_hours',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'total_standard_time_hours' => 'decimal:4',
    ];

    /**
     * Get the manufacturing product this routing is for (optional).
     */
    public function mfgProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'mfg_product_id');
    }

    /**
     * Get the operations (steps) in this routing.
     */
    public function routingOperations()
    {
        return $this->hasMany(MfgRoutingOperation::class, 'mfg_routing_id')->orderBy('sequence');
    }

    /**
     * Get the work orders that use this routing.
     */
    public function workOrders()
    {
        return $this->hasMany(MfgWorkOrder::class, 'mfg_routing_id');
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

