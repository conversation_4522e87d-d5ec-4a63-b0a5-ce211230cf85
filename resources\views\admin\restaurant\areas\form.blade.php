@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    {{ isset($area) ? __("Edit Restaurant Area") : __("Create Restaurant Area") }}
                </h1>
                <a href="{{ route('admin.restaurant.areas.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> {{ __("Back to Areas") }}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __("Area Information") }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ isset($area) ? route('admin.restaurant.areas.update', $area) : route('admin.restaurant.areas.store') }}" 
                          method="POST">
                        @csrf
                        @if(isset($area))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">{{ __("Area Name") }} <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $area->name ?? '') }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">{{ __("Branch") }} <span class="text-danger">*</span></label>
                                    <select class="form-select @error('branch_id') is-invalid @enderror" 
                                            id="branch_id" 
                                            name="branch_id" 
                                            required>
                                        <option value="">{{ __("Select Branch") }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" 
                                                    {{ old('branch_id', $area->branch_id ?? '') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('branch_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __("Description") }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3">{{ old('description', $area->description ?? '') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">{{ __("Area Color") }}</label>
                                    <div class="input-group">
                                        <input type="color" 
                                               class="form-control form-control-color @error('color') is-invalid @enderror" 
                                               id="color" 
                                               name="color" 
                                               value="{{ old('color', $area->color ?? '#007bff') }}">
                                        <input type="text" 
                                               class="form-control" 
                                               id="color_text" 
                                               value="{{ old('color', $area->color ?? '#007bff') }}" 
                                               readonly>
                                    </div>
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">{{ __("Sort Order") }}</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', $area->sort_order ?? 0) }}" 
                                           min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', $area->is_active ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    {{ __("Active") }}
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.restaurant.areas.index') }}" class="btn btn-secondary me-2">
                                {{ __("Cancel") }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                {{ isset($area) ? __("Update Area") : __("Create Area") }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __("Preview") }}</h5>
                </div>
                <div class="card-body">
                    <div class="area-preview p-3 rounded text-center" id="area-preview">
                        <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                        <h6 id="preview-name">{{ $area->name ?? __("Area Name") }}</h6>
                        <p class="text-muted mb-0" id="preview-description">
                            {{ $area->description ?? __("Area description will appear here") }}
                        </p>
                    </div>
                </div>
            </div>

            @if(isset($area) && $area->tables->count() > 0)
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{{ __("Tables in this Area") }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            @foreach($area->tables as $table)
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ $table->name }}</strong>
                                        <small class="text-muted d-block">{{ __("Capacity") }}: {{ $table->capacity }}</small>
                                    </div>
                                    <span class="badge" style="background-color: {{ $table->status_color }}">
                                        {{ ucfirst($table->status) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('color_text');
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const preview = document.getElementById('area-preview');
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');

    // Update color text when color changes
    colorInput.addEventListener('input', function() {
        colorText.value = this.value;
        preview.style.backgroundColor = this.value + '20'; // Add transparency
        preview.style.borderColor = this.value;
    });

    // Update preview when name changes
    nameInput.addEventListener('input', function() {
        previewName.textContent = this.value || '{{ __("Area Name") }}';
    });

    // Update preview when description changes
    descriptionInput.addEventListener('input', function() {
        previewDescription.textContent = this.value || '{{ __("Area description will appear here") }}';
    });

    // Initialize preview
    const initialColor = colorInput.value;
    preview.style.backgroundColor = initialColor + '20';
    preview.style.borderColor = initialColor;
    preview.style.border = '2px solid';
});
</script>
@endpush
