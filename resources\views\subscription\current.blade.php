@extends('layouts.admin')

@section('title', 'اشتراكي الحالي')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">
            <i class="bi bi-credit-card-2-front text-primary me-2"></i>
            اشتراكي الحالي
        </h2>
        <div class="btn-group">
            @if($subscription->status === 'trial')
                <a href="{{ route('subscription.upgrade') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-up-circle me-1"></i>
                    ترقية الباقة
                </a>
            @elseif($subscription->status === 'active')
                <a href="{{ route('subscription.upgrade') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-up-circle me-1"></i>
                    ترقية الباقة
                </a>
                <a href="{{ route('subscription.cancel') }}" class="btn btn-outline-danger">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء الاشتراك
                </a>
            @endif
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- معلومات الاشتراك الحالي -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        معلومات الاشتراك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">الباقة الحالية</h6>
                            <h4 class="text-primary">{{ $subscription->subscriptionPlan->name }}</h4>
                            <p class="text-muted">{{ $subscription->subscriptionPlan->description }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">حالة الاشتراك</h6>
                            <span class="badge fs-6 px-3 py-2" style="background-color: {{ $subscription->status_color }}">
                                {{ $subscription->status_text }}
                            </span>
                            
                            @if($subscription->status === 'trial')
                                <div class="mt-2">
                                    <small class="text-warning">
                                        <i class="bi bi-clock me-1"></i>
                                        تنتهي الفترة التجريبية في {{ $subscription->trial_end_date->diffForHumans() }}
                                    </small>
                                </div>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-muted">كود الاشتراك</h6>
                            <p class="mb-0">{{ $subscription->subscription_code }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">تاريخ البداية</h6>
                            <p class="mb-0">{{ $subscription->start_date->format('Y-m-d') }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">تاريخ النهاية</h6>
                            <p class="mb-0">{{ $subscription->end_date->format('Y-m-d') }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">الأيام المتبقية</h6>
                            <p class="mb-0 {{ $subscription->days_remaining <= 7 ? 'text-danger' : 'text-success' }}">
                                {{ $subscription->days_remaining }} يوم
                            </p>
                        </div>
                    </div>

                    @if($subscription->status === 'active')
                        <hr>
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-muted">المبلغ المدفوع</h6>
                                <p class="mb-0">{{ number_format($subscription->amount_paid, 2) }} ريال</p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted">طريقة الدفع</h6>
                                <p class="mb-0">{{ $subscription->payment_method ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted">التجديد التلقائي</h6>
                                <p class="mb-0">
                                    @if($subscription->auto_renew)
                                        <span class="text-success">
                                            <i class="bi bi-check-circle me-1"></i>مفعل
                                        </span>
                                    @else
                                        <span class="text-danger">
                                            <i class="bi bi-x-circle me-1"></i>معطل
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>
                        الاستخدام الحالي
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($usageStats as $resource => $stats)
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="text-muted">
                                    @switch($resource)
                                        @case('branches') الفروع @break
                                        @case('users') المستخدمين @break
                                        @case('customers') العملاء @break
                                        @case('sales_representatives') المناديب @break
                                        @case('invoices_this_month') فواتير هذا الشهر @break
                                        @default {{ $resource }}
                                    @endswitch
                                </span>
                                <span class="fw-bold">
                                    {{ $stats['current'] }} / {{ $stats['limit'] == -1 ? '∞' : $stats['limit'] }}
                                </span>
                            </div>
                            @if($stats['limit'] > 0)
                                @php
                                    $percentage = ($stats['current'] / $stats['limit']) * 100;
                                    $progressClass = $percentage >= 90 ? 'bg-danger' : ($percentage >= 70 ? 'bg-warning' : 'bg-success');
                                @endphp
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar {{ $progressClass }}" 
                                         style="width: {{ min(100, $percentage) }}%"></div>
                                </div>
                                @if($percentage >= 90)
                                    <small class="text-danger">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        اقتربت من الحد الأقصى
                                    </small>
                                @endif
                            @else
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-primary" style="width: 100%"></div>
                                </div>
                                <small class="text-muted">غير محدود</small>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- مميزات الباقة -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-check-circle me-2"></i>
                        المميزات المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">الوحدات</h6>
                            <ul class="list-unstyled">
                                @if($subscription->subscriptionPlan->has_sales)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        المبيعات
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_purchases)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        المشتريات
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_inventory)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        المخزون
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_accounting)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        المحاسبة
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_hr)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        الموارد البشرية
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_crm)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        إدارة العملاء
                                    </li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">المميزات الإضافية</h6>
                            <ul class="list-unstyled">
                                @if($subscription->subscriptionPlan->has_pos)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        نقاط البيع
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_manufacturing)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        التصنيع
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_reports)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        التقارير
                                    </li>
                                @endif
                                @if($subscription->subscriptionPlan->has_api_access)
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        الوصول للـ API
                                    </li>
                                @endif
                                <li class="mb-2">
                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                    {{ $subscription->subscriptionPlan->storage_space_gb }} جيجابايت تخزين
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-receipt me-2"></i>
                        آخر الفواتير
                    </h5>
                </div>
                <div class="card-body">
                    @if($subscription->subscriptionInvoices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($subscription->subscriptionInvoices->take(5) as $invoice)
                                        <tr>
                                            <td>{{ $invoice->invoice_number }}</td>
                                            <td>{{ $invoice->invoice_date->format('Y-m-d') }}</td>
                                            <td>{{ number_format($invoice->total_amount, 2) }} ريال</td>
                                            <td>
                                                <span class="badge {{ $invoice->status === 'paid' ? 'bg-success' : 'bg-warning' }}">
                                                    {{ $invoice->status === 'paid' ? 'مدفوعة' : 'معلقة' }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('subscription.invoices') }}" class="btn btn-outline-primary btn-sm">
                                عرض جميع الفواتير
                            </a>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-receipt display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد فواتير</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @if($subscription->status === 'trial')
                            <div class="col-md-4">
                                <a href="{{ route('subscription.upgrade') }}" class="card text-decoration-none border-primary">
                                    <div class="card-body text-center">
                                        <i class="bi bi-arrow-up-circle text-primary fs-1 mb-3"></i>
                                        <h6 class="card-title">ترقية الباقة</h6>
                                        <p class="card-text text-muted">احصل على مميزات أكثر</p>
                                    </div>
                                </a>
                            </div>
                        @endif

                        <div class="col-md-4">
                            <a href="{{ route('subscription.invoices') }}" class="card text-decoration-none border-info">
                                <div class="card-body text-center">
                                    <i class="bi bi-receipt text-info fs-1 mb-3"></i>
                                    <h6 class="card-title">عرض الفواتير</h6>
                                    <p class="card-text text-muted">تصفح فواتير الاشتراك</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-4">
                            <a href="{{ route('subscription.plans') }}" class="card text-decoration-none border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-grid text-success fs-1 mb-3"></i>
                                    <h6 class="card-title">مقارنة الباقات</h6>
                                    <p class="card-text text-muted">اطلع على جميع الباقات</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
