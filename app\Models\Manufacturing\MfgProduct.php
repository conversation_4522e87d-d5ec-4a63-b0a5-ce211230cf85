<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Product; // Assuming a general Product model exists
use App\Models\User;

class MfgProduct extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_products';

    protected $fillable = [
        'product_id',
        'type',
        'mfg_code',
        'description',
        'default_bom_id',
        'default_routing_id',
        'standard_cost',
        'costing_method',
        'can_be_manufactured',
        'can_be_purchased',
        'lead_time_days',
        'safety_stock_quantity',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'standard_cost' => 'decimal:4',
        'lead_time_days' => 'decimal:2',
        'safety_stock_quantity' => 'decimal:4',
        'can_be_manufactured' => 'boolean',
        'can_be_purchased' => 'boolean',
    ];

    /**
     * Get the general product information.
     */
    public function generalProduct()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the default Bill of Material for this manufacturing product.
     */
    public function defaultBom()
    {
        return $this->belongsTo(MfgBom::class, 'default_bom_id');
    }

    /**
     * Get all Bill of Materials for this manufacturing product.
     */
    public function boms()
    {
        return $this->hasMany(MfgBom::class, 'mfg_product_id');
    }

    /**
     * Get the default Routing for this manufacturing product.
     */
    public function defaultRouting()
    {
        return $this->belongsTo(MfgRouting::class, 'default_routing_id');
    }

    /**
     * Get all Routings for this manufacturing product.
     */
    public function routings()
    {
        return $this->hasMany(MfgRouting::class, 'mfg_product_id');
    }

    public function workOrders()
    {
        return $this->hasMany(MfgWorkOrder::class, 'mfg_product_id');
    }

    public function bomItemsAsComponent()
    {
        return $this->hasMany(MfgBomItem::class, 'component_product_id');
    }

    public function productionTransactions()
    {
        return $this->hasMany(MfgProductionTransaction::class, 'mfg_product_id');
    }

    public function materialAllocations()
    {
        return $this->hasMany(MfgMaterialAllocation::class, 'product_id');
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

