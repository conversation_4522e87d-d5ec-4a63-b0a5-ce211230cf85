@extends('layouts.admin')

@section('title', 'إدارة المناطق')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">
            <i class="bi bi-geo-alt-fill text-primary me-2"></i>
            إدارة المناطق
        </h2>
        <div class="btn-group">
            <a href="{{ route('admin.sales-representatives.areas.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg me-1"></i>
                إضافة منطقة جديدة
            </a>
            <a href="{{ route('admin.sales-representatives.areas.map') }}" class="btn btn-outline-info">
                <i class="bi bi-map me-1"></i>
                عرض الخريطة
            </a>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المناطق</h6>
                            <h3 class="mb-0">{{ $salesAreas->total() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-geo-alt-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناطق النشطة</h6>
                            <h3 class="mb-0">{{ $salesAreas->where('is_active', true)->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناطق المعينة</h6>
                            <h3 class="mb-0">{{ $salesAreas->filter(function($area) { return $area->activeSalesRepresentatives->count() > 0; })->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناطق غير المعينة</h6>
                            <h3 class="mb-0">{{ $salesAreas->filter(function($area) { return $area->activeSalesRepresentatives->count() == 0; })->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-x-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.sales-representatives.areas.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="اسم المنطقة أو الكود">
                    </div>
                    <div class="col-md-2">
                        <label for="city" class="form-label">المدينة</label>
                        <select class="form-select" id="city" name="city">
                            <option value="">جميع المدن</option>
                            @foreach($salesAreas->pluck('city')->unique() as $city)
                                <option value="{{ $city }}" {{ request('city') == $city ? 'selected' : '' }}>
                                    {{ $city }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="area_type" class="form-label">نوع المنطقة</label>
                        <select class="form-select" id="area_type" name="area_type">
                            <option value="">جميع الأنواع</option>
                            <option value="urban" {{ request('area_type') == 'urban' ? 'selected' : '' }}>حضرية</option>
                            <option value="suburban" {{ request('area_type') == 'suburban' ? 'selected' : '' }}>ضواحي</option>
                            <option value="rural" {{ request('area_type') == 'rural' ? 'selected' : '' }}>ريفية</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority_level" class="form-label">مستوى الأولوية</label>
                        <select class="form-select" id="priority_level" name="priority_level">
                            <option value="">جميع المستويات</option>
                            <option value="1" {{ request('priority_level') == '1' ? 'selected' : '' }}>عالية</option>
                            <option value="2" {{ request('priority_level') == '2' ? 'selected' : '' }}>متوسطة</option>
                            <option value="3" {{ request('priority_level') == '3' ? 'selected' : '' }}>منخفضة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="is_active" class="form-label">الحالة</label>
                        <select class="form-select" id="is_active" name="is_active">
                            <option value="">جميع الحالات</option>
                            <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>نشطة</option>
                            <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>غير نشطة</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{{ route('admin.sales-representatives.areas.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المناطق -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-table me-2"></i>
                قائمة المناطق
            </h5>
        </div>
        <div class="card-body">
            @if($salesAreas->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>كود المنطقة</th>
                                <th>اسم المنطقة</th>
                                <th>المدينة / الحي</th>
                                <th>نوع المنطقة</th>
                                <th>مستوى الأولوية</th>
                                <th>المناديب المعينين</th>
                                <th>بدل السفر</th>
                                <th>وقت الزيارة المقدر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($salesAreas as $area)
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ $area->code }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $area->name }}</strong>
                                            @if($area->description)
                                                <br><small class="text-muted">{{ Str::limit($area->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $area->city }}</strong>
                                            @if($area->district)
                                                <br><small class="text-muted">{{ $area->district }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: {{ $area->area_type_color }}">
                                            @switch($area->area_type)
                                                @case('urban') حضرية @break
                                                @case('suburban') ضواحي @break
                                                @case('rural') ريفية @break
                                                @default {{ $area->area_type }}
                                            @endswitch
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $area->priority_badge }}">
                                            {{ $area->priority_text }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($area->activeSalesRepresentatives->count() > 0)
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    {{ $area->activeSalesRepresentatives->count() }} مندوب
                                                </button>
                                                <ul class="dropdown-menu">
                                                    @foreach($area->activeSalesRepresentatives as $rep)
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('admin.sales-representatives.representatives.show', $rep) }}">
                                                                <i class="bi bi-person me-2"></i>{{ $rep->name }}
                                                            </a>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @else
                                            <span class="text-muted">غير معينة</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($area->travel_allowance > 0)
                                            {{ number_format($area->travel_allowance, 2) }} ريال
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($area->estimated_visit_time > 0)
                                            {{ $area->estimated_visit_time }} دقيقة
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $area->is_active ? 'bg-success' : 'bg-danger' }}">
                                            {{ $area->is_active ? 'نشطة' : 'غير نشطة' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.sales-representatives.areas.show', $area) }}" 
                                               class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.sales-representatives.areas.edit', $area) }}" 
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="showAssignModal({{ $area->id }})" title="تعيين مندوب">
                                                <i class="bi bi-person-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete({{ $area->id }})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $salesAreas->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="bi bi-geo-alt-slash display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">لا توجد مناطق</h5>
                    <p class="text-muted">لم يتم العثور على أي مناطق مطابقة لمعايير البحث.</p>
                    <a href="{{ route('admin.sales-representatives.areas.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-lg me-1"></i>
                        إضافة منطقة جديدة
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal for Assign Representative -->
<div class="modal fade" id="assignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعيين مندوب للمنطقة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="assignForm">
                <div class="modal-body">
                    <input type="hidden" id="area_id" name="area_id">
                    
                    <div class="mb-3">
                        <label for="sales_representative_id" class="form-label">المندوب <span class="text-danger">*</span></label>
                        <select class="form-select" id="sales_representative_id" name="sales_representative_id" required>
                            <option value="">اختر المندوب</option>
                            @foreach(\App\Models\Modules\SalesRepresentatives\SalesRepresentative::where('is_active', true)->where('status', 'active')->get() as $rep)
                                <option value="{{ $rep->id }}">{{ $rep->name }} ({{ $rep->employee_code }})</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="assignment_type" class="form-label">نوع التعيين <span class="text-danger">*</span></label>
                        <select class="form-select" id="assignment_type" name="assignment_type" required>
                            <option value="primary">أساسي</option>
                            <option value="secondary">ثانوي</option>
                            <option value="temporary">مؤقت</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="effective_from" class="form-label">ساري من <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="effective_from" name="effective_from" 
                                   value="{{ now()->format('Y-m-d') }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="effective_to" class="form-label">ساري إلى</label>
                            <input type="date" class="form-control" id="effective_to" name="effective_to">
                        </div>
                    </div>

                    <div class="mb-3 mt-3">
                        <label for="commission_override" class="form-label">تجاوز العمولة (%)</label>
                        <input type="number" class="form-control" id="commission_override" name="commission_override" 
                               step="0.01" min="0" max="100">
                    </div>

                    <div class="mb-3">
                        <label for="assignment_notes" class="form-label">ملاحظات التعيين</label>
                        <textarea class="form-control" id="assignment_notes" name="assignment_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تعيين المندوب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Delete Confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف هذه المنطقة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showAssignModal(areaId) {
    document.getElementById('area_id').value = areaId;
    const assignModal = new bootstrap.Modal(document.getElementById('assignModal'));
    assignModal.show();
}

function confirmDelete(areaId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/sales-representatives/areas/${areaId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Handle assign form submission
document.getElementById('assignForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const areaId = formData.get('area_id');
    
    fetch(`/admin/sales-representatives/areas/${areaId}/assign-representative`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ أثناء تعيين المندوب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تعيين المندوب');
    });
});

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#city, #area_type, #priority_level, #is_active');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
@endpush
