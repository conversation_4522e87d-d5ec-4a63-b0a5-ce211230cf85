<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><?php echo e(__("Ticketing Reports")); ?></h1>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($totalTickets); ?></h4>
                            <p class="mb-0"><?php echo e(__("Total Tickets")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($openTickets); ?></h4>
                            <p class="mb-0"><?php echo e(__("Open Tickets")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($pendingTickets); ?></h4>
                            <p class="mb-0"><?php echo e(__("Pending Tickets")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($closedTickets); ?></h4>
                            <p class="mb-0"><?php echo e(__("Closed Tickets")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- التذاكر حسب الفئة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__("Tickets by Category")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($ticketsByCategory->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__("Category")); ?></th>
                                        <th><?php echo e(__("Count")); ?></th>
                                        <th><?php echo e(__("Percentage")); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $ticketsByCategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($item->category->name ?? __("Unknown")); ?></td>
                                            <td><?php echo e($item->count); ?></td>
                                            <td><?php echo e($totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0); ?>%</td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e(__("No data available")); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- التذاكر حسب الأولوية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__("Tickets by Priority")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($ticketsByPriority->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__("Priority")); ?></th>
                                        <th><?php echo e(__("Count")); ?></th>
                                        <th><?php echo e(__("Percentage")); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $ticketsByPriority; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($item->priority && $item->priority->color): ?>
                                                    <span style="display:inline-block; width: 12px; height: 12px; background-color: <?php echo e($item->priority->color); ?>; border-radius: 50%; margin-right: 5px;"></span>
                                                <?php endif; ?>
                                                <?php echo e($item->priority->name ?? __("Unknown")); ?>

                                            </td>
                                            <td><?php echo e($item->count); ?></td>
                                            <td><?php echo e($totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0); ?>%</td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e(__("No data available")); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- التذاكر حسب الحالة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__("Tickets by Status")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($ticketsByStatus->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__("Status")); ?></th>
                                        <th><?php echo e(__("Count")); ?></th>
                                        <th><?php echo e(__("Percentage")); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $ticketsByStatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($item->status && $item->status->color): ?>
                                                    <span style="display:inline-block; width: 12px; height: 12px; background-color: <?php echo e($item->status->color); ?>; border-radius: 50%; margin-right: 5px;"></span>
                                                <?php endif; ?>
                                                <?php echo e($item->status->name ?? __("Unknown")); ?>

                                            </td>
                                            <td><?php echo e($item->count); ?></td>
                                            <td><?php echo e($totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0); ?>%</td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e(__("No data available")); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- التذاكر حسب الوكيل -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__("Tickets by Agent")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($ticketsByAgent->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__("Agent")); ?></th>
                                        <th><?php echo e(__("Count")); ?></th>
                                        <th><?php echo e(__("Percentage")); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $ticketsByAgent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($item->assignedTo->name ?? __("Unknown")); ?></td>
                                            <td><?php echo e($item->count); ?></td>
                                            <td><?php echo e($totalTickets > 0 ? round(($item->count / $totalTickets) * 100, 1) : 0); ?>%</td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e(__("No assigned tickets")); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني للتذاكر الحديثة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__("Recent Tickets (Last 30 Days)")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($recentTickets->count() > 0): ?>
                        <canvas id="recentTicketsChart" width="400" height="100"></canvas>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e(__("No recent tickets")); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if($recentTickets->count() > 0): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('recentTicketsChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($recentTickets->pluck('date')); ?>,
            datasets: [{
                label: '<?php echo e(__("Tickets Created")); ?>',
                data: <?php echo json_encode($recentTickets->pluck('count')); ?>,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/ticketing/reports/index.blade.php ENDPATH**/ ?>