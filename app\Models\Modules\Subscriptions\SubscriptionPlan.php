<?php

namespace App\Models\Modules\Subscriptions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubscriptionPlan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'price',
        'billing_cycle',
        'max_users',
        'max_branches',
        'has_pos',
        'has_inventory',
        'has_accounting',
        'has_manufacturing',
        'has_hr',
        'has_crm',
        'has_purchases',
        'has_sales',
        'has_reports',
        'has_api_access',
        'storage_space_gb',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'max_users' => 'integer',
        'max_branches' => 'integer',
        'has_pos' => 'boolean',
        'has_inventory' => 'boolean',
        'has_accounting' => 'boolean',
        'has_manufacturing' => 'boolean',
        'has_hr' => 'boolean',
        'has_crm' => 'boolean',
        'has_purchases' => 'boolean',
        'has_sales' => 'boolean',
        'has_reports' => 'boolean',
        'has_api_access' => 'boolean',
        'storage_space_gb' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the invoice limit for this plan.
     */
    public function invoiceLimit()
    {
        return $this->hasOne(\App\Models\InvoiceLimit::class);
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2) . ' ريال';
    }

    /**
     * Get the billing cycle in Arabic.
     */
    public function getBillingCycleArabicAttribute()
    {
        return match($this->billing_cycle) {
            'monthly' => 'شهري',
            'quarterly' => 'ربع سنوي',
            'semi_annually' => 'نصف سنوي',
            'annually' => 'سنوي',
            default => $this->billing_cycle,
        };
    }
}
