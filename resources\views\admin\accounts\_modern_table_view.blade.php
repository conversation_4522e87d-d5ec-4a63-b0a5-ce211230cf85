@if(empty($rootAccounts))
    <div class="empty-state text-center p-5">
        <i class="bi bi-folder2-open display-4 text-muted mb-3"></i>
        <h5>لا توجد حسابات</h5>
        <p>لم يتم إضافة أي حسابات بعد. يمكنك إضافة حسابات جديدة من خلال النقر على زر "إضافة حساب جديد".</p>
        <a href="{{ route('admin.accounts.create') }}" class="btn btn-primary mt-3">
            <i class="bi bi-plus-circle me-1"></i> إضافة حساب جديد
        </a>
    </div>
@else
    <div class="table-responsive">
        <table class="accounts-custom-table modern-accounts-table" data-no-datatables="true">
            <thead>
                <tr>
                    <th width="10%">الكود</th>
                    <th width="30%">اسم الحساب</th>
                    <th width="15%">النوع</th>
                    <th width="15%">المستوى</th>
                    <th width="15%">الرصيد</th>
                    <th width="15%">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $allAccounts = [];

                    // Flatten the accounts hierarchy for table view
                    foreach ($accountTypes as $type) {
                        if (isset($rootAccounts[$type->id])) {
                            foreach ($rootAccounts[$type->id]['accounts'] as $account) {
                                $allAccounts[] = [
                                    'account' => $account,
                                    'type' => $type,
                                    'level' => 0,
                                    'parent_name' => null
                                ];

                                // Add children recursively using a loop instead of recursion
                                if ($account->children && $account->children->count() > 0) {
                                    $queue = [];
                                    foreach ($account->children as $child) {
                                        $queue[] = [
                                            'account' => $child,
                                            'type' => $type,
                                            'level' => 1,
                                            'parent_name' => $account->name_ar
                                        ];
                                    }

                                    // Process all children in the queue
                                    while (!empty($queue)) {
                                        $current = array_shift($queue);
                                        $allAccounts[] = $current;

                                        $currentAccount = $current['account'];
                                        $currentLevel = $current['level'];

                                        if ($currentAccount->children && $currentAccount->children->count() > 0) {
                                            foreach ($currentAccount->children as $child) {
                                                $queue[] = [
                                                    'account' => $child,
                                                    'type' => $type,
                                                    'level' => $currentLevel + 1,
                                                    'parent_name' => $currentAccount->name_ar
                                                ];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                @endphp

                @foreach($allAccounts as $item)
                    @php
                        $account = $item['account'];
                        $type = $item['type'];
                        $level = $item['level'];
                        $parent_name = $item['parent_name'];

                        $balance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                        $balanceClass = $balance < 0 ? 'text-danger' : 'text-success';

                        // Level text
                        $levelText = 'رئيسي';
                        if ($level == 1) {
                            $levelText = 'فرعي - المستوى الأول';
                        } elseif ($level == 2) {
                            $levelText = 'فرعي - المستوى الثاني';
                        } elseif ($level == 3) {
                            $levelText = 'فرعي - المستوى الثالث';
                        } elseif ($level > 3) {
                            $levelText = 'فرعي - المستوى ' . $level;
                        }
                    @endphp
                    <tr class="account-row level-{{ $level }}">
                        <td class="account-code">{{ $account->code }}</td>
                        <td class="account-name">
                            <div class="d-flex align-items-center">
                                <div class="account-type-indicator type-bg-{{ $type->slug }} me-2"></div>
                                <div>
                                    @if($level > 0)
                                        <div class="account-indent" style="padding-right: {{ $level * 20 }}px;">
                                            @if($account->is_control_account)
                                                <i class="bi bi-folder2 text-warning me-1"></i>
                                            @else
                                                <i class="bi bi-file-earmark-text text-primary me-1"></i>
                                            @endif
                                            {{ $account->name_ar }}
                                        </div>
                                    @else
                                        <div>
                                            @if($account->is_control_account)
                                                <i class="bi bi-folder2 text-warning me-1"></i>
                                            @else
                                                <i class="bi bi-file-earmark-text text-primary me-1"></i>
                                            @endif
                                            {{ $account->name_ar }}
                                        </div>
                                    @endif

                                    @if($parent_name && $level > 0)
                                        <small class="text-muted d-block">
                                            تابع لـ: {{ $parent_name }}
                                        </small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge type-badge-{{ $type->slug }}">{{ $type->name_ar }}</span>
                        </td>
                        <td>{{ $levelText }}</td>
                        <td class="{{ $balanceClass }}">
                            {{ number_format(abs($balance), 2) }}
                            <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                        </td>
                        <td>
                            <div class="account-actions">
                                <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-info" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endif
