@extends("layouts.admin")

@section("title", "إعدادات مزودي خدمة الدفع الآجل")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إعدادات مزودي خدمة الدفع الآجل (BNPL)</h3>
                    <div class="card-tools">
                        <a href="{{ route("admin.bnpl_settings.create") }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة إعداد جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session("success"))
                        <div class="alert alert-success" role="alert">
                            {{ session("success") }}
                        </div>
                    @endif

                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المزود</th>
                                <th>البيئة</th>
                                <th>مفعل</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($settings as $setting)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ ucfirst($setting->provider_name) }}</td>
                                    <td>{{ $setting->environment == "production" ? "إنتاج" : "تجريبي" }}</td>
                                    <td>
                                        @if ($setting->is_active)
                                            <span class="badge badge-success">نعم</span>
                                        @else
                                            <span class="badge badge-danger">لا</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route("admin.bnpl_settings.edit", $setting->id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <form action="{{ route("admin.bnpl_settings.destroy", $setting->id) }}" method="POST" style="display: inline-block;">
                                            @csrf
                                            @method("DELETE")
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("هل أنت متأكد من رغبتك في الحذف؟")">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد إعدادات حالياً.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

