<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("whatsapp_configurations", function (Blueprint $table) {
            $table->id();
            $table->string("provider_name")->comment("Name of the WhatsApp Business API provider, e.g., Twilio, Meta");
            $table->string("phone_number_id")->comment("Registered phone number ID with WhatsApp");
            $table->text("access_token")->comment("API access token (should be encrypted at rest)");
            $table->string("business_account_id")->nullable()->comment("Optional WhatsApp Business Account ID");
            $table->string("app_id")->nullable()->comment("Optional App ID if required by provider");
            $table->boolean("is_active")->default(false)->comment("To mark the currently active configuration");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("whatsapp_configurations");
    }
};
