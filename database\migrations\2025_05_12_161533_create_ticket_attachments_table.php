<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create("ticket_attachments", function (Blueprint $table) {
            $table->id();
            $table->foreignId("ticket_id")->nullable()->constrained("tickets")->onDelete("cascade");
            $table->foreignId("ticket_reply_id")->nullable()->constrained("ticket_replies")->onDelete("cascade");
            $table->foreignId("user_id")->comment("User who uploaded the attachment")->constrained("users")->onDelete("cascade");
            
            $table->string("file_name");
            $table->string("file_path");
            $table->string("file_type")->nullable(); // e.g., image/jpeg, application/pdf
            $table->unsignedBigInteger("file_size")->nullable(); // in bytes
            
            $table->timestamps();

            // Ensure that an attachment belongs to either a ticket or a ticket reply, but not necessarily both.
            // This can be handled at the application level or with a check constraint if the DB supports it.
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("ticket_attachments");
    }
};

