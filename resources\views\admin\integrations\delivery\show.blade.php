@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Delivery Integration Details & Logs</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <p>This page displays detailed status, configuration summary, and logs for delivery service integrations.</p>

    <div class="card mb-3">
        <div class="card-header">Overall Delivery Integration Status</div>
        <div class="card-body">
            <p><strong>Aramex Status:</strong> {{-- $aramexStatus ?? "Configured" --}} Configured & Active</p>
            <p><strong>SMSA Express Status:</strong> {{-- $smsaStatus ?? "Not Configured" --}} Not Configured</p>
            {{-- Add more providers as they are integrated --}}
        </div>
    </div>

    <div class="card">
        <div class="card-header">Integration Logs (Last 50 Entries)</div>
        <div class="card-body">
            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Provider</th>
                        <th>Event Type</th>
                        <th>Details</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- @forelse($logs as $log) --}}
                    {{-- Replace with actual log data --}}
                    <tr>
                        <td>2023-05-13 10:05:00</td>
                        <td>Aramex</td>
                        <td>Shipment Creation</td>
                        <td>Order #12345 to Riyadh</td>
                        <td><span class="badge bg-success">Success</span></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 09:30:00</td>
                        <td>Aramex</td>
                        <td>API Connection Test</td>
                        <td>Connection to Aramex API</td>
                        <td><span class="badge bg-success">Success</span></td>
                    </tr>
                    <tr>
                        <td>2023-05-12 15:00:00</td>
                        <td>SMSA</td>
                        <td>Configuration Update</td>
                        <td>API Key updated</td>
                        <td><span class="badge bg-info">Info</span></td>
                    </tr>
                     <tr>
                        <td>2023-05-12 14:00:00</td>
                        <td>Aramex</td>
                        <td>Shipment Tracking</td>
                        <td>Order #12300 - Out for Delivery</td>
                        <td><span class="badge bg-warning">Warning</span> Data Mismatch</td>
                    </tr>
                    {{-- @empty
                    <tr>
                        <td colspan="5" class="text-center">No integration logs found.</td>
                    </tr>
                    @endforelse --}}
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-3">
        <a href="{{ route("admin.integrations.delivery.index") }}" class="btn btn-secondary">Back to Delivery Integrations</a>
    </div>
</div>
@endsection

