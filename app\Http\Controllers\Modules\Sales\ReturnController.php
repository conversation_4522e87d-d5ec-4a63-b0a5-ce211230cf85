<?php

namespace App\Http\Controllers\Modules\Sales;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Modules\Sales\SalesReturn;
use App\Models\Modules\Sales\Customer;
use Illuminate\Support\Facades\Validator;

class ReturnController extends Controller
{
    /**
     * Display a listing of the returns.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $returns = SalesReturn::with('customer')->get();
        return view('admin.sales.returns.index');
    }

    /**
     * Show the form for creating a new return.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $customers = Customer::active()->get();
        return view('admin.sales.returns.create', compact('customers'));
    }

    /**
     * Store a newly created return in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validation and storage logic will be implemented later
        return redirect()->route('admin.sales.returns.index')
            ->with('success', 'تم إنشاء مرتجع المبيعات بنجاح');
    }

    /**
     * Display the specified return.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // $return = SalesReturn::with(['customer', 'items'])->findOrFail($id);
        return view('admin.sales.returns.show');
    }

    /**
     * Show the form for editing the specified return.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // $return = SalesReturn::findOrFail($id);
        // $customers = Customer::active()->get();
        return view('admin.sales.returns.edit');
    }

    /**
     * Update the specified return in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Validation and update logic will be implemented later
        return redirect()->route('admin.sales.returns.index')
            ->with('success', 'تم تحديث مرتجع المبيعات بنجاح');
    }

    /**
     * Remove the specified return from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Delete logic will be implemented later
        return redirect()->route('admin.sales.returns.index')
            ->with('success', 'تم حذف مرتجع المبيعات بنجاح');
    }
}
