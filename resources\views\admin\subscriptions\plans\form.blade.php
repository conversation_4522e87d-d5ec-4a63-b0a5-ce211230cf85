@extends('layouts.admin')

@section('title', isset($plan) ? 'تعديل خطة اشتراك' : 'إضافة خطة اشتراك جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($plan) ? 'تعديل خطة اشتراك' : 'إضافة خطة اشتراك جديدة' }}</h3>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ isset($plan) ? route('admin.subscriptions.plans.update', $plan) : route('admin.subscriptions.plans.store') }}" method="POST">
                        @csrf
                        @if(isset($plan))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الخطة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $plan->name ?? '') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">كود الخطة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror" id="code" name="code" value="{{ old('code', $plan->code ?? '') }}" required>
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">يجب أن يكون الكود فريدًا ويفضل استخدام الحروف والأرقام فقط</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الخطة</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $plan->description ?? '') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">السعر <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $plan->price ?? '') }}" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="billing_cycle" class="form-label">دورة الفوترة <span class="text-danger">*</span></label>
                                    <select class="form-select @error('billing_cycle') is-invalid @enderror" id="billing_cycle" name="billing_cycle" required>
                                        <option value="monthly" {{ old('billing_cycle', $plan->billing_cycle ?? '') == 'monthly' ? 'selected' : '' }}>شهري</option>
                                        <option value="quarterly" {{ old('billing_cycle', $plan->billing_cycle ?? '') == 'quarterly' ? 'selected' : '' }}>ربع سنوي</option>
                                        <option value="semi_annually" {{ old('billing_cycle', $plan->billing_cycle ?? '') == 'semi_annually' ? 'selected' : '' }}>نصف سنوي</option>
                                        <option value="annually" {{ old('billing_cycle', $plan->billing_cycle ?? '') == 'annually' ? 'selected' : '' }}>سنوي</option>
                                    </select>
                                    @error('billing_cycle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_users" class="form-label">الحد الأقصى للمستخدمين <span class="text-danger">*</span></label>
                                    <input type="number" min="1" class="form-control @error('max_users') is-invalid @enderror" id="max_users" name="max_users" value="{{ old('max_users', $plan->max_users ?? '1') }}" required>
                                    @error('max_users')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_branches" class="form-label">الحد الأقصى للفروع <span class="text-danger">*</span></label>
                                    <input type="number" min="1" class="form-control @error('max_branches') is-invalid @enderror" id="max_branches" name="max_branches" value="{{ old('max_branches', $plan->max_branches ?? '1') }}" required>
                                    @error('max_branches')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="storage_space_gb" class="form-label">مساحة التخزين (جيجابايت) <span class="text-danger">*</span></label>
                                    <input type="number" min="1" class="form-control @error('storage_space_gb') is-invalid @enderror" id="storage_space_gb" name="storage_space_gb" value="{{ old('storage_space_gb', $plan->storage_space_gb ?? '1') }}" required>
                                    @error('storage_space_gb')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mt-3 mb-3">الميزات المتاحة</h5>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_pos" name="has_pos" {{ old('has_pos', $plan->has_pos ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_pos">نقاط البيع</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_inventory" name="has_inventory" {{ old('has_inventory', $plan->has_inventory ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_inventory">إدارة المخزون</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_accounting" name="has_accounting" {{ old('has_accounting', $plan->has_accounting ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_accounting">المحاسبة</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_manufacturing" name="has_manufacturing" {{ old('has_manufacturing', $plan->has_manufacturing ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_manufacturing">التصنيع</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_hr" name="has_hr" {{ old('has_hr', $plan->has_hr ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_hr">الموارد البشرية</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_crm" name="has_crm" {{ old('has_crm', $plan->has_crm ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_crm">إدارة علاقات العملاء</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_purchases" name="has_purchases" {{ old('has_purchases', $plan->has_purchases ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_purchases">المشتريات</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_sales" name="has_sales" {{ old('has_sales', $plan->has_sales ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_sales">المبيعات</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_reports" name="has_reports" {{ old('has_reports', $plan->has_reports ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_reports">التقارير</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="has_api_access" name="has_api_access" {{ old('has_api_access', $plan->has_api_access ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="has_api_access">الوصول إلى API</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-switch mb-3 mt-3">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ old('is_active', $plan->is_active ?? true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">نشط</label>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.subscriptions.plans.index') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">{{ isset($plan) ? 'تحديث' : 'حفظ' }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
