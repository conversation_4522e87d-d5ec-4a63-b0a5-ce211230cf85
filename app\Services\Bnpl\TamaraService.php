<?php

namespace App\Services\Bnpl;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TamaraService implements BnplServiceInterface
{
    private string $apiKey;
    private string $baseUrl;
    private string $notificationToken;
    private string $publicKey; 
    private bool $isProduction;

    public function initialize(array $settings): void
    {
        $this->isProduction = ($settings["environment"] ?? "sandbox") === "production";

        $this->apiKey = $this->isProduction ? ($settings["api_key_production"] ?? "") : ($settings["api_key_sandbox"] ?? "");
        $this->notificationToken = $this->isProduction ? ($settings["notification_token_production"] ?? "") : ($settings["notification_token_sandbox"] ?? "");
        $this->publicKey = $this->isProduction ? ($settings["public_key_production"] ?? "") : ($settings["public_key_sandbox"] ?? "");
        $this->baseUrl = $this->isProduction ? "https://api.tamara.co" : "https://api-sandbox.tamara.co";

        if (empty($this->apiKey)) {
            Log::error("TamaraService: API key is not configured for the current environment.", ["environment" => $settings["environment"] ?? "sandbox"]);
        }
        if (empty($this->notificationToken)) {
            Log::error("TamaraService: Notification token is not configured for the current environment.", ["environment" => $settings["environment"] ?? "sandbox"]);
        }
    }

    public function createCheckoutSession(array $orderDetails): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tamara API key not configured."];
        }
        try {
            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json",
            ])->post($this->baseUrl . "/checkout", $orderDetails);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "checkout_url" => $responseData["checkout_url"] ?? null,
                    "session_id" => $responseData["checkout_id"] ?? null, 
                    "order_id" => $responseData["order_id"] ?? null
                ];
            } else {
                Log::error("Tamara createCheckoutSession failed: ", ["status" => $response->status(), "body" => $response->body(), "request" => $orderDetails]);
                return ["success" => false, "error_message" => "Failed to create Tamara checkout session. Status: " . $response->status() . " Body: " . $response->body()];
            }
        } catch (\Exception $e) {
            Log::error("Tamara createCheckoutSession exception: " . $e->getMessage(), ["request" => $orderDetails]);
            return ["success" => false, "error_message" => "Exception during Tamara checkout session creation: " . $e->getMessage()];
        }
    }

    public function authorizeOrder(string $tamaraOrderId, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tamara API key not configured."];
        }
        try {
            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
            ])->post($this->baseUrl . "/orders/" . $tamaraOrderId . "/authorise");

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "order_id" => $responseData["order_id"] ?? $tamaraOrderId,
                    "status" => $responseData["status"] ?? "AUTHORIZED"
                ];
            } else {
                Log::error("Tamara authorizeOrder failed: ", ["status" => $response->status(), "body" => $response->body(), "order_id" => $tamaraOrderId]);
                return ["success" => false, "error_message" => "Failed to authorize Tamara order. Status: " . $response->status() . " Body: " . $response->body()];
            }
        } catch (\Exception $e) {
            Log::error("Tamara authorizeOrder exception: " . $e->getMessage(), ["order_id" => $tamaraOrderId]);
            return ["success" => false, "error_message" => "Exception during Tamara order authorization: " . $e->getMessage()];
        }
    }

    public function capturePayment(string $tamaraOrderId, float $amount, string $currency, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tamara API key not configured."];
        }
        try {
            $payload = [
                "order_id" => $tamaraOrderId,
                "total_amount" => ["amount" => (string)number_format($amount, 2, ".", ""), "currency" => $currency],
                "shipping_info" => $params["shipping_info"] ?? [], 
                "items" => $params["items"] ?? [], 
            ];

            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json",
            ])->post($this->baseUrl . "/payments/capture", $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "capture_id" => $responseData["capture_id"] ?? null,
                    "status" => $responseData["status"] ?? "CAPTURED"
                ];
            } else {
                Log::error("Tamara capturePayment failed: ", ["status" => $response->status(), "body" => $response->body(), "payload" => $payload]);
                return ["success" => false, "error_message" => "Failed to capture Tamara payment. Status: " . $response->status() . " Body: " . $response->body()];
            }
        } catch (\Exception $e) {
            Log::error("Tamara capturePayment exception: " . $e->getMessage(), ["payload" => $payload]);
            return ["success" => false, "error_message" => "Exception during Tamara payment capture: " . $e->getMessage()];
        }
    }

    public function refundPayment(string $tamaraOrderId, ?string $captureId, float $amount, string $currency, string $reason, array $params = []): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tamara API key not configured."];
        }
        if (empty($captureId)) {
            Log::error("Tamara refundPayment: Capture ID is required for refunds.");
            return ["success" => false, "error_message" => "Capture ID is required for Tamara refunds."];
        }

        try {
            $payload = [
                "capture_id" => $captureId, 
                "total_amount" => ["amount" => (string)number_format($amount, 2, ".", ""), "currency" => $currency],
                "comment" => $reason,
                "items" => $params["items"] ?? [], 
            ];

            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey,
                "Content-Type" => "application/json",
            ])->post($this->baseUrl . "/refunds", $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "refund_id" => $responseData["refund_id"] ?? null,
                    "status" => "REFUND_INITIATED"
                ];
            } else {
                Log::error("Tamara refundPayment failed: ", ["status" => $response->status(), "body" => $response->body(), "payload" => $payload]);
                return ["success" => false, "error_message" => "Failed to process Tamara refund. Status: " . $response->status() . " Body: " . $response->body()];
            }
        } catch (\Exception $e) {
            Log::error("Tamara refundPayment exception: " . $e->getMessage(), ["payload" => $payload]);
            return ["success" => false, "error_message" => "Exception during Tamara refund processing: " . $e->getMessage()];
        }
    }

    public function getOrderStatus(string $tamaraOrderId): array
    {
        if (empty($this->apiKey)) {
            return ["success" => false, "error_message" => "Tamara API key not configured."];
        }
        try {
            $response = Http::withHeaders([
                "Authorization" => "Bearer " . $this->apiKey
            ])->get($this->baseUrl . "/orders/" . $tamaraOrderId);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    "success" => true, 
                    "status" => $responseData["status"] ?? null,
                    "details" => $responseData
                ];
            } else {
                Log::error("Tamara getOrderStatus failed: ", ["status" => $response->status(), "body" => $response->body(), "order_id" => $tamaraOrderId]);
                return ["success" => false, "error_message" => "Failed to get Tamara order status. Status: " . $response->status() . " Body: " . $response->body()];
            }
        } catch (\Exception $e) {
            Log::error("Tamara getOrderStatus exception: " . $e->getMessage(), ["order_id" => $tamaraOrderId]);
            return ["success" => false, "error_message" => "Exception during Tamara order status retrieval: " . $e->getMessage()];
        }
    }

    public function verifyWebhookSignature(array $requestHeaders, string $requestBody): bool
    {
        $tamaraSignature = $requestHeaders["tamara-signature"] ?? $requestHeaders["Tamara-Signature"] ?? null;
        $tamaraTimestamp = $requestHeaders["tamara-timestamp"] ?? $requestHeaders["Tamara-Timestamp"] ?? null;

        if (!$tamaraSignature || !$tamaraTimestamp) {
            Log::warning("Tamara webhook verification: Tamara-Signature or Tamara-Timestamp header missing.");
            return false;
        }

        if (empty($this->notificationToken)) {
            Log::error("Tamara webhook verification: Notification token not configured in TamaraService.");
            return false;
        }
        
        // Construct the message to sign according to Tamara's documentation
        // The message is the concatenation of the request URL, the sorted JSON request body, and the timestamp.
        // This part requires careful implementation as per Tamara's specific guidelines.
        // For now, using a simplified approach. A more robust solution would involve:
        // 1. Getting the full request URL.
        // 2. Parsing the JSON request body ($requestBody).
        // 3. Sorting the parsed JSON by key, recursively if nested objects.
        // 4. Re-serializing the sorted JSON to a string without spaces.
        // 5. Concatenating: $requestUrl . $sortedJsonString . $tamaraTimestamp
        // This is a placeholder for the actual message construction logic.
        // Assuming $requestBody is already the sorted, serialized JSON for simplicity here, which is unlikely.
        // A proper implementation is crucial for security.
        
        // A more accurate (but still potentially incomplete without full context of request URL and sorting) approach:
        // $webhookUrl = $requestHeaders["x-forwarded-proto"] . "://" . $requestHeaders["host"] . $requestHeaders["request-uri"]; // This depends on your server setup
        // $sortedBody = $this->sortAndSerializeJson($requestBody); // You'd need to implement this helper
        // $messageToSign = $webhookUrl . $sortedBody . $tamaraTimestamp;
        
        // Simplified placeholder - THIS IS NOT SECURE FOR PRODUCTION without correct message construction
        $messageToSign = $requestBody . $tamaraTimestamp; 
        Log::warning("Tamara webhook verification: Using simplified message signing for verification. Ensure this matches Tamara's exact requirements for production.", ["message_signed" => $messageToSign]);

        $computedSignature = hash_hmac("sha256", $messageToSign, $this->notificationToken);

        if (hash_equals($computedSignature, $tamaraSignature)) {
            return true;
        }

        Log::warning("Tamara webhook verification failed: Signatures do not match.", [
            "received_signature" => $tamaraSignature,
            "computed_signature" => $computedSignature,
            "timestamp" => $tamaraTimestamp,
            "message_signed_DEBUG" => $messageToSign // For debugging, remove in production
        ]);
        return false;
    }

    // Helper function placeholder for sorting and serializing JSON - to be implemented correctly
    // private function sortAndSerializeJson(string $jsonString): string {
    //     $data = json_decode($jsonString, true);
    //     if (json_last_error() !== JSON_ERROR_NONE) {
    //         return $jsonString; // Or handle error
    //     }
    //     // Implement recursive sorting here
    //     // ksort($data, SORT_STRING);
    //     // ... deep sort ...
    //     return json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    // }

    public function handleWebhook(array $webhookData): array
    {
        $eventType = $webhookData["event_type"] ?? null;
        $orderId = $webhookData["order_id"] ?? null;
        $status = $webhookData["order_status"] ?? $webhookData["status"] ?? null;

        Log::info("Tamara webhook received: ", $webhookData);

        if (!$orderId) {
            Log::error("Tamara webhook handling: Order ID missing.", $webhookData);
            return ["success" => false, "error_message" => "Order ID missing in webhook data."];
        }
        
        // Further processing based on eventType and status
        // event(new TamaraPaymentUpdated($orderId, $status, $webhookData));

        return ["success" => true, "message" => "Tamara webhook processed for event: " . $eventType . " and order ID: " . $orderId];
    }
}

