<?php

namespace App\Models\Modules\WhatsAppIntegration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class WhatsAppConfiguration extends Model
{
    use HasFactory;

    protected $table = "whatsapp_configurations";

    protected $fillable = [
        "provider_name",
        "phone_number_id",
        "access_token",
        "business_account_id",
        "app_id",
        "is_active",
    ];

    protected $casts = [
        "is_active" => "boolean",
    ];

    /**
     * Encrypt the access token before saving.
     */
    public function setAccessTokenAttribute($value)
    {
        $this->attributes["access_token"] = Crypt::encryptString($value);
    }

    /**
     * Decrypt the access token when retrieving.
     */
    public function getAccessTokenAttribute($value)
    {
        try {
            return Crypt::decryptString($value);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return null; // Or handle the exception as needed
        }
    }
}

