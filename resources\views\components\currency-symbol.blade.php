{{--
    مكون عرض رمز العملة السعودية
    الاستخدام: <x-currency-symbol />
    أو: <x-currency-symbol currency="SAR" />
    أو: <x-currency-symbol currency="USD" />
--}}

@props([
    'currency' => 'SAR',
    'class' => '',
    'size' => 'normal' // normal, large, small
])

@php
    $currencyConfig = config("currency.supported_currencies.{$currency}");
    $symbol = $currencyConfig['symbol'] ?? '﷼';
    
    // تحديد الكلاسات حسب العملة والحجم
    $classes = ['currency-symbol'];
    
    // إضافة كلاس خاص بالعملة
    $classes[] = 'currency-' . strtolower($currency);
    
    // إضافة كلاس الحجم
    switch($size) {
        case 'large':
            $classes[] = 'currency-large';
            break;
        case 'small':
            $classes[] = 'currency-small';
            break;
        default:
            $classes[] = 'currency-normal';
    }
    
    // إضافة الكلاسات المخصصة
    if($class) {
        $classes[] = $class;
    }
    
    $classString = implode(' ', $classes);
@endphp

<span class="{{ $classString }}" title="{{ $currencyConfig['name'] ?? 'ريال سعودي' }}">{{ $symbol }}</span>
