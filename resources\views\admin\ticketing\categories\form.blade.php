@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($category) ? __("Edit Ticket Category") : __("Create Ticket Category") }}</h1>
    <form action="{{ isset($category) ? route("admin.ticketing.ticket_categories.update", $category->id) : route("admin.ticketing.ticket_categories.store") }}" method="POST">
        @csrf
        @if(isset($category))
            @method("PUT")
        @endif
        <div class="form-group">
            <label for="name">{{ __("Name") }}</label>
            <input type="text" name="name" id="name" class="form-control @error("name") is-invalid @enderror" value="{{ old("name", $category->name ?? "") }}" required>
            @error("name")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group">
            <label for="description">{{ __("Description") }}</label>
            <textarea name="description" id="description" class="form-control @error("description") is-invalid @enderror" rows="3">{{ old("description", $category->description ?? "") }}</textarea>
            @error("description")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old("is_active", $category->is_active ?? true) ? "checked" : "" }}>
            <label class="form-check-label" for="is_active">{{ __("Active") }}</label>
        </div>
        <button type="submit" class="btn btn-primary">{{ isset($category) ? __("Update Category") : __("Create Category") }}</button>
        <a href="{{ route("admin.ticketing.ticket_categories.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

