<?php

namespace App\Http\Controllers\Modules\GeneralLedger;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Modules\GeneralLedger\Tax;
use Illuminate\Support\Facades\Validator;

class TaxController extends Controller
{
    /**
     * Display a listing of the taxes.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $taxes = Tax::all();
        return view('admin.taxes.index', compact('taxes'));
    }

    /**
     * Show the form for creating a new tax.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.taxes.create');
    }

    /**
     * Store a newly created tax in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:taxes',
            'rate' => 'required|numeric|min:0|max:100',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // If this tax is set as default, unset any other default tax
        if ($request->has('is_default') && $request->is_default) {
            Tax::where('is_default', true)->update(['is_default' => false]);
        }

        $tax = new Tax();
        $tax->name = $request->name;
        $tax->code = $request->code;
        $tax->rate = $request->rate;
        $tax->is_default = $request->has('is_default');
        $tax->is_active = $request->has('is_active');
        $tax->description = $request->description;
        $tax->save();

        return redirect()->route('admin.taxes.index')
            ->with('success', 'تم إضافة الضريبة بنجاح');
    }

    /**
     * Display the specified tax.
     *
     * @param  \App\Models\Modules\GeneralLedger\Tax  $tax
     * @return \Illuminate\Http\Response
     */
    public function show(Tax $tax)
    {
        return view('admin.taxes.show', compact('tax'));
    }

    /**
     * Show the form for editing the specified tax.
     *
     * @param  \App\Models\Modules\GeneralLedger\Tax  $tax
     * @return \Illuminate\Http\Response
     */
    public function edit(Tax $tax)
    {
        return view('admin.taxes.edit', compact('tax'));
    }

    /**
     * Update the specified tax in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Modules\GeneralLedger\Tax  $tax
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Tax $tax)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:taxes,code,' . $tax->id,
            'rate' => 'required|numeric|min:0|max:100',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // If this tax is set as default, unset any other default tax
        if ($request->has('is_default') && $request->is_default && !$tax->is_default) {
            Tax::where('is_default', true)->update(['is_default' => false]);
        }

        $tax->name = $request->name;
        $tax->code = $request->code;
        $tax->rate = $request->rate;
        $tax->is_default = $request->has('is_default');
        $tax->is_active = $request->has('is_active');
        $tax->description = $request->description;
        $tax->save();

        return redirect()->route('admin.taxes.index')
            ->with('success', 'تم تحديث الضريبة بنجاح');
    }

    /**
     * Remove the specified tax from storage.
     *
     * @param  \App\Models\Modules\GeneralLedger\Tax  $tax
     * @return \Illuminate\Http\Response
     */
    public function destroy(Tax $tax)
    {
        // Check if this tax is used in any transactions
        $isUsed = false; // Implement logic to check if tax is used

        if ($isUsed) {
            return redirect()->route('admin.taxes.index')
                ->with('error', 'لا يمكن حذف هذه الضريبة لأنها مستخدمة في معاملات');
        }

        // Don't allow deleting the default tax
        if ($tax->is_default) {
            return redirect()->route('admin.taxes.index')
                ->with('error', 'لا يمكن حذف الضريبة الافتراضية');
        }

        $tax->delete();

        return redirect()->route('admin.taxes.index')
            ->with('success', 'تم حذف الضريبة بنجاح');
    }
}
