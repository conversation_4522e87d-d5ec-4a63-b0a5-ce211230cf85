@extends('layouts.admin')

@section('title', 'إدارة الاشتراكات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">الاشتراكات</h3>
                    <a href="{{ route('admin.subscriptions.subscriptions.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة اشتراك جديد
                    </a>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الاشتراك</th>
                                    <th>العميل</th>
                                    <th>الخطة</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($subscriptions as $subscription)
                                    <tr>
                                        <td>{{ $subscription->id }}</td>
                                        <td>{{ $subscription->subscription_number }}</td>
                                        <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                        <td>{{ $subscription->plan->name ?? 'غير محدد' }}</td>
                                        <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                        <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                        <td>
                                            @if ($subscription->status == 'active')
                                                <span class="badge bg-success">{{ $subscription->status_arabic }}</span>
                                            @elseif ($subscription->status == 'pending')
                                                <span class="badge bg-warning">{{ $subscription->status_arabic }}</span>
                                            @elseif ($subscription->status == 'expired')
                                                <span class="badge bg-danger">{{ $subscription->status_arabic }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $subscription->status_arabic }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $subscription->formatted_price_paid }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.subscriptions.subscriptions.show', $subscription) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.subscriptions.subscriptions.edit', $subscription) }}" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route('admin.subscriptions.subscriptions.destroy', $subscription) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد اشتراكات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $subscriptions->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
