@extends("layouts.admin")

@section("content")
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ __("Ticket Statuses") }}</h1>
        <a href="{{ route("admin.ticketing.ticket_statuses.create") }}" class="btn btn-primary">{{ __("Create New Status") }}</a>
    </div>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>{{ __("ID") }}</th>
                <th>{{ __("Name") }}</th>
                <th>{{ __("Type") }}</th>
                <th>{{ __("Color") }}</th>
                <th>{{ __("Default New") }}</th>
                <th>{{ __("Default Closed") }}</th>
                <th>{{ __("Active") }}</th>
                <th>{{ __("Actions") }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($statuses as $status)
                <tr>
                    <td>{{ $status->id }}</td>
                    <td>{{ $status->name }}</td>
                    <td>{{ ucfirst($status->type) }}</td>
                    <td>
                        <span style="display:inline-block; width: 20px; height: 20px; background-color: {{ $status->color ?? '#FFFFFF' }}; border: 1px solid #ccc;"></span>
                        {{ $status->color }}
                    </td>
                    <td>
                        @if($status->is_default_new)
                            <span class="badge badge-info">{{ __("Yes") }}</span>
                        @else
                            {{ __("No") }}
                        @endif
                    </td>
                    <td>
                        @if($status->is_default_closed)
                            <span class="badge badge-secondary">{{ __("Yes") }}</span>
                        @else
                            {{ __("No") }}
                        @endif
                    </td>
                    <td>
                        @if($status->is_active)
                            <span class="badge badge-success">{{ __("Yes") }}</span>
                        @else
                            <span class="badge badge-danger">{{ __("No") }}</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route("admin.ticketing.ticket_statuses.show", $status->id) }}" class="btn btn-sm btn-info">{{ __("View") }}</a>
                        <a href="{{ route("admin.ticketing.ticket_statuses.edit", $status->id) }}" class="btn btn-sm btn-warning">{{ __("Edit") }}</a>
                        <form action="{{ route("admin.ticketing.ticket_statuses.destroy", $status->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("{{ __("Are you sure you want to delete this status?") }}")">{{ __("Delete") }}</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="text-center">{{ __("No ticket statuses found.") }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $statuses->links() }}
</div>
@endsection

