@extends('layouts.admin')

@section('title', 'تعديل الصلاحية: {{ $permission->name }}')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تعديل الصلاحية: {{ $permission->name }}</h3>
                    <a href="{{ route('admin.permissions_system.permissions.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-right"></i> العودة للقائمة
                    </a>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.permissions_system.permissions.update', $permission->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">اسم الصلاحية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $permission->name) }}" required>
                                    <small class="form-text text-muted">مثال: إنشاء مستخدمين، تعديل فواتير</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="slug" class="form-label">معرف الصلاحية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="slug" name="slug" value="{{ old('slug', $permission->slug) }}" required>
                                    <small class="form-text text-muted">مثال: create-users, edit-invoices</small>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="group_name" class="form-label">المجموعة</label>
                                    <input type="text" class="form-control" id="group_name" name="group_name" value="{{ old('group_name', $permission->group_name) }}">
                                    <small class="form-text text-muted">مثال: المستخدمين، الفواتير، التقارير</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="module" class="form-label">الوحدة</label>
                                    <input type="text" class="form-control" id="module" name="module" value="{{ old('module', $permission->module) }}">
                                    <small class="form-text text-muted">مثال: permissions_system, sales, purchases</small>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $permission->description) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> حفظ التغييرات
                                </button>
                                <a href="{{ route('admin.permissions_system.permissions.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-x"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // توليد معرف الصلاحية تلقائيًا من الاسم إذا كان فارغًا
        $('#name').on('input', function() {
            if ($('#slug').val() === '') {
                let slug = $(this).val()
                    .toLowerCase()
                    .replace(/\s+/g, '-')           // استبدال المسافات بشرطات
                    .replace(/[^\w\-]+/g, '')       // إزالة الأحرف غير اللاتينية
                    .replace(/\-\-+/g, '-')         // استبدال الشرطات المتعددة بشرطة واحدة
                    .replace(/^-+/, '')             // إزالة الشرطات من بداية النص
                    .replace(/-+$/, '');            // إزالة الشرطات من نهاية النص

                $('#slug').val(slug);
            }
        });
    });
</script>
@endpush
