<?php

namespace App\Http\Controllers\Modules\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InventoryReportController extends Controller
{
    /**
     * Display inventory reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.reports.inventory.index');
    }

    /**
     * Generate stock levels report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function stockLevels(Request $request)
    {
        return view('admin.reports.inventory.stock_levels');
    }

    /**
     * Generate low stock report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function lowStock(Request $request)
    {
        return view('admin.reports.inventory.low_stock');
    }

    /**
     * Generate stock movements report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function stockMovements(Request $request)
    {
        return view('admin.reports.inventory.movements');
    }

    /**
     * Generate inventory valuation report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function inventoryValuation(Request $request)
    {
        return view('admin.reports.inventory.valuation');
    }

    /**
     * Generate ABC analysis report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function abcAnalysis(Request $request)
    {
        return view('admin.reports.inventory.abc_analysis');
    }

    /**
     * Generate aging report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function aging(Request $request)
    {
        return view('admin.reports.inventory.aging');
    }

    /**
     * Generate dead stock report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deadStock(Request $request)
    {
        return view('admin.reports.inventory.dead_stock');
    }
}
