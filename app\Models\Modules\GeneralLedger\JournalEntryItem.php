<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JournalEntryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        "journal_entry_id",
        "account_id",
        "debit_amount",
        "credit_amount",
        "description",
    ];

    protected $casts = [
        "debit_amount" => "decimal:2",
        "credit_amount" => "decimal:2",
    ];

    public function journalEntry()
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}

