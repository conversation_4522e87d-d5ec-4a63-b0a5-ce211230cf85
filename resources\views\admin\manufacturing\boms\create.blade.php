@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Add New Bill of Materials (BOM)</h1>
        <form action="{{ route("admin.manufacturing.boms.store") }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="name">BOM Name</label>
                <input type="text" name="name" id="name" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="description">Description</label>
                <textarea name="description" id="description" class="form-control"></textarea>
            </div>
            <div class="form-group">
                <label for="item_id">Item (Produces)</label>
                <select name="item_id" id="item_id" class="form-control" required>
                    {{-- @foreach($manufacturedItems as $item) --}}
                    {{-- <option value="{{ $item->id }}">{{ $item->name }} ({{ $item->item_code }})</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Produced Item</option> 
                </select>
            </div>
            <div class="form-group">
                <label for="version">Version</label>
                <input type="text" name="version" id="version" class="form-control" value="1.0" required>
            </div>
            <div class="form-group">
                <label for="quantity_produced">Quantity Produced (by this BOM)</label>
                <input type="number" step="0.0001" name="quantity_produced" id="quantity_produced" class="form-control" value="1.0000" required>
            </div>
            <div class="form-check">
                <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" checked>
                <label for="is_active" class="form-check-label">Is Active</label>
            </div>
            <div class="form-group">
                <label for="valid_from">Valid From</label>
                <input type="date" name="valid_from" id="valid_from" class="form-control">
            </div>
            <div class="form-group">
                <label for="valid_to">Valid To</label>
                <input type="date" name="valid_to" id="valid_to" class="form-control">
            </div>
            <div class="form-group">
                <label for="branch_id">Branch</label>
                <select name="branch_id" id="branch_id" class="form-control">
                     {{-- @foreach($branches as $branch) --}}
                    {{-- <option value="{{ $branch->id }}">{{ $branch->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Branch (Optional)</option>
                </select>
            </div>

            <h3 class="mt-4">BOM Items (Components)</h3>
            <div id="bom-items-container">
                {{-- JavaScript will add item rows here --}}
                <div class="row bom-item-row mb-2">
                    <div class="col-md-4">
                        <label>Component Item</label>
                        <select name="bom_items[0][item_id]" class="form-control bom-item-select">
                            {{-- @foreach($componentItems as $item) --}}
                            {{-- <option value="{{ $item->id }}">{{ $item->name }} ({{ $item->item_code }})</option> --}}
                            {{-- @endforeach --}}
                           <option value="">Select Component</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>Quantity</label>
                        <input type="number" step="0.0001" name="bom_items[0][quantity]" class="form-control" required>
                    </div>
                    <div class="col-md-3">
                        <label>Unit of Measure</label>
                        <input type="text" name="bom_items[0][unit_of_measure]" class="form-control" required>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-sm remove-bom-item">Remove</button>
                    </div>
                </div>
            </div>
            <button type="button" id="add-bom-item" class="btn btn-secondary mt-2">Add Component Item</button>

            <button type="submit" class="btn btn-success mt-4">Save BOM</button>
        </form>
    </div>

@push("scripts")
<script>
    document.addEventListener("DOMContentLoaded", function() {
        let itemIndex = 0;
        const container = document.getElementById("bom-items-container");
        const addButton = document.getElementById("add-bom-item");

        if (addButton) {
            addButton.addEventListener("click", function() {
                itemIndex++;
                const newRow = document.createElement("div");
                newRow.classList.add("row", "bom-item-row", "mb-2");
                newRow.innerHTML = `
                    <div class="col-md-4">
                        <select name="bom_items[${itemIndex}][item_id]" class="form-control bom-item-select">
                            {{-- @foreach($componentItems as $item) --}}
                            {{-- <option value="{{ $item->id }}">{{ $item->name }} ({{ $item->item_code }})</option> --}}
                            {{-- @endforeach --}}
                            <option value="">Select Component</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="number" step="0.0001" name="bom_items[${itemIndex}][quantity]" class="form-control" required>
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="bom_items[${itemIndex}][unit_of_measure]" class="form-control" required>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm remove-bom-item">Remove</button>
                    </div>
                `;
                container.appendChild(newRow);
            });
        }

        container.addEventListener("click", function(e) {
            if (e.target && e.target.classList.contains("remove-bom-item")) {
                e.target.closest(".bom-item-row").remove();
            }
        });
    });
</script>
@endpush

@endsection
