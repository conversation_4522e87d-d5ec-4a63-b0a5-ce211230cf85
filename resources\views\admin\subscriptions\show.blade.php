@extends('layouts.admin')

@section('title', 'تفاصيل الاشتراك')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل الاشتراك: {{ $subscription->subscription_number }}</h3>
                    <div>
                        <a href="{{ route('admin.subscriptions.subscriptions.edit', $subscription) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.subscriptions.subscriptions.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات الاشتراك</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">رقم الاشتراك</th>
                                            <td>{{ $subscription->subscription_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>العميل</th>
                                            <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>خطة الاشتراك</th>
                                            <td>{{ $subscription->plan->name ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ البدء</th>
                                            <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الانتهاء</th>
                                            <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>الأيام المتبقية</th>
                                            <td>
                                                @if($subscription->remaining_days > 0)
                                                    <span class="badge bg-success">{{ $subscription->remaining_days }} يوم</span>
                                                @else
                                                    <span class="badge bg-danger">منتهي</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if ($subscription->status == 'active')
                                                    <span class="badge bg-success">{{ $subscription->status_arabic }}</span>
                                                @elseif ($subscription->status == 'pending')
                                                    <span class="badge bg-warning">{{ $subscription->status_arabic }}</span>
                                                @elseif ($subscription->status == 'expired')
                                                    <span class="badge bg-danger">{{ $subscription->status_arabic }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $subscription->status_arabic }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>المبلغ المدفوع</th>
                                            <td>{{ $subscription->formatted_price_paid }}</td>
                                        </tr>
                                        <tr>
                                            <th>طريقة الدفع</th>
                                            <td>{{ $subscription->payment_method ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>مرجع الدفع</th>
                                            <td>{{ $subscription->payment_reference ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تجديد تلقائي</th>
                                            <td>
                                                @if ($subscription->auto_renew)
                                                    <span class="badge bg-success">نعم</span>
                                                @else
                                                    <span class="badge bg-danger">لا</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>تفاصيل الخطة</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscription->plan)
                                        <table class="table table-bordered">
                                            <tr>
                                                <th style="width: 30%">اسم الخطة</th>
                                                <td>{{ $subscription->plan->name }}</td>
                                            </tr>
                                            <tr>
                                                <th>السعر</th>
                                                <td>{{ $subscription->plan->formatted_price }}</td>
                                            </tr>
                                            <tr>
                                                <th>دورة الفوترة</th>
                                                <td>{{ $subscription->plan->billing_cycle_arabic }}</td>
                                            </tr>
                                            <tr>
                                                <th>الحد الأقصى للمستخدمين</th>
                                                <td>{{ $subscription->plan->max_users }}</td>
                                            </tr>
                                            <tr>
                                                <th>الحد الأقصى للفروع</th>
                                                <td>{{ $subscription->plan->max_branches }}</td>
                                            </tr>
                                            <tr>
                                                <th>المستخدمين الحاليين</th>
                                                <td>{{ $subscription->current_users_count }} / {{ $subscription->plan->max_users }}</td>
                                            </tr>
                                            <tr>
                                                <th>الفروع الحالية</th>
                                                <td>{{ $subscription->current_branches_count }} / {{ $subscription->plan->max_branches }}</td>
                                            </tr>
                                            <tr>
                                                <th>مساحة التخزين</th>
                                                <td>{{ $subscription->plan->storage_space_gb }} جيجابايت</td>
                                            </tr>
                                        </table>
                                        
                                        <h6 class="mt-3">الميزات المتاحة:</h6>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center">
                                                    @if($subscription->plan->has_pos)
                                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                    @else
                                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                    @endif
                                                    <span>نقاط البيع</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center">
                                                    @if($subscription->plan->has_inventory)
                                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                    @else
                                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                    @endif
                                                    <span>إدارة المخزون</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center">
                                                    @if($subscription->plan->has_accounting)
                                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                    @else
                                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                    @endif
                                                    <span>المحاسبة</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center">
                                                    @if($subscription->plan->has_manufacturing)
                                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                    @else
                                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                    @endif
                                                    <span>التصنيع</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <a href="{{ route('admin.subscriptions.plans.show', $subscription->plan) }}" class="btn btn-sm btn-primary mt-3">
                                            عرض تفاصيل الخطة
                                        </a>
                                    @else
                                        <p class="text-center">لا توجد معلومات عن الخطة</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($subscription->status != 'cancelled')
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>الإجراءات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h6>تجديد الاشتراك</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <form action="{{ route('admin.subscriptions.renew', $subscription) }}" method="POST">
                                                            @csrf
                                                            <div class="mb-3">
                                                                <label for="renew_end_date" class="form-label">تاريخ الانتهاء الجديد <span class="text-danger">*</span></label>
                                                                <input type="date" class="form-control" id="renew_end_date" name="end_date" value="{{ date('Y-m-d', strtotime('+1 month', strtotime($subscription->end_date))) }}" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="renew_price_paid" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                                                <div class="input-group">
                                                                    <input type="number" step="0.01" class="form-control" id="renew_price_paid" name="price_paid" value="{{ $subscription->plan->price ?? 0 }}" required>
                                                                    <span class="input-group-text">ريال</span>
                                                                </div>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="renew_payment_method" class="form-label">طريقة الدفع</label>
                                                                <select class="form-select" id="renew_payment_method" name="payment_method">
                                                                    <option value="">اختر طريقة الدفع</option>
                                                                    <option value="cash">نقدي</option>
                                                                    <option value="bank_transfer">تحويل بنكي</option>
                                                                    <option value="credit_card">بطاقة ائتمان</option>
                                                                    <option value="other">أخرى</option>
                                                                </select>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="renew_payment_reference" class="form-label">مرجع الدفع</label>
                                                                <input type="text" class="form-control" id="renew_payment_reference" name="payment_reference">
                                                            </div>
                                                            <button type="submit" class="btn btn-success">تجديد الاشتراك</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h6>إلغاء الاشتراك</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>سيؤدي إلغاء الاشتراك إلى إيقاف الخدمة للعميل. هل أنت متأكد من رغبتك في إلغاء هذا الاشتراك؟</p>
                                                        <form action="{{ route('admin.subscriptions.cancel', $subscription) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟')">
                                                            @csrf
                                                            <button type="submit" class="btn btn-danger">إلغاء الاشتراك</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>سجل المدفوعات</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscription->payments->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>رقم الإيصال</th>
                                                        <th>المبلغ</th>
                                                        <th>تاريخ الدفع</th>
                                                        <th>طريقة الدفع</th>
                                                        <th>الحالة</th>
                                                        <th>ملاحظات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($subscription->payments as $payment)
                                                        <tr>
                                                            <td>{{ $payment->id }}</td>
                                                            <td>{{ $payment->receipt_number }}</td>
                                                            <td>{{ $payment->formatted_amount }}</td>
                                                            <td>{{ $payment->payment_date->format('Y-m-d') }}</td>
                                                            <td>{{ $payment->payment_method }}</td>
                                                            <td>
                                                                @if ($payment->status == 'paid')
                                                                    <span class="badge bg-success">{{ $payment->status_arabic }}</span>
                                                                @elseif ($payment->status == 'pending')
                                                                    <span class="badge bg-warning">{{ $payment->status_arabic }}</span>
                                                                @elseif ($payment->status == 'failed')
                                                                    <span class="badge bg-danger">{{ $payment->status_arabic }}</span>
                                                                @else
                                                                    <span class="badge bg-secondary">{{ $payment->status_arabic }}</span>
                                                                @endif
                                                            </td>
                                                            <td>{{ $payment->notes ?? '-' }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا توجد مدفوعات مسجلة</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>سجل التغييرات</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscription->changes->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>نوع التغيير</th>
                                                        <th>التفاصيل</th>
                                                        <th>الخطة القديمة</th>
                                                        <th>الخطة الجديدة</th>
                                                        <th>تاريخ التغيير</th>
                                                        <th>فرق السعر</th>
                                                        <th>بواسطة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($subscription->changes as $change)
                                                        <tr>
                                                            <td>{{ $change->id }}</td>
                                                            <td>{{ $change->change_type_arabic }}</td>
                                                            <td>{{ $change->change_details }}</td>
                                                            <td>{{ $change->oldPlan->name ?? '-' }}</td>
                                                            <td>{{ $change->newPlan->name ?? '-' }}</td>
                                                            <td>{{ $change->effective_date->format('Y-m-d') }}</td>
                                                            <td>{{ $change->formatted_price_difference }}</td>
                                                            <td>{{ $change->user->name ?? 'غير محدد' }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا توجد تغييرات مسجلة</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($subscription->notes)
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>ملاحظات</h5>
                                    </div>
                                    <div class="card-body">
                                        {{ $subscription->notes }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
