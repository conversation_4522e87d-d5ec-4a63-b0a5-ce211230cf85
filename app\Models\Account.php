<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'account_type_id',
        'parent_id',
        'description_ar',
        'description_en',
        'is_control_account',
        'accepts_entries',
        'opening_balance_debit',
        'opening_balance_credit',
        'opening_balance_date',
        'branch_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_control_account' => 'boolean',
        'accepts_entries' => 'boolean',
        'opening_balance_debit' => 'decimal:2',
        'opening_balance_credit' => 'decimal:2',
        'opening_balance_date' => 'date',
    ];

    /**
     * Get the account type that owns the account.
     */
    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    /**
     * Get the parent account.
     */
    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * Get the children accounts.
     */
    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * Get all descendants (children, grandchildren, etc.)
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Check if account has children
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Get the name attribute based on current locale
     */
    public function getNameAttribute()
    {
        return $this->name_ar ?? $this->name_en ?? 'غير محدد';
    }

    /**
     * Get the description attribute based on current locale
     */
    public function getDescriptionAttribute()
    {
        return $this->description_ar ?? $this->description_en ?? '';
    }

    /**
     * Get the full account path (parent > child > grandchild)
     */
    public function getFullPathAttribute()
    {
        $path = collect([$this->name]);
        $parent = $this->parent;

        while ($parent) {
            $path->prepend($parent->name);
            $parent = $parent->parent;
        }

        return $path->implode(' > ');
    }

    /**
     * Get the account level (0 for root accounts, 1 for first level children, etc.)
     */
    public function getLevelAttribute()
    {
        $level = 0;
        $parent = $this->parent;

        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }

        return $level;
    }

    /**
     * Scope to get only root accounts (accounts with no parent)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get only leaf accounts (accounts with no children)
     */
    public function scopeLeaves($query)
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * Scope to get active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
