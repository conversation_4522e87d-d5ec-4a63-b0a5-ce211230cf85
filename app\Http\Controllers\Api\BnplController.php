<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BnplProviderSetting;
use App\Services\Bnpl\TabbyService;
use App\Services\Bnpl\TamaraService;
use App\Services\Bnpl\BnplServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class BnplController extends Controller
{
    protected TabbyService $tabbyService;
    protected TamaraService $tamaraService;

    public function __construct(TabbyService $tabbyService, TamaraService $tamaraService)
    {
        $this->tabbyService = $tabbyService;
        $this->tamaraService = $tamaraService;
    }

    private function getProviderService(string $providerName): ?BnplServiceInterface
    {
        $setting = BnplProviderSetting::where("provider_name", $providerName)
                                      ->where("is_active", true)
                                      // Consider environment for fetching the correct active setting
                                      ->where("environment", config("app.env") === "production" ? "production" : "sandbox")
                                      ->first();

        if (!$setting) {
            Log::error("Active BNPL provider setting not found for: " . $providerName . " in environment: " . (config("app.env") === "production" ? "production" : "sandbox"));
            return null;
        }

        $service = null;
        $serviceSettings = [
            "api_key_sandbox" => $setting->api_key_sandbox,
            "api_key_production" => $setting->api_key_production,
            "public_key_sandbox" => $setting->public_key_sandbox,
            "public_key_production" => $setting->public_key_production,
            "notification_token_sandbox" => $setting->notification_token_sandbox,
            "notification_token_production" => $setting->notification_token_production,
            "webhook_secret_sandbox" => $setting->webhook_secret_sandbox,
            "webhook_secret_production" => $setting->webhook_secret_production,
            "merchant_code_sandbox" => $setting->merchant_code_sandbox,
            "merchant_code_production" => $setting->merchant_code_production,
            "environment" => $setting->environment,
        ];

        if ($providerName === "tabby") {
            $service = $this->tabbyService;
        } elseif ($providerName === "tamara") {
            $service = $this->tamaraService;
        }

        if ($service) {
            $service->initialize($serviceSettings);
        }
        return $service;
    }

    public function initiateCheckout(Request $request)
    {
        try {
            $validatedData = $request->validate([
                "provider" => "required|string|in:tabby,tamara",
                "order_details" => "required|array",
                // Further validation for order_details structure is recommended here
            ]);

            $providerName = $validatedData["provider"];
            $orderDetails = $validatedData["order_details"];

            $service = $this->getProviderService($providerName);

            if (!$service) {
                return response()->json(["success" => false, "error_message" => "BNPL provider " . $providerName . " is not configured or active for the current environment."], 400);
            }
            
            // Construct merchant URLs dynamically
            $orderReference = $orderDetails["order_reference_id"] ?? $orderDetails["payment"]["order"]["reference_id"] ?? uniqid("order_");

            if ($providerName === "tabby") {
                // Tabby expects 'payment' structure within orderDetails
                $orderDetails["payment"]["order"]["reference_id"] = $orderReference;
                $orderDetails["merchant_urls"] = [
                    "success" => url("/api/bnpl/callback/tabby/success?order_id=" . $orderReference),
                    "cancel" => url("/api/bnpl/callback/tabby/cancel?order_id=" . $orderReference),
                    "failure" => url("/api/bnpl/callback/tabby/failure?order_id=" . $orderReference),
                ];
            } elseif ($providerName === "tamara") {
                 $orderDetails["order_reference_id"] = $orderReference;
                 $orderDetails["merchant_url"] = [
                    "success" => url("/api/bnpl/callback/tamara/success?order_id=" . $orderReference),
                    "failure" => url("/api/bnpl/callback/tamara/failure?order_id=" . $orderReference),
                    "cancel" => url("/api/bnpl/callback/tamara/cancel?order_id=" . $orderReference),
                    "notification" => route("api.bnpl.webhook.tamara")
                ];
            }

            $result = $service->createCheckoutSession($orderDetails);

            return response()->json($result, $result["success"] ? 200 : 400);

        } catch (ValidationException $e) {
            return response()->json(["success" => false, "error_message" => "Validation failed", "errors" => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error("BNPL initiateCheckout exception: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(["success" => false, "error_message" => "An unexpected error occurred: " . $e->getMessage()], 500);
        }
    }

    public function checkPaymentStatus(Request $request)
    {
        try {
            $validatedData = $request->validate([
                "provider" => "required|string|in:tabby,tamara",
                "order_id" => "required|string", // This is the provider's order ID / payment ID / session ID
            ]);

            $providerName = $validatedData["provider"];
            $providerOrderId = $validatedData["order_id"];

            $service = $this->getProviderService($providerName);

            if (!$service) {
                return response()->json(["success" => false, "error_message" => "BNPL provider " . $providerName . " is not configured or active."], 400);
            }

            $result = $service->getOrderStatus($providerOrderId);
            return response()->json($result, $result["success"] ? 200 : 400);

        } catch (ValidationException $e) {
            return response()->json(["success" => false, "error_message" => "Validation failed", "errors" => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error("BNPL checkPaymentStatus exception: " . $e->getMessage());
            return response()->json(["success" => false, "error_message" => "An unexpected error occurred."], 500);
        }
    }

    // Tabby Webhook Handler
    public function handleTabbyWebhook(Request $request)
    {
        Log::info("Tabby webhook received", ["headers" => $request->headers->all(), "body" => $request->getContent()]);
        $service = $this->getProviderService("tabby");
        if (!$service) {
            Log::error("Tabby webhook: Service not available or configured.");
            return response()->json(["success" => false, "error_message" => "Tabby service not configured."], 500);
        }

        if (!$service->verifyWebhookSignature($request->headers->all(), $request->getContent())) {
            Log::warning("Tabby webhook: Signature verification failed.");
            return response()->json(["success" => false, "error_message" => "Signature verification failed."], 403);
        }

        $webhookData = $request->json()->all();
        $result = $service->handleWebhook($webhookData);
        // TODO: Implement application-specific order update logic based on $result or $webhookData
        return response()->json($result, $result["success"] ? 200 : 400);
    }

    // Tamara Webhook Handler
    public function handleTamaraWebhook(Request $request)
    {
        Log::info("Tamara webhook received", ["headers" => $request->headers->all(), "body" => $request->getContent()]);
        $service = $this->getProviderService("tamara");
        if (!$service) {
            Log::error("Tamara webhook: Service not available or configured.");
            return response()->json(["success" => false, "error_message" => "Tamara service not configured."], 500);
        }

        if (!$service->verifyWebhookSignature($request->headers->all(), $request->getContent())) {
            Log::warning("Tamara webhook: Signature verification failed.");
            return response()->json(["success" => false, "error_message" => "Signature verification failed."], 403);
        }

        $webhookData = $request->json()->all();
        $result = $service->handleWebhook($webhookData);
        // TODO: Implement application-specific order update logic based on $result or $webhookData
        return response()->json($result, $result["success"] ? 200 : 400);
    }

    // Tamara Callbacks
    public function handleTamaraSuccessCallback(Request $request)
    {
        $orderId = $request->query("orderId"); // Tamara typically sends orderId in query
        $paymentStatus = $request->query("paymentStatus");
        Log::info("Tamara success callback received", ["orderId" => $orderId, "paymentStatus" => $paymentStatus, "query" => $request->all()]);
        // Here, you would typically fetch the order from your DB using $orderId (your internal reference)
        // Then, you might call Tamara's getOrderStatus API to confirm the status before marking as successful.
        // For now, just return a success message or redirect to a success page.
        // This is a client-side redirect URL, so it should ideally show a success page to the user.
        return response()->json(["success" => true, "message" => "Tamara payment successful for order: " . $orderId, "status" => $paymentStatus]);
    }

    public function handleTamaraFailureCallback(Request $request)
    {
        $orderId = $request->query("orderId");
        Log::warning("Tamara failure callback received", ["orderId" => $orderId, "query" => $request->all()]);
        return response()->json(["success" => false, "message" => "Tamara payment failed for order: " . $orderId]);
    }

    public function handleTamaraCancelCallback(Request $request)
    {
        $orderId = $request->query("orderId");
        Log::info("Tamara cancel callback received", ["orderId" => $orderId, "query" => $request->all()]);
        return response()->json(["success" => true, "message" => "Tamara payment cancelled for order: " . $orderId]);
    }
    
    // Tabby Callbacks (similar structure if needed, often handled by webhook primarily)
     public function handleTabbySuccessCallback(Request $request)
    {
        $orderId = $request->query("order_id"); 
        Log::info("Tabby success callback received", ["orderId" => $orderId, "query" => $request->all()]);
        return response()->json(["success" => true, "message" => "Tabby payment successful for order: " . $orderId]);
    }

    public function handleTabbyFailureCallback(Request $request)
    {
        $orderId = $request->query("order_id");
        Log::warning("Tabby failure callback received", ["orderId" => $orderId, "query" => $request->all()]);
        return response()->json(["success" => false, "message" => "Tabby payment failed for order: " . $orderId]);
    }

    public function handleTabbyCancelCallback(Request $request)
    {
        $orderId = $request->query("order_id");
        Log::info("Tabby cancel callback received", ["orderId" => $orderId, "query" => $request->all()]);
        return response()->json(["success" => true, "message" => "Tabby payment cancelled for order: " . $orderId]);
    }
}

