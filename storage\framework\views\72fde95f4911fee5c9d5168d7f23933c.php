<?php $__env->startSection('title', 'اختبار رمز الريال المكدس'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- العنوان الرئيسي -->
            <div class="card border-success shadow-lg">
                <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <h1 class="display-4 mb-3">
                        <i class="bi bi-currency-exchange me-3"></i>
                        رمز الريال السعودي المكدس
                    </h1>
                    <div class="display-1 mb-3" style="font-size: 8rem;">
                        <span class="riyal-stacked-large">
                            <span class="top-letter">ر</span>
                            <span class="bottom-letters">ال</span>
                        </span>
                    </div>
                    <p class="lead mb-0">الرمز المكدس للريال السعودي - حرف الراء فوق الألف واللام</p>
                </div>
            </div>

            <!-- مقارنة الرموز -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h3 class="mb-0">
                                <i class="bi bi-compare me-2"></i>
                                مقارنة أشكال رمز الريال السعودي
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="card border-primary h-100">
                                        <div class="card-header bg-primary text-white">
                                            <h5>الرمز المكدس الجديد</h5>
                                        </div>
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <div style="font-size: 4rem;" class="mb-3">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <p class="text-muted">حرف الراء فوق الألف واللام</p>
                                            <div class="mt-3">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                                1,000.00
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success h-100">
                                        <div class="card-header bg-success text-white">
                                            <h5>الرمز البسيط</h5>
                                        </div>
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <div style="font-size: 4rem;" class="mb-3">
                                                <span class="riyal-simple">ر.س</span>
                                            </div>
                                            <p class="text-muted">ر.س (ريال سعودي)</p>
                                            <div class="mt-3">
                                                1,000.00 ر.س
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-warning h-100">
                                        <div class="card-header bg-warning text-dark">
                                            <h5>الرمز Unicode</h5>
                                        </div>
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <div style="font-size: 4rem;" class="mb-3">
                                                ﷼
                                            </div>
                                            <p class="text-muted">﷼ (U+FDFC)</p>
                                            <div class="mt-3">
                                                1,000.00 ﷼
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبار الأحجام -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h3 class="mb-0">
                                <i class="bi bi-zoom-in me-2"></i>
                                اختبار الرمز المكدس بأحجام مختلفة
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2 mb-4">
                                    <div class="card border-light shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-muted">صغير</h6>
                                            <div style="font-size: 1rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">1rem</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-4">
                                    <div class="card border-success shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-success">عادي</h6>
                                            <div style="font-size: 1.5rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">1.5rem</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-4">
                                    <div class="card border-info shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-info">متوسط</h6>
                                            <div style="font-size: 2rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">2rem</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-4">
                                    <div class="card border-warning shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-warning">كبير</h6>
                                            <div style="font-size: 3rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">3rem</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-4">
                                    <div class="card border-danger shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-danger">كبير جداً</h6>
                                            <div style="font-size: 4rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">4rem</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 mb-4">
                                    <div class="card border-dark shadow-sm h-100">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <h6 class="card-title text-dark">عملاق</h6>
                                            <div style="font-size: 5rem;">
                                                <span class="riyal-stacked">
                                                    <span class="top-letter">ر</span>
                                                    <span class="bottom-letters">ال</span>
                                                </span>
                                            </div>
                                            <small class="text-muted mt-2">5rem</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبار الوظائف -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-code-slash me-2"></i>
                                اختبار Helper Functions
                            </h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tr>
                                    <td><strong>getStackedSymbol():</strong></td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::getStackedSymbol(); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>getSimpleSymbol():</strong></td>
                                    <td><?php echo e(\App\Helpers\CurrencyHelper::getSimpleSymbol()); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>format() مكدس:</strong></td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::format(1000, 'SAR', true, true); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>format() بسيط:</strong></td>
                                    <td><?php echo \App\Helpers\CurrencyHelper::format(1000, 'SAR', true, false); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-calculator me-2"></i>
                                أمثلة المبالغ
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>مبلغ صغير:</span>
                                    <span class="fs-6"><?php echo \App\Helpers\CurrencyHelper::format(25.50, 'SAR', true, true); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>مبلغ متوسط:</span>
                                    <span class="fs-5"><?php echo \App\Helpers\CurrencyHelper::format(1500, 'SAR', true, true); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>مبلغ كبير:</span>
                                    <span class="fs-4"><?php echo \App\Helpers\CurrencyHelper::format(125000, 'SAR', true, true); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>مبلغ ضخم:</span>
                                    <span class="fs-3"><?php echo \App\Helpers\CurrencyHelper::format(1500000, 'SAR', true, true); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبار في السياقات المختلفة -->
            <div class="row mt-4 mb-4">
                <div class="col-md-12">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h4 class="mb-0">
                                <i class="bi bi-grid me-2"></i>
                                اختبار في السياقات المختلفة
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <h6>في الجداول:</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>البند</th>
                                                <th>المبلغ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>المجموع</td>
                                                <td><?php echo \App\Helpers\CurrencyHelper::format(2500, 'SAR', true, true); ?></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-md-3">
                                    <h6>في البطاقات:</h6>
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5>المبيعات اليوم</h5>
                                            <h3 class="text-success"><?php echo \App\Helpers\CurrencyHelper::format(12500, 'SAR', true, true); ?></h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h6>في التنبيهات:</h6>
                                    <div class="alert alert-success">
                                        <strong>تم الدفع:</strong><br>
                                        <?php echo \App\Helpers\CurrencyHelper::format(750, 'SAR', true, true); ?>

                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h6>في الأزرار:</h6>
                                    <button class="btn btn-primary btn-lg">
                                        ادفع <?php echo \App\Helpers\CurrencyHelper::format(199, 'SAR', true, true); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/stacked-riyal-test.blade.php ENDPATH**/ ?>