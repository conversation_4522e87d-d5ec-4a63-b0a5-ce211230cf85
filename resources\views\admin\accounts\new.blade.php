@extends('layouts.admin')

@section('title', 'دليل الحسابات')

@section('header', 'دليل الحسابات')

@push('scripts')
<script>
// تعطيل DataTables فوراً قبل أي شيء آخر
(function() {
    // حفظ jQuery الأصلي
    var originalJQuery = window.jQuery;

    // إعادة تعريف jQuery لتعطيل DataTables
    Object.defineProperty(window, 'jQuery', {
        get: function() {
            return originalJQuery;
        },
        set: function(newJQuery) {
            originalJQuery = newJQuery;
            if (newJQuery && newJQuery.fn) {
                // تعطيل DataTables
                newJQuery.fn.DataTable = function() {
                    console.log('DataTables blocked completely');
                    return this;
                };
                newJQuery.fn.dataTable = function() {
                    console.log('dataTable blocked completely');
                    return this;
                };
            }
        }
    });

    // تعطيل فوري إذا كان jQuery محمل بالفعل
    if (window.jQuery && window.jQuery.fn) {
        window.jQuery.fn.DataTable = function() {
            console.log('DataTables blocked immediately');
            return this;
        };
        window.jQuery.fn.dataTable = function() {
            console.log('dataTable blocked immediately');
            return this;
        };
    }
})();
</script>
@endpush

@push('styles')
<link rel="stylesheet" href="{{ asset('css/modern-accounts.css') }}">
<script>
// حماية فورية من DataTables
(function() {
    // تعطيل DataTables فوراً قبل تحميل أي شيء
    window.accountsPageDataTablesDisabled = true;

    // منع تطبيق DataTables على مستوى النافذة
    if (typeof window.jQuery !== 'undefined' && window.jQuery.fn) {
        if (window.jQuery.fn.DataTable) {
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables blocked on accounts page');
                return this;
            };
        }
        if (window.jQuery.fn.dataTable) {
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable blocked on accounts page');
                return this;
            };
        }
    }

    // منع تحميل DataTables عند تحميل jQuery لاحقاً
    var originalJQuery = window.jQuery;
    Object.defineProperty(window, 'jQuery', {
        get: function() { return originalJQuery; },
        set: function(val) {
            originalJQuery = val;
            if (val && val.fn) {
                if (val.fn.DataTable) {
                    val.fn.DataTable = function() {
                        console.log('DataTables blocked on accounts page');
                        return this;
                    };
                }
                if (val.fn.dataTable) {
                    val.fn.dataTable = function() {
                        console.log('dataTable blocked on accounts page');
                        return this;
                    };
                }
            }
        }
    });
})();
</script>
<style>
/* ===== COMMON STYLES ===== */
.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.btn-icon {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-icon i {
    margin-right: 5px;
}

/* ===== COMMON STYLES ===== */

/* Search and filters */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #d1d3e2;
}

/* Custom table styling to replace Bootstrap table class */
.accounts-custom-table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.accounts-custom-table th,
.accounts-custom-table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.accounts-custom-table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.accounts-custom-table tbody + tbody {
    border-top: 2px solid #dee2e6;
}

/* إخفاء عناصر DataTables في صفحة الحسابات */
.accounts-page-no-datatables .dataTables_wrapper,
.accounts-page-no-datatables .dataTables_length,
.accounts-page-no-datatables .dataTables_filter,
.accounts-page-no-datatables .dataTables_info,
.accounts-page-no-datatables .dataTables_paginate {
    display: none !important;
}

/* منع ظهور أي عناصر DataTables على جداول الحسابات */
.modern-accounts-table_wrapper,
.accounts-table_wrapper,
.accounts-custom-table_wrapper {
    display: none !important;
}
</style>
@endpush

@section('content')
<script>
// حماية إضافية فورية
if (window.jQuery && window.jQuery.fn) {
    if (window.jQuery.fn.DataTable) {
        window.jQuery.fn.DataTable = function() {
            console.log('DataTables blocked in content section');
            return this;
        };
    }
    if (window.jQuery.fn.dataTable) {
        window.jQuery.fn.dataTable = function() {
            console.log('dataTable blocked in content section');
            return this;
        };
    }
}
// إزالة class table فوراً
if (window.jQuery) {
    window.jQuery(function() {
        window.jQuery('.modern-accounts-table, .accounts-table').removeClass('table').addClass('accounts-custom-table');
    });
}
</script>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-journal-bookmark-fill text-primary me-2"></i>إدارة الحسابات</h5>
                    <a href="{{ route('admin.accounts.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> إضافة حساب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (session("success"))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session("success") }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session("error"))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session("error") }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if ($errors->any())
        <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="admin-card mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0"><i class="bi bi-journal-bookmark-fill text-primary me-2"></i>جميع الحسابات</h5>
        </div>
            <ul class="nav nav-tabs mb-4" id="accountTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="tree-tab" data-bs-toggle="tab" data-bs-target="#tree" type="button" role="tab" aria-controls="tree" aria-selected="true">
                        <i class="bi bi-diagram-3 me-1"></i> عرض شجري
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="table-tab" data-bs-toggle="tab" data-bs-target="#table" type="button" role="tab" aria-controls="table" aria-selected="false">
                        <i class="bi bi-table me-1"></i> عرض جدولي
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="accountTabsContent">
                <!-- عرض شجري -->
                <div class="tab-pane fade show active" id="tree" role="tabpanel" aria-labelledby="tree-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex gap-2">
                            <button id="expandAll" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrows-expand me-1"></i> توسيع الكل
                            </button>
                            <button id="collapseAll" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrows-collapse me-1"></i> طي الكل
                            </button>
                        </div>
                        <div class="search-box">
                            <input type="text" id="treeSearch" class="form-control form-control-sm" placeholder="بحث في الحسابات...">
                            <i class="bi bi-search search-icon"></i>
                        </div>
                    </div>

                    @include('admin.accounts._modern_tree_view')
                </div>

                <!-- عرض جدولي -->
                <div class="tab-pane fade" id="table" role="tabpanel" aria-labelledby="table-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-funnel me-1"></i> تصفية حسب النوع
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                <li><button class="dropdown-item filter-accounts" data-type="all">جميع الحسابات</button></li>
                                <li><hr class="dropdown-divider"></li>
                                @foreach($accountTypes as $type)
                                    <li>
                                        <button class="dropdown-item filter-accounts" data-type="{{ $type->slug }}">
                                            <span class="type-indicator type-{{ $type->slug }} me-2"></span>
                                            {{ $type->name_ar }}
                                        </button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        <div class="search-box">
                            <input type="text" id="tableSearch" class="form-control form-control-sm" placeholder="بحث في الحسابات...">
                            <i class="bi bi-search search-icon"></i>
                        </div>
                    </div>

                    @include('admin.accounts._modern_table_view')
                </div>
            </div>
            </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // تعطيل DataTables بالكامل في هذه الصفحة
    window.jQuery = window.jQuery || {};
    if (window.jQuery.fn) {
        delete window.jQuery.fn.DataTable;
        delete window.jQuery.fn.dataTable;
        window.jQuery.fn.DataTable = function() {
            console.log('DataTables تم تعطيله في صفحة الحسابات');
            return this;
        };
        window.jQuery.fn.dataTable = function() {
            console.log('dataTable تم تعطيله في صفحة الحسابات');
            return this;
        };
    }

    // منع تطبيق DataTables فوراً عند تحميل الصفحة
    $(document).ready(function() {
        // إزالة class "table" من جداول الحسابات فوراً
        $('.modern-accounts-table, .accounts-table').removeClass('table').addClass('accounts-custom-table');

        // منع أي تطبيق لـ DataTables
        if ($.fn.DataTable) {
            $('.modern-accounts-table, .accounts-table, .accounts-custom-table').each(function() {
                if ($.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable().destroy();
                }
            });
        }

        // إضافة معرف للصفحة
        $('body').addClass('accounts-page-no-datatables');

        // تعطيل تطبيق DataTables على مستوى الصفحة
        window.accountsPageDataTablesDisabled = true;

        // منع تطبيق DataTables العام
        if (window.jQuery && window.jQuery.fn.DataTable) {
            var originalDataTableFn = window.jQuery.fn.DataTable;
            window.jQuery.fn.DataTable = function(options) {
                if (window.accountsPageDataTablesDisabled && (
                    $(this).hasClass('modern-accounts-table') ||
                    $(this).hasClass('accounts-table') ||
                    $(this).hasClass('accounts-custom-table') ||
                    $(this).hasClass('table')
                )) {
                    console.log('DataTables تم منعه على صفحة الحسابات');
                    return this;
                }
                return originalDataTableFn.call(this, options);
            };
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Toggle tree nodes with animation
        document.querySelectorAll('.tree-toggle, .modern-tree-node-toggle').forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.stopPropagation();
                this.querySelector('i').classList.toggle('bi-chevron-left');
                this.querySelector('i').classList.toggle('bi-chevron-down');

                // Get the target collapse element
                const targetId = this.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);

                // Toggle the clicked node with smooth animation
                if (target.classList.contains('show')) {
                    target.classList.remove('show');
                } else {
                    target.classList.add('show');
                }
            });
        });

        // Toggle account type sections
        document.querySelectorAll('.account-type-header').forEach(function(header) {
            header.addEventListener('click', function() {
                this.querySelector('.toggle-icon').classList.toggle('bi-chevron-down');
                this.querySelector('.toggle-icon').classList.toggle('bi-chevron-right');

                // Toggle the target
                const targetId = this.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                target.classList.toggle('show');
            });
        });

        // Expand all button
        document.getElementById('expandAll').addEventListener('click', function() {
            // Expand all account type sections
            document.querySelectorAll('.account-type-header').forEach(function(header) {
                const targetId = header.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                target.classList.add('show');
                header.querySelector('.toggle-icon').classList.remove('bi-chevron-right');
                header.querySelector('.toggle-icon').classList.add('bi-chevron-down');
            });

            // Expand all tree nodes
            document.querySelectorAll('.tree-toggle, .modern-tree-node-toggle').forEach(function(toggle) {
                const targetId = toggle.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                target.classList.add('show');
                toggle.querySelector('i').classList.remove('bi-chevron-left');
                toggle.querySelector('i').classList.add('bi-chevron-down');
            });
        });

        // Collapse all button
        document.getElementById('collapseAll').addEventListener('click', function() {
            // Collapse all tree nodes
            document.querySelectorAll('.tree-toggle, .modern-tree-node-toggle').forEach(function(toggle) {
                const targetId = toggle.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                target.classList.remove('show');
                toggle.querySelector('i').classList.remove('bi-chevron-down');
                toggle.querySelector('i').classList.add('bi-chevron-left');
            });

            // Keep account type sections open
            document.querySelectorAll('.account-type-header .toggle-icon').forEach(function(icon) {
                icon.classList.remove('bi-chevron-right');
                icon.classList.add('bi-chevron-down');
            });
        });

        // Tree View Search
        document.getElementById('treeSearch').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();

            if (searchTerm.length > 0) {
                // Hide all nodes first
                document.querySelectorAll('.modern-tree-node').forEach(function(node) {
                    node.style.display = 'none';
                });

                // Check each account type section
                document.querySelectorAll('.account-type-card').forEach(function(section) {
                    let hasMatch = false;

                    // Check each node in this section
                    section.querySelectorAll('.modern-tree-node').forEach(function(node) {
                        const accountName = node.querySelector('.modern-tree-node-name').textContent.toLowerCase();
                        const accountCode = node.querySelector('.modern-tree-node-code').textContent.toLowerCase();

                        if (accountName.includes(searchTerm) || accountCode.includes(searchTerm)) {
                            hasMatch = true;
                            node.style.display = '';

                            // Expand parent nodes
                            let parent = node.parentElement.closest('.modern-tree-node');
                            while (parent) {
                                parent.style.display = '';
                                const collapseId = parent.querySelector('.modern-tree-node-toggle')?.getAttribute('data-bs-target');
                                if (collapseId) {
                                    document.querySelector(collapseId).classList.add('show');
                                }
                                parent = parent.parentElement.closest('.modern-tree-node');
                            }
                        }
                    });

                    // Show/hide the entire section based on matches
                    if (hasMatch) {
                        section.style.display = '';
                        section.querySelector('.collapse').classList.add('show');
                    } else {
                        section.style.display = 'none';
                    }
                });

                // Highlight search terms
                document.querySelectorAll('.modern-tree-node-name').forEach(function(element) {
                    const text = element.innerHTML;
                    const highlightedText = text.replace(
                        new RegExp(searchTerm, 'gi'),
                        match => `<span class="highlight" style="background-color: yellow;">${match}</span>`
                    );
                    element.innerHTML = highlightedText;
                });
            } else {
                // Show all when search is cleared
                document.querySelectorAll('.account-type-card').forEach(function(section) {
                    section.style.display = '';
                });
                document.querySelectorAll('.modern-tree-node').forEach(function(node) {
                    node.style.display = '';
                });

                // Remove highlighting
                document.querySelectorAll('.modern-tree-node-name').forEach(function(element) {
                    const text = element.innerHTML;
                    element.innerHTML = text.replace(/<span class="highlight"[^>]*>(.*?)<\/span>/gi, '$1');
                });
            }
        });

        // Table View Search
        document.getElementById('tableSearch').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            filterTableRows(searchTerm);
        });

        // Filter accounts by type
        document.querySelectorAll('.filter-accounts').forEach(function(button) {
            button.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                const searchTerm = document.getElementById('tableSearch').value.toLowerCase();

                // Update active state in dropdown
                document.querySelectorAll('.filter-accounts').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');

                // Filter rows
                filterTableRows(searchTerm, type);
            });
        });

        // Function to filter table rows
        function filterTableRows(searchTerm = '', accountType = 'all') {
            document.querySelectorAll('.modern-accounts-table tbody tr').forEach(function(row) {
                const code = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
                const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const type = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const typeBadge = row.querySelector('[class*="type-badge-"]');
                const typeClass = typeBadge ? typeBadge.className.match(/type-badge-([^\s]+)/)?.[1] : '';

                let matchesSearch = true;
                let matchesType = true;

                // Check search term
                if (searchTerm.length > 0) {
                    matchesSearch = code.includes(searchTerm) || name.includes(searchTerm) || type.includes(searchTerm);
                }

                // Check account type
                if (accountType !== 'all') {
                    matchesType = typeClass === accountType;
                }

                // Show/hide row based on both filters
                if (matchesSearch && matchesType) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // تعطيل DataTables التلقائي على الجداول في هذه الصفحة
        $(document).ready(function() {
            // منع تطبيق DataTables على جداول الحسابات
            if ($.fn.DataTable) {
                // إزالة class "table" من جداول الحسابات لمنع التطبيق التلقائي
                $('.modern-accounts-table, .accounts-table').removeClass('table').addClass('accounts-custom-table');

                // إذا كان DataTables مطبق بالفعل، قم بإزالته
                $('.modern-accounts-table, .accounts-table, .accounts-custom-table').each(function() {
                    if ($.fn.DataTable.isDataTable(this)) {
                        $(this).DataTable().destroy();
                        $(this).removeClass('dataTable');
                    }
                });

                // منع أي محاولة لتطبيق DataTables على هذه الجداول
                var originalDataTable = $.fn.DataTable;
                $.fn.DataTable = function(options) {
                    if ($(this).hasClass('modern-accounts-table') || $(this).hasClass('accounts-table') || $(this).hasClass('accounts-custom-table')) {
                        console.log('تم منع تطبيق DataTables على جدول الحسابات');
                        return this;
                    }
                    return originalDataTable.call(this, options);
                };

                // منع تطبيق DataTables العام على جميع الجداول في هذه الصفحة
                var originalJQueryDataTable = $.fn.dataTable;
                $.fn.dataTable = function(options) {
                    if ($(this).hasClass('modern-accounts-table') || $(this).hasClass('accounts-table') || $(this).hasClass('accounts-custom-table')) {
                        console.log('تم منع تطبيق dataTable على جدول الحسابات');
                        return this;
                    }
                    return originalJQueryDataTable.call(this, options);
                };

                // إضافة معرف خاص للصفحة لمنع التطبيق العام
                $('body').addClass('accounts-page-no-datatables');
            }
        });

        // منع تطبيق DataTables من الصفحات الأخرى
        $(document).on('DOMContentLoaded', function() {
            if ($('body').hasClass('accounts-page-no-datatables')) {
                // تعطيل أي محاولة لتطبيق DataTables على الجداول
                $('.table').each(function() {
                    if ($(this).hasClass('modern-accounts-table') || $(this).hasClass('accounts-table') || $(this).hasClass('accounts-custom-table')) {
                        $(this).removeClass('table');
                    }
                });
            }
        });

        // Handle tab switching via URL hash
        if (window.location.hash) {
            const hash = window.location.hash;
            if (hash === '#table') {
                const tableTab = document.querySelector('#table-tab');
                const tabInstance = new bootstrap.Tab(tableTab);
                tabInstance.show();
            }
        }

        // Update URL hash on tab change
        const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabs.forEach(function(tab) {
            tab.addEventListener('shown.bs.tab', function(e) {
                const target = e.target.getAttribute('data-bs-target');
                if (history.pushState) {
                    history.pushState(null, null, target);
                } else {
                    location.hash = target;
                }
            });
        });
    });
</script>
@endpush
