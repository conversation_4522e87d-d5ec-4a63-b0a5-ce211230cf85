<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_production_costs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('work_order_id')->constrained('manufacturing_work_orders')->onDelete('cascade');
            $table->foreignId('item_id')->nullable()->constrained('manufacturing_items')->comment('Specific item cost is related to, if applicable (e.g. finished good)');
            $table->enum('cost_type', ['material', 'labor', 'overhead', 'other']);
            $table->text('description')->nullable();
            $table->decimal('amount', 15, 4);
            $table->foreignId('currency_id')->nullable()->constrained('currencies')->onDelete('set null'); // Assuming currencies table exists
            $table->foreignId('account_id')->nullable()->constrained('accounts')->onDelete('set null')->comment('Account to debit/credit for this cost');
            $table->timestamp('cost_date')->useCurrent();
            $table->foreignId('created_by_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_production_costs');
    }
};
