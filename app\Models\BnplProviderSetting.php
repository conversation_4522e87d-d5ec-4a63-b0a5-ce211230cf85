<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class BnplProviderSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'provider_name',
        'display_name',
        'api_key_sandbox',
        'api_key_production',
        'public_key_sandbox',
        'public_key_production',
        'notification_token_sandbox',
        'notification_token_production',
        'webhook_secret_sandbox',
        'webhook_secret_production',
        'merchant_code_sandbox',
        'merchant_code_production',
        'environment',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Accessors and Mutators for encrypted fields

    public function setApiKeySandboxAttribute($value)
    {
        $this->attributes['api_key_sandbox'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getApiKeySandboxAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setApiKeyProductionAttribute($value)
    {
        $this->attributes['api_key_production'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getApiKeyProductionAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setPublicKeySandboxAttribute($value)
    {
        $this->attributes['public_key_sandbox'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getPublicKeySandboxAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setPublicKeyProductionAttribute($value)
    {
        $this->attributes['public_key_production'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getPublicKeyProductionAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setNotificationTokenSandboxAttribute($value)
    {
        $this->attributes['notification_token_sandbox'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getNotificationTokenSandboxAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setNotificationTokenProductionAttribute($value)
    {
        $this->attributes['notification_token_production'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getNotificationTokenProductionAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setWebhookSecretSandboxAttribute($value)
    {
        $this->attributes['webhook_secret_sandbox'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getWebhookSecretSandboxAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function setWebhookSecretProductionAttribute($value)
    {
        $this->attributes['webhook_secret_production'] = $value ? Crypt::encryptString($value) : null;
    }

    public function getWebhookSecretProductionAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }
}

