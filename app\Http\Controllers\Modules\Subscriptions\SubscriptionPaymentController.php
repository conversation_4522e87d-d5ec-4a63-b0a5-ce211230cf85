<?php

namespace App\Http\Controllers\Modules\Subscriptions;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Modules\Subscriptions\SubscriptionPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class SubscriptionPaymentController extends Controller
{
    /**
     * إنشاء مثيل جديد من وحدة التحكم.
     */
    public function __construct()
    {
        // التحقق من أن المستخدم هو سوبر أدمن إذا كان الإعداد يتطلب ذلك
        if (Config::get('subscriptions.super_admin_only', true)) {
            $this->middleware(function ($request, $next) {
                if (!Auth::user() || !Auth::user()->hasRole('super_admin')) {
                    abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
                }
                return $next($request);
            });
        }
    }
    /**
     * Display a listing of the subscription payments.
     */
    public function index()
    {
        $payments = SubscriptionPayment::with('subscription.tenant')->latest()->paginate(10);
        return view('admin.subscriptions.payments.index', compact('payments'));
    }

    /**
     * Show the form for creating a new subscription payment.
     */
    public function create()
    {
        $subscriptions = Subscription::with('tenant')->where('status', '!=', 'cancelled')->get();
        return view('admin.subscriptions.payments.form', compact('subscriptions'));
    }

    /**
     * Store a newly created subscription payment in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'receipt_number' => 'nullable|string|max:255',
            'status' => 'required|in:paid,pending,failed,refunded',
            'notes' => 'nullable|string',
        ]);

        // Generate receipt number if not provided
        if (empty($validatedData['receipt_number'])) {
            $validatedData['receipt_number'] = 'RCPT-' . date('Ymd') . '-' . strtoupper(uniqid());
        }

        SubscriptionPayment::create($validatedData);

        return redirect()->route('admin.subscriptions.payments.index')
            ->with('success', 'تم إنشاء سجل الدفع بنجاح');
    }

    /**
     * Display the specified subscription payment.
     */
    public function show(SubscriptionPayment $payment)
    {
        $payment->load('subscription.tenant');
        return view('admin.subscriptions.payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the specified subscription payment.
     */
    public function edit(SubscriptionPayment $payment)
    {
        $subscriptions = Subscription::with('tenant')->where('status', '!=', 'cancelled')->get();
        return view('admin.subscriptions.payments.form', compact('payment', 'subscriptions'));
    }

    /**
     * Update the specified subscription payment in storage.
     */
    public function update(Request $request, SubscriptionPayment $payment)
    {
        $validatedData = $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'receipt_number' => 'nullable|string|max:255',
            'status' => 'required|in:paid,pending,failed,refunded',
            'notes' => 'nullable|string',
        ]);

        $payment->update($validatedData);

        return redirect()->route('admin.subscriptions.payments.index')
            ->with('success', 'تم تحديث سجل الدفع بنجاح');
    }

    /**
     * Remove the specified subscription payment from storage.
     */
    public function destroy(SubscriptionPayment $payment)
    {
        $payment->delete();

        return redirect()->route('admin.subscriptions.payments.index')
            ->with('success', 'تم حذف سجل الدفع بنجاح');
    }
}
