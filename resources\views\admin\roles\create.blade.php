@extends("layouts.admin")

@section("main-content")
    <div class="container">
        <h2>Create New Role</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.roles.store") }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="name">Role Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name") }}" required>
            </div>
            <div class="form-group">
                <label for="slug">Role Slug (e.g., admin, editor, sales-manager):</label>
                <input type="text" class="form-control" id="slug" name="slug" value="{{ old("slug") }}" required>
            </div>
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea class="form-control" id="description" name="description">{{ old("description") }}</textarea>
            </div>
            
            <div class="form-group">
                <h5>Assign Permissions</h5>
                @foreach($permissionGroups as $group => $permissions)
                    <h6>{{ $group }}</h6>
                    @foreach($permissions as $permission)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" value="{{ $permission->id }}" id="permission_{{ $permission->id }}">
                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                {{ $permission->name }}
                            </label>
                        </div>
                    @endforeach
                    <hr>
                @endforeach
            </div>

            <button type="submit" class="btn btn-success">Create Role</button>
            <a href="{{ route("admin.roles.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

