@extends('layouts.admin')

@section('content')
    <div class="container">
        <h1>Manufacturing Items</h1>
        <a href="{{ route('admin.manufacturing.items.create') }}" class="btn btn-primary">Add New Item</a>
        <table class="table mt-3">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Item Code</th>
                    <th>Type</th>
                    <th>Unit</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {{-- @foreach ($items as $item) --}}
                {{-- Example Row --}}
                <tr>
                    <td>1</td>
                    <td>Sample Item</td>
                    <td>ITM001</td>
                    <td>Finished Good</td>
                    <td>PCS</td>
                    <td>
                        <a href="#" class="btn btn-sm btn-info">View</a>
                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                        <form action="#" method="POST" style="display:inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                {{-- @endforeach --}}
            </tbody>
        </table>
    </div>
@endsection
