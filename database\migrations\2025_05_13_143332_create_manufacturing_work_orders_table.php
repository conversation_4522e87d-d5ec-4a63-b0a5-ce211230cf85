<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_work_orders', function (Blueprint $table) {
            $table->id();
            $table->string('work_order_number')->unique()->comment('Unique identifier for the work order');
            $table->foreignId('item_id')->constrained('manufacturing_items')->comment('The item to be produced');
            $table->foreignId('bom_id')->nullable()->constrained('manufacturing_boms')->comment('BOM used for this work order, if applicable');
            $table->decimal('quantity_to_produce', 15, 4);
            $table->decimal('quantity_produced', 15, 4)->default(0.0000);
            $table->decimal('quantity_scrapped', 15, 4)->default(0.0000);
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'])->default('pending');
            $table->timestamp('planned_start_date')->nullable();
            $table->timestamp('planned_end_date')->nullable();
            $table->timestamp('actual_start_date')->nullable();
            $table->timestamp('actual_end_date')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null');
            $table->foreignId('created_by_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to_id')->nullable()->constrained('users')->onDelete('set null')->comment('User or team responsible for the work order');
            // Add fields for routing/operations if more detailed tracking is needed
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_work_orders');
    }
};
