<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم مدير النظام
        $adminUser = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        // إنشاء مستخدم عادي
        $normalUser = User::create([
            'name' => 'مستخدم عادي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        // الحصول على دور المدير ودور المستخدم
        $adminRole = Role::where('name', 'admin')->first();
        $userRole = Role::where('name', 'user')->first();

        // تعيين الأدوار للمستخدمين
        if ($adminRole) {
            $adminUser->roles()->attach($adminRole);
        }

        if ($userRole) {
            $normalUser->roles()->attach($userRole);
        }
    }
}
