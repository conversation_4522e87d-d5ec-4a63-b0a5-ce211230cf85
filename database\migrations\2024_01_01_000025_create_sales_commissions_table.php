<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_commissions', function (Blueprint $table) {
            $table->id();
            $table->string('commission_code')->unique(); // كود العمولة
            $table->foreignId('sales_representative_id')->constrained('sales_representatives')->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained('orders')->onDelete('cascade'); // ربط بالطلب
            $table->foreignId('invoice_id')->nullable()->constrained('invoices')->onDelete('cascade'); // ربط بالفاتورة
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->enum('commission_type', ['sales', 'collection', 'target_bonus', 'special_bonus'])->default('sales');
            $table->date('transaction_date'); // تاريخ المعاملة
            $table->decimal('base_amount', 12, 2); // المبلغ الأساسي
            $table->decimal('commission_rate', 5, 2); // نسبة العمولة
            $table->decimal('commission_amount', 10, 2); // مبلغ العمولة
            $table->decimal('additional_bonus', 10, 2)->default(0); // مكافأة إضافية
            $table->decimal('deductions', 10, 2)->default(0); // خصومات
            $table->decimal('net_commission', 10, 2); // صافي العمولة
            $table->enum('calculation_method', ['percentage', 'fixed', 'tiered', 'custom'])->default('percentage');
            $table->json('calculation_details')->nullable(); // تفاصيل الحساب
            $table->enum('payment_status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending');
            $table->date('payment_date')->nullable(); // تاريخ الدفع
            $table->string('payment_reference')->nullable(); // مرجع الدفع
            $table->text('commission_notes')->nullable(); // ملاحظات العمولة
            $table->boolean('is_recurring')->default(false); // عمولة متكررة
            $table->integer('period_month')->nullable(); // شهر الفترة
            $table->integer('period_year')->nullable(); // سنة الفترة
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_commissions');
    }
};
