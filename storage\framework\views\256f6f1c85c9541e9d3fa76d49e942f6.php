<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0"><?php echo e(__("Restaurant Areas")); ?></h1>
                <a href="<?php echo e(route('admin.restaurant.areas.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__("Add New Area")); ?>

                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Areas List")); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($areas->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__("Name")); ?></th>
                                        <th><?php echo e(__("Description")); ?></th>
                                        <th><?php echo e(__("Color")); ?></th>
                                        <th><?php echo e(__("Tables Count")); ?></th>
                                        <th><?php echo e(__("Branch")); ?></th>
                                        <th><?php echo e(__("Sort Order")); ?></th>
                                        <th><?php echo e(__("Status")); ?></th>
                                        <th><?php echo e(__("Actions")); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $areas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $area): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($area->name); ?></strong>
                                            </td>
                                            <td><?php echo e($area->description ?? '-'); ?></td>
                                            <td>
                                                <span class="badge" style="background-color: <?php echo e($area->color); ?>; color: white;">
                                                    <?php echo e($area->color); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo e($area->tables->count()); ?> <?php echo e(__("Tables")); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($area->branch->name ?? '-'); ?></td>
                                            <td><?php echo e($area->sort_order); ?></td>
                                            <td>
                                                <?php if($area->is_active): ?>
                                                    <span class="badge bg-success"><?php echo e(__("Active")); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger"><?php echo e(__("Inactive")); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.restaurant.areas.show', $area)); ?>" 
                                                       class="btn btn-sm btn-outline-info" title="<?php echo e(__('View')); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.restaurant.areas.edit', $area)); ?>" 
                                                       class="btn btn-sm btn-outline-warning" title="<?php echo e(__('Edit')); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.restaurant.areas.destroy', $area)); ?>" 
                                                          method="POST" style="display: inline-block;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                title="<?php echo e(__('Delete')); ?>"
                                                                onclick="return confirm('<?php echo e(__('Are you sure you want to delete this area?')); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($areas->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo e(__("No restaurant areas found")); ?></h5>
                            <p class="text-muted"><?php echo e(__("Start by creating your first restaurant area.")); ?></p>
                            <a href="<?php echo e(route('admin.restaurant.areas.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> <?php echo e(__("Add First Area")); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/restaurant/areas/index.blade.php ENDPATH**/ ?>