{{--
    مكون عرض المبالغ مع رمز العملة السعودية
    الاستخدام: <x-currency-amount amount="1000" />
    أو: <x-currency-amount amount="1000" currency="SAR" />
    أو: <x-currency-amount amount="1000" currency="USD" class="text-success" />
--}}

@php
    // الحصول على المعاملات مع القيم الافتراضية
    $amount = $attributes->get('amount', 0);
    $currency = $attributes->get('currency', 'SAR');
    $class = $attributes->get('class', '');
    $showSymbol = $attributes->get('showSymbol', true);
    $size = $attributes->get('size', 'normal');
    $type = $attributes->get('type', 'normal');

    // التأكد من أن المبلغ رقم
    $amount = is_numeric($amount) ? (float) $amount : 0;

    // تنسيق المبلغ باستخدام CurrencyHelper
    $formattedAmount = \App\Helpers\CurrencyHelper::format($amount, $currency, $showSymbol);

    // تحديد الكلاسات
    $classes = ['currency-amount'];

    // إضافة كلاس خاص بالعملة
    $classes[] = 'currency-' . strtolower($currency);

    // إضافة كلاس الحجم
    switch($size) {
        case 'large':
            $classes[] = 'amount-large';
            break;
        case 'small':
            $classes[] = 'amount-small';
            break;
        default:
            $classes[] = 'amount-normal';
    }

    // إضافة كلاس النوع
    switch($type) {
        case 'positive':
            $classes[] = 'amount-positive';
            break;
        case 'negative':
            $classes[] = 'amount-negative';
            break;
        case 'neutral':
            $classes[] = 'amount-neutral';
            break;
        default:
            // تحديد تلقائي حسب قيمة المبلغ
            if($amount > 0) {
                $classes[] = 'amount-positive';
            } elseif($amount < 0) {
                $classes[] = 'amount-negative';
            } else {
                $classes[] = 'amount-neutral';
            }
    }

    // إضافة الكلاسات المخصصة
    if($class) {
        $classes[] = $class;
    }

    $classString = implode(' ', $classes);

    // الحصول على معلومات العملة
    $currencyConfig = config("currency.supported_currencies.{$currency}");
    $currencyName = $currencyConfig['name'] ?? 'ريال سعودي';
@endphp

<span class="{{ $classString }}"
      title="{{ $currencyName }}: {{ number_format($amount, 2) }}"
      data-amount="{{ $amount }}"
      data-currency="{{ $currency }}">
    {{ $formattedAmount }}
</span>
