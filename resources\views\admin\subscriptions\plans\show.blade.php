@extends('layouts.admin')

@section('title', 'تفاصيل خطة الاشتراك')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل خطة الاشتراك: {{ $plan->name }}</h3>
                    <div>
                        <a href="{{ route('admin.subscriptions.plans.edit', $plan) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.subscriptions.plans.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات أساسية</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $plan->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الكود</th>
                                            <td>{{ $plan->code }}</td>
                                        </tr>
                                        <tr>
                                            <th>السعر</th>
                                            <td>{{ $plan->formatted_price }}</td>
                                        </tr>
                                        <tr>
                                            <th>دورة الفوترة</th>
                                            <td>{{ $plan->billing_cycle_arabic }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if ($plan->is_active)
                                                    <span class="badge bg-success">نشط</span>
                                                @else
                                                    <span class="badge bg-danger">غير نشط</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $plan->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $plan->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>الحدود والقيود</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 50%">الحد الأقصى للمستخدمين</th>
                                            <td>{{ $plan->max_users }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحد الأقصى للفروع</th>
                                            <td>{{ $plan->max_branches }}</td>
                                        </tr>
                                        <tr>
                                            <th>مساحة التخزين</th>
                                            <td>{{ $plan->storage_space_gb }} جيجابايت</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>الوصف</h5>
                                </div>
                                <div class="card-body">
                                    {{ $plan->description ?? 'لا يوجد وصف' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>الميزات المتاحة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_pos)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>نقاط البيع</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_inventory)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>إدارة المخزون</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_accounting)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>المحاسبة</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_manufacturing)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>التصنيع</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_hr)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>الموارد البشرية</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_crm)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>إدارة علاقات العملاء</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_purchases)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>المشتريات</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_sales)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>المبيعات</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_reports)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>التقارير</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                @if($plan->has_api_access)
                                                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                @else
                                                    <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                                @endif
                                                <span>الوصول إلى API</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>الاشتراكات النشطة</h5>
                                    <a href="{{ route('admin.subscriptions.subscriptions.index', ['plan_id' => $plan->id]) }}" class="btn btn-sm btn-primary">عرض الكل</a>
                                </div>
                                <div class="card-body">
                                    @if($plan->subscriptions->where('status', 'active')->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>رقم الاشتراك</th>
                                                        <th>العميل</th>
                                                        <th>تاريخ البدء</th>
                                                        <th>تاريخ الانتهاء</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($plan->subscriptions->where('status', 'active')->take(5) as $subscription)
                                                        <tr>
                                                            <td>{{ $subscription->id }}</td>
                                                            <td>{{ $subscription->subscription_number }}</td>
                                                            <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                                            <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                                            <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                                            <td>
                                                                <a href="{{ route('admin.subscriptions.subscriptions.show', $subscription) }}" class="btn btn-sm btn-info">
                                                                    <i class="bi bi-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا توجد اشتراكات نشطة لهذه الخطة</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
