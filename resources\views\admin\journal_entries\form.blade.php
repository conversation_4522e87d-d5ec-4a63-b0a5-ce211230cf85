@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($journalEntry) ? __("Edit Journal Entry") : __("Add New Journal Entry") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($journalEntry) ? route("admin.journal_entries.update", $journalEntry->id) : route("admin.journal_entries.store") }}" method="POST" id="journalEntryForm">
        @csrf
        @if(isset($journalEntry))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="entry_number" class="form-label">{{ __("Entry Number") }}</label>
                    <input type="text" class="form-control" id="entry_number" name="entry_number" value="{{ old("entry_number", $journalEntry->entry_number ?? ($newEntryNumber ?? "")) }}" {{ isset($journalEntry) && $journalEntry->entry_number ? "readonly" : "" }}>
                    <small class="form-text text-muted">{{ __("Leave blank for auto-generation or if pre-filled.") }}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="entry_date" class="form-label">{{ __("Entry Date") }}</label>
                    <input type="date" class="form-control" id="entry_date" name="entry_date" value="{{ old("entry_date", $journalEntry->entry_date ?? date("Y-m-d")) }}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="branch_id" class="form-label">{{ __("Branch") }}</label>
                    <select class="form-control" id="branch_id" name="branch_id">
                        <option value="">{{ __("Select Branch") }}</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ (isset($journalEntry) && $journalEntry->branch_id == $branch->id) || old("branch_id") == $branch->id ? "selected" : "" }}>
                                {{ $branch->name_ar }} ({{ $branch->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="description_ar" class="form-label">{{ __("Description (Arabic)") }}</label>
            <textarea class="form-control" id="description_ar" name="description_ar" rows="2">{{ old("description_ar", $journalEntry->description_ar ?? "") }}</textarea>
        </div>
        <div class="mb-3">
            <label for="description_en" class="form-label">{{ __("Description (English)") }}</label>
            <textarea class="form-control" id="description_en" name="description_en" rows="2">{{ old("description_en", $journalEntry->description_en ?? "") }}</textarea>
        </div>

        <hr>
        <h3>{{ __("Journal Entry Details") }}</h3>
        <div id="entry_details_container">
            @if(isset($journalEntry) && $journalEntry->details->count() > 0)
                @foreach($journalEntry->details as $index => $detail)
                    <div class="row entry-detail-row mb-2">
                        <input type="hidden" name="details[{{ $index }}][id]" value="{{ $detail->id }}">
                        <div class="col-md-4">
                            <label class="form-label">{{ __("Account") }}</label>
                            <select name="details[{{ $index }}][account_id]" class="form-control account-select" required>
                                <option value="">{{ __("Select Account") }}</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}" {{ $detail->account_id == $account->id ? "selected" : "" }} data-code="{{ $account->code }}">
                                        {{ $account->name_ar }} ({{ $account->name_en }}) [{{ $account->code }}]
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __("Debit") }}</label>
                            <input type="number" step="0.01" name="details[{{ $index }}][debit]" class="form-control debit-input" value="{{ old("details.{$index}.debit", $detail->debit ?? "0.00") }}" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ __("Credit") }}</label>
                            <input type="number" step="0.01" name="details[{{ $index }}][credit]" class="form-control credit-input" value="{{ old("details.{$index}.credit", $detail->credit ?? "0.00") }}" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ __("Description") }}</label>
                            <input type="text" name="details[{{ $index }}][description]" class="form-control" value="{{ old("details.{$index}.description", $detail->description_ar ?? ($detail->description_en ?? "")) }}">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm remove-detail-row">{{ __("Remove") }}</button>
                        </div>
                    </div>
                @endforeach
            @else
                 {{-- Initial row for new entries --}}
                 <div class="row entry-detail-row mb-2">
                    <div class="col-md-4">
                        <label class="form-label">{{ __("Account") }}</label>
                        <select name="details[0][account_id]" class="form-control account-select" required>
                            <option value="">{{ __("Select Account") }}</option>
                            @foreach($accounts as $account)
                                <option value="{{ $account->id }}" data-code="{{ $account->code }}">
                                    {{ $account->name_ar }} ({{ $account->name_en }}) [{{ $account->code }}]
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">{{ __("Debit") }}</label>
                        <input type="number" step="0.01" name="details[0][debit]" class="form-control debit-input" value="0.00" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">{{ __("Credit") }}</label>
                        <input type="number" step="0.01" name="details[0][credit]" class="form-control credit-input" value="0.00" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ __("Description") }}</label>
                        <input type="text" name="details[0][description]" class="form-control">
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="button" class="btn btn-danger btn-sm remove-detail-row">{{ __("Remove") }}</button>
                    </div>
                </div>
            @endif
        </div>
        <button type="button" id="add_detail_row" class="btn btn-info mt-2 mb-3">{{ __("Add Detail Row") }}</button>

        <div class="row mt-3">
            <div class="col-md-6">
                <h4>{{ __("Total Debit:") }} <span id="total_debit_display">0.00</span></h4>
            </div>
            <div class="col-md-6">
                <h4>{{ __("Total Credit:") }} <span id="total_credit_display">0.00</span></h4>
            </div>
        </div>
        <div id="balance_warning" class="alert alert-warning mt-2" style="display:none;">{{ __("Totals do not balance!") }}</div>


        <div class="mt-4">
            <button type="submit" class="btn btn-success">{{ isset($journalEntry) ? __("Update Journal Entry") : __("Save Journal Entry") }}</button>
            <a href="{{ route("admin.journal_entries.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
        </div>
    </form>
</div>

@endsection

@push("scripts")
<script>
document.addEventListener("DOMContentLoaded", function() {
    let detailIndex = {{ (isset($journalEntry) && $journalEntry->details->count() > 0) ? $journalEntry->details->count() : 1 }};
    const container = document.getElementById("entry_details_container");
    const addRowButton = document.getElementById("add_detail_row");

    const accountsData = @json($accounts->map(function($acc) { 
        return ["id" => $acc->id, "name_ar" => $acc->name_ar, "name_en" => $acc->name_en, "code" => $acc->code]; 
    }));

    function createAccountOptions() {
        let options = 
            `<option value="">{{ __("Select Account") }}</option>`
        ;
        accountsData.forEach(account => {
            options += `<option value="${account.id}" data-code="${account.code}">${account.name_ar} (${account.name_en}) [${account.code}]</option>`;
        });
        return options;
    }

    if (addRowButton) {
        addRowButton.addEventListener("click", function() {
            const newRow = document.createElement("div");
            newRow.classList.add("row", "entry-detail-row", "mb-2");
            newRow.innerHTML = `
                <div class="col-md-4">
                    <select name="details[${detailIndex}][account_id]" class="form-control account-select" required>
                        ${createAccountOptions()}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" step="0.01" name="details[${detailIndex}][debit]" class="form-control debit-input" value="0.00" required>
                </div>
                <div class="col-md-2">
                    <input type="number" step="0.01" name="details[${detailIndex}][credit]" class="form-control credit-input" value="0.00" required>
                </div>
                <div class="col-md-3">
                    <input type="text" name="details[${detailIndex}][description]" class="form-control">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm remove-detail-row">{{ __("Remove") }}</button>
                </div>
            `;
            container.appendChild(newRow);
            detailIndex++;
            attachRemoveListeners();
            attachCalculationListeners();
        });
    }

    function attachRemoveListeners() {
        document.querySelectorAll(".remove-detail-row").forEach(button => {
            button.removeEventListener("click", handleRemoveRow); // Prevent multiple listeners
            button.addEventListener("click", handleRemoveRow);
        });
    }
    
    function handleRemoveRow(event) {
        if (document.querySelectorAll(".entry-detail-row").length > 1) {
            event.target.closest(".entry-detail-row").remove();
            calculateTotals();
        } else {
            alert("{{ __("At least one detail line is required.") }}");
        }
    }

    function calculateTotals() {
        let totalDebit = 0;
        let totalCredit = 0;
        document.querySelectorAll(".debit-input").forEach(input => totalDebit += parseFloat(input.value) || 0);
        document.querySelectorAll(".credit-input").forEach(input => totalCredit += parseFloat(input.value) || 0);

        document.getElementById("total_debit_display").textContent = totalDebit.toFixed(2);
        document.getElementById("total_credit_display").textContent = totalCredit.toFixed(2);

        const balanceWarning = document.getElementById("balance_warning");
        if (Math.abs(totalDebit - totalCredit) > 0.005) { // Using a small tolerance for float comparison
            balanceWarning.style.display = "block";
        } else {
            balanceWarning.style.display = "none";
        }
    }

    function attachCalculationListeners() {
        document.querySelectorAll(".debit-input, .credit-input").forEach(input => {
            input.removeEventListener("input", calculateTotals); // Prevent multiple listeners
            input.addEventListener("input", calculateTotals);
        });
    }
    
    // Initial setup
    attachRemoveListeners();
    attachCalculationListeners();
    calculateTotals(); // Calculate totals on page load for existing entries

    // Form submission validation
    const journalForm = document.getElementById("journalEntryForm");
    if(journalForm) {
        journalForm.addEventListener("submit", function(event){
            let totalDebit = 0;
            let totalCredit = 0;
            document.querySelectorAll(".debit-input").forEach(input => totalDebit += parseFloat(input.value) || 0);
            document.querySelectorAll(".credit-input").forEach(input => totalCredit += parseFloat(input.value) || 0);

            if (Math.abs(totalDebit - totalCredit) > 0.005) {
                event.preventDefault();
                alert("{{ __("Journal entry does not balance! Please correct the debit and credit amounts.") }}");
                document.getElementById("balance_warning").style.display = "block";
            }
            if (totalDebit === 0 && totalCredit === 0) {
                 event.preventDefault();
                alert("{{ __("Journal entry totals cannot both be zero.") }}");
            }
        });
    }
});
</script>
@endpush

