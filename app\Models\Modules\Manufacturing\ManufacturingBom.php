<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class ManufacturingBom extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'manufacturing_boms';

    protected $fillable = [
        'name',
        'description',
        'item_id', // The item this BOM produces
        'version',
        'is_active',
        'valid_from',
        'valid_to',
        'quantity_produced',
        'branch_id',
        'created_by_id',
        'updated_by_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'quantity_produced' => 'decimal:4',
    ];

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by_id');
    }

    public function bomItems()
    {
        return $this->hasMany(ManufacturingBomItem::class, 'bom_id');
    }

    public function workOrders()
    {
        return $this->hasMany(ManufacturingWorkOrder::class, 'bom_id');
    }
}

