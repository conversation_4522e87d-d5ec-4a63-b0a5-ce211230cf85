<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class RestaurantTable extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'number',
        'area_id',
        'capacity',
        'status',
        'x_position',
        'y_position',
        'shape',
        'settings',
        'is_active',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'capacity' => 'integer',
        'x_position' => 'decimal:2',
        'y_position' => 'decimal:2',
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the area that owns the table.
     */
    public function area(): BelongsTo
    {
        return $this->belongsTo(RestaurantArea::class, 'area_id');
    }

    /**
     * Get the branch that owns the table.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the table.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the orders for the table.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(RestaurantOrder::class, 'table_id');
    }

    /**
     * Get the current active order for the table.
     */
    public function currentOrder()
    {
        return $this->orders()
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->latest()
            ->first();
    }

    /**
     * Check if table is available.
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available' && $this->is_active;
    }

    /**
     * Check if table is occupied.
     */
    public function isOccupied(): bool
    {
        return $this->status === 'occupied';
    }

    /**
     * Scope a query to only include active tables.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include available tables.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')->where('is_active', true);
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'available' => '#28a745',
            'occupied' => '#dc3545',
            'reserved' => '#ffc107',
            'maintenance' => '#6c757d',
            default => '#007bff'
        };
    }
}
