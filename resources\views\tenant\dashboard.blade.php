@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('لوحة تحكم المستأجر') }}</div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger" role="alert">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4">
                            <div class="card mb-4">
                                <div class="card-header">{{ __('معلومات الاشتراك') }}</div>
                                <div class="card-body">
                                    @if ($subscription)
                                        <p><strong>{{ __('خطة الاشتراك:') }}</strong> {{ $subscription->plan->name }}</p>
                                        <p><strong>{{ __('تاريخ البدء:') }}</strong> {{ $subscription->start_date->format('Y-m-d') }}</p>
                                        <p><strong>{{ __('تاريخ الانتهاء:') }}</strong> {{ $subscription->end_date->format('Y-m-d') }}</p>
                                        <p><strong>{{ __('الحالة:') }}</strong> {{ $subscription->getStatusArabicAttribute() }}</p>
                                        <p><strong>{{ __('التجديد التلقائي:') }}</strong> {{ $subscription->auto_renew ? __('مفعل') : __('غير مفعل') }}</p>
                                        <a href="{{ route('tenant.subscription') }}" class="btn btn-primary">{{ __('إدارة الاشتراك') }}</a>
                                    @else
                                        <p>{{ __('لا يوجد اشتراك نشط حالياً.') }}</p>
                                        <a href="{{ route('tenant.subscription') }}" class="btn btn-primary">{{ __('الاشتراك الآن') }}</a>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card mb-4">
                                <div class="card-header">{{ __('المستخدمين') }}</div>
                                <div class="card-body">
                                    <p><strong>{{ __('عدد المستخدمين:') }}</strong> {{ $usersCount }}</p>
                                    @if ($subscription)
                                        <p><strong>{{ __('الحد الأقصى للمستخدمين:') }}</strong> {{ $subscription->plan->max_users }}</p>
                                        <div class="progress mb-3">
                                            <div class="progress-bar" role="progressbar" style="width: {{ ($usersCount / $subscription->plan->max_users) * 100 }}%;" aria-valuenow="{{ $usersCount }}" aria-valuemin="0" aria-valuemax="{{ $subscription->plan->max_users }}">{{ $usersCount }}/{{ $subscription->plan->max_users }}</div>
                                        </div>
                                    @endif
                                    <a href="{{ route('tenant.users') }}" class="btn btn-primary">{{ __('إدارة المستخدمين') }}</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card mb-4">
                                <div class="card-header">{{ __('الميزات المتاحة') }}</div>
                                <div class="card-body">
                                    @if ($subscription)
                                        <ul class="list-group">
                                            <li class="list-group-item {{ $subscription->plan->has_accounting ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_accounting ? 'check' : 'times' }}"></i> {{ __('المحاسبة') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_inventory ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_inventory ? 'check' : 'times' }}"></i> {{ __('المخزون') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_sales ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_sales ? 'check' : 'times' }}"></i> {{ __('المبيعات') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_purchases ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_purchases ? 'check' : 'times' }}"></i> {{ __('المشتريات') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_manufacturing ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_manufacturing ? 'check' : 'times' }}"></i> {{ __('التصنيع') }}
                                            </li>
                                        </ul>
                                    @else
                                        <p>{{ __('قم بالاشتراك للوصول إلى الميزات.') }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
