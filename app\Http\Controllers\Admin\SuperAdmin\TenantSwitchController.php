<?php

namespace App\Http\Controllers\Admin\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class TenantSwitchController extends Controller
{
    /**
     * عرض قائمة المستأجرين للتبديل بينهم
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // التأكد من أن المستخدم هو سوبر أدمن
        if (!Auth::user()->hasRole('super_admin')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية الوصول لهذه الصفحة');
        }

        // الحصول على قائمة المستأجرين
        $tenants = Tenant::with('owner')->get();

        return view('admin.super_admin.tenant_switch.index', compact('tenants'));
    }

    /**
     * التبديل إلى حساب مستأجر محدد
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $tenantId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switchToTenant(Request $request, $tenantId)
    {
        // التأكد من أن المستخدم هو سوبر أدمن
        if (!Auth::user()->hasRole('super_admin')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية الوصول لهذه الصفحة');
        }

        // البحث عن المستأجر
        $tenant = Tenant::findOrFail($tenantId);
        
        // حفظ معلومات السوبر أدمن في الجلسة
        Session::put('super_admin_id', Auth::id());
        
        // الحصول على مالك المستأجر أو أول مستخدم مرتبط بالمستأجر
        $tenantOwner = $tenant->owner ?? User::where('tenant_id', $tenant->id)->first();
        
        if (!$tenantOwner) {
            return redirect()->back()->with('error', 'لا يوجد مستخدمين مرتبطين بهذا المستأجر');
        }
        
        // تسجيل الخروج للمستخدم الحالي
        Auth::logout();
        
        // تسجيل الدخول كمالك المستأجر
        Auth::login($tenantOwner);
        
        // حفظ معلومات التبديل في الجلسة
        Session::put('is_switched_tenant', true);
        Session::put('tenant_name', $tenant->name);
        
        return redirect()->route('dashboard')->with('success', 'تم التبديل إلى حساب ' . $tenant->name . ' بنجاح');
    }

    /**
     * العودة إلى حساب السوبر أدمن
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switchBack(Request $request)
    {
        // التأكد من وجود معرف السوبر أدمن في الجلسة
        if (!Session::has('super_admin_id')) {
            return redirect()->route('dashboard')->with('error', 'لا يمكن العودة إلى حساب السوبر أدمن');
        }
        
        // الحصول على معرف السوبر أدمن
        $superAdminId = Session::get('super_admin_id');
        
        // البحث عن مستخدم السوبر أدمن
        $superAdmin = User::findOrFail($superAdminId);
        
        // تسجيل الخروج للمستخدم الحالي
        Auth::logout();
        
        // تسجيل الدخول كسوبر أدمن
        Auth::login($superAdmin);
        
        // إزالة معلومات التبديل من الجلسة
        Session::forget('is_switched_tenant');
        Session::forget('tenant_name');
        Session::forget('super_admin_id');
        
        return redirect()->route('admin.super_admin.dashboard')->with('success', 'تم العودة إلى حساب السوبر أدمن بنجاح');
    }
}
