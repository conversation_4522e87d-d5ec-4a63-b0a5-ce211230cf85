<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Branch;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with(["branch", "role"])->latest()->paginate(10);
        return view("admin.users.index", compact("users"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        $roles = Role::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        return view("admin.users.form", compact("branches", "roles"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255",
            "username" => "required|string|max:255|unique:users,username",
            "email" => "required|string|email|max:255|unique:users,email",
            "phone" => "nullable|string|max:20",
            "password" => ["required", "confirmed", Rules\Password::defaults()],
            "branch_id" => "nullable|exists:branches,id",
            "role_id" => "required|exists:roles,id",
            "language" => "required|in:ar,en",
            "is_active" => "boolean",
        ]);

        $validatedData["password"] = Hash::make($request->password);
        $validatedData["is_active"] = $request->has("is_active");

        User::create($validatedData);

        return redirect()->route("admin.users.index")->with("success", __("User created successfully."));
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(["branch", "role"]);
        return view("admin.users.show", compact("user"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $branches = Branch::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        $roles = Role::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        return view("admin.users.form", compact("user", "branches", "roles"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255",
            "username" => "required|string|max:255|unique:users,username," . $user->id,
            "email" => "required|string|email|max:255|unique:users,email," . $user->id,
            "phone" => "nullable|string|max:20",
            "password" => ["nullable", "confirmed", Rules\Password::defaults()],
            "branch_id" => "nullable|exists:branches,id",
            "role_id" => "required|exists:roles,id",
            "language" => "required|in:ar,en",
            "is_active" => "boolean",
        ]);

        if ($request->filled("password")) {
            $validatedData["password"] = Hash::make($request->password);
        } else {
            unset($validatedData["password"]);
        }
        $validatedData["is_active"] = $request->has("is_active");

        $user->update($validatedData);

        return redirect()->route("admin.users.index")->with("success", __("User updated successfully."));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Add checks here if user is super admin or has critical relations
        if ($user->id === 1) { // Assuming user with ID 1 is a super admin
            return redirect()->route("admin.users.index")->with("error", __("Cannot delete the super admin user."));
        }
        try {
            $user->delete();
            return redirect()->route("admin.users.index")->with("success", __("User deleted successfully."));
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.users.index")->with("error", __("Could not delete user. It might be in use or a database error occurred."));
        }
    }
}

