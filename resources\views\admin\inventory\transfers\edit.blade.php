@extends('layouts.admin')

@section('title', 'تعديل تحويل المخزون')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل تحويل المخزون</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.inventory.transfers.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <a href="{{ route('admin.inventory.transfers.show', 1) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.inventory.transfers.update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="transfer_number">رقم التحويل</label>
                                    <input type="text" class="form-control" id="transfer_number" name="transfer_number" value="TRF-2024-001" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="transfer_date">تاريخ التحويل <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="transfer_date" name="transfer_date" value="2024-03-15" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="from_warehouse_id">من مستودع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="from_warehouse_id" name="from_warehouse_id" required>
                                        <option value="">اختر المستودع المرسل</option>
                                        <option value="1" selected>المستودع الرئيسي</option>
                                        <option value="2">مستودع الفرع الثاني</option>
                                        <option value="3">مستودع المواد الخام</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="to_warehouse_id">إلى مستودع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="to_warehouse_id" name="to_warehouse_id" required>
                                        <option value="">اختر المستودع المستقبل</option>
                                        <option value="1">المستودع الرئيسي</option>
                                        <option value="2" selected>مستودع الفرع الثاني</option>
                                        <option value="3">مستودع المواد الخام</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes">ملاحظات التحويل</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">تحويل عاجل للأصناف المطلوبة في الفرع الثاني لتلبية طلبات العملاء. يرجى التعامل مع الأصناف بحذر خاصة الأجهزة الإلكترونية.</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الأصناف -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>أصناف التحويل</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="transferItemsTable">
                                        <thead>
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية المتاحة</th>
                                                <th>الكمية المحولة</th>
                                                <th>السعر</th>
                                                <th>القيمة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[0][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1" selected>لابتوب ديل XPS 13</option>
                                                        <option value="2">ماوس لاسلكي لوجيتك</option>
                                                        <option value="3">كيبورد ميكانيكي</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][available_quantity]" value="15" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][transfer_quantity]" value="5" required min="1">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][unit_price]" value="3500" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][total_value]" value="17500" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[1][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1">لابتوب ديل XPS 13</option>
                                                        <option value="2" selected>ماوس لاسلكي لوجيتك</option>
                                                        <option value="3">كيبورد ميكانيكي</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][available_quantity]" value="50" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][transfer_quantity]" value="20" required min="1">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][unit_price]" value="150" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][total_value]" value="3000" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[2][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1">لابتوب ديل XPS 13</option>
                                                        <option value="2">ماوس لاسلكي لوجيتك</option>
                                                        <option value="3" selected>كيبورد ميكانيكي</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[2][available_quantity]" value="25" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[2][transfer_quantity]" value="10" required min="1">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[2][unit_price]" value="250" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[2][total_value]" value="2500" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addRow()">
                                    <i class="fas fa-plus"></i> إضافة صنف
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_value">القيمة الإجمالية</label>
                                    <input type="number" class="form-control" id="total_value" name="total_value" value="23000" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="pending">معلق</option>
                                        <option value="approved">معتمد</option>
                                        <option value="in_transit" selected>في الطريق</option>
                                        <option value="completed">مكتمل</option>
                                        <option value="cancelled">ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الشحن -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>معلومات الشحن</h5>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_company">شركة النقل</label>
                                    <input type="text" class="form-control" id="shipping_company" name="shipping_company" value="شركة النقل السريع">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tracking_number">رقم الشحنة</label>
                                    <input type="text" class="form-control" id="tracking_number" name="tracking_number" value="SHP-2024-001">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="driver_name">اسم السائق</label>
                                    <input type="text" class="form-control" id="driver_name" name="driver_name" value="محمد أحمد">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="driver_phone">رقم هاتف السائق</label>
                                    <input type="text" class="form-control" id="driver_phone" name="driver_phone" value="0501234567">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('admin.inventory.transfers.show', 1) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let rowIndex = 3;

function addRow() {
    const tbody = document.querySelector('#transferItemsTable tbody');
    const newRow = `
        <tr>
            <td>
                <select class="form-control" name="items[${rowIndex}][item_id]" required>
                    <option value="">اختر الصنف</option>
                    <option value="1">لابتوب ديل XPS 13</option>
                    <option value="2">ماوس لاسلكي لوجيتك</option>
                    <option value="3">كيبورد ميكانيكي</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][available_quantity]" readonly>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][transfer_quantity]" required min="1">
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][unit_price]" step="0.01" required>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][total_value]" step="0.01" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', newRow);
    rowIndex++;
}

function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotal();
}

function calculateTotal() {
    let total = 0;
    const totalValueInputs = document.querySelectorAll('input[name*="[total_value]"]');
    totalValueInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    document.getElementById('total_value').value = total.toFixed(2);
}

// حساب القيمة عند تغيير الكمية والسعر
document.addEventListener('input', function(e) {
    if (e.target.name && (e.target.name.includes('[transfer_quantity]') || e.target.name.includes('[unit_price]'))) {
        const row = e.target.closest('tr');
        const quantity = parseFloat(row.querySelector('input[name*="[transfer_quantity]"]').value) || 0;
        const unitPrice = parseFloat(row.querySelector('input[name*="[unit_price]"]').value) || 0;
        const totalValue = quantity * unitPrice;
        row.querySelector('input[name*="[total_value]"]').value = totalValue.toFixed(2);
        
        calculateTotal();
    }
});

// منع تحويل من نفس المستودع إلى نفسه
document.getElementById('from_warehouse_id').addEventListener('change', function() {
    const toWarehouse = document.getElementById('to_warehouse_id');
    if (this.value === toWarehouse.value && this.value !== '') {
        alert('لا يمكن التحويل من المستودع إلى نفسه');
        toWarehouse.value = '';
    }
});

document.getElementById('to_warehouse_id').addEventListener('change', function() {
    const fromWarehouse = document.getElementById('from_warehouse_id');
    if (this.value === fromWarehouse.value && this.value !== '') {
        alert('لا يمكن التحويل من المستودع إلى نفسه');
        this.value = '';
    }
});

// حساب الإجمالي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>
@endpush
