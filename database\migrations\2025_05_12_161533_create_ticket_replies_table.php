<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create("ticket_replies", function (Blueprint $table) {
            $table->id();
            $table->foreignId("ticket_id")->constrained("tickets")->onDelete("cascade");
            $table->foreignId("user_id")->comment("User who made the reply (customer or staff)")->constrained("users")->onDelete("cascade");
            $table->longText("body");
            $table->string("reply_type")->default("public")->comment("public, internal_note"); // To differentiate between public replies and internal staff notes on a reply
            $table->ipAddress("submitter_ip")->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("ticket_replies");
    }
};

