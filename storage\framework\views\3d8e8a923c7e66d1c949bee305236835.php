<?php $__env->startSection('title', 'إدارة باقات الاشتراك'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">باقات الاشتراك</h3>
                    <a href="<?php echo e(route('admin.super_admin.subscription_plans.create')); ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة باقة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table id="subscription-plans-table" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الكود</th>
                                    <th>السعر</th>
                                    <th>دورة الفوترة</th>
                                    <th>حد الفواتير الشهري</th>
                                    <th>عدد المشتركين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($plan->id); ?></td>
                                        <td><?php echo e($plan->name); ?></td>
                                        <td><code><?php echo e($plan->code); ?></code></td>
                                        <td><?php echo e($plan->price); ?> ريال</td>
                                        <td>
                                            <?php switch($plan->billing_cycle):
                                                case ('monthly'): ?>
                                                    شهري
                                                    <?php break; ?>
                                                <?php case ('quarterly'): ?>
                                                    ربع سنوي
                                                    <?php break; ?>
                                                <?php case ('semi_annually'): ?>
                                                    نصف سنوي
                                                    <?php break; ?>
                                                <?php case ('annually'): ?>
                                                    سنوي
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <?php echo e($plan->billing_cycle); ?>

                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <?php echo e($plan->invoiceLimit->monthly_invoice_limit ?? 'غير محدد'); ?>

                                        </td>
                                        <td><?php echo e($plan->subscriptions_count); ?></td>
                                        <td>
                                            <?php if($plan->is_active): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.super_admin.subscription_plans.show', $plan)); ?>" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.super_admin.subscription_plans.edit', $plan)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($plan->id); ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>

                                            <!-- Modal for Delete Confirmation -->
                                            <div class="modal fade" id="deleteModal<?php echo e($plan->id); ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo e($plan->id); ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo e($plan->id); ?>">تأكيد الحذف</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            هل أنت متأكد من حذف باقة "<?php echo e($plan->name); ?>"؟
                                                            <?php if($plan->subscriptions_count > 0): ?>
                                                                <div class="alert alert-warning mt-3">
                                                                    <i class="bi bi-exclamation-triangle"></i>
                                                                    تحذير: هذه الباقة مرتبطة بـ <?php echo e($plan->subscriptions_count); ?> اشتراك.
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                            <form action="<?php echo e(route('admin.super_admin.subscription_plans.destroy', $plan)); ?>" method="POST" style="display: inline-block;">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('DELETE'); ?>
                                                                <button type="submit" class="btn btn-danger">حذف</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد باقات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        // انتظار تحميل الصفحة بالكامل
        setTimeout(function() {
            // التحقق من وجود الجدول
            var table = $('#subscription-plans-table');
            if (table.length) {
                // التحقق من وجود صفوف في الجدول
                var rows = table.find('tbody tr');

                // إذا كان هناك صف واحد فقط ويحتوي على "لا توجد باقات"، لا نطبق DataTables
                if (rows.length === 1 && rows.first().find('td').attr('colspan')) {
                    console.log('لا توجد بيانات لعرضها في الجدول');
                    return;
                }

                // إذا كان هناك بيانات، طبق DataTables
                if (rows.length > 0) {
                    try {
                        table.DataTable({
                            "language": {
                                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
                            },
                            "paging": true,
                            "searching": true,
                            "ordering": true,
                            "info": true,
                            "autoWidth": false,
                            "responsive": true,
                            "columnDefs": [
                                { "orderable": false, "targets": [8] } // تعطيل الترتيب للعمود الإجراءات (العمود الأخير)
                            ],
                            "order": [[ 0, "desc" ]], // ترتيب حسب ID تنازلي
                            "destroy": true // السماح بإعادة التهيئة
                        });
                        console.log('تم تحميل DataTables بنجاح');
                    } catch (error) {
                        console.error('خطأ في تحميل DataTables:', error);
                    }
                }
            }
        }, 200);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/super_admin/subscription_plans/index.blade.php ENDPATH**/ ?>