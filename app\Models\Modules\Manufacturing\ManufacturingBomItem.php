<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingBomItem extends Model
{
    use HasFactory;

    protected $table = 'manufacturing_bom_items';

    protected $fillable = [
        'bom_id',
        'item_id', // Component item
        'quantity',
        'unit_of_measure',
        'notes',
        'operation_sequence',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
    ];

    public function bom()
    {
        return $this->belongsTo(ManufacturingBom::class, 'bom_id');
    }

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }
}

