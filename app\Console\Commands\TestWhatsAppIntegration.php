<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WhatsAppService;
use App\Models\User; // Assuming you have a User model
use App\Models\Modules\WhatsAppIntegration\WhatsAppConfiguration;
use App\Models\Modules\WhatsAppIntegration\WhatsAppMessageTemplate;
use Illuminate\Support\Facades\Log;

class TestWhatsAppIntegration extends Command
{
    protected $signature = 'test:whatsapp-integration {--phone=} {--template=}';
    protected $description = 'Test WhatsApp Cloud API integration by sending a template message.';

    protected WhatsAppService $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        parent::__construct();
        $this->whatsAppService = $whatsAppService;
    }

    public function handle()
    {
        $this->info('Starting WhatsApp Integration Test...');

        // Ensure active configuration exists
        $activeConfig = WhatsAppConfiguration::where('is_active', true)->first();
        if (!$activeConfig) {
            $this->error('No active WhatsApp configuration found in the database. Please configure and activate one first.');
            Log::error('TestWhatsAppIntegration: No active WhatsApp configuration.');
            return 1;
        }
        $this->comment('Active WhatsApp Configuration Found: ID ' . $activeConfig->id . ' for Phone ID: ' . $activeConfig->phone_number_id);

        // Get recipient phone number
        $recipientPhoneNumber = $this->option('phone');
        if (!$recipientPhoneNumber) {
            $recipientPhoneNumber = $this->ask('Enter recipient WhatsApp number (with country code, e.g., 966xxxxxxxxx):');
        }
        if (!preg_match('/^[1-9][0-9]{10,14}$/', $recipientPhoneNumber)) { // Basic validation
            $this->error('Invalid phone number format.');
            return 1;
        }

        // Simulate or get a user. For testing, we can create a dummy user object or fetch one.
        // Let's assume the phone number provided is sufficient and we don't need a full User model instance for this test command directly.
        // However, the service expects a User model. So, let's create a mock one.
        $testUser = new User();
        $testUser->id = 0; // Dummy ID for test
        $testUser->phone_number = $recipientPhoneNumber;
        $testUser->name = 'Test User';

        // Get template name
        $templateName = $this->option('template');
        if (!$templateName) {
            $templateName = $this->ask('Enter WhatsApp template name (e.g., hello_world, order_confirmation):', 'hello_world');
        }

        // Check if template exists in DB and is approved
        $dbTemplate = WhatsAppMessageTemplate::where('name', $templateName)
                                          ->where('status', 'APPROVED') // Assuming 'ar' or a configurable default
                                          ->first();
        if (!$dbTemplate) {
            $this->warn("Template '{$templateName}' (approved) not found in DB. The API might still work if the template exists on Meta's side and parameters are simple.");
            // Create a dummy template object if not found, so the service can proceed with API call for testing purposes
            // This is risky for complex templates but okay for 'hello_world'
            $dbTemplate = new WhatsAppMessageTemplate();
            $dbTemplate->id = 0; // Dummy ID
            $dbTemplate->name = $templateName;
            $dbTemplate->language = 'en_US'; // Default, service will use this
            // $dbTemplate->components_json = json_encode(["body" => ["text" => "Hello {{1}}"]]); // Example for hello_world
        }
        $languageCode = $dbTemplate->language ?? 'en_US'; // Use template's language or default

        $this->comment("Attempting to send template '{$templateName}' in language '{$languageCode}' to {$recipientPhoneNumber}");

        // Define template parameters (customize based on the template)
        $templateParameters = [];
        if ($templateName === 'hello_world') {
            // No parameters needed for the basic hello_world template usually
            // If it has a body parameter like "Hello {{1}}", then:
            // $templateParameters = ["1" => "Test User"]; 
        } elseif ($templateName === 'order_confirmation') { // Example for a custom template
            $templateParameters = [
                "1" => "12345", // Order ID
                "2" => "SAR 500.00", // Amount
                "3" => "SuperMart", // Store Name
                // Example for a header image if template supports it
                // "HEADER_IMAGE_URL" => "https://www.example.com/order_image.png",
                // Example for a URL button if template supports it (dynamic part of URL)
                // "BUTTON_SUBTYPE_url_0" => "track/12345" 
            ];
            $this->info('Using example parameters for order_confirmation: ' . json_encode($templateParameters));
        } else {
            $this->warn("No predefined parameters for template '{$templateName}'. Sending without dynamic parameters unless they are part of the URL or header.");
            $customParamsStr = $this->ask("Enter JSON string for template parameters (e.g., {\"1\":\"Value1\", \"HEADER_IMAGE_URL\":\"url\"}) or leave empty:");
            if (!empty($customParamsStr)) {
                $decodedParams = json_decode($customParamsStr, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $templateParameters = $decodedParams;
                    $this->info('Using custom parameters: ' . json_encode($templateParameters));
                } else {
                    $this->error('Invalid JSON string for parameters: ' . json_last_error_msg());
                    return 1;
                }
            }
        }

        $this->info("Sending message via WhatsAppService...");
        $result = $this->whatsAppService->sendTemplateMessage($templateName, $testUser, $templateParameters, $languageCode);

        if ($result['success']) {
            $this->info("Message sending initiated successfully (check WhatsApp and logs for delivery status).");
            $this->line("API Response: " . json_encode($result['api_response'] ?? 'No API response in result'));
            $this->line("Log ID: " . ($result['log_id'] ?? 'N/A'));
        } else {
            $this->error("Failed to send message: " . $result['message']);
            if (isset($result['details'])) {
                $this->error("Details: " . (is_string($result['details']) ? $result['details'] : json_encode($result['details'])));
            }
            $this->line("Log ID: " . ($result['log_id'] ?? 'N/A'));
        }

        $this->info('WhatsApp Integration Test Finished.');
        return 0;
    }
}

