<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_production_transactions", function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("mfg_work_order_id");
            $table->foreign("mfg_work_order_id")->references("id")->on("mfg_work_orders")->onDelete("cascade");
            $table->unsignedBigInteger("mfg_product_id"); // Product being transacted (component or finished good)
            $table->foreign("mfg_product_id")->references("id")->on("mfg_products")->onDelete("cascade");
            $table->unsignedBigInteger("mfg_operation_id")->nullable(); // Operation related to this transaction
            $table->foreign("mfg_operation_id")->references("id")->on("mfg_operations")->onDelete("set null");
            $table->unsignedBigInteger("mfg_work_center_id")->nullable();
            $table->foreign("mfg_work_center_id")->references("id")->on("mfg_work_centers")->onDelete("set null");
            $table->string("transaction_type"); // e.g., Issue, Receipt, Scrap, Labor, MachineTime
            $table->decimal("quantity", 15, 4);
            $table->string("unit_of_measure");
            $table->timestamp("transaction_date");
            $table->unsignedBigInteger('employee_id')->nullable(); // Added based on design document
            // $table->foreign('employee_id')->references('id')->on('employees')->onDelete('set null'); // Assuming 'employees' table
            $table->decimal("cost", 15, 4)->nullable(); // Actual cost if different from standard
            $table->unsignedBigInteger("inventory_location_id_from")->nullable(); // For component issues
            // $table->foreign("inventory_location_id_from")->references("id")->on("inventory_locations");
            $table->unsignedBigInteger("inventory_location_id_to")->nullable(); // For finished good receipts
            // $table->foreign("inventory_location_id_to")->references("id")->on("inventory_locations");
            $table->text("notes")->nullable();
            $table->unsignedBigInteger("created_by")->nullable();
            $table->foreign("created_by")->references("id")->on("users")->onDelete("set null");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_production_transactions");
    }
};

