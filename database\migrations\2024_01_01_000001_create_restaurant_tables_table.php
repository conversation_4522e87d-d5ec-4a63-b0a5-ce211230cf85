<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_tables', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الطاولة
            $table->string('number')->unique(); // رقم الطاولة
            $table->foreignId('area_id')->nullable()->constrained('restaurant_areas')->onDelete('set null'); // المنطقة
            $table->integer('capacity'); // عدد الأشخاص
            $table->enum('status', ['available', 'occupied', 'reserved', 'maintenance'])->default('available'); // حالة الطاولة
            $table->decimal('x_position', 8, 2)->nullable(); // موقع X في المخطط
            $table->decimal('y_position', 8, 2)->nullable(); // موقع Y في المخطط
            $table->string('shape', 20)->default('rectangle'); // شكل الطاولة
            $table->json('settings')->nullable(); // إعدادات إضافية
            $table->boolean('is_active')->default(true);
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_tables');
    }
};
