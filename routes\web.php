<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Modules\GeneralLedger\AccountController;
use App\Http\Controllers\Modules\GeneralLedger\AccountTypeController;
use App\Http\Controllers\Modules\GeneralLedger\JournalEntryController;
use App\Http\Controllers\Modules\GeneralLedger\ReportController;
use App\Http\Controllers\Modules\GeneralLedger\FiscalYearController;
use App\Http\Controllers\Modules\GeneralLedger\TaxController;
use App\Http\Controllers\Modules\Sales\CustomerController;
use App\Http\Controllers\Modules\Sales\InvoiceController;
use App\Http\Controllers\Modules\Sales\QuotationController;
use App\Http\Controllers\Modules\Sales\ReturnController;
use App\Http\Controllers\Modules\POS\POSSaleController;
use App\Http\Controllers\Modules\POS\POSSessionController;
use App\Http\Controllers\Modules\Manufacturing\BomController;
use App\Http\Controllers\Modules\Manufacturing\ItemController;
use App\Http\Controllers\Modules\Manufacturing\WorkOrderController;
use App\Http\Controllers\Modules\Branches\BranchController;
use App\Http\Controllers\Modules\Ticketing\TicketController;
use App\Http\Controllers\Modules\Ticketing\TicketCategoryController;
use App\Http\Controllers\Modules\Users\UserController;
use App\Http\Controllers\Modules\Users\RoleController;
use App\Http\Controllers\Modules\Permissions\PermissionController;
use App\Http\Controllers\Admin\PermissionGroupController;
use App\Http\Controllers\Modules\Subscriptions\SubscriptionController;
use App\Http\Controllers\Modules\Subscriptions\SubscriptionPaymentController;
use App\Http\Controllers\Modules\Subscriptions\SubscriptionPlanController;
use App\Http\Controllers\Modules\WhatsAppIntegration\WhatsAppConfigurationController;
use App\Http\Controllers\Modules\WhatsAppIntegration\WhatsAppMessageLogController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Modules\Purchases\SupplierController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// الصفحة الرئيسية - لوحة التحكم
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/admin/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

// مسارات الإدارة
Route::prefix('admin')->name('admin.')->group(function () {
    // الدفتر العام
    Route::get('accounts/tree', [AccountController::class, 'tree'])->name('accounts.tree');
    Route::get('accounts/new', [App\Http\Controllers\Admin\AccountController::class, 'index'])->name('accounts.new');
    Route::resource('accounts', AccountController::class);
    Route::resource('account_types', AccountTypeController::class);
    Route::resource('journal_entries', JournalEntryController::class);
    Route::get('financial_reports', [ReportController::class, 'index'])->name('financial_reports.index');
    Route::resource('fiscal_years', FiscalYearController::class);
    Route::resource('taxes', TaxController::class);

    // المبيعات
    Route::prefix('sales')->name('sales.')->group(function () {
        Route::resource('invoices', InvoiceController::class);
        Route::resource('customers', CustomerController::class);
        Route::resource('quotations', QuotationController::class);
        Route::resource('returns', ReturnController::class);
        Route::get('reports', [InvoiceController::class, 'reports'])->name('reports.index');
    });

    // المشتريات
    Route::prefix('purchases')->name('purchases.')->group(function () {
        Route::resource('suppliers', SupplierController::class);
        Route::resource('invoices', App\Http\Controllers\Modules\Purchases\PurchaseInvoiceController::class);
        Route::resource('returns', App\Http\Controllers\Modules\Purchases\PurchaseReturnController::class);
        Route::resource('orders', App\Http\Controllers\Modules\Purchases\PurchaseOrderController::class);
    });

    // نقاط البيع
    Route::get('pos', [POSSaleController::class, 'index'])->name('pos.index');
    Route::resource('pos_transactions', POSSessionController::class);

    // التصنيع
    Route::prefix('manufacturing')->name('manufacturing.')->group(function () {
        Route::resource('items', ItemController::class);
        Route::resource('boms', BomController::class);
        Route::resource('work_orders', WorkOrderController::class);
    });

    // الفروع
    Route::resource('branches', BranchController::class);

    // نظام التذاكر
    Route::prefix('ticketing')->name('ticketing.')->group(function () {
        Route::resource('tickets', TicketController::class);
        Route::resource('ticket_categories', TicketCategoryController::class);
        // Route::resource('ticket_priorities', TicketPriorityController::class);
        // Route::resource('ticket_statuses', TicketStatusController::class);
    });

    // المستخدمين والصلاحيات
    Route::resource('users', UserController::class);
    Route::resource('roles', RoleController::class);

    // نظام الصلاحيات
    Route::prefix('permissions_system')->name('permissions_system.')->group(function () {
        Route::resource('roles', RoleController::class);
        Route::resource('permissions', PermissionController::class);
        Route::resource('groups', PermissionGroupController::class)->parameters([
            'groups' => 'group'
        ]);
    });

    // الاشتراكات - متاحة فقط للسوبر أدمن
    Route::prefix('subscriptions')->name('subscriptions.')->middleware(['auth', 'super_admin'])->group(function () {
        Route::resource('subscriptions', SubscriptionController::class);
        Route::resource('plans', SubscriptionPlanController::class);
        Route::resource('payments', SubscriptionPaymentController::class);
    });

    // السوبر أدمن
    Route::prefix('super_admin')->name('super_admin.')->middleware(['auth', 'super_admin'])->group(function () {
        Route::get('dashboard', function() { return view('admin.dashboard'); })->name('dashboard');
        Route::resource('subscription_plans', App\Http\Controllers\Admin\SuperAdmin\SubscriptionPlanController::class);
        Route::resource('subscriptions', App\Http\Controllers\Admin\SuperAdmin\SubscriptionController::class);

        // إدارة حدود الفواتير
        Route::get('invoice_limits', [App\Http\Controllers\Admin\SuperAdmin\InvoiceLimitController::class, 'index'])->name('invoice_limits.index');
        Route::post('invoice_limits/update', [App\Http\Controllers\Admin\SuperAdmin\InvoiceLimitController::class, 'update'])->name('invoice_limits.update');

        // مسارات التبديل بين المستأجرين
        Route::get('tenant_switch', function() { return view('admin.dashboard'); })->name('tenant_switch.index');
    });

    // تكامل واتساب
    Route::resource('whatsapp_settings', WhatsAppConfigurationController::class);
    Route::resource('whatsapp_messages', WhatsAppMessageLogController::class);

    // تكامل هيئة الزكاة والضريبة والجمارك (ZATCA)
    Route::prefix('integrations')->name('integrations.')->group(function () {
        Route::prefix('zatca')->name('zatca.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'index'])->name('index');
            Route::get('/edit', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'edit'])->name('edit');
            Route::put('/update', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'update'])->name('update');
            Route::get('/logs', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'showInvoiceLogs'])->name('show');
            Route::post('/generate-qr', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'generateQrCode'])->name('generate_qr');
            Route::post('/submit-invoice', [App\Http\Controllers\Modules\Integrations\ZatcaIntegrationController::class, 'submitInvoiceToZatca'])->name('submit_invoice');
        });
    });

    // الإعدادات
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('settings', [SettingsController::class, 'update'])->name('settings.update');
    Route::get('profile', [UserController::class, 'profile'])->name('profile.index');

    // اختبار العملة
    Route::get('currency-test', function() {
        return view('admin.currency-test');
    })->name('currency.test');

    // المنتجات والمخزون
    Route::resource('items', ItemController::class);

    // المخزون
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::resource('categories', App\Http\Controllers\Modules\Inventory\CategoryController::class);
        Route::resource('warehouses', App\Http\Controllers\Modules\Inventory\WarehouseController::class);
        Route::resource('adjustments', App\Http\Controllers\Modules\Inventory\AdjustmentController::class);
        Route::resource('transfers', App\Http\Controllers\Modules\Inventory\TransferController::class);
    });

    // التقارير
    Route::prefix('reports')->name('reports.')->group(function () {
        // التقارير المالية
        Route::prefix('financial')->name('financial.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\Reports\FinancialReportController::class, 'index'])->name('index');
            Route::get('trial-balance', [App\Http\Controllers\Modules\Reports\FinancialReportController::class, 'trialBalance'])->name('trial_balance');
            Route::get('balance-sheet', [App\Http\Controllers\Modules\Reports\FinancialReportController::class, 'balanceSheet'])->name('balance_sheet');
            Route::get('income-statement', [App\Http\Controllers\Modules\Reports\FinancialReportController::class, 'incomeStatement'])->name('income_statement');
            Route::get('cash-flow', [App\Http\Controllers\Modules\Reports\FinancialReportController::class, 'cashFlow'])->name('cash_flow');
        });

        // تقارير المبيعات
        Route::prefix('sales')->name('sales.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\Reports\SalesReportController::class, 'index'])->name('index');
            Route::get('summary', [App\Http\Controllers\Modules\Reports\SalesReportController::class, 'salesSummary'])->name('summary');
            Route::get('by-customer', [App\Http\Controllers\Modules\Reports\SalesReportController::class, 'salesByCustomer'])->name('by_customer');
            Route::get('by-product', [App\Http\Controllers\Modules\Reports\SalesReportController::class, 'salesByProduct'])->name('by_product');
        });

        // تقارير المشتريات
        Route::prefix('purchases')->name('purchases.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\Reports\PurchaseReportController::class, 'index'])->name('index');
            Route::get('summary', [App\Http\Controllers\Modules\Reports\PurchaseReportController::class, 'purchaseSummary'])->name('summary');
            Route::get('by-supplier', [App\Http\Controllers\Modules\Reports\PurchaseReportController::class, 'purchaseBySupplier'])->name('by_supplier');
        });

        // تقارير المخزون
        Route::prefix('inventory')->name('inventory.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\Reports\InventoryReportController::class, 'index'])->name('index');
            Route::get('stock-levels', [App\Http\Controllers\Modules\Reports\InventoryReportController::class, 'stockLevels'])->name('stock_levels');
            Route::get('low-stock', [App\Http\Controllers\Modules\Reports\InventoryReportController::class, 'lowStock'])->name('low_stock');
        });
    });

    // الموارد البشرية
    Route::prefix('hr')->name('hr.')->group(function () {
        Route::resource('employees', App\Http\Controllers\Modules\HR\EmployeeController::class);
        Route::resource('departments', App\Http\Controllers\Modules\HR\DepartmentController::class);
        Route::resource('payroll', App\Http\Controllers\Modules\HR\PayrollController::class);
        Route::post('payroll/generate-monthly', [App\Http\Controllers\Modules\HR\PayrollController::class, 'generateMonthly'])->name('payroll.generate_monthly');
        Route::get('payroll/summary', [App\Http\Controllers\Modules\HR\PayrollController::class, 'summary'])->name('payroll.summary');
        Route::get('payroll/{id}/export', [App\Http\Controllers\Modules\HR\PayrollController::class, 'export'])->name('payroll.export');
    });

    // نقاط البيع المتقدمة
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::resource('terminals', App\Http\Controllers\Modules\POS\TerminalController::class);
        Route::post('terminals/{id}/toggle-status', [App\Http\Controllers\Modules\POS\TerminalController::class, 'toggleStatus'])->name('terminals.toggle_status');

        Route::resource('sessions', App\Http\Controllers\Modules\POS\POSSessionManagementController::class);
        Route::post('sessions/{id}/close', [App\Http\Controllers\Modules\POS\POSSessionManagementController::class, 'close'])->name('sessions.close');
        Route::post('sessions/{id}/force-close', [App\Http\Controllers\Modules\POS\POSSessionManagementController::class, 'forceClose'])->name('sessions.force_close');
        Route::get('sessions/{id}/report', [App\Http\Controllers\Modules\POS\POSSessionManagementController::class, 'report'])->name('sessions.report');
        Route::get('sessions/daily-summary', [App\Http\Controllers\Modules\POS\POSSessionManagementController::class, 'dailySummary'])->name('sessions.daily_summary');

        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'index'])->name('index');
            Route::post('/', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'update'])->name('update');
            Route::get('receipt', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'receiptSettings'])->name('receipt');
            Route::post('receipt', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'updateReceiptSettings'])->name('receipt.update');
            Route::get('payment-methods', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'paymentMethods'])->name('payment_methods');
            Route::post('payment-methods', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'updatePaymentMethods'])->name('payment_methods.update');
            Route::get('tax', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'taxSettings'])->name('tax');
            Route::post('tax', [App\Http\Controllers\Modules\POS\POSSettingsController::class, 'updateTaxSettings'])->name('tax.update');
        });
    });

    // التصنيع المتقدم
    Route::prefix('manufacturing')->name('manufacturing.')->group(function () {
        Route::resource('operations', App\Http\Controllers\Modules\Manufacturing\OperationController::class);
        Route::post('operations/{id}/start', [App\Http\Controllers\Modules\Manufacturing\OperationController::class, 'start'])->name('operations.start');
        Route::post('operations/{id}/complete', [App\Http\Controllers\Modules\Manufacturing\OperationController::class, 'complete'])->name('operations.complete');
    });

    // إدارة النظام
    Route::prefix('system')->name('system.')->group(function () {
        Route::prefix('backup')->name('backup.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\System\BackupController::class, 'index'])->name('index');
            Route::post('create', [App\Http\Controllers\Modules\System\BackupController::class, 'create'])->name('create');
            Route::get('download/{filename}', [App\Http\Controllers\Modules\System\BackupController::class, 'download'])->name('download');
            Route::delete('delete/{filename}', [App\Http\Controllers\Modules\System\BackupController::class, 'delete'])->name('delete');
            Route::post('restore/{filename}', [App\Http\Controllers\Modules\System\BackupController::class, 'restore'])->name('restore');
            Route::post('schedule', [App\Http\Controllers\Modules\System\BackupController::class, 'schedule'])->name('schedule');
            Route::get('settings', [App\Http\Controllers\Modules\System\BackupController::class, 'settings'])->name('settings');
            Route::post('settings', [App\Http\Controllers\Modules\System\BackupController::class, 'updateSettings'])->name('settings.update');
        });

        Route::prefix('logs')->name('logs.')->group(function () {
            Route::get('/', [App\Http\Controllers\Modules\System\LogController::class, 'index'])->name('index');
            Route::get('application', [App\Http\Controllers\Modules\System\LogController::class, 'application'])->name('application');
            Route::get('errors', [App\Http\Controllers\Modules\System\LogController::class, 'errors'])->name('errors');
            Route::get('access', [App\Http\Controllers\Modules\System\LogController::class, 'access'])->name('access');
            Route::get('audit', [App\Http\Controllers\Modules\System\LogController::class, 'audit'])->name('audit');
            Route::delete('clear/{type}', [App\Http\Controllers\Modules\System\LogController::class, 'clear'])->name('clear');
            Route::get('download/{type}', [App\Http\Controllers\Modules\System\LogController::class, 'download'])->name('download');
            Route::get('viewer/{type}', [App\Http\Controllers\Modules\System\LogController::class, 'viewer'])->name('viewer');
            Route::post('search', [App\Http\Controllers\Modules\System\LogController::class, 'search'])->name('search');
        });
    });
});

// مسارات نقطة البيع
Route::prefix('pos')->name('pos.')->group(function () {
    Route::get('/', [App\Http\Controllers\PosFrontend\PosFrontendController::class, 'index'])->name('index');
    Route::get('/login', [App\Http\Controllers\PosFrontend\PosFrontendController::class, 'showLoginForm'])->name('login.form');
    Route::post('/login', [App\Http\Controllers\PosFrontend\PosFrontendController::class, 'login'])->name('login');
    Route::get('/main', [App\Http\Controllers\PosFrontend\PosFrontendController::class, 'main'])->name('main');
    Route::post('/logout', [App\Http\Controllers\PosFrontend\PosFrontendController::class, 'logout'])->name('logout');
});

// مسارات المصادقة
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::get('/logout', [LoginController::class, 'showLogoutForm'])->name('logout.confirm');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// مسار العودة من حساب المستأجر إلى حساب السوبر أدمن
Route::get('/switch-back', function() { return redirect()->route('admin.dashboard'); })->name('tenant_switch.back');

