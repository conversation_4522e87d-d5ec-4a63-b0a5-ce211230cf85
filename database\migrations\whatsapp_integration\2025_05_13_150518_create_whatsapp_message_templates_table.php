<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("whatsapp_message_templates", function (Blueprint $table) {
            $table->id();
            $table->string("name")->unique()->comment("Unique internal name for the template");
            $table->string("namespace")->nullable()->comment("Optional template namespace if required by provider");
            $table->string("language_code", 10)->comment("Language code of the template, e.g., ar, en_US");
            $table->string("category")->nullable()->comment("Optional template category, e.g., TRANSACTIONAL, MARKETING");
            $table->json("components_json")->comment("Stores template components (header, body, footer, buttons) as JSO<PERSON> from WhatsApp API");
            $table->string("status", 50)->comment("Template status: PENDING, APPROVED, REJECTED, PAUSED, DISABLED");
            $table->string("provider_template_id")->nullable()->comment("Optional template ID from the provider");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("whatsapp_message_templates");
    }
};
