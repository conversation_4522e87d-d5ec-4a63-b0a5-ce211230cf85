<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /**
     * Show the login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'login' => ['required', 'string'],
            'password' => ['required'],
        ]);

        $loginField = filter_var($request->login, FILTER_VALIDATE_EMAIL) ? 'email' : 'name';

        $credentials = [
            $loginField => $request->login,
            'password' => $request->password,
        ];

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();

            $user = Auth::user();

            // توجيه المستخدم حسب دوره
            if ($user->hasRole('admin')) {
                return redirect()->intended(route('admin.dashboard'));
            } elseif ($user->hasRole('tenant')) {
                return redirect()->intended(route('tenant.dashboard'));
            } else {
                // المستخدمين العاديين (موظفي المستأجرين)
                if ($user->tenant_id) {
                    return redirect()->intended(route('tenant.dashboard'));
                }

                return redirect()->intended(route('dashboard'));
            }
        }

        return back()->withErrors([
            'login' => 'بيانات الاعتماد المقدمة غير صحيحة.',
        ])->onlyInput('login');
    }

    /**
     * Show the logout confirmation page.
     *
     * @return \Illuminate\View\View
     */
    public function showLogoutForm()
    {
        return view('auth.logout');
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/login')->with('success', 'تم تسجيل الخروج بنجاح');
    }
}
