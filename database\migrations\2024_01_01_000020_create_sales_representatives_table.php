<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_representatives', function (Blueprint $table) {
            $table->id();
            $table->string('employee_code')->unique(); // كود الموظف
            $table->string('name'); // اسم المندوب
            $table->string('phone'); // رقم الهاتف
            $table->string('email')->nullable(); // البريد الإلكتروني
            $table->string('national_id')->unique(); // رقم الهوية
            $table->date('birth_date')->nullable(); // تاريخ الميلاد
            $table->text('address')->nullable(); // العنوان
            $table->date('hire_date'); // تاريخ التوظيف
            $table->decimal('base_salary', 10, 2)->default(0); // الراتب الأساسي
            $table->decimal('commission_rate', 5, 2)->default(0); // نسبة العمولة
            $table->decimal('target_amount', 12, 2)->default(0); // الهدف الشهري
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active'); // حالة المندوب
            $table->enum('commission_type', ['percentage', 'fixed', 'tiered'])->default('percentage'); // نوع العمولة
            $table->json('commission_tiers')->nullable(); // شرائح العمولة
            $table->string('vehicle_type')->nullable(); // نوع المركبة
            $table->string('vehicle_number')->nullable(); // رقم المركبة
            $table->string('license_number')->nullable(); // رقم الرخصة
            $table->date('license_expiry')->nullable(); // انتهاء الرخصة
            $table->text('notes')->nullable(); // ملاحظات
            $table->string('profile_image')->nullable(); // صورة شخصية
            $table->json('documents')->nullable(); // المستندات
            $table->boolean('is_active')->default(true);
            $table->foreignId('manager_id')->nullable()->constrained('users')->onDelete('set null'); // المدير المباشر
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_representatives');
    }
};
