<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo $__env->yieldContent("title", "نقطة البيع"); ?></title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --pos-primary-bg: #343a40; /* Dark grey */
            --pos-secondary-bg: #f8f9fa; /* Light grey */
            --pos-accent-color: #007bff; /* Bootstrap primary blue */
            --pos-text-color-light: #ffffff;
            --pos-text-color-dark: #212529;
            --pos-border-color: #dee2e6;
        }

        body {
            font-family: "Cairo", sans-serif;
            background-color: var(--pos-secondary-bg);
            color: var(--pos-text-color-dark);
            overscroll-behavior-y: contain; /* Prevent pull-to-refresh on mobile */
        }

        .pos-container {
            display: flex;
            flex-direction: column;
            height: 100vh; /* Full viewport height */
            max-height: -webkit-fill-available; /* iOS Safari full height */
        }

        .pos-header {
            background-color: var(--pos-primary-bg);
            color: var(--pos-text-color-light);
            padding: 0.75rem 1rem;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pos-header .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .pos-toolbar {
            background-color: #e9ecef;
            padding: 0.5rem 0.75rem;
            border-bottom: 1px solid var(--pos-border-color);
            flex-shrink: 0;
            overflow-x: auto; /* Allow horizontal scrolling for many buttons */
            white-space: nowrap;
        }

        .pos-toolbar .btn {
            margin-right: 0.5rem; /* RTL: margin-left */
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
        .pos-toolbar .btn:last-child {
            margin-right: 0;
        }

        .pos-main-content {
            flex-grow: 1;
            overflow: hidden; /* Prevent body scroll, individual sections will scroll */
            display: flex;
            flex-direction: column; /* Default for single column layout on mobile */
        }
        
        .pos-main-layout {
            display: flex;
            flex-grow: 1;
            overflow: hidden; /* Important for child scrolling */
            flex-direction: column; /* Mobile first: stack product and cart */
        }

        .products-section,
        .cart-section {
            padding: 0.75rem;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }

        .products-section {
            background-color: #ffffff;
            /* On mobile, it takes full width or a significant portion */
        }

        .cart-section {
            background-color: var(--pos-secondary-bg);
            border-top: 1px solid var(--pos-border-color); /* Separator on mobile */
            flex-shrink: 0; /* Cart summary should not shrink excessively */
        }

        /* Desktop and larger screens */
        @media (min-width: 768px) {
            .pos-main-layout {
                flex-direction: row; /* Side-by-side layout */
            }
            .products-section {
                flex: 3; /* Takes more space */
                border-left: 1px solid var(--pos-border-color); /* RTL: border-left */
            }
            .cart-section {
                flex: 2;
                border-top: none; /* No top border on desktop */
            }
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 0.75rem;
        }

        .product-card {
            border: 1px solid #eee;
            padding: 0.75rem;
            text-align: center;
            cursor: pointer;
            border-radius: 0.375rem;
            background-color: #fff;
            transition: box-shadow 0.2s ease-in-out;
        }
        .product-card:hover {
            box-shadow: 0 0 10px rgba(0,0,0,0.15);
        }
        .product-card img {
            max-width: 100%;
            height: 70px;
            object-fit: contain; /* Changed to contain to see full image */
            margin-bottom: 0.5rem;
            border-radius: 0.25rem;
        }
        .product-card strong {
            font-size: 0.85rem;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .product-card div:last-child {
            font-size: 0.9rem;
            font-weight: bold;
            color: var(--pos-accent-color);
        }

        .cart-items .d-flex {
            padding-bottom: 0.5rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        .cart-items .d-flex:last-child {
            border-bottom: none;
        }
        .cart-items .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .cart-summary {
            padding-top: 0.75rem;
            margin-top: 0.75rem;
            border-top: 2px dashed #ccc;
        }
        .cart-summary .btn-lg {
            padding: 0.75rem 1.25rem;
            font-size: 1.1rem;
        }

        /* Modal improvements */
        .modal-header {
            background-color: var(--pos-primary-bg);
            color: var(--pos-text-color-light);
        }
        .modal-header .btn-close {
            filter: invert(1) grayscale(100%) brightness(200%);
        }
        .modal-footer {
            background-color: #f8f9fa;
        }

        /* Ensure touch targets are large enough */
        .btn, .form-control, .form-select, .nav-link {
            min-height: 44px; /* Minimum touch target size */
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .form-control-sm, .form-select-sm, .btn-sm {
             min-height: 38px;
        }

    </style>
    <?php echo $__env->yieldPushContent("styles"); ?>
    <link rel="manifest" href="/manifest.json"> 
    <meta name="theme-color" content="#343a40"/> 
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="نقطة البيع">
    
</head>
<body>
    <div class="pos-container">
        <?php if (! empty(trim($__env->yieldContent("pos_header")))): ?>
            <header class="pos-header">
                <?php echo $__env->yieldContent("pos_header"); ?>
            </header>
        <?php endif; ?>

        
        <?php echo $__env->yieldContent("pos_toolbar"); ?>

        <main class="pos-main-content">
            <?php echo $__env->yieldContent("content"); ?>
        </main>

        <?php if (! empty(trim($__env->yieldContent("pos_footer")))): ?>
            <footer class="pos-footer text-center">
                <?php echo $__env->yieldContent("pos_footer", "© " . date("Y") . " نظام نقاط البيع"); ?>
            </footer>
        <?php endif; ?>
    </div>

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <?php echo $__env->yieldPushContent("scripts"); ?>

    <script>
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", () => {
                navigator.serviceWorker.register("/js/pos_frontend/service-worker.js") // Ensure this path is correct
                    .then(registration => {
                        console.log("ServiceWorker registration successful with scope: ", registration.scope);
                    })
                    .catch(error => {
                        console.log("ServiceWorker registration failed: ", error);
                    });
            });
        }
        // Prevent body scroll on touch devices when a modal is open to avoid background scrolling issues
        document.addEventListener("DOMContentLoaded", () => {
            const modals = document.querySelectorAll(".modal");
            modals.forEach(modal => {
                modal.addEventListener("shown.bs.modal", () => {
                    document.body.style.overflow = "hidden"; 
                    document.body.style.position = "fixed"; // Helps on some iOS versions
                    document.body.style.width = "100%";
                });
                modal.addEventListener("hidden.bs.modal", () => {
                    document.body.style.overflow = "";
                    document.body.style.position = "";
                    document.body.style.width = "";
                });
            });
        });
    </script>
</body>
</html>

<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/pos_frontend/layouts/app.blade.php ENDPATH**/ ?>