<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BnplProviderSetting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class BnplProviderSettingController extends Controller
{
    public function index()
    {
        $settings = BnplProviderSetting::all();
        // Decrypt for display if necessary, though usually not shown directly in index
        // Or better, don't fetch sensitive fields for the index view unless masked.
        return view("admin.bnpl_settings.index", compact("settings"));
    }

    public function create()
    {
        return view("admin.bnpl_settings.form");
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "provider_name" => ["required", Rule::in(["tabby", "tamara"])],
            "display_name" => "required|string|max:255",
            "api_key_sandbox" => "nullable|string|max:1000",
            "api_key_production" => "nullable|string|max:1000",
            "public_key_sandbox" => "nullable|string|max:1000", // For Tamara
            "public_key_production" => "nullable|string|max:1000", // For Tamara
            "notification_token_sandbox" => "nullable|string|max:1000", // For Tamara Webhook
            "notification_token_production" => "nullable|string|max:1000", // For Tamara Webhook
            "webhook_secret_sandbox" => "nullable|string|max:1000", // For Tabby Webhook
            "webhook_secret_production" => "nullable|string|max:1000", // For Tabby Webhook
            "merchant_code_sandbox" => "nullable|string|max:255", // For Tabby
            "merchant_code_production" => "nullable|string|max:255", // For Tabby
            "environment" => ["required", Rule::in(["sandbox", "production"])],
            "is_active" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");

        // The model's mutators will handle encryption automatically.
        BnplProviderSetting::create($validatedData);

        return redirect()->route("admin.bnpl_settings.index")->with("success", "تم حفظ إعدادات المزود بنجاح.");
    }

    public function edit(BnplProviderSetting $bnpl_setting)
    {
        // Values will be automatically decrypted by model accessors when accessed in the view
        return view("admin.bnpl_settings.form", ["setting" => $bnpl_setting]);
    }

    public function update(Request $request, BnplProviderSetting $bnpl_setting)
    {
        $validatedData = $request->validate([
            "provider_name" => ["required", Rule::in(["tabby", "tamara"])],
            "display_name" => "required|string|max:255",
            // Allow empty strings to clear a key, model will handle null conversion if needed
            "api_key_sandbox" => "nullable|string|max:1000",
            "api_key_production" => "nullable|string|max:1000",
            "public_key_sandbox" => "nullable|string|max:1000",
            "public_key_production" => "nullable|string|max:1000",
            "notification_token_sandbox" => "nullable|string|max:1000",
            "notification_token_production" => "nullable|string|max:1000",
            "webhook_secret_sandbox" => "nullable|string|max:1000",
            "webhook_secret_production" => "nullable|string|max:1000",
            "merchant_code_sandbox" => "nullable|string|max:255",
            "merchant_code_production" => "nullable|string|max:255",
            "environment" => ["required", Rule::in(["sandbox", "production"])],
            "is_active" => "boolean",
        ]);

        $validatedData["is_active"] = $request->has("is_active");

        // The model's mutators will handle encryption automatically on assignment.
        // If a field is not present in $validatedData (e.g. due to being empty and nullable),
        // it won't be passed to update, preserving existing value unless explicitly cleared.
        // To clear a field, ensure the form submits an empty string for it and validation allows nullable.
        $bnpl_setting->fill($validatedData); // Use fill to only update provided fields
        $bnpl_setting->save();

        return redirect()->route("admin.bnpl_settings.index")->with("success", "تم تحديث إعدادات المزود بنجاح.");
    }

    public function destroy(BnplProviderSetting $bnpl_setting)
    {
        $bnpl_setting->delete();
        return redirect()->route("admin.bnpl_settings.index")->with("success", "تم حذف إعدادات المزود بنجاح.");
    }
}

