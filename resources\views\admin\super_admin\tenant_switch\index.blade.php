@extends('layouts.admin')

@section('header')
    تبديل حسابات المستأجرين
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary bg-opacity-10 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-primary">
                        <i class="bi bi-people-fill me-2"></i>قائمة المستأجرين
                    </h5>
                    <a href="{{ route('admin.super_admin.dashboard') }}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrow-right-circle me-1"></i>العودة للوحة التحكم
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle-fill fs-4 me-2"></i>
                                <div>{{ session('success') }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                                <div>{{ session('error') }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم المستأجر</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>عدد المستخدمين</th>
                                    <th>الاشتراك</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tenants as $tenant)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $tenant->name }}</td>
                                    <td>{{ $tenant->email }}</td>
                                    <td>{{ $tenant->users->count() }}</td>
                                    <td>
                                        @if($tenant->activeSubscription)
                                            <span class="badge bg-success">{{ $tenant->activeSubscription->plan->name }}</span>
                                        @else
                                            <span class="badge bg-warning">لا يوجد اشتراك نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($tenant->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.super_admin.tenant_switch.switch', $tenant->id) }}" 
                                               class="btn btn-sm btn-primary" 
                                               onclick="return confirm('هل أنت متأكد من رغبتك في التبديل إلى حساب {{ $tenant->name }}؟')">
                                                <i class="bi bi-box-arrow-in-right me-1"></i>تبديل
                                            </a>
                                            <a href="{{ route('admin.super_admin.tenants.edit', $tenant->id) }}" class="btn btn-sm btn-info">
                                                <i class="bi bi-pencil-square me-1"></i>تعديل
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">لا يوجد مستأجرين</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
