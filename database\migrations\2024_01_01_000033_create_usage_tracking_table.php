<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usage_tracking', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('users')->onDelete('cascade');
            $table->string('resource_type'); // نوع المورد (users, branches, invoices, etc.)
            $table->string('action'); // العملية (create, update, delete, view)
            $table->integer('count')->default(1); // العدد
            $table->date('usage_date'); // تاريخ الاستخدام
            $table->integer('usage_month'); // شهر الاستخدام
            $table->integer('usage_year'); // سنة الاستخدام
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();

            $table->index(['tenant_id', 'resource_type', 'usage_date']);
            $table->index(['tenant_id', 'usage_month', 'usage_year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usage_tracking');
    }
};
