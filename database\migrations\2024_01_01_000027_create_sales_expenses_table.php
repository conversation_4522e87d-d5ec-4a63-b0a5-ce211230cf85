<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_expenses', function (Blueprint $table) {
            $table->id();
            $table->string('expense_code')->unique(); // كود المصروف
            $table->foreignId('sales_representative_id')->constrained('sales_representatives')->onDelete('cascade');
            $table->foreignId('sales_visit_id')->nullable()->constrained('sales_visits')->onDelete('set null');
            $table->enum('expense_type', ['fuel', 'meals', 'accommodation', 'transportation', 'communication', 'entertainment', 'other'])->default('fuel');
            $table->date('expense_date'); // تاريخ المصروف
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('currency', 3)->default('SAR'); // العملة
            $table->text('description'); // وصف المصروف
            $table->string('receipt_number')->nullable(); // رقم الإيصال
            $table->string('vendor_name')->nullable(); // اسم المورد
            $table->json('receipt_images')->nullable(); // صور الإيصالات
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'paid'])->default('pending');
            $table->text('approval_notes')->nullable(); // ملاحظات الموافقة
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->date('payment_date')->nullable(); // تاريخ الدفع
            $table->string('payment_reference')->nullable(); // مرجع الدفع
            $table->boolean('is_reimbursable')->default(true); // قابل للاسترداد
            $table->decimal('reimbursed_amount', 10, 2)->default(0); // المبلغ المسترد
            $table->json('expense_categories')->nullable(); // فئات المصروف
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_expenses');
    }
};
