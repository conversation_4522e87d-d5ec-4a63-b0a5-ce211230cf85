@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ __("Area Details") }}: {{ $area->name }}</h1>
                <div>
                    <a href="{{ route('admin.restaurant.areas.edit', $area) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> {{ __("Edit Area") }}
                    </a>
                    <a href="{{ route('admin.restaurant.areas.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ __("Back to Areas") }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Area Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __("Area Information") }}</h5>
                </div>
                <div class="card-body">
                    <div class="area-display p-3 rounded mb-3" 
                         style="background-color: {{ $area->color }}20; border: 2px solid {{ $area->color }};">
                        <div class="text-center">
                            <i class="fas fa-map-marker-alt fa-3x mb-2" style="color: {{ $area->color }}"></i>
                            <h4>{{ $area->name }}</h4>
                            @if($area->description)
                                <p class="text-muted">{{ $area->description }}</p>
                            @endif
                        </div>
                    </div>

                    <table class="table table-borderless">
                        <tr>
                            <td><strong>{{ __("Name") }}:</strong></td>
                            <td>{{ $area->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Branch") }}:</strong></td>
                            <td>{{ $area->branch->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Color") }}:</strong></td>
                            <td>
                                <span class="badge" style="background-color: {{ $area->color }}; color: white;">
                                    {{ $area->color }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Sort Order") }}:</strong></td>
                            <td>{{ $area->sort_order }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Status") }}:</strong></td>
                            <td>
                                @if($area->is_active)
                                    <span class="badge bg-success">{{ __("Active") }}</span>
                                @else
                                    <span class="badge bg-danger">{{ __("Inactive") }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Created") }}:</strong></td>
                            <td>{{ $area->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __("Updated") }}:</strong></td>
                            <td>{{ $area->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tables in Area -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        {{ __("Tables in this Area") }} 
                        <span class="badge bg-primary">{{ $area->tables->count() }}</span>
                    </h5>
                    <a href="{{ route('admin.restaurant.tables.create', ['area_id' => $area->id]) }}" 
                       class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> {{ __("Add Table") }}
                    </a>
                </div>
                <div class="card-body">
                    @if($area->tables->count() > 0)
                        <div class="row">
                            @foreach($area->tables as $table)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card table-card h-100">
                                        <div class="card-body text-center">
                                            <div class="table-icon mb-2" 
                                                 style="color: {{ $table->status_color }}">
                                                <i class="fas fa-table fa-2x"></i>
                                            </div>
                                            <h6 class="card-title">{{ $table->name }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">{{ __("Table") }} #{{ $table->number }}</small><br>
                                                <small class="text-muted">{{ __("Capacity") }}: {{ $table->capacity }}</small>
                                            </p>
                                            <span class="badge mb-2" 
                                                  style="background-color: {{ $table->status_color }}">
                                                {{ ucfirst($table->status) }}
                                            </span>
                                            
                                            @if($table->currentOrder)
                                                <div class="mt-2">
                                                    <small class="text-info">
                                                        <i class="fas fa-receipt"></i> 
                                                        {{ __("Order") }} #{{ $table->currentOrder->order_number }}
                                                    </small>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('admin.restaurant.tables.show', $table) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.restaurant.tables.edit', $table) }}" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($table->status === 'available')
                                                    <button class="btn btn-sm btn-outline-success" 
                                                            onclick="updateTableStatus({{ $table->id }}, 'occupied')">
                                                        <i class="fas fa-user-plus"></i>
                                                    </button>
                                                @elseif($table->status === 'occupied')
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="updateTableStatus({{ $table->id }}, 'available')">
                                                        <i class="fas fa-user-minus"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-table fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __("No tables in this area") }}</h5>
                            <p class="text-muted">{{ __("Start by adding tables to this area.") }}</p>
                            <a href="{{ route('admin.restaurant.tables.create', ['area_id' => $area->id]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> {{ __("Add First Table") }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.table-card {
    transition: transform 0.2s;
    border: 1px solid #dee2e6;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.table-icon {
    transition: color 0.3s;
}

.area-display {
    transition: all 0.3s;
}

.btn-group .btn {
    flex: 1;
}
</style>
@endpush

@push('scripts')
<script>
function updateTableStatus(tableId, status) {
    if (confirm('{{ __("Are you sure you want to change the table status?") }}')) {
        fetch(`/admin/restaurant/tables/${tableId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{{ __("Error updating table status") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("Error updating table status") }}');
        });
    }
}
</script>
@endpush
