<?php

return [
    /*
    |--------------------------------------------------------------------------
    | إعدادات تكامل هيئة الزكاة والضريبة والجمارك (ZATCA)
    |--------------------------------------------------------------------------
    |
    | هذا الملف يحتوي على جميع إعدادات التكامل مع هيئة الزكاة والضريبة والجمارك
    | للفوترة الإلكترونية (فاتورة) في المملكة العربية السعودية
    |
    */

    // بيئة العمل - sandbox للاختبار، production للإنتاج
    'environment' => env('ZATCA_ENVIRONMENT', 'sandbox'),

    // معلومات البائع الأساسية
    'seller_name' => env('ZATCA_SELLER_NAME', 'اسم الشركة'),
    'vat_registration_number' => env('ZATCA_VAT_REGISTRATION_NUMBER', '310123456789013'),
    
    // عنوان البائع
    'seller_street' => env('ZATCA_SELLER_STREET', 'شارع الملك فهد'),
    'seller_building_number' => env('ZATCA_SELLER_BUILDING_NUMBER', '1234'),
    'seller_plot_identification' => env('ZATCA_SELLER_PLOT_IDENTIFICATION', '1234'),
    'seller_city_subdivision' => env('ZATCA_SELLER_CITY_SUBDIVISION', 'حي النخيل'),
    'seller_city' => env('ZATCA_SELLER_CITY', 'الرياض'),
    'seller_postal_zone' => env('ZATCA_SELLER_POSTAL_ZONE', '12345'),
    'seller_country_code' => env('ZATCA_SELLER_COUNTRY_CODE', 'SA'),

    // إعدادات شهادة CSR
    'csr_common_name' => env('ZATCA_CSR_COMMON_NAME', null), // سيتم استخدام VAT number إذا لم يتم تحديده
    'csr_organization_unit' => env('ZATCA_CSR_ORGANIZATION_UNIT', 'قسم تقنية المعلومات'),
    'csr_invoice_type' => env('ZATCA_CSR_INVOICE_TYPE', '1100'), // 1100 للفواتير المبسطة والعادية
    'csr_business_category' => env('ZATCA_CSR_BUSINESS_CATEGORY', 'تجارة التجزئة'),

    // كلمات مرور المفاتيح الخاصة
    'csr_private_key_password' => env('ZATCA_CSR_PRIVATE_KEY_PASSWORD', ''),
    'csid_private_key_password' => env('ZATCA_CSID_PRIVATE_KEY_PASSWORD', ''),

    // OTP للحصول على شهادة الامتثال
    'compliance_otp' => env('ZATCA_COMPLIANCE_OTP', ''),

    // إعدادات API
    'api_endpoints' => [
        'sandbox' => [
            'compliance' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/compliance',
            'production' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/production',
            'reporting' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/reporting/single',
            'clearance' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/clearance/single',
        ],
        'production' => [
            'compliance' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/compliance',
            'production' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/production',
            'reporting' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/reporting/single',
            'clearance' => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/clearance/single',
        ],
    ],

    // إعدادات الفوترة الإلكترونية
    'invoice_settings' => [
        // تفعيل الفوترة الإلكترونية
        'enabled' => env('ZATCA_ENABLED', true),
        
        // تفعيل التوقيع الرقمي
        'digital_signature_enabled' => env('ZATCA_DIGITAL_SIGNATURE_ENABLED', true),
        
        // تفعيل إرسال الفواتير تلقائياً
        'auto_submit' => env('ZATCA_AUTO_SUBMIT', false),
        
        // تفعيل QR Code
        'qr_code_enabled' => env('ZATCA_QR_CODE_ENABLED', true),
        
        // المرحلة الحالية (1 أو 2)
        'current_phase' => env('ZATCA_CURRENT_PHASE', 2),
    ],

    // إعدادات الضرائب
    'tax_settings' => [
        'default_vat_rate' => env('ZATCA_DEFAULT_VAT_RATE', 15), // 15% ضريبة القيمة المضافة
        'vat_exemption_reason_code' => env('ZATCA_VAT_EXEMPTION_REASON_CODE', 'VATEX-SA-29'),
        'vat_exemption_reason_text' => env('ZATCA_VAT_EXEMPTION_REASON_TEXT', 'معفى من ضريبة القيمة المضافة'),
    ],

    // إعدادات التخزين
    'storage' => [
        'certificates_path' => env('ZATCA_CERTIFICATES_PATH', 'zatca'),
        'invoices_path' => env('ZATCA_INVOICES_PATH', 'zatca/invoices'),
        'logs_path' => env('ZATCA_LOGS_PATH', 'zatca/logs'),
    ],

    // إعدادات السجلات والتتبع
    'logging' => [
        'enabled' => env('ZATCA_LOGGING_ENABLED', true),
        'level' => env('ZATCA_LOG_LEVEL', 'info'), // debug, info, warning, error
        'retention_days' => env('ZATCA_LOG_RETENTION_DAYS', 365),
    ],

    // إعدادات إعادة المحاولة
    'retry_settings' => [
        'max_attempts' => env('ZATCA_MAX_RETRY_ATTEMPTS', 3),
        'delay_seconds' => env('ZATCA_RETRY_DELAY_SECONDS', 5),
    ],

    // إعدادات التحقق والاختبار
    'testing' => [
        'mock_responses' => env('ZATCA_MOCK_RESPONSES', false),
        'skip_validation' => env('ZATCA_SKIP_VALIDATION', false),
        'test_mode' => env('ZATCA_TEST_MODE', false),
    ],
];
