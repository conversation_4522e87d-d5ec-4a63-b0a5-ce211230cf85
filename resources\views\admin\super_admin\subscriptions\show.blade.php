@extends('layouts.admin')

@section('title', 'تفاصيل الاشتراك')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل الاشتراك #{{ $subscription->id }}</h3>
                    <div>
                        <a href="{{ route('admin.super_admin.subscriptions.edit', $subscription->id) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.super_admin.subscriptions.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات الاشتراك</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">رقم الاشتراك</th>
                                            <td>{{ $subscription->id }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @switch($subscription->status)
                                                    @case('active')
                                                        <span class="badge bg-success">نشط</span>
                                                        @break
                                                    @case('expired')
                                                        <span class="badge bg-danger">منتهي</span>
                                                        @break
                                                    @case('cancelled')
                                                        <span class="badge bg-warning">ملغي</span>
                                                        @break
                                                    @case('pending')
                                                        <span class="badge bg-info">قيد الانتظار</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">{{ $subscription->status }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ البدء</th>
                                            <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الانتهاء</th>
                                            <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>المبلغ المدفوع</th>
                                            <td>{{ $subscription->price_paid }} ريال</td>
                                        </tr>
                                        <tr>
                                            <th>طريقة الدفع</th>
                                            <td>{{ $subscription->payment_method ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>مرجع الدفع</th>
                                            <td>{{ $subscription->payment_reference ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تجديد تلقائي</th>
                                            <td>
                                                @if($subscription->auto_renew)
                                                    <span class="badge bg-success">نعم</span>
                                                @else
                                                    <span class="badge bg-danger">لا</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>ملاحظات</th>
                                            <td>{{ $subscription->notes ?? 'لا توجد ملاحظات' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات المستأجر</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscription->tenant)
                                        <table class="table table-bordered">
                                            <tr>
                                                <th style="width: 30%">الاسم</th>
                                                <td>{{ $subscription->tenant->name }}</td>
                                            </tr>
                                            <tr>
                                                <th>البريد الإلكتروني</th>
                                                <td>{{ $subscription->tenant->email }}</td>
                                            </tr>
                                            <tr>
                                                <th>رقم الهاتف</th>
                                                <td>{{ $subscription->tenant->phone ?? 'غير محدد' }}</td>
                                            </tr>
                                            <tr>
                                                <th>تاريخ التسجيل</th>
                                                <td>{{ $subscription->tenant->created_at->format('Y-m-d') }}</td>
                                            </tr>
                                        </table>
                                    @else
                                        <div class="alert alert-warning">
                                            لا توجد معلومات عن المستأجر
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات الباقة</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscription->plan)
                                        <table class="table table-bordered">
                                            <tr>
                                                <th style="width: 30%">اسم الباقة</th>
                                                <td>{{ $subscription->plan->name }}</td>
                                            </tr>
                                            <tr>
                                                <th>السعر</th>
                                                <td>{{ $subscription->plan->price }} ريال</td>
                                            </tr>
                                            <tr>
                                                <th>دورة الفوترة</th>
                                                <td>
                                                    @switch($subscription->plan->billing_cycle)
                                                        @case('monthly')
                                                            شهري
                                                            @break
                                                        @case('quarterly')
                                                            ربع سنوي
                                                            @break
                                                        @case('semi_annually')
                                                            نصف سنوي
                                                            @break
                                                        @case('annually')
                                                            سنوي
                                                            @break
                                                        @default
                                                            {{ $subscription->plan->billing_cycle }}
                                                    @endswitch
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>الحد الأقصى للمستخدمين</th>
                                                <td>{{ $subscription->plan->max_users }}</td>
                                            </tr>
                                            <tr>
                                                <th>الحد الأقصى للفروع</th>
                                                <td>{{ $subscription->plan->max_branches }}</td>
                                            </tr>
                                        </table>
                                    @else
                                        <div class="alert alert-warning">
                                            لا توجد معلومات عن الباقة
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($subscription->changes && $subscription->changes->count() > 0)
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">سجل التغييرات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>التاريخ</th>
                                                        <th>نوع التغيير</th>
                                                        <th>التفاصيل</th>
                                                        <th>الباقة القديمة</th>
                                                        <th>الباقة الجديدة</th>
                                                        <th>المبلغ</th>
                                                        <th>بواسطة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($subscription->changes as $change)
                                                        <tr>
                                                            <td>{{ $change->effective_date->format('Y-m-d') }}</td>
                                                            <td>
                                                                @switch($change->change_type)
                                                                    @case('new')
                                                                        <span class="badge bg-success">جديد</span>
                                                                        @break
                                                                    @case('renew')
                                                                        <span class="badge bg-info">تجديد</span>
                                                                        @break
                                                                    @case('upgrade')
                                                                        <span class="badge bg-primary">ترقية</span>
                                                                        @break
                                                                    @case('downgrade')
                                                                        <span class="badge bg-warning">تخفيض</span>
                                                                        @break
                                                                    @case('cancel')
                                                                        <span class="badge bg-danger">إلغاء</span>
                                                                        @break
                                                                    @case('update')
                                                                        <span class="badge bg-secondary">تحديث</span>
                                                                        @break
                                                                    @default
                                                                        <span class="badge bg-secondary">{{ $change->change_type }}</span>
                                                                @endswitch
                                                            </td>
                                                            <td>{{ $change->change_details }}</td>
                                                            <td>{{ $change->oldPlan->name ?? '-' }}</td>
                                                            <td>{{ $change->newPlan->name ?? '-' }}</td>
                                                            <td>{{ $change->price_difference }} ريال</td>
                                                            <td>{{ $change->user->name ?? 'غير محدد' }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
