<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('item_code')->unique()->comment('Unique code for the item');
            $table->enum('item_type', ['raw_material', 'semi_finished_good', 'finished_good', 'service', 'asset', 'expense', 'other'])->default('raw_material');
            $table->string('unit_of_measure');
            $table->decimal('standard_cost', 15, 4)->default(0.0000);
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null'); // Assuming branches table exists
            $table->foreignId('account_id')->nullable()->constrained('accounts')->onDelete('set null')->comment('Default inventory/expense account');
            $table->boolean('is_manufactured')->default(false)->comment('Is this item manufactured internally?');
            $table->boolean('is_purchased')->default(true)->comment('Is this item purchased?');
            $table->boolean('is_sold')->default(false)->comment('Is this item sold?');
            $table->decimal('min_stock_level', 15, 4)->nullable();
            $table->decimal('max_stock_level', 15, 4)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_items');
    }
};
