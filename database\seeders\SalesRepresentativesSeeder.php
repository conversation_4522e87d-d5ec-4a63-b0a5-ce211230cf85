<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\SalesRepresentatives\SalesArea;
use App\Models\Modules\SalesRepresentatives\SalesRoute;
use App\Models\Modules\SalesRepresentatives\SalesVisit;
use App\Models\Modules\SalesRepresentatives\SalesCommission;
use App\Models\Modules\SalesRepresentatives\SalesTarget;
use App\Models\Modules\SalesRepresentatives\SalesExpense;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Modules\Sales\Customer;

class SalesRepresentativesSeeder extends Seeder
{
    public function run(): void
    {
        // Get first branch and user
        $branch = Branch::first();
        $user = User::first();

        if (!$branch || !$user) {
            $this->command->error('Please create a branch and user first');
            return;
        }

        // Create Sales Areas
        $areas = [
            [
                'name' => 'شمال الرياض',
                'code' => 'RYD-N',
                'city' => 'الرياض',
                'district' => 'الملز',
                'area_type' => 'urban',
                'priority_level' => 1,
                'travel_allowance' => 50.00,
                'estimated_visit_time' => 45,
            ],
            [
                'name' => 'جنوب الرياض',
                'code' => 'RYD-S',
                'city' => 'الرياض',
                'district' => 'العليا',
                'area_type' => 'urban',
                'priority_level' => 1,
                'travel_allowance' => 45.00,
                'estimated_visit_time' => 40,
            ],
            [
                'name' => 'شرق الرياض',
                'code' => 'RYD-E',
                'city' => 'الرياض',
                'district' => 'الروضة',
                'area_type' => 'suburban',
                'priority_level' => 2,
                'travel_allowance' => 60.00,
                'estimated_visit_time' => 50,
            ],
            [
                'name' => 'غرب الرياض',
                'code' => 'RYD-W',
                'city' => 'الرياض',
                'district' => 'المعذر',
                'area_type' => 'suburban',
                'priority_level' => 2,
                'travel_allowance' => 55.00,
                'estimated_visit_time' => 45,
            ],
            [
                'name' => 'جدة الشمالية',
                'code' => 'JED-N',
                'city' => 'جدة',
                'district' => 'الروضة',
                'area_type' => 'urban',
                'priority_level' => 1,
                'travel_allowance' => 70.00,
                'estimated_visit_time' => 50,
            ],
            [
                'name' => 'جدة الجنوبية',
                'code' => 'JED-S',
                'city' => 'جدة',
                'district' => 'الحمراء',
                'area_type' => 'urban',
                'priority_level' => 1,
                'travel_allowance' => 65.00,
                'estimated_visit_time' => 45,
            ],
        ];

        $createdAreas = [];
        foreach ($areas as $areaData) {
            $area = SalesArea::create([
                'name' => $areaData['name'],
                'code' => $areaData['code'],
                'description' => 'منطقة ' . $areaData['name'] . ' للمبيعات',
                'city' => $areaData['city'],
                'district' => $areaData['district'],
                'boundaries' => 'حدود منطقة ' . $areaData['name'],
                'area_type' => $areaData['area_type'],
                'priority_level' => $areaData['priority_level'],
                'travel_allowance' => $areaData['travel_allowance'],
                'estimated_visit_time' => $areaData['estimated_visit_time'],
                'special_instructions' => 'تعليمات خاصة لمنطقة ' . $areaData['name'],
                'requires_approval' => rand(0, 1),
                'is_active' => true,
                'region_manager_id' => $user->id,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
            $createdAreas[] = $area;
        }

        // Create Sales Representatives
        $representatives = [
            [
                'employee_code' => 'REP001',
                'name' => 'أحمد محمد السعيد',
                'phone' => '0501234567',
                'email' => '<EMAIL>',
                'national_id' => '1234567890',
                'commission_rate' => 5.00,
                'commission_type' => 'percentage',
                'vehicle_type' => 'سيدان',
                'vehicle_number' => 'أ ب ج 1234',
            ],
            [
                'employee_code' => 'REP002',
                'name' => 'محمد عبدالله الأحمد',
                'phone' => '0502345678',
                'email' => '<EMAIL>',
                'national_id' => '2345678901',
                'commission_rate' => 4.50,
                'commission_type' => 'percentage',
                'vehicle_type' => 'هاتشباك',
                'vehicle_number' => 'د هـ و 5678',
            ],
            [
                'employee_code' => 'REP003',
                'name' => 'عبدالرحمن صالح المطيري',
                'phone' => '0503456789',
                'email' => '<EMAIL>',
                'national_id' => '3456789012',
                'commission_rate' => 6.00,
                'commission_type' => 'percentage',
                'vehicle_type' => 'SUV',
                'vehicle_number' => 'ز ح ط 9012',
            ],
            [
                'employee_code' => 'REP004',
                'name' => 'خالد فهد العتيبي',
                'phone' => '0504567890',
                'email' => '<EMAIL>',
                'national_id' => '4567890123',
                'commission_rate' => 300.00,
                'commission_type' => 'fixed',
                'vehicle_type' => 'بيك أب',
                'vehicle_number' => 'ي ك ل 3456',
            ],
            [
                'employee_code' => 'REP005',
                'name' => 'سعد عبدالعزيز القحطاني',
                'phone' => '0505678901',
                'email' => '<EMAIL>',
                'national_id' => '5678901234',
                'commission_rate' => 5.50,
                'commission_type' => 'tiered',
                'vehicle_type' => 'سيدان',
                'vehicle_number' => 'م ن س 7890',
            ],
            [
                'employee_code' => 'REP006',
                'name' => 'فيصل ناصر الدوسري',
                'phone' => '0506789012',
                'email' => '<EMAIL>',
                'national_id' => '6789012345',
                'commission_rate' => 4.00,
                'commission_type' => 'percentage',
                'vehicle_type' => 'كوبيه',
                'vehicle_number' => 'ع ف ص 1357',
            ],
        ];

        $createdRepresentatives = [];
        foreach ($representatives as $index => $repData) {
            $commissionTiers = null;
            if ($repData['commission_type'] === 'tiered') {
                $commissionTiers = [
                    ['min_amount' => 0, 'max_amount' => 10000, 'rate' => 3.00],
                    ['min_amount' => 10001, 'max_amount' => 25000, 'rate' => 5.00],
                    ['min_amount' => 25001, 'max_amount' => null, 'rate' => 7.00],
                ];
            }

            $representative = SalesRepresentative::create([
                'employee_code' => $repData['employee_code'],
                'name' => $repData['name'],
                'phone' => $repData['phone'],
                'email' => $repData['email'],
                'national_id' => $repData['national_id'],
                'birth_date' => now()->subYears(rand(25, 45))->subDays(rand(1, 365)),
                'address' => 'العنوان التفصيلي للمندوب ' . $repData['name'],
                'hire_date' => now()->subMonths(rand(6, 36)),
                'base_salary' => rand(3000, 8000),
                'commission_rate' => $repData['commission_rate'],
                'target_amount' => rand(15000, 50000),
                'status' => collect(['active', 'active', 'active', 'inactive'])->random(),
                'commission_type' => $repData['commission_type'],
                'commission_tiers' => $commissionTiers,
                'vehicle_type' => $repData['vehicle_type'],
                'vehicle_number' => $repData['vehicle_number'],
                'license_number' => rand(100000000, 999999999),
                'license_expiry' => now()->addYears(rand(1, 3)),
                'notes' => 'ملاحظات خاصة بالمندوب ' . $repData['name'],
                'is_active' => true,
                'manager_id' => $user->id,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
            $createdRepresentatives[] = $representative;

            // Assign areas to representatives
            $assignedAreas = collect($createdAreas)->random(rand(1, 3));
            foreach ($assignedAreas as $area) {
                $representative->salesAreas()->attach($area->id, [
                    'assigned_date' => now()->subDays(rand(1, 30)),
                    'effective_from' => now()->subDays(rand(1, 30)),
                    'effective_to' => null,
                    'assignment_type' => 'primary',
                    'commission_override' => null,
                    'assignment_notes' => 'تعيين المندوب لمنطقة ' . $area->name,
                    'is_active' => true,
                    'assigned_by' => $user->id,
                ]);
            }
        }

        // Create Sales Routes
        foreach ($createdAreas as $area) {
            $routesCount = rand(2, 4);
            for ($i = 1; $i <= $routesCount; $i++) {
                $route = SalesRoute::create([
                    'name' => 'خط سير ' . $i . ' - ' . $area->name,
                    'code' => $area->code . '-R' . str_pad($i, 2, '0', STR_PAD_LEFT),
                    'description' => 'خط السير رقم ' . $i . ' في منطقة ' . $area->name,
                    'route_type' => collect(['daily', 'weekly', 'monthly'])->random(),
                    'schedule_days' => ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
                    'start_time' => '08:00',
                    'end_time' => '17:00',
                    'estimated_duration' => rand(360, 540), // 6-9 hours
                    'estimated_distance' => rand(50, 200),
                    'fuel_allowance' => rand(100, 300),
                    'max_customers' => rand(15, 30),
                    'priority_level' => rand(1, 3),
                    'special_instructions' => 'تعليمات خاصة لخط السير ' . $i,
                    'requires_vehicle' => true,
                    'is_active' => true,
                    'sales_representative_id' => collect($createdRepresentatives)->random()->id,
                    'sales_area_id' => $area->id,
                    'branch_id' => $branch->id,
                    'tenant_id' => $user->id,
                ]);
            }
        }

        // Create some customers for visits
        $customers = [];
        for ($i = 1; $i <= 20; $i++) {
            $customer = Customer::create([
                'name' => 'عميل رقم ' . $i,
                'email' => 'customer' . $i . '@example.com',
                'phone' => '050' . rand(1000000, 9999999),
                'address' => 'عنوان العميل رقم ' . $i . ' - ' . collect(['الرياض', 'جدة'])->random(),
                'tax_number' => rand(100000000000000, 999999999999999),
                'contact_person' => 'مسؤول العميل ' . $i,
                'credit_limit' => rand(5000, 50000),
                'is_active' => true,
                'notes' => 'ملاحظات العميل رقم ' . $i,
                'tenant_id' => $user->id,
            ]);
            $customers[] = $customer;
        }

        // Create Sales Visits
        $visitCounter = 1;
        foreach ($createdRepresentatives as $representative) {
            $visitsCount = rand(10, 25);
            for ($i = 1; $i <= $visitsCount; $i++) {
                $visitDate = now()->subDays(rand(1, 60));
                $customer = collect($customers)->random();

                SalesVisit::create([
                    'visit_code' => 'VST' . $visitDate->format('Ymd') . str_pad($visitCounter++, 4, '0', STR_PAD_LEFT),
                    'sales_representative_id' => $representative->id,
                    'customer_id' => $customer->id,
                    'sales_route_id' => null,
                    'visit_date' => $visitDate,
                    'planned_start_time' => '09:00',
                    'planned_end_time' => '10:00',
                    'actual_start_time' => rand(0, 1) ? '09:' . rand(10, 50) : null,
                    'actual_end_time' => rand(0, 1) ? '10:' . rand(10, 50) : null,
                    'visit_type' => collect(['scheduled', 'unscheduled', 'follow_up'])->random(),
                    'visit_purpose' => collect(['sales', 'collection', 'survey'])->random(),
                    'visit_status' => collect(['completed', 'completed', 'completed', 'cancelled'])->random(),
                    'visit_result' => collect(['successful', 'unsuccessful', 'partial'])->random(),
                    'visit_notes' => 'ملاحظات الزيارة رقم ' . $i,
                    'customer_feedback' => 'تعليقات العميل على الزيارة',
                    'distance_traveled' => rand(5, 50),
                    'duration_minutes' => rand(30, 120),
                    'order_amount' => rand(0, 1) ? rand(500, 5000) : 0,
                    'collection_amount' => rand(0, 1) ? rand(200, 2000) : 0,
                    'requires_follow_up' => rand(0, 1),
                    'next_visit_date' => rand(0, 1) ? now()->addDays(rand(7, 30)) : null,
                    'branch_id' => $branch->id,
                    'tenant_id' => $user->id,
                ]);
            }
        }

        // Create Sales Commissions
        $commissionCounter = 1;
        foreach ($createdRepresentatives as $representative) {
            $commissionsCount = rand(5, 15);
            for ($i = 1; $i <= $commissionsCount; $i++) {
                $transactionDate = now()->subDays(rand(1, 90));
                $baseAmount = rand(1000, 10000);
                $commissionRate = $representative->commission_rate;

                if ($representative->commission_type === 'percentage') {
                    $commissionAmount = ($baseAmount * $commissionRate) / 100;
                } elseif ($representative->commission_type === 'fixed') {
                    $commissionAmount = $commissionRate;
                } else {
                    $commissionAmount = ($baseAmount * 5) / 100; // Default for tiered
                }

                SalesCommission::create([
                    'commission_code' => 'COM' . $transactionDate->format('Ymd') . str_pad($commissionCounter++, 4, '0', STR_PAD_LEFT),
                    'sales_representative_id' => $representative->id,
                    'customer_id' => collect($customers)->random()->id,
                    'commission_type' => collect(['sales', 'collection', 'target_bonus'])->random(),
                    'transaction_date' => $transactionDate,
                    'base_amount' => $baseAmount,
                    'commission_rate' => $commissionRate,
                    'commission_amount' => $commissionAmount,
                    'additional_bonus' => rand(0, 1) ? rand(50, 200) : 0,
                    'deductions' => rand(0, 1) ? rand(10, 100) : 0,
                    'net_commission' => $commissionAmount,
                    'calculation_method' => $representative->commission_type,
                    'payment_status' => collect(['pending', 'approved', 'paid'])->random(),
                    'payment_date' => rand(0, 1) ? $transactionDate->addDays(rand(1, 30)) : null,
                    'commission_notes' => 'ملاحظات العمولة رقم ' . $i,
                    'is_recurring' => false,
                    'period_month' => $transactionDate->month,
                    'period_year' => $transactionDate->year,
                    'approved_by' => rand(0, 1) ? $user->id : null,
                    'approved_at' => rand(0, 1) ? $transactionDate->addDays(rand(1, 7)) : null,
                    'branch_id' => $branch->id,
                    'tenant_id' => $user->id,
                ]);
            }
        }

        // Create Sales Targets
        foreach ($createdRepresentatives as $representative) {
            // Current month target
            SalesTarget::create([
                'sales_representative_id' => $representative->id,
                'target_type' => 'monthly',
                'target_year' => now()->year,
                'target_month' => now()->month,
                'sales_target' => rand(20000, 60000),
                'visits_target' => rand(50, 100),
                'new_customers_target' => rand(5, 15),
                'collection_target' => rand(15000, 40000),
                'achieved_sales' => rand(10000, 50000),
                'achieved_visits' => rand(30, 80),
                'achieved_new_customers' => rand(2, 12),
                'achieved_collection' => rand(8000, 35000),
                'bonus_amount' => rand(0, 1000),
                'status' => 'active',
                'target_notes' => 'هدف شهر ' . now()->format('Y-m'),
                'start_date' => now()->startOfMonth(),
                'end_date' => now()->endOfMonth(),
                'set_by' => $user->id,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);

            // Previous month target
            $previousMonth = now()->subMonth();
            SalesTarget::create([
                'sales_representative_id' => $representative->id,
                'target_type' => 'monthly',
                'target_year' => $previousMonth->year,
                'target_month' => $previousMonth->month,
                'sales_target' => rand(20000, 60000),
                'visits_target' => rand(50, 100),
                'new_customers_target' => rand(5, 15),
                'collection_target' => rand(15000, 40000),
                'achieved_sales' => rand(15000, 65000),
                'achieved_visits' => rand(40, 110),
                'achieved_new_customers' => rand(3, 18),
                'achieved_collection' => rand(12000, 45000),
                'bonus_amount' => rand(200, 1500),
                'status' => 'completed',
                'target_notes' => 'هدف شهر ' . $previousMonth->format('Y-m'),
                'start_date' => $previousMonth->startOfMonth(),
                'end_date' => $previousMonth->endOfMonth(),
                'set_by' => $user->id,
                'branch_id' => $branch->id,
                'tenant_id' => $user->id,
            ]);
        }

        // Create Sales Expenses
        $expenseCounter = 1;
        foreach ($createdRepresentatives as $representative) {
            $expensesCount = rand(8, 20);
            for ($i = 1; $i <= $expensesCount; $i++) {
                $expenseDate = now()->subDays(rand(1, 60));

                SalesExpense::create([
                    'expense_code' => 'EXP' . $expenseDate->format('Ymd') . str_pad($expenseCounter++, 4, '0', STR_PAD_LEFT),
                    'sales_representative_id' => $representative->id,
                    'expense_type' => collect(['fuel', 'meals', 'accommodation', 'transportation', 'communication'])->random(),
                    'expense_date' => $expenseDate,
                    'amount' => rand(50, 500),
                    'currency' => 'SAR',
                    'description' => 'وصف المصروف رقم ' . $i,
                    'receipt_number' => 'REC' . rand(100000, 999999),
                    'vendor_name' => 'المورد رقم ' . rand(1, 10),
                    'approval_status' => collect(['pending', 'approved', 'paid'])->random(),
                    'approval_notes' => 'ملاحظات الموافقة',
                    'approved_by' => rand(0, 1) ? $user->id : null,
                    'approved_at' => rand(0, 1) ? $expenseDate->addDays(rand(1, 5)) : null,
                    'payment_date' => rand(0, 1) ? $expenseDate->addDays(rand(5, 15)) : null,
                    'is_reimbursable' => true,
                    'reimbursed_amount' => rand(0, 1) ? rand(50, 500) : 0,
                    'branch_id' => $branch->id,
                    'tenant_id' => $user->id,
                ]);
            }
        }

        $this->command->info('Sales representatives data seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . count($createdAreas) . ' sales areas');
        $this->command->info('- ' . count($createdRepresentatives) . ' sales representatives');
        $this->command->info('- Multiple routes, visits, commissions, targets, and expenses');
    }
}
