@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Configure ZATCA E-Invoicing Integration</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ route("admin.integrations.zatca.update") }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method("PUT") {{-- Or POST --}}

        <div class="card mb-3">
            <div class="card-header">Onboarding & Credentials</div>
            <div class="card-body">
                <div class="form-group">
                    <label for="zatca_phase">Compliance Phase</label>
                    <select name="zatca_phase" id="zatca_phase" class="form-control">
                        <option value="phase_1" {{-- old("zatca_phase", $settings->zatca_phase ?? "") == "phase_1" ? "selected" : "" --}}>Phase 1: Generation & Storage</option>
                        <option value="phase_2" {{-- old("zatca_phase", $settings->zatca_phase ?? "phase_2") == "phase_2" ? "selected" : "" --}}>Phase 2: Integration (Clearance/Reporting)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="onboarding_status">Onboarding Status with ZATCA</label>
                    <select name="onboarding_status" id="onboarding_status" class="form-control">
                        <option value="pending" {{-- old("onboarding_status", $settings->zatca_onboarding_status ?? "") == "pending" ? "selected" : "" --}}>Pending</option>
                        <option value="onboarded" {{-- old("onboarding_status", $settings->zatca_onboarding_status ?? "onboarded") == "onboarded" ? "selected" : "" --}}>Onboarded (CSID Received)</option>
                        <option value="failed" {{-- old("onboarding_status", $settings->zatca_onboarding_status ?? "") == "failed" ? "selected" : "" --}}>Failed</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="zatca_environment">ZATCA Environment</label>
                    <select name="zatca_environment" id="zatca_environment" class="form-control">
                        <option value="simulation" {{-- old("zatca_environment", $settings->zatca_environment ?? "simulation") == "simulation" ? "selected" : "" --}}>Simulation (Test)</option>
                        <option value="production" {{-- old("zatca_environment", $settings->zatca_environment ?? "") == "production" ? "selected" : "" --}}>Production (Live)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="csid_production">Production CSID (Cryptographic Stamp ID)</label>
                    <textarea name="csid_production" id="csid_production" class="form-control" rows="3">{{-- old("csid_production", $settings->csid_production ?? "") --}}</textarea>
                    <small class="form-text text-muted">The production cryptographic stamp ID provided by ZATCA after successful onboarding.</small>
                </div>

                <div class="form-group">
                    <label for="csid_simulation">Simulation CSID (Optional)</label>
                    <textarea name="csid_simulation" id="csid_simulation" class="form-control" rows="3">{{-- old("csid_simulation", $settings->csid_simulation ?? "") --}}</textarea>
                    <small class="form-text text-muted">The simulation/testing cryptographic stamp ID if provided by ZATCA.</small>
                </div>

                <div class="form-group">
                    <label for="compliance_csid_production">Production Compliance CSID (for compliance checks)</label>
                    <input type="text" name="compliance_csid_production" id="compliance_csid_production" class="form-control" value="{{-- old("compliance_csid_production", $settings->compliance_csid_production ?? "") --}}">
                </div>
                 <div class="form-group">
                    <label for="compliance_csid_simulation">Simulation Compliance CSID (for compliance checks)</label>
                    <input type="text" name="compliance_csid_simulation" id="compliance_csid_simulation" class="form-control" value="{{-- old("compliance_csid_simulation", $settings->compliance_csid_simulation ?? "") --}}">
                </div>

                <div class="form-group">
                    <label for="secret_production">Production API Secret</label>
                    <input type="password" name="secret_production" id="secret_production" class="form-control" value="{{-- old("secret_production", $settings->secret_production ?? "") --}}">
                </div>
                 <div class="form-group">
                    <label for="secret_simulation">Simulation API Secret</label>
                    <input type="password" name="secret_simulation" id="secret_simulation" class="form-control" value="{{-- old("secret_simulation", $settings->secret_simulation ?? "") --}}">
                </div>

            </div>
        </div>

        <div class="card mb-3">
            <div class="card-header">Solution & SDK Details</div>
            <div class="card-body">
                <div class="form-group">
                    <label for="sdk_provider">E-Invoicing Solution Provider (if any)</label>
                    <input type="text" name="sdk_provider" id="sdk_provider" class="form-control" value="{{-- old("sdk_provider", $settings->sdk_provider ?? "Internal Solution") --}}">
                </div>
                <div class="form-group">
                    <label for="sdk_version">Solution/SDK Version</label>
                    <input type="text" name="sdk_version" id="sdk_version" class="form-control" value="{{-- old("sdk_version", $settings->sdk_version ?? "1.0.0") --}}">
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-success mt-3">Save ZATCA Configuration</button>
        <a href="{{ route("admin.integrations.zatca.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

