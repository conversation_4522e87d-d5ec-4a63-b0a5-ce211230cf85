<?php

namespace App\Models\Modules\Sales;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\TenantScoped;

class Customer extends Model
{
    use HasFactory, TenantScoped;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'tax_number',
        'contact_person',
        'credit_limit',
        'is_active',
        'notes',
        'tenant_id', // إضافة حقل معرف المستأجر
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'credit_limit' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the formatted credit limit.
     *
     * @return string
     */
    public function getFormattedCreditLimitAttribute()
    {
        return number_format($this->credit_limit, 2);
    }

    /**
     * Get the status text.
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'نشط' : 'غير نشط';
    }

    /**
     * Scope a query to only include active customers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the sales invoices for the customer.
     */
    public function salesInvoices()
    {
        return $this->hasMany(SalesInvoice::class);
    }

    /**
     * Get the sales returns for the customer.
     */
    public function salesReturns()
    {
        return $this->hasMany(SalesReturn::class);
    }

    /**
     * Get the quotations for the customer.
     */
    public function quotations()
    {
        return $this->hasMany(Quotation::class);
    }

    /**
     * Get the total sales amount for the customer.
     *
     * @return float
     */
    public function getTotalSalesAttribute()
    {
        return $this->salesInvoices()->sum('total_amount');
    }

    /**
     * Get the total due amount for the customer.
     *
     * @return float
     */
    public function getTotalDueAttribute()
    {
        return $this->salesInvoices()->sum('due_amount');
    }
}
