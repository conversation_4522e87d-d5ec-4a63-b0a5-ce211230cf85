<?php

namespace App\Http\Controllers\Modules\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FinancialReportController extends Controller
{
    /**
     * Display financial reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.reports.financial.index');
    }

    /**
     * Generate trial balance report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function trialBalance(Request $request)
    {
        return view('admin.reports.financial.trial_balance');
    }

    /**
     * Generate balance sheet report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function balanceSheet(Request $request)
    {
        return view('admin.reports.financial.balance_sheet');
    }

    /**
     * Generate income statement report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function incomeStatement(Request $request)
    {
        return view('admin.reports.financial.income_statement');
    }

    /**
     * Generate cash flow statement report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function cashFlow(Request $request)
    {
        return view('admin.reports.financial.cash_flow');
    }

    /**
     * Generate general ledger report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function generalLedger(Request $request)
    {
        return view('admin.reports.financial.general_ledger');
    }

    /**
     * Generate accounts receivable report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function accountsReceivable(Request $request)
    {
        return view('admin.reports.financial.accounts_receivable');
    }

    /**
     * Generate accounts payable report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function accountsPayable(Request $request)
    {
        return view('admin.reports.financial.accounts_payable');
    }
}
