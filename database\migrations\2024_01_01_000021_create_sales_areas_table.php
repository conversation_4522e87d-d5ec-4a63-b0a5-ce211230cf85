<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_areas', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المنطقة
            $table->string('code')->unique(); // كود المنطقة
            $table->text('description')->nullable(); // وصف المنطقة
            $table->string('city'); // المدينة
            $table->string('district')->nullable(); // الحي
            $table->text('boundaries')->nullable(); // حدود المنطقة
            $table->json('coordinates')->nullable(); // إحداثيات المنطقة
            $table->string('postal_code')->nullable(); // الرمز البريدي
            $table->enum('area_type', ['urban', 'suburban', 'rural'])->default('urban'); // نوع المنطقة
            $table->integer('priority_level')->default(1); // مستوى الأولوية
            $table->decimal('travel_allowance', 8, 2)->default(0); // بدل السفر
            $table->integer('estimated_visit_time')->default(30); // الوقت المقدر للزيارة (بالدقائق)
            $table->text('special_instructions')->nullable(); // تعليمات خاصة
            $table->boolean('requires_approval')->default(false); // يتطلب موافقة
            $table->boolean('is_active')->default(true);
            $table->foreignId('region_manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_areas');
    }
};
