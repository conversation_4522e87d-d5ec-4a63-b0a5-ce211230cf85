/* ===== TABS STYLES ===== */
.nav-tabs {
    border-bottom: 1px solid #e3e6f0;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
    background: transparent;
}

.nav-tabs .nav-link:hover {
    color: #4e73df;
    border-bottom-color: rgba(78, 115, 223, 0.3);
}

.nav-tabs .nav-link.active {
    color: #4e73df;
    background-color: transparent;
    border-bottom: 3px solid #4e73df;
    font-weight: 600;
}

.tab-content {
    padding: 10px 0;
}

.tab-pane {
    animation: fadeIn 0.3s ease-out forwards;
}

/* ===== MODERN TREE VIEW STYLES ===== */
.modern-accounts-tree {
    margin-top: 1rem;
}

.account-type-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    transition: all 0.3s ease;
}

.account-type-card:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.account-type-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

.account-type-header:hover {
    background-color: #f8f9fc;
}

.account-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    color: white;
    font-size: 1.2rem;
}

.account-type-info {
    display: flex;
    flex-direction: column;
}

.account-type-name {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.account-type-header .toggle-icon {
    margin-right: 5px;
    transition: transform 0.3s;
    color: #4e73df;
    font-size: 1.1rem;
}

.account-type-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.account-type-balance {
    font-weight: 600;
    text-align: left;
}

.account-type-balance .balance-amount {
    display: block;
    font-size: 1.1rem;
}

.account-type-balance small {
    font-size: 0.75rem;
    opacity: 0.8;
}

.account-type-content {
    padding: 0;
    background-color: #fff;
}

/* Modern Tree structure */
.modern-tree-container {
    padding: 10px 0;
}

.modern-tree {
    list-style: none;
    padding: 0;
    margin: 0;
}

.modern-tree-node {
    position: relative;
    padding: 0;
}

.modern-tree-node-content {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    transition: all 0.2s;
    border-radius: 6px;
    margin: 4px 10px;
    background-color: #f8f9fc;
}

.modern-tree-node-content:hover {
    background-color: #eaecf4;
}

.modern-tree-node-toggle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #4e73df;
    transition: transform 0.3s;
    margin-left: 5px;
}

.modern-tree-node-toggle.collapsed i {
    transform: rotate(-90deg);
}

.modern-tree-node-icon {
    margin: 0 5px;
    width: 20px;
    text-align: center;
}

.modern-tree-node-details {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
}

.modern-tree-node-text {
    display: flex;
    align-items: center;
}

.modern-tree-node-code {
    font-family: monospace;
    color: #858796;
    margin-left: 10px;
    font-size: 0.875rem;
    background-color: rgba(0,0,0,0.05);
    padding: 2px 6px;
    border-radius: 4px;
}

.modern-tree-node-name {
    font-weight: 500;
}

.modern-tree-node-balance {
    font-weight: 600;
    color: #28a745;
    margin-left: 15px;
    text-align: left;
}

.modern-tree-node-balance.negative {
    color: #dc3545;
}

.modern-tree-node-balance small {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-right: 3px;
}

.modern-tree-node-actions {
    display: none;
    gap: 5px;
}

.modern-tree-node-content:hover .modern-tree-node-actions {
    display: flex;
}

.modern-tree-children {
    padding-right: 20px;
    position: relative;
}

.modern-tree-children::before {
    content: '';
    position: absolute;
    top: 0;
    right: 9px;
    bottom: 0;
    width: 2px;
    background-color: #e3e6f0;
}

.parent-node {
    background-color: #f1f3f9;
    border-right: 3px solid #4e73df;
}

/* Type background colors */
.type-bg-assets { background-color: #4e73df; }
.type-bg-liabilities { background-color: #1cc88a; }
.type-bg-equity { background-color: #f6c23e; }
.type-bg-revenue { background-color: #36b9cc; }
.type-bg-expenses { background-color: #e74a3b; }

/* ===== MODERN TABLE VIEW STYLES ===== */
.modern-accounts-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.modern-accounts-table thead th {
    background-color: #f8f9fc;
    color: #5a5c69;
    font-weight: 600;
    text-align: right;
    padding: 15px;
    border-bottom: 2px solid #e3e6f0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.modern-accounts-table tbody tr {
    transition: all 0.2s;
    border-bottom: 1px solid #f0f0f0;
}

.modern-accounts-table tbody tr:hover {
    background-color: #f8f9fc;
}

.modern-accounts-table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 252, 0.5);
}

.modern-accounts-table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

.account-row.level-0 {
    font-weight: 600;
}

.account-row.level-1 {
    background-color: rgba(248, 249, 252, 0.7);
}

.account-row.level-2 {
    background-color: rgba(248, 249, 252, 0.9);
}

.account-type-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.account-indent {
    display: flex;
    align-items: center;
}

.type-badge-assets {
    background-color: #eaecf7;
    color: #4e73df;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.type-badge-liabilities {
    background-color: #e6f8f1;
    color: #1cc88a;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.type-badge-equity {
    background-color: #fef6e6;
    color: #f6c23e;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.type-badge-revenue {
    background-color: #e8f7fa;
    color: #36b9cc;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.type-badge-expenses {
    background-color: #fbecea;
    color: #e74a3b;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

.modern-tree-node {
    animation: fadeIn 0.3s ease-out forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-tree-node-details {
        flex-direction: column;
        align-items: flex-start;
    }

    .modern-tree-node-balance {
        margin-top: 5px;
        margin-right: 30px;
    }

    .modern-accounts-table {
        display: block;
        overflow-x: auto;
    }
}
