@extends("layouts.admin")

@section("title", isset($setting) ? "تعديل إعدادات " . ucfirst($setting->provider_name) : "إضافة إعدادات مزود دفع آجل جديد")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($setting) ? "تعديل إعدادات " . ucfirst($setting->provider_name) : "إضافة إعدادات مزود دفع آجل جديد" }}</h3>
                </div>
                <div class="card-body">
                    <form action="{{ isset($setting) ? route("admin.bnpl_settings.update", $setting->id) : route("admin.bnpl_settings.store") }}" method="POST">
                        @csrf
                        @if (isset($setting))
                            @method("PUT")
                        @endif

                        <div class="form-group">
                            <label for="provider_name">اسم المزود <span class="text-danger">*</span></label>
                            <select name="provider_name" id="provider_name" class="form-control @error("provider_name") is-invalid @enderror">
                                <option value="">-- اختر المزود --</option>
                                <option value="tabby" {{ (isset($setting) && $setting->provider_name == "tabby") || old("provider_name") == "tabby" ? "selected" : "" }}>تابي (Tabby)</option>
                                <option value="tamara" {{ (isset($setting) && $setting->provider_name == "tamara") || old("provider_name") == "tamara" ? "selected" : "" }}>تمارا (Tamara)</option>
                            </select>
                            @error("provider_name")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="api_key">مفتاح API <span class="text-danger">*</span></label>
                            <input type="text" name="api_key" id="api_key" class="form-control @error("api_key") is-invalid @enderror" value="{{ isset($setting) ? $setting->api_key : old("api_key") }}">
                            @error("api_key")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="public_key">المفتاح العام (Public Key) <span class="text-muted">(اختياري لبعض المزودين)</span></label>
                            <input type="text" name="public_key" id="public_key" class="form-control @error("public_key") is-invalid @enderror" value="{{ isset($setting) ? $setting->public_key : old("public_key") }}">
                            @error("public_key")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="notification_token">رمز Webhook/Notification <span class="text-danger">*</span></label>
                            <input type="text" name="notification_token" id="notification_token" class="form-control @error("notification_token") is-invalid @enderror" value="{{ isset($setting) ? $setting->notification_token : old("notification_token") }}">
                            @error("notification_token")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="merchant_code">رمز التاجر (Merchant Code) <span class="text-muted">(خاص بتابي)</span></label>
                            <input type="text" name="merchant_code" id="merchant_code" class="form-control @error("merchant_code") is-invalid @enderror" value="{{ isset($setting) ? $setting->merchant_code : old("merchant_code") }}">
                            @error("merchant_code")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="environment">البيئة <span class="text-danger">*</span></label>
                            <select name="environment" id="environment" class="form-control @error("environment") is-invalid @enderror">
                                <option value="sandbox" {{ (isset($setting) && $setting->environment == "sandbox") || old("environment") == "sandbox" ? "selected" : "" }}>تجريبي (Sandbox)</option>
                                <option value="production" {{ (isset($setting) && $setting->environment == "production") || old("environment") == "production" ? "selected" : "" }}>إنتاج (Production)</option>
                            </select>
                            @error("environment")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" name="is_active" class="custom-control-input" id="is_active" value="1" {{ (isset($setting) && $setting->is_active) || old("is_active") ? "checked" : "" }}>
                                <label class="custom-control-label" for="is_active">مفعل</label>
                            </div>
                            @error("is_active")
                                <span class="invalid-feedback d-block" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">{{ isset($setting) ? "تحديث الإعدادات" : "حفظ الإعدادات" }}</button>
                            <a href="{{ route("admin.bnpl_settings.index") }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

