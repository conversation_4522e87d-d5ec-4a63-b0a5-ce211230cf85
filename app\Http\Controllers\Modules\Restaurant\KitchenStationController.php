<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\KitchenStation;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class KitchenStationController extends Controller
{
    public function index()
    {
        $kitchenStations = KitchenStation::with(['branch', 'menuItems', 'printers'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.restaurant.kitchen-stations.index', compact('kitchenStations'));
    }

    public function create()
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.kitchen-stations.form', compact('branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');

        KitchenStation::create($validatedData);

        return redirect()->route('admin.restaurant.kitchen-stations.index')
            ->with('success', __('Kitchen station created successfully.'));
    }

    public function show(KitchenStation $kitchenStation)
    {
        $kitchenStation->load(['branch', 'menuItems', 'printers', 'orderItems' => function($query) {
            $query->with(['order', 'menuItem'])->latest()->take(20);
        }]);

        return view('admin.restaurant.kitchen-stations.show', compact('kitchenStation'));
    }

    public function edit(KitchenStation $kitchenStation)
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.kitchen-stations.form', compact('kitchenStation', 'branches'));
    }

    public function update(Request $request, KitchenStation $kitchenStation)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_active'] = $request->has('is_active');

        $kitchenStation->update($validatedData);

        return redirect()->route('admin.restaurant.kitchen-stations.index')
            ->with('success', __('Kitchen station updated successfully.'));
    }

    public function destroy(KitchenStation $kitchenStation)
    {
        // Check if station has menu items
        if ($kitchenStation->menuItems()->count() > 0) {
            return redirect()->route('admin.restaurant.kitchen-stations.index')
                ->with('error', __('Cannot delete kitchen station that has menu items assigned to it.'));
        }

        // Check if station has printers
        if ($kitchenStation->printers()->count() > 0) {
            return redirect()->route('admin.restaurant.kitchen-stations.index')
                ->with('error', __('Cannot delete kitchen station that has printers assigned to it.'));
        }

        $kitchenStation->delete();

        return redirect()->route('admin.restaurant.kitchen-stations.index')
            ->with('success', __('Kitchen station deleted successfully.'));
    }
}
