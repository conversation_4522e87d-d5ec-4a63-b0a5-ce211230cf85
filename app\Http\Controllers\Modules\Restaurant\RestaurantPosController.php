<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\MenuCategory;
use App\Models\Modules\Restaurant\MenuItem;
use App\Models\Modules\Restaurant\RestaurantTable;
use App\Models\Modules\Restaurant\RestaurantOrder;
use App\Models\Modules\Restaurant\RestaurantOrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RestaurantPosController extends Controller
{
    public function index()
    {
        return view('admin.restaurant.pos.index');
    }

    public function getMenu()
    {
        $categories = MenuCategory::with(['availableMenuItems' => function($query) {
            $query->with(['modifiers.activeOptions']);
        }])
        ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
        ->available()
        ->ordered()
        ->get();

        return response()->json([
            'success' => true,
            'data' => $categories->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'color' => $category->color,
                    'image' => $category->image ? asset('storage/' . $category->image) : null,
                    'items' => $category->availableMenuItems->map(function($item) {
                        return [
                            'id' => $item->id,
                            'name' => $item->name,
                            'description' => $item->description,
                            'price' => $item->price,
                            'final_price' => $item->final_price,
                            'image' => $item->image ? asset('storage/' . $item->image) : null,
                            'preparation_time' => $item->preparation_time,
                            'is_spicy' => $item->is_spicy,
                            'is_vegetarian' => $item->is_vegetarian,
                            'is_vegan' => $item->is_vegan,
                            'is_gluten_free' => $item->is_gluten_free,
                            'modifiers' => $item->modifiers->map(function($modifier) {
                                return [
                                    'id' => $modifier->id,
                                    'name' => $modifier->name,
                                    'type' => $modifier->type,
                                    'is_required' => $modifier->is_required,
                                    'min_selections' => $modifier->min_selections,
                                    'max_selections' => $modifier->max_selections,
                                    'options' => $modifier->activeOptions->map(function($option) {
                                        return [
                                            'id' => $option->id,
                                            'name' => $option->name,
                                            'price_adjustment' => $option->price_adjustment,
                                            'is_default' => $option->is_default,
                                        ];
                                    })
                                ];
                            })
                        ];
                    })
                ];
            })
        ]);
    }

    public function getTables()
    {
        $tables = RestaurantTable::with(['area', 'currentOrder'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('number')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $tables->map(function($table) {
                return [
                    'id' => $table->id,
                    'name' => $table->name,
                    'number' => $table->number,
                    'capacity' => $table->capacity,
                    'status' => $table->status,
                    'status_color' => $table->status_color,
                    'area_name' => $table->area->name ?? null,
                    'area_color' => $table->area->color ?? null,
                    'current_order' => $table->currentOrder ? [
                        'id' => $table->currentOrder->id,
                        'order_number' => $table->currentOrder->order_number,
                        'total_amount' => $table->currentOrder->total_amount,
                        'status' => $table->currentOrder->status,
                    ] : null
                ];
            })
        ]);
    }

    public function createOrder(Request $request)
    {
        $validatedData = $request->validate([
            'type' => 'required|in:dine_in,takeaway,delivery',
            'table_id' => 'nullable|exists:restaurant_tables,id',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'delivery_address' => 'nullable|string',
            'guests_count' => 'nullable|integer|min:1|max:50',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.notes' => 'nullable|string',
            'items.*.modifiers' => 'nullable|array',
            'items.*.modifiers.*.modifier_id' => 'required|exists:menu_item_modifiers,id',
            'items.*.modifiers.*.option_id' => 'required|exists:menu_item_modifier_options,id',
        ]);

        try {
            DB::beginTransaction();

            // Create order
            $orderData = [
                'order_number' => RestaurantOrder::generateOrderNumber(),
                'type' => $validatedData['type'],
                'table_id' => $validatedData['table_id'] ?? null,
                'customer_name' => $validatedData['customer_name'] ?? null,
                'customer_phone' => $validatedData['customer_phone'] ?? null,
                'delivery_address' => $validatedData['delivery_address'] ?? null,
                'guests_count' => $validatedData['guests_count'] ?? 1,
                'notes' => $validatedData['notes'] ?? null,
                'status' => 'pending',
                'order_time' => now(),
                'waiter_id' => Auth::id(),
                'cashier_id' => Auth::id(),
                'tenant_id' => Auth::user()->tenant_id ?? Auth::id(),
                'branch_id' => Auth::user()->branch_id ?? 1, // Default branch
            ];

            $order = RestaurantOrder::create($orderData);

            // Create order items
            foreach ($validatedData['items'] as $itemData) {
                $menuItem = MenuItem::find($itemData['menu_item_id']);
                
                $orderItem = RestaurantOrderItem::create([
                    'order_id' => $order->id,
                    'menu_item_id' => $menuItem->id,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $menuItem->price,
                    'total_price' => $menuItem->price * $itemData['quantity'],
                    'notes' => $itemData['notes'] ?? null,
                    'status' => 'pending',
                    'kitchen_station_id' => $menuItem->kitchen_station_id,
                ]);

                // Add modifiers if any
                if (isset($itemData['modifiers'])) {
                    foreach ($itemData['modifiers'] as $modifierData) {
                        $option = \App\Models\Modules\Restaurant\MenuItemModifierOption::find($modifierData['option_id']);
                        
                        \App\Models\Modules\Restaurant\RestaurantOrderItemModifier::create([
                            'order_item_id' => $orderItem->id,
                            'modifier_id' => $modifierData['modifier_id'],
                            'modifier_option_id' => $option->id,
                            'price_adjustment' => $option->price_adjustment,
                        ]);
                    }
                    
                    // Recalculate item total with modifiers
                    $orderItem->calculateTotalPrice();
                }
            }

            // Calculate order totals
            $order->calculateTotals();

            // Update table status if dine-in
            if ($order->type === 'dine_in' && $order->table_id) {
                $order->table->update(['status' => 'occupied']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Order created successfully.'),
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_amount' => $order->total_amount,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => __('Error creating order: ') . $e->getMessage()
            ], 500);
        }
    }
}
