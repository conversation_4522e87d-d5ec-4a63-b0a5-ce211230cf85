<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\RestaurantOrderItem;
use App\Models\Modules\Restaurant\KitchenStation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class KitchenDisplayController extends Controller
{
    public function index()
    {
        $kitchenStations = KitchenStation::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        return view('admin.restaurant.kitchen-display.index', compact('kitchenStations'));
    }

    public function getOrders(Request $request)
    {
        $stationId = $request->get('station_id');
        
        $query = RestaurantOrderItem::with(['order.table', 'menuItem', 'modifiers.modifierOption'])
            ->whereHas('order', function($q) {
                $q->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                  ->whereIn('status', ['confirmed', 'preparing']);
            })
            ->whereIn('status', ['pending', 'preparing']);

        if ($stationId) {
            $query->where('kitchen_station_id', $stationId);
        }

        $orderItems = $query->orderBy('created_at')->get();

        return response()->json([
            'success' => true,
            'data' => $orderItems->map(function($item) {
                return [
                    'id' => $item->id,
                    'order_number' => $item->order->order_number,
                    'table_name' => $item->order->table->name ?? 'Takeaway',
                    'menu_item_name' => $item->menuItem->name,
                    'quantity' => $item->quantity,
                    'status' => $item->status,
                    'status_color' => $item->status_color,
                    'notes' => $item->notes,
                    'special_instructions' => $item->special_instructions,
                    'modifiers' => $item->modifiers->map(function($mod) {
                        return $mod->modifierOption->name ?? '';
                    })->implode(', '),
                    'preparation_time' => $item->preparation_time,
                    'created_at' => $item->created_at->format('H:i'),
                ];
            })
        ]);
    }

    public function updateItemStatus(Request $request, RestaurantOrderItem $item)
    {
        $request->validate([
            'status' => 'required|in:pending,preparing,ready,served,cancelled'
        ]);

        switch ($request->status) {
            case 'preparing':
                $item->startPreparation();
                break;
            case 'ready':
                $item->markAsReady();
                break;
            case 'served':
                $item->markAsServed();
                break;
            default:
                $item->update(['status' => $request->status]);
        }

        // Check if all order items are ready/served
        $order = $item->order;
        $allItemsReady = $order->orderItems()->whereNotIn('status', ['ready', 'served'])->count() === 0;
        
        if ($allItemsReady && $order->status !== 'ready') {
            $order->update(['status' => 'ready', 'ready_time' => now()]);
        }

        return response()->json([
            'success' => true,
            'message' => __('Item status updated successfully.'),
            'status' => $item->status,
            'status_color' => $item->status_color
        ]);
    }
}
