<?php

namespace App\Services;

use App\Models\Modules\WhatsAppIntegration\WhatsAppConfiguration;
use App\Models\Modules\WhatsAppIntegration\WhatsAppMessageTemplate;
use App\Models\Modules\WhatsAppIntegration\WhatsAppMessageLog;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\App; // Added for environment check

class WhatsAppService
{
    protected ?WhatsAppConfiguration $activeConfiguration = null; // Initialize as null
    protected string $graphApiBaseUrl = "https://graph.facebook.com/"; // Default, can be overridden by config
    protected string $defaultApiVersion = "v19.0"; // Default, can be overridden by config

    public function __construct()
    {
        // Only try to load configuration if not in testing environment or if explicitly allowed
        if (!App::environment('testing') || config('whatsapp.force_db_init_in_testing', false)) {
            try {
                $this->activeConfiguration = WhatsAppConfiguration::where("is_active", true)->first();
                if ($this->activeConfiguration && !empty($this->activeConfiguration->api_version)) {
                    $this->defaultApiVersion = $this->activeConfiguration->api_version;
                }
            } catch (\Illuminate\Database\QueryException $e) {
                // Log the error if DB is not available but we are not in a context where it should be skipped
                if (!App::environment('testing')) { // Avoid logging during normal testing runs where DB might not be set up for this service
                    Log::warning('WhatsAppService: Could not connect to database or whatsapp_configurations table missing during construction.', ['error' => $e->getMessage()]);
                }
                // Continue without active configuration if DB is not ready
                $this->activeConfiguration = null;
            }
        }
    }

    /**
     * Sends a message using a pre-approved template via WhatsApp Cloud API.
     *
     * @param string $templateName The machine-name of the template.
     * @param User $recipientUser The user model of the recipient.
     * @param array $templateParameters Associative array of parameters for the template.
     *                                  For text-based components: ["1" => "value1", "2" => "value2"]
     *                                  For header (IMAGE/VIDEO/DOCUMENT): ["HEADER_IMAGE_URL" => "url"]
     *                                  For buttons (URL): ["BUTTON_SUBTYPE_url_0" => "dynamic_url_part"]
     * @param string $languageCode The language code for the template (e.g., "en_US", "ar").
     * @param mixed|null $relatedModel Optional related Eloquent model for logging.
     * @return array Result of the send attempt.
     */
    public function sendTemplateMessage(string $templateName, User $recipientUser, array $templateParameters, string $languageCode = "en_US", $relatedModel = null): array
    {
        if (!$this->activeConfiguration) {
            Log::error("WhatsApp Service: No active WhatsApp configuration found or service not initialized properly (e.g. DB issue or testing environment).");
            return ["success" => false, "message" => "No active WhatsApp configuration or service not initialized."];
        }

        if (empty($this->activeConfiguration->phone_number_id) || empty($this->activeConfiguration->access_token)) {
            Log::error("WhatsApp Service: Active configuration is missing Phone Number ID or Access Token.");
            return ["success" => false, "message" => "Active WhatsApp configuration is incomplete (missing Phone Number ID or Access Token)."];
        }

        if (!$recipientUser->phone_number) {
            Log::error("WhatsApp Service: Recipient user ID {" . $recipientUser->id . "} does not have a phone number.");
            return ["success" => false, "message" => "Recipient user does not have a phone number."];
        }

        $dbTemplate = null;
        try {
             $dbTemplate = WhatsAppMessageTemplate::where("name", $templateName)
                                            ->where("language", $languageCode)
                                            ->where("status", "APPROVED")
                                            ->first();
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error("WhatsAppService: Database error when fetching template '{$templateName}'.", ['error' => $e->getMessage()]);
            return ["success" => false, "message" => "Database error fetching template."];
        }

        if (!$dbTemplate) {
            Log::error("WhatsApp Service: Approved template '{$templateName}' for language '{$languageCode}' not found in DB.");
            return ["success" => false, "message" => "Approved template '{$templateName}' for language '{$languageCode}' not found or not approved."];
        }
        
        $components = [];
        $bodyParameters = [];
        $headerParameters = []; // Not used directly in this simplified loop, but good for structure
        $buttonParameters = []; // Not used directly in this simplified loop

        foreach ($templateParameters as $key => $value) {
            if (is_numeric($key)) { 
                $bodyParameters[] = ["type" => "text", "text" => (string)$value];
            } elseif (str_starts_with(strtoupper($key), "HEADER_IMAGE_URL")) {
                $components[] = ["type" => "header", "parameters" => [["type" => "image", "image" => ["link" => (string)$value]]]];
            } elseif (str_starts_with(strtoupper($key), "HEADER_DOCUMENT_URL")) {
                $components[] = ["type" => "header", "parameters" => [["type" => "document", "document" => ["link" => (string)$value, "filename" => $templateParameters["HEADER_DOCUMENT_FILENAME"] ?? basename((string)$value)]]]];
            } elseif (str_starts_with(strtoupper($key), "HEADER_VIDEO_URL")) {
                $components[] = ["type" => "header", "parameters" => [["type" => "video", "video" => ["link" => (string)$value]]]];
            } elseif (str_starts_with(strtoupper($key), "BUTTON_SUBTYPE_URL_")) { // e.g. BUTTON_SUBTYPE_URL_0
                $parts = explode("_", $key);
                $buttonIndex = (int)end($parts);
                $components[] = [
                        "type" => "button",
                        "sub_type" => "url",
                        "index" => (string)$buttonIndex, 
                        "parameters" => [
                            ["type" => "text", "text" => (string)$value]
                        ]
                    ];
            }
        }
        if (!empty($bodyParameters)) {
            $components[] = ["type" => "body", "parameters" => $bodyParameters];
        }

        $payload = [
            "messaging_product" => "whatsapp",
            "to" => preg_replace('/[^0-9]/', '', $recipientUser->phone_number),
            "type" => "template",
            "template" => [
                "name" => $templateName,
                "language" => [
                    "code" => $languageCode
                ],
                "components" => $components
            ]
        ];
        
        $logEntry = null;
        try {
            $logEntry = WhatsAppMessageLog::create([
                "user_id" => $recipientUser->id,
                "recipient_phone_number" => $recipientUser->phone_number,
                "template_id" => $dbTemplate->id,
                "configuration_id" => $this->activeConfiguration->id,
                "message_content" => json_encode($payload), 
                "message_type" => "TEMPLATE",
                "direction" => "OUTGOING",
                "status" => "PENDING_SEND",
                "related_model_type" => $relatedModel ? get_class($relatedModel) : null,
                "related_model_id" => $relatedModel ? $relatedModel->id : null,
            ]);
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error("WhatsAppService: Database error when creating log entry.", ['error' => $e->getMessage()]);
            // Decide if we should proceed without logging or return error
            // For now, let's proceed but log the failure to create a log entry
        }

        try {
            $apiUrl = $this->graphApiBaseUrl . $this->defaultApiVersion . "/" . $this->activeConfiguration->phone_number_id . "/messages";
            
            $response = Http::withToken($this->activeConfiguration->access_token)
                ->timeout(30) 
                ->post($apiUrl, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                if ($logEntry) {
                    $logEntry->message_sid = $responseData["messages"][0]["id"] ?? null;
                    $logEntry->status = "SENT_TO_API";
                    $logEntry->sent_at = now();
                    $logEntry->api_response = json_encode($responseData);
                    $logEntry->save();
                }
                Log::info("WhatsApp Service: Message '{$templateName}' to {$recipientUser->phone_number} sent successfully to API. SID: " . ($logEntry->message_sid ?? 'N/A'), $responseData);
                return ["success" => true, "message" => "Message sent successfully to WhatsApp API.", "log_id" => $logEntry->id ?? null, "api_response" => $responseData];
            } else {
                if ($logEntry) {
                    $logEntry->status = "FAILED";
                    $logEntry->error_message = "API Error: " . $response->status() . " - " . $response->body();
                    $logEntry->api_response = $response->body();
                    $logEntry->save();
                }
                Log::error("WhatsApp Service: Failed to send message '{$templateName}' to {$recipientUser->phone_number}. API Error: ", ["status" => $response->status(), "body" => $response->body(), "payload" => $payload]);
                return ["success" => false, "message" => "Failed to send message via WhatsApp API: " . $response->status(), "details" => $response->json() ?? $response->body(), "log_id" => $logEntry->id ?? null];
            }
        } catch (Exception $e) {
            if ($logEntry) {
                $logEntry->status = "FAILED";
                $logEntry->error_message = "Exception: " . $e->getMessage();
                $logEntry->save();
            }
            Log::error("WhatsApp Service: Exception while sending message '{$templateName}' to {$recipientUser->phone_number}. Error: " . $e->getMessage(), ["payload" => $payload]);
            return ["success" => false, "message" => "Exception occurred: " . $e->getMessage(), "log_id" => $logEntry->id ?? null];
        }
    }

    public function sendTextMessage(User $recipientUser, string $messageBody, $relatedModel = null): array
    {
        return ["success" => false, "message" => "sendTextMessage not yet fully implemented for direct API call."];
    }
}

