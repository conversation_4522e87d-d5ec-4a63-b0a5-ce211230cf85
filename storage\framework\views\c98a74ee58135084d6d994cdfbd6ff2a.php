<?php $__env->startSection("content"); ?>
<div class="container">
    <h1><?php echo e(isset($status) ? __("Edit Ticket Status") : __("Create Ticket Status")); ?></h1>
    <form action="<?php echo e(isset($status) ? route("admin.ticketing.ticket_statuses.update", $status->id) : route("admin.ticketing.ticket_statuses.store")); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <?php if(isset($status)): ?>
            <?php echo method_field("PUT"); ?>
        <?php endif; ?>
        <div class="form-group">
            <label for="name"><?php echo e(__("Name")); ?></label>
            <input type="text" name="name" id="name" class="form-control <?php $__errorArgs = ["name"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old("name", $status->name ?? "")); ?>" required>
            <?php $__errorArgs = ["name"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="form-group">
            <label for="type"><?php echo e(__("Type")); ?></label>
            <select name="type" id="type" class="form-control <?php $__errorArgs = ["type"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                <option value="open" <?php echo e(old("type", $status->type ?? "") == "open" ? "selected" : ""); ?>><?php echo e(__("Open")); ?></option>
                <option value="pending" <?php echo e(old("type", $status->type ?? "") == "pending" ? "selected" : ""); ?>><?php echo e(__("Pending")); ?></option>
                <option value="closed" <?php echo e(old("type", $status->type ?? "") == "closed" ? "selected" : ""); ?>><?php echo e(__("Closed")); ?></option>
            </select>
            <?php $__errorArgs = ["type"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="form-group">
            <label for="color"><?php echo e(__("Color (e.g., #RRGGBB)")); ?></label>
            <input type="text" name="color" id="color" class="form-control <?php $__errorArgs = ["color"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old("color", $status->color ?? "")); ?>">
            <?php $__errorArgs = ["color"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_default_new" id="is_default_new" class="form-check-input" value="1" <?php echo e(old("is_default_new", $status->is_default_new ?? false) ? "checked" : ""); ?>>
            <label class="form-check-label" for="is_default_new"><?php echo e(__("Default for New Tickets")); ?></label>
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_default_closed" id="is_default_closed" class="form-check-input" value="1" <?php echo e(old("is_default_closed", $status->is_default_closed ?? false) ? "checked" : ""); ?>>
            <label class="form-check-label" for="is_default_closed"><?php echo e(__("Default for Closed Tickets")); ?></label>
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" <?php echo e(old("is_active", $status->is_active ?? true) ? "checked" : ""); ?>>
            <label class="form-check-label" for="is_active"><?php echo e(__("Active")); ?></label>
        </div>
        <button type="submit" class="btn btn-primary"><?php echo e(isset($status) ? __("Update Status") : __("Create Status")); ?></button>
        <a href="<?php echo e(route("admin.ticketing.ticket_statuses.index")); ?>" class="btn btn-secondary"><?php echo e(__("Cancel")); ?></a>
    </form>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/ticketing/statuses/form.blade.php ENDPATH**/ ?>