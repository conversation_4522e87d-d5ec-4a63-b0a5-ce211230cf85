<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use App\Models\PermissionGroup;
use App\Models\Role;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    /**
     * عرض قائمة الأدوار
     */
    public function index()
    {
        $roles = Role::withCount(['permissions', 'users'])->get();
        return view('admin.permissions_system.roles.index', compact('roles'));
    }

    /**
     * عرض نموذج إنشاء دور جديد
     */
    public function create()
    {
        $permissionGroups = PermissionGroup::with('permissions')->orderBy('order')->get();
        return view('admin.permissions_system.roles.form', compact('permissionGroups'));
    }

    /**
     * حفظ دور جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role = Role::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return redirect()->route('admin.permissions_system.roles.index')
            ->with('success', 'تم إنشاء الدور بنجاح');
    }

    /**
     * عرض دور محدد
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);
        $permissionGroups = PermissionGroup::with(['permissions' => function ($query) use ($role) {
            $query->select('permissions.*', \DB::raw('CASE WHEN permission_role.role_id IS NOT NULL THEN 1 ELSE 0 END AS assigned'))
                ->leftJoin('permission_role', function ($join) use ($role) {
                    $join->on('permissions.id', '=', 'permission_role.permission_id')
                        ->where('permission_role.role_id', '=', $role->id);
                });
        }])->orderBy('order')->get();

        return view('admin.permissions_system.roles.show', compact('role', 'permissionGroups'));
    }

    /**
     * عرض نموذج تعديل دور
     */
    public function edit(Role $role)
    {
        $permissionGroups = PermissionGroup::with('permissions')->orderBy('order')->get();
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.permissions_system.roles.form', compact('role', 'permissionGroups', 'rolePermissions'));
    }

    /**
     * تحديث دور
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        $role->permissions()->sync($request->permissions ?? []);

        return redirect()->route('admin.permissions_system.roles.index')
            ->with('success', 'تم تحديث الدور بنجاح');
    }

    /**
     * حذف دور
     */
    public function destroy(Role $role)
    {
        // التحقق من عدم استخدام الدور من قبل أي مستخدم
        if ($role->users()->count() > 0) {
            return redirect()->route('admin.permissions_system.roles.index')
                ->with('error', 'لا يمكن حذف الدور لأنه مستخدم من قبل مستخدمين');
        }

        // التحقق من أن الدور ليس دورًا نظاميًا
        if ($role->is_system) {
            return redirect()->route('admin.permissions_system.roles.index')
                ->with('error', 'لا يمكن حذف دور نظامي');
        }

        $role->permissions()->detach();
        $role->delete();

        return redirect()->route('admin.permissions_system.roles.index')
            ->with('success', 'تم حذف الدور بنجاح');
    }
}
