@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('إدارة الاشتراك') }}</div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger" role="alert">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if ($subscription)
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">{{ __('تفاصيل الاشتراك الحالي') }}</div>
                                    <div class="card-body">
                                        <p><strong>{{ __('رقم الاشتراك:') }}</strong> {{ $subscription->subscription_number }}</p>
                                        <p><strong>{{ __('خطة الاشتراك:') }}</strong> {{ $subscription->plan->name }}</p>
                                        <p><strong>{{ __('تاريخ البدء:') }}</strong> {{ $subscription->start_date->format('Y-m-d') }}</p>
                                        <p><strong>{{ __('تاريخ الانتهاء:') }}</strong> {{ $subscription->end_date->format('Y-m-d') }}</p>
                                        <p><strong>{{ __('الحالة:') }}</strong> {{ $subscription->getStatusArabicAttribute() }}</p>
                                        <p><strong>{{ __('التجديد التلقائي:') }}</strong> {{ $subscription->auto_renew ? __('مفعل') : __('غير مفعل') }}</p>
                                        <p><strong>{{ __('عدد المستخدمين الحالي:') }}</strong> {{ $subscription->current_users_count }}</p>
                                        <p><strong>{{ __('الحد الأقصى للمستخدمين:') }}</strong> {{ $subscription->plan->max_users }}</p>
                                        <p><strong>{{ __('عدد الفروع الحالي:') }}</strong> {{ $subscription->current_branches_count }}</p>
                                        <p><strong>{{ __('الحد الأقصى للفروع:') }}</strong> {{ $subscription->plan->max_branches }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">{{ __('الميزات المتاحة') }}</div>
                                    <div class="card-body">
                                        <ul class="list-group">
                                            <li class="list-group-item {{ $subscription->plan->has_accounting ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_accounting ? 'check' : 'times' }}"></i> {{ __('المحاسبة') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_inventory ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_inventory ? 'check' : 'times' }}"></i> {{ __('المخزون') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_sales ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_sales ? 'check' : 'times' }}"></i> {{ __('المبيعات') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_purchases ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_purchases ? 'check' : 'times' }}"></i> {{ __('المشتريات') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_manufacturing ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_manufacturing ? 'check' : 'times' }}"></i> {{ __('التصنيع') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_hr ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_hr ? 'check' : 'times' }}"></i> {{ __('الموارد البشرية') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_crm ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_crm ? 'check' : 'times' }}"></i> {{ __('إدارة علاقات العملاء') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_reports ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_reports ? 'check' : 'times' }}"></i> {{ __('التقارير') }}
                                            </li>
                                            <li class="list-group-item {{ $subscription->plan->has_api_access ? 'list-group-item-success' : 'list-group-item-secondary' }}">
                                                <i class="fas fa-{{ $subscription->plan->has_api_access ? 'check' : 'times' }}"></i> {{ __('واجهة برمجة التطبيقات') }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">{{ __('سجل المدفوعات') }}</div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>{{ __('رقم الإيصال') }}</th>
                                                <th>{{ __('تاريخ الدفع') }}</th>
                                                <th>{{ __('المبلغ') }}</th>
                                                <th>{{ __('طريقة الدفع') }}</th>
                                                <th>{{ __('الحالة') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($payments as $payment)
                                                <tr>
                                                    <td>{{ $payment->receipt_number }}</td>
                                                    <td>{{ $payment->payment_date->format('Y-m-d') }}</td>
                                                    <td>{{ number_format($payment->amount, 2) }} ريال</td>
                                                    <td>{{ $payment->payment_method }}</td>
                                                    <td>{{ $payment->status }}</td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">{{ __('لا توجد مدفوعات') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            {{ __('لا يوجد اشتراك نشط حالياً. يرجى الاشتراك للاستمرار في استخدام النظام.') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
