<?php $__env->startSection('title', 'إدارة العملاء'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة العملاء</h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('admin.sales.customers.create')); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة عميل جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم الضريبي</th>
                                    <th>حد الائتمان</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($customer->name); ?></td>
                                        <td><?php echo e($customer->email ?: '-'); ?></td>
                                        <td><?php echo e($customer->phone ?: '-'); ?></td>
                                        <td><?php echo e($customer->tax_number ?: '-'); ?></td>
                                        <td><?php echo e($customer->formatted_credit_limit); ?></td>
                                        <td>
                                            <?php if($customer->is_active): ?>
                                                <span class="badge badge-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo e(route('admin.sales.customers.show', $customer)); ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.sales.customers.edit', $customer)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.sales.customers.destroy', $customer)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا يوجد عملاء مسجلين</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة العملاء
        console.log('تم تعطيل DataTables في صفحة العملاء');

        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة العملاء');
                return this;
            };

            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة العملاء');
                return this;
            };
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>