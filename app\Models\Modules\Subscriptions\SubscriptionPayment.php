<?php

namespace App\Models\Modules\Subscriptions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'amount',
        'payment_date',
        'payment_method',
        'transaction_id',
        'receipt_number',
        'status',
        'notes',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the subscription for this payment.
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the formatted amount.
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    /**
     * Get the status in Arabic.
     */
    public function getStatusArabicAttribute()
    {
        return match($this->status) {
            'paid' => 'مدفوع',
            'pending' => 'قيد الانتظار',
            'failed' => 'فشل',
            'refunded' => 'مسترجع',
            default => $this->status,
        };
    }
}
