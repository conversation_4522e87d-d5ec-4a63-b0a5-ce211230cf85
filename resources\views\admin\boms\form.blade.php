@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($bom) ? __("admin_views.edit_bom") : __("admin_views.add_new_bom") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($bom) ? route("admin.boms.update", $bom->id) : route("admin.boms.store") }}" method="POST">
        @csrf
        @if(isset($bom))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="name_ar">{{ __("admin_views.name_ar") }} <span class="text-danger">*</span></label>
                    <input type="text" name="name_ar" id="name_ar" class="form-control" value="{{ old("name_ar", $bom->name_ar ?? "") }}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="name_en">{{ __("admin_views.name_en") }} <span class="text-danger">*</span></label>
                    <input type="text" name="name_en" id="name_en" class="form-control" value="{{ old("name_en", $bom->name_en ?? "") }}" required>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="bom_code">{{ __("admin_views.bom_code") }}</label>
                    <input type="text" name="bom_code" id="bom_code" class="form-control" value="{{ old("bom_code", $bom->bom_code ?? "") }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="item_id">{{ __("admin_views.item_to_produce") }} <span class="text-danger">*</span></label>
                    <select name="item_id" id="item_id" class="form-control" required>
                        <option value="">{{ __("admin_views.select_item") }}</option>
                        @foreach($items as $item)
                            <option value="{{ $item->id }}" {{ old("item_id", $bom->item_id ?? "") == $item->id ? "selected" : "" }}>
                                {{ $item->name_ar }} ({{ $item->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="quantity_to_produce">{{ __("admin_views.quantity_to_produce") }} <span class="text-danger">*</span></label>
                    <input type="number" step="0.0001" name="quantity_to_produce" id="quantity_to_produce" class="form-control" value="{{ old("quantity_to_produce", $bom->quantity_to_produce ?? "1.0000") }}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="unit_of_measure_ar">{{ __("admin_views.unit_of_measure_ar") }}</label>
                    <input type="text" name="unit_of_measure_ar" id="unit_of_measure_ar" class="form-control" value="{{ old("unit_of_measure_ar", $bom->unit_of_measure_ar ?? "") }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="unit_of_measure_en">{{ __("admin_views.unit_of_measure_en") }}</label>
                    <input type="text" name="unit_of_measure_en" id="unit_of_measure_en" class="form-control" value="{{ old("unit_of_measure_en", $bom->unit_of_measure_en ?? "") }}">
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description_ar">{{ __("admin_views.description_ar") }}</label>
            <textarea name="description_ar" id="description_ar" class="form-control">{{ old("description_ar", $bom->description_ar ?? "") }}</textarea>
        </div>

        <div class="form-group">
            <label for="description_en">{{ __("admin_views.description_en") }}</label>
            <textarea name="description_en" id="description_en" class="form-control">{{ old("description_en", $bom->description_en ?? "") }}</textarea>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="branch_id">{{ __("admin_views.branch") }}</label>
                    <select name="branch_id" id="branch_id" class="form-control">
                        <option value="">{{ __("admin_views.select_branch") }}</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ old("branch_id", $bom->branch_id ?? "") == $branch->id ? "selected" : "" }}>
                                {{ $branch->name_ar }} ({{ $branch->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                 <div class="form-group">
                    <label for="valid_from">{{ __("admin_views.valid_from") }}</label>
                    <input type="date" name="valid_from" id="valid_from" class="form-control" value="{{ old("valid_from", isset($bom) && $bom->valid_from ? $bom->valid_from->format("Y-m-d") : "") }}">
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="valid_to">{{ __("admin_views.valid_to") }}</label>
                    <input type="date" name="valid_to" id="valid_to" class="form-control" value="{{ old("valid_to", isset($bom) && $bom->valid_to ? $bom->valid_to->format("Y-m-d") : "") }}">
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_default" id="is_default" value="1" {{ old("is_default", $bom->is_default ?? false) ? "checked" : "" }}>
                <label class="form-check-label" for="is_default">
                    {{ __("admin_views.is_default_bom") }}
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old("is_active", $bom->is_active ?? true) ? "checked" : "" }}>
                <label class="form-check-label" for="is_active">
                    {{ __("admin_views.is_active") }}
                </label>
            </div>
        </div>
        
        <h4>{{ __("admin_views.bom_items") }}</h4>
        <div id="bom-items-container">
            @if(isset($bom) && $bom->bomItems)
                @foreach($bom->bomItems as $index => $bomItem)
                    <div class="row bom-item-row mb-2">
                        <input type="hidden" name="bom_items[{{ $index }}][id]" value="{{ $bomItem->id }}">
                        <div class="col-md-4">
                            <label>{{ __("admin_views.component_item") }}</label>
                            <select name="bom_items[{{ $index }}][item_id]" class="form-control component-item-select" required>
                                <option value="">{{ __("admin_views.select_item") }}</option>
                                @foreach($rawMaterialsAndSemiFinished as $material)
                                    <option value="{{ $material->id }}" {{ $bomItem->item_id == $material->id ? "selected" : "" }}>
                                        {{ $material->name_ar }} ({{ $material->name_en }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>{{ __("admin_views.quantity_required") }}</label>
                            <input type="number" step="0.0001" name="bom_items[{{ $index }}][quantity_required]" class="form-control" value="{{ $bomItem->quantity_required }}" required>
                        </div>
                        <div class="col-md-2">
                            <label>{{ __("admin_views.unit_of_measure_ar") }}</label>
                            <input type="text" name="bom_items[{{ $index }}][unit_of_measure_ar]" class="form-control" value="{{ $bomItem->unit_of_measure_ar }}">
                        </div>
                         <div class="col-md-2">
                            <label>{{ __("admin_views.scrap_percentage") }}</label>
                            <input type="number" step="0.0001" name="bom_items[{{ $index }}][scrap_percentage]" class="form-control" value="{{ $bomItem->scrap_percentage ?? 0.0000 }}">
                        </div>
                        <div class="col-md-2">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm remove-bom-item">{{ __("admin_views.remove") }}</button>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
        <button type="button" id="add-bom-item" class="btn btn-info btn-sm mb-3">{{ __("admin_views.add_component_item") }}</button>


        <button type="submit" class="btn btn-success">{{ isset($bom) ? __("admin_views.update_bom") : __("admin_views.save_bom") }}</button>
        <a href="{{ route("admin.boms.index") }}" class="btn btn-secondary">{{ __("admin_views.cancel") }}</a>
    </form>
</div>

@endsection

@push("scripts")
<script>
    document.addEventListener("DOMContentLoaded", function() {
        let bomItemIndex = {{ isset($bom) && $bom->bomItems ? $bom->bomItems->count() : 0 }};
        const container = document.getElementById("bom-items-container");
        const addButton = document.getElementById("add-bom-item");

        const rawMaterialsJson = @json($rawMaterialsAndSemiFinished);

        function createBomItemRow(index) {
            const itemRow = document.createElement("div");
            itemRow.classList.add("row", "bom-item-row", "mb-2");

            let selectOptions = `<option value="">{{ __("admin_views.select_item") }}</option>`;
            rawMaterialsJson.forEach(material => {
                selectOptions += `<option value="${material.id}">${material.name_ar} (${material.name_en})</option>`;
            });

            itemRow.innerHTML = `
                <div class="col-md-4">
                    <label>{{ __("admin_views.component_item") }}</label>
                    <select name="bom_items[${index}][item_id]" class="form-control component-item-select" required>
                        ${selectOptions}
                    </select>
                </div>
                <div class="col-md-2">
                    <label>{{ __("admin_views.quantity_required") }}</label>
                    <input type="number" step="0.0001" name="bom_items[${index}][quantity_required]" class="form-control" required>
                </div>
                <div class="col-md-2">
                    <label>{{ __("admin_views.unit_of_measure_ar") }}</label>
                    <input type="text" name="bom_items[${index}][unit_of_measure_ar]" class="form-control">
                </div>
                <div class="col-md-2">
                    <label>{{ __("admin_views.scrap_percentage") }}</label>
                    <input type="number" step="0.0001" name="bom_items[${index}][scrap_percentage]" class="form-control" value="0.0000">
                </div>
                <div class="col-md-2">
                    <label>&nbsp;</label>
                    <button type="button" class="btn btn-danger btn-sm remove-bom-item">{{ __("admin_views.remove") }}</button>
                </div>
            `;
            return itemRow;
        }

        addButton.addEventListener("click", function() {
            const newRow = createBomItemRow(bomItemIndex);
            container.appendChild(newRow);
            bomItemIndex++;
        });

        container.addEventListener("click", function(event) {
            if (event.target.classList.contains("remove-bom-item")) {
                event.target.closest(".bom-item-row").remove();
            }
        });
    });
</script>
@endpush

