<?php

namespace App\Models\Modules\WhatsAppIntegration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User; // Import the User model

class WhatsAppOptIn extends Model
{
    use HasFactory;

    protected $table = "whatsapp_opt_ins";

    protected $fillable = [
        "user_id",
        "phone_number",
        "opt_in_source",
        "opt_in_status",
        "opt_in_at",
        "opt_out_at",
    ];

    protected $casts = [
        "opt_in_status" => "boolean",
        "opt_in_at" => "datetime",
        "opt_out_at" => "datetime",
    ];

    /**
     * Get the user that this opt-in record belongs to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

