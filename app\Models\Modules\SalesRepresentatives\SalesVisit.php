<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Customer;

class SalesVisit extends Model
{
    use HasFactory;

    protected $fillable = [
        'visit_code',
        'sales_representative_id',
        'customer_id',
        'sales_route_id',
        'visit_date',
        'planned_start_time',
        'planned_end_time',
        'actual_start_time',
        'actual_end_time',
        'visit_type',
        'visit_purpose',
        'visit_status',
        'visit_result',
        'visit_notes',
        'customer_feedback',
        'location_coordinates',
        'distance_traveled',
        'duration_minutes',
        'order_amount',
        'collection_amount',
        'visit_photos',
        'visit_documents',
        'requires_follow_up',
        'next_visit_date',
        'follow_up_notes',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'visit_date' => 'date',
        'planned_start_time' => 'datetime:H:i',
        'planned_end_time' => 'datetime:H:i',
        'actual_start_time' => 'datetime:H:i',
        'actual_end_time' => 'datetime:H:i',
        'location_coordinates' => 'array',
        'distance_traveled' => 'decimal:2',
        'duration_minutes' => 'integer',
        'order_amount' => 'decimal:2',
        'collection_amount' => 'decimal:2',
        'visit_photos' => 'array',
        'visit_documents' => 'array',
        'requires_follow_up' => 'boolean',
        'next_visit_date' => 'date',
    ];

    /**
     * Generate unique visit code.
     */
    public static function generateVisitCode(): string
    {
        $prefix = 'VST';
        $date = now()->format('Ymd');
        $lastVisit = static::whereDate('created_at', now())->latest()->first();
        $sequence = $lastVisit ? (int)substr($lastVisit->visit_code, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the sales representative for this visit.
     */
    public function salesRepresentative(): BelongsTo
    {
        return $this->belongsTo(SalesRepresentative::class);
    }

    /**
     * Get the customer for this visit.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the sales route for this visit.
     */
    public function salesRoute(): BelongsTo
    {
        return $this->belongsTo(SalesRoute::class);
    }

    /**
     * Get the branch that owns the visit.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the visit.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get visit type color.
     */
    public function getVisitTypeColorAttribute(): string
    {
        return match($this->visit_type) {
            'scheduled' => '#007bff',
            'unscheduled' => '#ffc107',
            'follow_up' => '#28a745',
            'collection' => '#17a2b8',
            'delivery' => '#6f42c1',
            default => '#6c757d'
        };
    }

    /**
     * Get visit status color.
     */
    public function getVisitStatusColorAttribute(): string
    {
        return match($this->visit_status) {
            'planned' => '#6c757d',
            'in_progress' => '#ffc107',
            'completed' => '#28a745',
            'cancelled' => '#dc3545',
            'postponed' => '#fd7e14',
            default => '#6c757d'
        };
    }

    /**
     * Get visit result color.
     */
    public function getVisitResultColorAttribute(): string
    {
        return match($this->visit_result) {
            'successful' => '#28a745',
            'unsuccessful' => '#dc3545',
            'partial' => '#ffc107',
            'rescheduled' => '#17a2b8',
            default => '#6c757d'
        };
    }

    /**
     * Get visit type text in Arabic.
     */
    public function getVisitTypeTextAttribute(): string
    {
        return match($this->visit_type) {
            'scheduled' => 'مجدولة',
            'unscheduled' => 'غير مجدولة',
            'follow_up' => 'متابعة',
            'collection' => 'تحصيل',
            'delivery' => 'توصيل',
            default => 'غير محدد'
        };
    }

    /**
     * Get visit status text in Arabic.
     */
    public function getVisitStatusTextAttribute(): string
    {
        return match($this->visit_status) {
            'planned' => 'مخططة',
            'in_progress' => 'قيد التنفيذ',
            'completed' => 'مكتملة',
            'cancelled' => 'ملغية',
            'postponed' => 'مؤجلة',
            default => 'غير محدد'
        };
    }

    /**
     * Get visit result text in Arabic.
     */
    public function getVisitResultTextAttribute(): string
    {
        return match($this->visit_result) {
            'successful' => 'ناجحة',
            'unsuccessful' => 'غير ناجحة',
            'partial' => 'جزئية',
            'rescheduled' => 'معاد جدولتها',
            default => 'غير محدد'
        };
    }

    /**
     * Calculate actual duration in minutes.
     */
    public function getActualDurationAttribute(): ?int
    {
        if (!$this->actual_start_time || !$this->actual_end_time) {
            return null;
        }

        return $this->actual_start_time->diffInMinutes($this->actual_end_time);
    }

    /**
     * Check if visit is overdue.
     */
    public function isOverdue(): bool
    {
        if ($this->visit_status === 'completed') {
            return false;
        }

        return $this->visit_date->isPast() && $this->planned_end_time && now()->gt($this->planned_end_time);
    }

    /**
     * Check if visit is today.
     */
    public function isToday(): bool
    {
        return $this->visit_date->isToday();
    }

    /**
     * Start the visit.
     */
    public function startVisit(): void
    {
        $this->update([
            'visit_status' => 'in_progress',
            'actual_start_time' => now(),
        ]);
    }

    /**
     * Complete the visit.
     */
    public function completeVisit(array $data): void
    {
        $this->update(array_merge($data, [
            'visit_status' => 'completed',
            'actual_end_time' => now(),
            'duration_minutes' => $this->actual_start_time ? now()->diffInMinutes($this->actual_start_time) : 0,
        ]));
    }

    /**
     * Cancel the visit.
     */
    public function cancelVisit(string $reason): void
    {
        $this->update([
            'visit_status' => 'cancelled',
            'visit_notes' => $this->visit_notes . "\n\nسبب الإلغاء: " . $reason,
        ]);
    }

    /**
     * Scope a query to only include today's visits.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('visit_date', now());
    }

    /**
     * Scope a query to only include completed visits.
     */
    public function scopeCompleted($query)
    {
        return $query->where('visit_status', 'completed');
    }

    /**
     * Scope a query to only include pending visits.
     */
    public function scopePending($query)
    {
        return $query->whereIn('visit_status', ['planned', 'in_progress']);
    }

    /**
     * Scope a query to filter by visit type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('visit_type', $type);
    }

    /**
     * Scope a query to filter by sales representative.
     */
    public function scopeByRepresentative($query, $representativeId)
    {
        return $query->where('sales_representative_id', $representativeId);
    }

    /**
     * Scope a query to order by visit date and time.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('visit_date')->orderBy('planned_start_time');
    }
}
