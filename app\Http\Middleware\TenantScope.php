<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use App\Models\User;

class TenantScope
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // تخطي التحقق إذا كان المستخدم غير مسجل الدخول
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // تخطي التحقق إذا كان المستخدم مدير نظام
        if ($user->hasRole('admin')) {
            return $next($request);
        }

        // تحديد المستأجر (tenant) للمستخدم الحالي
        $tenantId = $this->getTenantId($user);
        
        if ($tenantId) {
            // تخزين معرف المستأجر في الجلسة للاستخدام لاحقاً
            session(['tenant_id' => $tenantId]);
            
            // تطبيق نطاق المستأجر على جميع الاستعلامات
            $this->applyTenantScope($tenantId);
        }

        return $next($request);
    }

    /**
     * الحصول على معرف المستأجر للمستخدم الحالي
     */
    protected function getTenantId(User $user)
    {
        // إذا كان المستخدم هو المستأجر نفسه
        if ($user->hasRole('tenant')) {
            return $user->id;
        }

        // إذا كان المستخدم تابع لمستأجر (مثل موظف في شركة العميل)
        if ($user->tenant_id) {
            return $user->tenant_id;
        }

        return null;
    }

    /**
     * تطبيق نطاق المستأجر على جميع الاستعلامات
     */
    protected function applyTenantScope($tenantId)
    {
        // تطبيق نطاق المستأجر على النماذج التي تدعم ذلك
        // سيتم تنفيذ هذا من خلال trait TenantScoped في النماذج
    }
}
