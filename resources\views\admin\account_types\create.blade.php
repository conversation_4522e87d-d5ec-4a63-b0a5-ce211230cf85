@extends("layouts.admin")

@section("main-content")
    <div class="container">
        <h2>Create New Account Type</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.account_types.store") }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name") }}" required>
            </div>
            <div class="form-group">
                <label for="code">Code (Optional):</label>
                <input type="text" class="form-control" id="code" name="code" value="{{ old("code") }}">
            </div>
            <div class="form-group">
                <label for="is_debit_balance">Is Debit Balance?</label>
                <select class="form-control" id="is_debit_balance" name="is_debit_balance">
                    <option value="1" {{ old("is_debit_balance", "1") == "1" ? "selected" : "" }}>Yes (e.g., Assets, Expenses)</option>
                    <option value="0" {{ old("is_debit_balance") == "0" ? "selected" : "" }}>No (e.g., Liabilities, Equity, Revenue)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="description">Description (Optional):</label>
                <textarea class="form-control" id="description" name="description">{{ old("description") }}</textarea>
            </div>
            <button type="submit" class="btn btn-success">Create Account Type</button>
            <a href="{{ route("admin.account_types.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

