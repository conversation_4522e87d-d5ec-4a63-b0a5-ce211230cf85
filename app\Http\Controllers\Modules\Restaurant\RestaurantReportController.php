<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\RestaurantOrder;
use App\Models\Modules\Restaurant\RestaurantOrderItem;
use App\Models\Modules\Restaurant\MenuItem;
use App\Models\Modules\Restaurant\RestaurantTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RestaurantReportController extends Controller
{
    public function index()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        
        // Today's stats
        $todayStats = [
            'orders_count' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->whereDate('created_at', $today)->count(),
            'revenue' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->whereDate('created_at', $today)->sum('total_amount'),
            'avg_order_value' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->whereDate('created_at', $today)->avg('total_amount') ?? 0,
        ];

        // This month's stats
        $monthStats = [
            'orders_count' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->where('created_at', '>=', $thisMonth)->count(),
            'revenue' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->where('created_at', '>=', $thisMonth)->sum('total_amount'),
        ];

        return view('admin.restaurant.reports.index', compact('todayStats', 'monthStats'));
    }

    public function sales(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        $orders = RestaurantOrder::with(['table', 'orderItems.menuItem'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $summary = [
            'total_orders' => $orders->total(),
            'total_revenue' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
                ->sum('total_amount'),
            'avg_order_value' => RestaurantOrder::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
                ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
                ->avg('total_amount') ?? 0,
        ];

        return view('admin.restaurant.reports.sales', compact('orders', 'summary', 'startDate', 'endDate'));
    }

    public function menuPerformance(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        $menuPerformance = RestaurantOrderItem::select(
                'menu_items.name_ar',
                'menu_items.name_en', 
                'menu_items.price',
                DB::raw('SUM(restaurant_order_items.quantity) as total_quantity'),
                DB::raw('SUM(restaurant_order_items.total_price) as total_revenue'),
                DB::raw('COUNT(DISTINCT restaurant_order_items.order_id) as orders_count')
            )
            ->join('menu_items', 'restaurant_order_items.menu_item_id', '=', 'menu_items.id')
            ->join('restaurant_orders', 'restaurant_order_items.order_id', '=', 'restaurant_orders.id')
            ->where('restaurant_orders.tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereBetween('restaurant_orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('menu_items.id', 'menu_items.name_ar', 'menu_items.name_en', 'menu_items.price')
            ->orderBy('total_quantity', 'desc')
            ->paginate(20);

        return view('admin.restaurant.reports.menu-performance', compact('menuPerformance', 'startDate', 'endDate'));
    }

    public function tableTurnover(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::today()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::today()->format('Y-m-d'));

        $tableTurnover = RestaurantOrder::select(
                'restaurant_tables.name as table_name',
                'restaurant_tables.number as table_number',
                'restaurant_tables.capacity',
                DB::raw('COUNT(*) as orders_count'),
                DB::raw('SUM(restaurant_orders.total_amount) as total_revenue'),
                DB::raw('AVG(restaurant_orders.total_amount) as avg_order_value'),
                DB::raw('SUM(restaurant_orders.guests_count) as total_guests')
            )
            ->join('restaurant_tables', 'restaurant_orders.table_id', '=', 'restaurant_tables.id')
            ->where('restaurant_orders.tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('restaurant_orders.type', 'dine_in')
            ->whereBetween('restaurant_orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('restaurant_tables.id', 'restaurant_tables.name', 'restaurant_tables.number', 'restaurant_tables.capacity')
            ->orderBy('orders_count', 'desc')
            ->paginate(20);

        return view('admin.restaurant.reports.table-turnover', compact('tableTurnover', 'startDate', 'endDate'));
    }

    public function kitchenPerformance(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::today()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::today()->format('Y-m-d'));

        $kitchenPerformance = RestaurantOrderItem::select(
                'kitchen_stations.name as station_name',
                DB::raw('COUNT(*) as items_count'),
                DB::raw('AVG(TIMESTAMPDIFF(MINUTE, restaurant_order_items.preparation_started_at, restaurant_order_items.ready_at)) as avg_prep_time'),
                DB::raw('SUM(CASE WHEN restaurant_order_items.status = "ready" THEN 1 ELSE 0 END) as completed_items'),
                DB::raw('SUM(CASE WHEN restaurant_order_items.status = "cancelled" THEN 1 ELSE 0 END) as cancelled_items')
            )
            ->join('kitchen_stations', 'restaurant_order_items.kitchen_station_id', '=', 'kitchen_stations.id')
            ->join('restaurant_orders', 'restaurant_order_items.order_id', '=', 'restaurant_orders.id')
            ->where('restaurant_orders.tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereBetween('restaurant_orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('kitchen_stations.id', 'kitchen_stations.name')
            ->orderBy('items_count', 'desc')
            ->paginate(20);

        return view('admin.restaurant.reports.kitchen-performance', compact('kitchenPerformance', 'startDate', 'endDate'));
    }
}
