<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\TenantScoped;

class Account extends Model
{
    use HasFactory, TenantScoped;

    protected $fillable = [
        'name_ar',
        'name_en',
        'code',
        'account_type_id',
        'parent_id',
        'description_ar',
        'description_en',
        'is_control_account',
        'accepts_entries',
        'is_active',
        'tenant_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_control_account' => 'boolean',
        'accepts_entries' => 'boolean',
    ];

    /**
     * العلاقة مع نوع الحساب
     */
    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    /**
     * العلاقة مع الحساب الأب
     */
    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * العلاقة مع الحسابات الفرعية
     */
    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * الحصول على جميع الحسابات الفرعية بشكل متداخل
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * الحصول على الحسابات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * الحصول على الحسابات الرئيسية (التي ليس لها أب)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * الحصول على الحسابات التي تقبل القيود
     */
    public function scopeAcceptsEntries($query)
    {
        return $query->where('accepts_entries', true);
    }
}

