@extends('layouts.admin')

@section('title', 'إدارة الاشتراكات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">الاشتراكات</h3>
                    <a href="{{ route('admin.super_admin.subscriptions.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة اشتراك جديد
                    </a>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table id="subscriptions-table" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>المستأجر</th>
                                    <th>الباقة</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>التجديد التلقائي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($subscriptions as $subscription)
                                    <tr>
                                        <td>{{ $subscription->id }}</td>
                                        <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                        <td>{{ $subscription->plan->name ?? 'غير محدد' }}</td>
                                        <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                        <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                        <td>
                                            @switch($subscription->status)
                                                @case('active')
                                                    <span class="badge bg-success">نشط</span>
                                                    @break
                                                @case('expired')
                                                    <span class="badge bg-danger">منتهي</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge bg-warning">ملغي</span>
                                                    @break
                                                @case('pending')
                                                    <span class="badge bg-info">قيد الانتظار</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ $subscription->status }}</span>
                                            @endswitch
                                        </td>
                                        <td>{{ $subscription->price_paid }} ريال</td>
                                        <td>
                                            @if($subscription->auto_renew)
                                                <span class="badge bg-success">نعم</span>
                                            @else
                                                <span class="badge bg-danger">لا</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.super_admin.subscriptions.show', $subscription->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.super_admin.subscriptions.edit', $subscription->id) }}" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route('admin.super_admin.subscriptions.destroy', $subscription->id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد اشتراكات مسجلة</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // انتظار تحميل الصفحة بالكامل
        setTimeout(function() {
            // التحقق من وجود الجدول
            var table = $('#subscriptions-table');
            if (table.length) {
                // التحقق من وجود صفوف في الجدول
                var rows = table.find('tbody tr');

                // إذا كان هناك صف واحد فقط ويحتوي على "لا توجد اشتراكات"، لا نطبق DataTables
                if (rows.length === 1 && rows.first().find('td').attr('colspan')) {
                    console.log('لا توجد بيانات لعرضها في الجدول');
                    return;
                }

                // إذا كان هناك بيانات، طبق DataTables
                if (rows.length > 0) {
                    try {
                        table.DataTable({
                            "language": {
                                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
                            },
                            "paging": true,
                            "searching": true,
                            "ordering": true,
                            "info": true,
                            "autoWidth": false,
                            "responsive": true,
                            "columnDefs": [
                                { "orderable": false, "targets": [8] } // تعطيل الترتيب للعمود الإجراءات (العمود الأخير)
                            ],
                            "order": [[ 0, "desc" ]], // ترتيب حسب ID تنازلي
                            "destroy": true // السماح بإعادة التهيئة
                        });
                        console.log('تم تحميل DataTables بنجاح');
                    } catch (error) {
                        console.error('خطأ في تحميل DataTables:', error);
                    }
                }
            }
        }, 200);
    });
</script>
@endpush
