<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("bnpl_provider_settings")) {
            Schema::create("bnpl_provider_settings", function (Blueprint $table) {
                $table->id();
                $table->string("provider_name")->unique(); // e.g., "tabby", "tamara"
                $table->string("display_name"); // e.g., "Tabby", "Tamara"
                $table->text("api_key_sandbox")->nullable(); // Encrypted
                $table->text("api_key_production")->nullable(); // Encrypted
                $table->text("public_key_sandbox")->nullable(); // Encrypted - for Tamara
                $table->text("public_key_production")->nullable(); // Encrypted - for Tamara
                $table->text("notification_token_sandbox")->nullable(); // Encrypted - for Tamara Webhook
                $table->text("notification_token_production")->nullable(); // Encrypted - for Tamara Webhook
                $table->text("webhook_secret_sandbox")->nullable(); // Encrypted - for Tabby Webhook
                $table->text("webhook_secret_production")->nullable(); // Encrypted - for Tabby Webhook
                $table->string("merchant_code_sandbox")->nullable(); // for Tabby
                $table->string("merchant_code_production")->nullable(); // for Tabby
                $table->enum("environment", ["sandbox", "production"])->default("sandbox");
                $table->boolean("is_active")->default(false);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("bnpl_provider_settings");
    }
};

