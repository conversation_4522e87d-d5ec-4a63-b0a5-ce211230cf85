<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><?php echo e(__("Tickets")); ?></h1>
        <a href="<?php echo e(route("admin.ticketing.tickets.create")); ?>" class="btn btn-primary"><?php echo e(__("Create New Ticket")); ?></a>
    </div>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover">
                <thead>
                    <tr>
                        <th><?php echo e(__("Ticket #")); ?></th>
                        <th><?php echo e(__("Subject")); ?></th>
                        <th><?php echo e(__("User")); ?></th>
                        <th><?php echo e(__("Category")); ?></th>
                        <th><?php echo e(__("Priority")); ?></th>
                        <th><?php echo e(__("Status")); ?></th>
                        <th><?php echo e(__("Assigned To")); ?></th>
                        <th><?php echo e(__("Last Reply")); ?></th>
                        <th><?php echo e(__("Created At")); ?></th>
                        <th><?php echo e(__("Actions")); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><a href="<?php echo e(route("admin.ticketing.tickets.show", $ticket->id)); ?>"><?php echo e($ticket->ticket_number); ?></a></td>
                            <td><?php echo e(Str::limit($ticket->subject, 40)); ?></td>
                            <td><?php echo e($ticket->user->name); ?></td>
                            <td><span class="badge" style="background-color: <?php echo e($ticket->category->color ?? '#ddd'); ?>"><?php echo e($ticket->category->name); ?></span></td>
                            <td><span class="badge" style="background-color: <?php echo e($ticket->priority->color ?? '#ddd'); ?>"><?php echo e($ticket->priority->name); ?></span></td>
                            <td><span class="badge" style="background-color: <?php echo e($ticket->status->color ?? '#ddd'); ?>"><?php echo e($ticket->status->name); ?></span></td>
                            <td><?php echo e($ticket->assignedTo->name ?? __("Unassigned")); ?></td>
                            <td><?php echo e($ticket->last_reply_at ? $ticket->last_reply_at->diffForHumans() : __("No replies yet")); ?></td>
                            <td><?php echo e($ticket->created_at->format("Y-m-d H:i")); ?></td>
                            <td>
                                <a href="<?php echo e(route("admin.ticketing.tickets.show", $ticket->id)); ?>" class="btn btn-sm btn-info"><?php echo e(__("View")); ?></a>
                                <a href="<?php echo e(route("admin.ticketing.tickets.edit", $ticket->id)); ?>" class="btn btn-sm btn-warning"><?php echo e(__("Edit")); ?></a>
                                <form action="<?php echo e(route("admin.ticketing.tickets.destroy", $ticket->id)); ?>" method="POST" style="display: inline-block;">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field("DELETE"); ?>
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("<?php echo e(__("Are you sure you want to delete this ticket?")); ?>")"><?php echo e(__("Delete")); ?></button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="10" class="text-center"><?php echo e(__("No tickets found.")); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <?php echo e($tickets->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/ticketing/tickets/index.blade.php ENDPATH**/ ?>