@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>Create New Branch</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.branches.store") }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="name">Branch Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name") }}" required>
            </div>
            <div class="form-group">
                <label for="code">Branch Code:</label>
                <input type="text" class="form-control" id="code" name="code" value="{{ old("code") }}">
            </div>
            <div class="form-group">
                <label for="address">Address:</label>
                <textarea class="form-control" id="address" name="address">{{ old("address") }}</textarea>
            </div>
            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="text" class="form-control" id="phone" name="phone" value="{{ old("phone") }}">
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" class="form-control" id="email" name="email" value="{{ old("email") }}">
            </div>
            <div class="form-group">
                <label for="parent_id">Parent Branch:</label>
                <select class="form-control" id="parent_id" name="parent_id">
                    <option value="">None</option>
                    @foreach ($branches as $branch)
                        <option value="{{ $branch->id }}" {{ old("parent_id") == $branch->id ? "selected" : "" }}>{{ $branch->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label for="is_active">Active:</label>
                <select class="form-control" id="is_active" name="is_active">
                    <option value="1" {{ old("is_active", 1) == 1 ? "selected" : "" }}>Yes</option>
                    <option value="0" {{ old("is_active") == 0 ? "selected" : "" }}>No</option>
                </select>
            </div>
            <button type="submit" class="btn btn-success">Create Branch</button>
            <a href="{{ route("admin.branches.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

