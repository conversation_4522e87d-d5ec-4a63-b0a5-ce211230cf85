@extends('layouts.admin')

@section('title', 'تسويات المخزون')

@push('styles')
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تسويات المخزون</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.inventory.adjustments.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة تسوية جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في التسويات...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="increase">زيادة</option>
                                <option value="decrease">نقص</option>
                                <option value="damage">تالف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="approved">معتمد</option>
                                <option value="rejected">مرفوض</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم التسوية</th>
                                    <th>التاريخ</th>
                                    <th>المستودع</th>
                                    <th>النوع</th>
                                    <th>عدد الأصناف</th>
                                    <th>القيمة الإجمالية</th>
                                    <th>الحالة</th>
                                    <th>المسؤول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>ADJ-2024-001</td>
                                    <td>2024-03-15</td>
                                    <td>المستودع الرئيسي</td>
                                    <td><span class="badge badge-success">زيادة</span></td>
                                    <td>5</td>
                                    <td>{{ currency_format(2500) }}</td>
                                    <td><span class="badge badge-warning">معلق</span></td>
                                    <td>أحمد محمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.inventory.adjustments.show', 1) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory.adjustments.edit', 1) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.inventory.adjustments.destroy', 1) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>ADJ-2024-002</td>
                                    <td>2024-03-14</td>
                                    <td>مستودع الفرع الثاني</td>
                                    <td><span class="badge badge-danger">نقص</span></td>
                                    <td>3</td>
                                    <td>{{ currency_format(1200) }}</td>
                                    <td><span class="badge badge-success">معتمد</span></td>
                                    <td>فاطمة أحمد</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.inventory.adjustments.show', 2) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory.adjustments.edit', 2) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.inventory.adjustments.destroy', 2) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة تسويات المخزون
        console.log('تم تعطيل DataTables في صفحة تسويات المخزون');

        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة تسويات المخزون');
                return this;
            };

            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة تسويات المخزون');
                return this;
            };
        }

        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });

        // فلترة حسب النوع
        $('#typeFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                }
            });
        });

        // فلترة حسب الحالة
        $('#statusFilter').on('change', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                if (value === '') {
                    $(this).show();
                } else {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                }
            });
        });
    });
</script>
@endpush
