<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\SalesRepresentatives\SalesArea;
use App\Models\Modules\SalesRepresentatives\SalesRoute;
use App\Models\Modules\SalesRepresentatives\SalesVisit;
use App\Models\Modules\SalesRepresentatives\SalesCommission;
use App\Models\Modules\SalesRepresentatives\SalesTarget;
use App\Models\Modules\SalesRepresentatives\SalesExpense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SalesReportController extends Controller
{
    public function index()
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        
        // إحصائيات عامة
        $stats = [
            'total_representatives' => SalesRepresentative::where('tenant_id', $tenantId)->count(),
            'active_representatives' => SalesRepresentative::where('tenant_id', $tenantId)->where('status', 'active')->count(),
            'total_areas' => SalesArea::where('tenant_id', $tenantId)->count(),
            'total_routes' => SalesRoute::where('tenant_id', $tenantId)->count(),
            'monthly_visits' => SalesVisit::where('tenant_id', $tenantId)
                ->whereBetween('visit_date', [now()->startOfMonth(), now()->endOfMonth()])
                ->count(),
            'completed_visits' => SalesVisit::where('tenant_id', $tenantId)
                ->whereBetween('visit_date', [now()->startOfMonth(), now()->endOfMonth()])
                ->where('visit_status', 'completed')
                ->count(),
            'monthly_commissions' => SalesCommission::where('tenant_id', $tenantId)
                ->whereBetween('transaction_date', [now()->startOfMonth(), now()->endOfMonth()])
                ->sum('net_commission'),
            'pending_expenses' => SalesExpense::where('tenant_id', $tenantId)
                ->where('approval_status', 'pending')
                ->sum('amount'),
        ];

        // أفضل المناديب هذا الشهر
        $topRepresentatives = SalesRepresentative::where('tenant_id', $tenantId)
            ->withSum(['salesCommissions as monthly_commission' => function($query) {
                $query->whereBetween('transaction_date', [now()->startOfMonth(), now()->endOfMonth()]);
            }], 'net_commission')
            ->orderBy('monthly_commission', 'desc')
            ->take(5)
            ->get();

        // إحصائيات الزيارات اليومية (آخر 7 أيام)
        $dailyVisits = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dailyVisits[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('l'),
                'visits' => SalesVisit::where('tenant_id', $tenantId)
                    ->whereDate('visit_date', $date)
                    ->count(),
                'completed' => SalesVisit::where('tenant_id', $tenantId)
                    ->whereDate('visit_date', $date)
                    ->where('visit_status', 'completed')
                    ->count(),
            ];
        }

        return view('admin.sales-representatives.reports.index', compact('stats', 'topRepresentatives', 'dailyVisits'));
    }

    public function representativesPerformance(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $representatives = SalesRepresentative::where('tenant_id', $tenantId)
            ->with(['branch'])
            ->withCount([
                'salesVisits as total_visits' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('visit_date', [$startDate, $endDate]);
                },
                'salesVisits as completed_visits' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('visit_date', [$startDate, $endDate])
                          ->where('visit_status', 'completed');
                },
            ])
            ->withSum([
                'salesCommissions as total_commission' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('transaction_date', [$startDate, $endDate]);
                }
            ], 'net_commission')
            ->withSum([
                'salesCommissions as total_sales' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('transaction_date', [$startDate, $endDate])
                          ->where('commission_type', 'sales');
                }
            ], 'base_amount')
            ->get()
            ->map(function($rep) {
                $rep->completion_rate = $rep->total_visits > 0 ? 
                    round(($rep->completed_visits / $rep->total_visits) * 100, 2) : 0;
                return $rep;
            });

        return view('admin.sales-representatives.reports.representatives-performance', 
            compact('representatives', 'startDate', 'endDate'));
    }

    public function areasPerformance(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $areas = SalesArea::where('tenant_id', $tenantId)
            ->with(['activeSalesRepresentatives'])
            ->withCount([
                'salesVisits as total_visits' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('visit_date', [$startDate, $endDate]);
                },
            ])
            ->get()
            ->map(function($area) use ($startDate, $endDate) {
                $area->total_sales = $area->calculateSales($startDate, $endDate);
                $area->representatives_count = $area->activeSalesRepresentatives->count();
                return $area;
            });

        return view('admin.sales-representatives.reports.areas-performance', 
            compact('areas', 'startDate', 'endDate'));
    }

    public function routesEfficiency(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $routes = SalesRoute::where('tenant_id', $tenantId)
            ->with(['salesRepresentative', 'salesArea'])
            ->withCount([
                'salesVisits as total_visits' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('visit_date', [$startDate, $endDate]);
                },
                'customers as total_customers'
            ])
            ->get()
            ->map(function($route) use ($startDate, $endDate) {
                $route->completion_rate = $route->calculateCompletionRate($startDate, $endDate);
                $route->efficiency_score = $route->total_visits > 0 ? 
                    round(($route->total_visits / $route->max_customers) * 100, 2) : 0;
                return $route;
            });

        return view('admin.sales-representatives.reports.routes-efficiency', 
            compact('routes', 'startDate', 'endDate'));
    }

    public function visitsAnalysis(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // إحصائيات الزيارات حسب النوع
        $visitsByType = SalesVisit::where('tenant_id', $tenantId)
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->select('visit_type', DB::raw('count(*) as count'))
            ->groupBy('visit_type')
            ->get();

        // إحصائيات الزيارات حسب الحالة
        $visitsByStatus = SalesVisit::where('tenant_id', $tenantId)
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->select('visit_status', DB::raw('count(*) as count'))
            ->groupBy('visit_status')
            ->get();

        // إحصائيات الزيارات حسب النتيجة
        $visitsByResult = SalesVisit::where('tenant_id', $tenantId)
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->whereNotNull('visit_result')
            ->select('visit_result', DB::raw('count(*) as count'))
            ->groupBy('visit_result')
            ->get();

        // متوسط مدة الزيارات
        $averageDuration = SalesVisit::where('tenant_id', $tenantId)
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->whereNotNull('duration_minutes')
            ->avg('duration_minutes');

        // الزيارات اليومية
        $dailyVisits = SalesVisit::where('tenant_id', $tenantId)
            ->whereBetween('visit_date', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(visit_date) as date'),
                DB::raw('count(*) as total'),
                DB::raw('sum(case when visit_status = "completed" then 1 else 0 end) as completed')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.sales-representatives.reports.visits-analysis', compact(
            'visitsByType', 'visitsByStatus', 'visitsByResult', 'averageDuration', 'dailyVisits', 'startDate', 'endDate'
        ));
    }

    public function commissionsSummary(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // إجمالي العمولات
        $totalCommissions = SalesCommission::where('tenant_id', $tenantId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->sum('net_commission');

        // العمولات حسب النوع
        $commissionsByType = SalesCommission::where('tenant_id', $tenantId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->select('commission_type', DB::raw('sum(net_commission) as total'))
            ->groupBy('commission_type')
            ->get();

        // العمولات حسب حالة الدفع
        $commissionsByStatus = SalesCommission::where('tenant_id', $tenantId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->select('payment_status', DB::raw('sum(net_commission) as total'))
            ->groupBy('payment_status')
            ->get();

        // أفضل المناديب في العمولات
        $topEarners = SalesCommission::where('tenant_id', $tenantId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->with('salesRepresentative')
            ->select('sales_representative_id', DB::raw('sum(net_commission) as total_commission'))
            ->groupBy('sales_representative_id')
            ->orderBy('total_commission', 'desc')
            ->take(10)
            ->get();

        // العمولات الشهرية
        $monthlyCommissions = SalesCommission::where('tenant_id', $tenantId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->select(
                DB::raw('YEAR(transaction_date) as year'),
                DB::raw('MONTH(transaction_date) as month'),
                DB::raw('sum(net_commission) as total')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return view('admin.sales-representatives.reports.commissions-summary', compact(
            'totalCommissions', 'commissionsByType', 'commissionsByStatus', 'topEarners', 'monthlyCommissions', 'startDate', 'endDate'
        ));
    }

    public function targetsAchievement(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $year = $request->get('year', now()->year);
        $month = $request->get('month', now()->month);

        $targets = SalesTarget::where('tenant_id', $tenantId)
            ->where('target_year', $year)
            ->where('target_month', $month)
            ->with('salesRepresentative')
            ->get()
            ->map(function($target) {
                $target->updateAchievements();
                return $target;
            });

        // إحصائيات الإنجاز
        $achievementStats = [
            'total_targets' => $targets->count(),
            'achieved_targets' => $targets->where('overall_achievement_percentage', '>=', 100)->count(),
            'average_achievement' => $targets->avg('overall_achievement_percentage'),
            'total_sales_target' => $targets->sum('sales_target'),
            'total_achieved_sales' => $targets->sum('achieved_sales'),
        ];

        return view('admin.sales-representatives.reports.targets-achievement', compact(
            'targets', 'achievementStats', 'year', 'month'
        ));
    }

    public function expensesAnalysis(Request $request)
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // إجمالي المصروفات
        $totalExpenses = SalesExpense::where('tenant_id', $tenantId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');

        // المصروفات حسب النوع
        $expensesByType = SalesExpense::where('tenant_id', $tenantId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->select('expense_type', DB::raw('sum(amount) as total'))
            ->groupBy('expense_type')
            ->get();

        // المصروفات حسب حالة الموافقة
        $expensesByStatus = SalesExpense::where('tenant_id', $tenantId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->select('approval_status', DB::raw('sum(amount) as total'))
            ->groupBy('approval_status')
            ->get();

        // أعلى المناديب في المصروفات
        $topSpenders = SalesExpense::where('tenant_id', $tenantId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->with('salesRepresentative')
            ->select('sales_representative_id', DB::raw('sum(amount) as total_expenses'))
            ->groupBy('sales_representative_id')
            ->orderBy('total_expenses', 'desc')
            ->take(10)
            ->get();

        // المصروفات اليومية
        $dailyExpenses = SalesExpense::where('tenant_id', $tenantId)
            ->whereBetween('expense_date', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(expense_date) as date'),
                DB::raw('sum(amount) as total')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.sales-representatives.reports.expenses-analysis', compact(
            'totalExpenses', 'expensesByType', 'expensesByStatus', 'topSpenders', 'dailyExpenses', 'startDate', 'endDate'
        ));
    }
}
