@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.operations_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.operations_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tools me-1"></i>
            {{ __(\'manufacturing.operations_list\') }}
            <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_operation\') }}</a> {{-- Link to create page --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for operations table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.op_code\') }}</th>
                        <th>{{ __(\'manufacturing.op_name\') }}</th>
                        <th>{{ __(\'manufacturing.op_default_work_center\') }}</th>
                        <th>{{ __(\'manufacturing.op_standard_setup_time\') }}</th>
                        <th>{{ __(\'manufacturing.op_standard_run_time_per_unit\') }}</th>
                        <th>{{ __(\'manufacturing.is_active\') }}</th>
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>OP-CUT-001</td>
                        <td>Wood Cutting</td>
                        <td>WC-ASSEMBLY-01</td>
                        <td>0.50 hrs</td>
                        <td>0.10 hrs</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a> {{-- Link to show/edit page --}}
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a> {{-- Link to edit page --}}
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button> {{-- Form for delete --}}
                        </td>
                    </tr>
                     <tr>
                        <td>OP-PAINT-001</td>
                        <td>Surface Painting</td>
                        <td>WC-PAINTING-01</td>
                        <td>1.00 hrs</td>
                        <td>0.25 hrs</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a>
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button>
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

