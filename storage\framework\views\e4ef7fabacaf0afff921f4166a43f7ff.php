<?php $__env->startSection('title', 'تقارير المخزون'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-boxes me-2"></i>
                        تقارير المخزون
                    </h4>
                    <a href="<?php echo e(route('admin.reports.financial.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        العودة للتقارير
                    </a>
                </div>
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">1,245</h4>
                                    <p class="card-text">إجمالي الأصناف</p>
                                    <small>في المخزون</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">456,750 ريال</h4>
                                    <p class="card-text">قيمة المخزون</p>
                                    <small>إجمالي</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">67</h4>
                                    <p class="card-text">مخزون منخفض</p>
                                    <small>يحتاج تجديد</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">23</h4>
                                    <p class="card-text">مخزون منتهي</p>
                                    <small>نفد المخزون</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر التقارير -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                فلاتر التقارير
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="category_id" class="form-label">فئة المنتج</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">جميع الفئات</option>
                                        <option value="1">إلكترونيات</option>
                                        <option value="2">ملابس</option>
                                        <option value="3">أدوات منزلية</option>
                                        <option value="4">مواد غذائية</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="warehouse_id" class="form-label">المستودع</label>
                                    <select class="form-select" id="warehouse_id" name="warehouse_id">
                                        <option value="">جميع المستودعات</option>
                                        <option value="1">المستودع الرئيسي</option>
                                        <option value="2">مستودع الفرع الأول</option>
                                        <option value="3">مستودع الفرع الثاني</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="stock_status" class="form-label">حالة المخزون</label>
                                    <select class="form-select" id="stock_status" name="stock_status">
                                        <option value="">جميع الحالات</option>
                                        <option value="in_stock">متوفر</option>
                                        <option value="low_stock">مخزون منخفض</option>
                                        <option value="out_of_stock">نفد المخزون</option>
                                        <option value="overstock">مخزون زائد</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="value_range" class="form-label">نطاق القيمة</label>
                                    <select class="form-select" id="value_range" name="value_range">
                                        <option value="">جميع القيم</option>
                                        <option value="0-1000">0 - 1,000 ريال</option>
                                        <option value="1000-5000">1,000 - 5,000 ريال</option>
                                        <option value="5000-10000">5,000 - 10,000 ريال</option>
                                        <option value="10000+">أكثر من 10,000 ريال</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>
                                        تطبيق الفلاتر
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- أنواع التقارير -->
                    <div class="row">
                        <!-- تقرير مستويات المخزون -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-bar-chart me-2"></i>
                                        مستويات المخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">عرض تفصيلي لمستويات المخزون الحالية لجميع المنتجات</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewReport('stock_levels')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-primary" onclick="exportReport('stock_levels')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المخزون المنخفض -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        المخزون المنخفض
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">قائمة بالمنتجات التي وصلت لحد الطلب الأدنى وتحتاج تجديد</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-warning" onclick="viewReport('low_stock')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="exportReport('low_stock')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير حركة المخزون -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-arrow-left-right me-2"></i>
                                        حركة المخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تتبع حركات الدخول والخروج للمخزون مع التواريخ والأسباب</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-success" onclick="viewReport('movements')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="exportReport('movements')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير تقييم المخزون -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-currency-dollar me-2"></i>
                                        تقييم المخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تقييم مالي شامل للمخزون بالتكلفة والقيمة السوقية</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewReport('valuation')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="exportReport('valuation')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تحليل ABC -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-graph-up-arrow me-2"></i>
                                        تحليل ABC
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تصنيف المنتجات حسب الأهمية والقيمة (A, B, C)</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewReport('abc_analysis')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="exportReport('abc_analysis')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المخزون الراكد -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-hourglass me-2"></i>
                                        المخزون الراكد
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">المنتجات التي لم تتحرك لفترة طويلة وتحتاج إجراءات</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-danger" onclick="viewReport('dead_stock')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="exportReport('dead_stock')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المنتجات الأكثر حركة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-fire me-2"></i>
                                المنتجات الأكثر حركة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>كود المنتج</th>
                                            <th>اسم المنتج</th>
                                            <th>الفئة</th>
                                            <th>الكمية الحالية</th>
                                            <th>قيمة المخزون</th>
                                            <th>آخر حركة</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>PRD-001</code></td>
                                            <td>جهاز كمبيوتر محمول</td>
                                            <td>إلكترونيات</td>
                                            <td>45 قطعة</td>
                                            <td>67,500 ريال</td>
                                            <td>2024-01-17</td>
                                            <td><span class="badge bg-success">متوفر</span></td>
                                        </tr>
                                        <tr>
                                            <td><code>PRD-002</code></td>
                                            <td>قميص قطني</td>
                                            <td>ملابس</td>
                                            <td>8 قطع</td>
                                            <td>800 ريال</td>
                                            <td>2024-01-16</td>
                                            <td><span class="badge bg-warning">مخزون منخفض</span></td>
                                        </tr>
                                        <tr>
                                            <td><code>PRD-003</code></td>
                                            <td>طقم أواني طبخ</td>
                                            <td>أدوات منزلية</td>
                                            <td>0 قطع</td>
                                            <td>0 ريال</td>
                                            <td>2024-01-10</td>
                                            <td><span class="badge bg-danger">نفد المخزون</span></td>
                                        </tr>
                                        <tr>
                                            <td><code>PRD-004</code></td>
                                            <td>عبوة أرز 5 كيلو</td>
                                            <td>مواد غذائية</td>
                                            <td>156 قطعة</td>
                                            <td>4,680 ريال</td>
                                            <td>2024-01-17</td>
                                            <td><span class="badge bg-success">متوفر</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات العامة -->
                    <div class="d-flex justify-content-center mt-4">
                        <button class="btn btn-success me-2" onclick="exportAllReports()">
                            <i class="bi bi-download me-1"></i>
                            تصدير جميع التقارير
                        </button>
                        <button class="btn btn-primary me-2" onclick="scheduleReports()">
                            <i class="bi bi-calendar-event me-1"></i>
                            جدولة التقارير
                        </button>
                        <button class="btn btn-info me-2" onclick="emailReports()">
                            <i class="bi bi-envelope me-1"></i>
                            إرسال بالبريد
                        </button>
                        <button class="btn btn-warning" onclick="generateAlerts()">
                            <i class="bi bi-bell me-1"></i>
                            تنبيهات المخزون
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function viewReport(type) {
    alert(`سيتم عرض تقرير: ${getReportName(type)}`);
}

function exportReport(type) {
    alert(`سيتم تصدير تقرير: ${getReportName(type)} إلى Excel`);
}

function exportAllReports() {
    alert('سيتم تصدير جميع تقارير المخزون إلى ملف مضغوط');
}

function scheduleReports() {
    alert('سيتم فتح نافذة جدولة تقارير المخزون');
}

function emailReports() {
    alert('سيتم فتح نافذة إرسال تقارير المخزون بالبريد الإلكتروني');
}

function generateAlerts() {
    alert('سيتم إنشاء تنبيهات للمخزون المنخفض والمنتهي');
}

function resetFilters() {
    document.getElementById('category_id').value = '';
    document.getElementById('warehouse_id').value = '';
    document.getElementById('stock_status').value = '';
    document.getElementById('value_range').value = '';
}

function getReportName(type) {
    const names = {
        'stock_levels': 'مستويات المخزون',
        'low_stock': 'المخزون المنخفض',
        'movements': 'حركة المخزون',
        'valuation': 'تقييم المخزون',
        'abc_analysis': 'تحليل ABC',
        'dead_stock': 'المخزون الراكد'
    };
    return names[type] || type;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/reports/inventory/index.blade.php ENDPATH**/ ?>