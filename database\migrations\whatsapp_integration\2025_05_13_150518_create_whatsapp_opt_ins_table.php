<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("whatsapp_opt_ins", function (Blueprint $table) {
            $table->id();
            $table->foreignId("user_id")->constrained("users")->onDelete("cascade")->comment("Foreign key to users table");
            $table->string("phone_number", 30)->comment("Phone number that opted-in (with country code)");
            $table->string("opt_in_source")->nullable()->comment("Source of the opt-in, e.g., web_form, manual_entry, checkout");
            $table->boolean("opt_in_status")->default(true)->comment("True = opted-in, False = opted-out");
            $table->timestamp("opt_in_at")->nullable();
            $table->timestamp("opt_out_at")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("whatsapp_opt_ins");
    }
};
