<?php

namespace App\Http\Controllers\Modules\Sales;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Modules\Sales\Quotation;
use App\Models\Modules\Sales\Customer;
use Illuminate\Support\Facades\Validator;

class QuotationController extends Controller
{
    /**
     * Display a listing of the quotations.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $quotations = Quotation::with('customer')->get();
        return view('admin.sales.quotations.index');
    }

    /**
     * Show the form for creating a new quotation.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $customers = Customer::active()->get();
        return view('admin.sales.quotations.create', compact('customers'));
    }

    /**
     * Store a newly created quotation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validation and storage logic will be implemented later
        return redirect()->route('admin.sales.quotations.index')
            ->with('success', 'تم إنشاء عرض السعر بنجاح');
    }

    /**
     * Display the specified quotation.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // $quotation = Quotation::with(['customer', 'items'])->findOrFail($id);
        return view('admin.sales.quotations.show');
    }

    /**
     * Show the form for editing the specified quotation.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // $quotation = Quotation::findOrFail($id);
        // $customers = Customer::active()->get();
        return view('admin.sales.quotations.edit');
    }

    /**
     * Update the specified quotation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Validation and update logic will be implemented later
        return redirect()->route('admin.sales.quotations.index')
            ->with('success', 'تم تحديث عرض السعر بنجاح');
    }

    /**
     * Remove the specified quotation from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Delete logic will be implemented later
        return redirect()->route('admin.sales.quotations.index')
            ->with('success', 'تم حذف عرض السعر بنجاح');
    }
}
