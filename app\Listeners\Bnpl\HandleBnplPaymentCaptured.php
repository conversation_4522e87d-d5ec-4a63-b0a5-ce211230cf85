<?php

namespace App\Listeners\Bnpl;

use App\Events\Bnpl\BnplPaymentCapturedEvent;
use App\Services\Accounting\BnplAccountingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
// Import necessary models or services for notifications

class HandleBnplPaymentCaptured // implements ShouldQueue
{
    protected BnplAccountingService $accountingService;

    /**
     * Create the event listener.
     *
     * @param BnplAccountingService $accountingService
     */
    public function __construct(BnplAccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Handle the event.
     *
     * @param  BnplPaymentCapturedEvent  $event
     * @return void
     */
    public function handle(BnplPaymentCapturedEvent $event): void
    {
        Log::info('BNPL Payment Captured Event Received for Order ID: ' . $event->order->id . ' with BNPL Transaction ID: ' . $event->bnplTransaction->id);

        // 1. Update Order Status to 'paid' or 'processing' based on business logic
        // Example: $event->order->update(['status' => 'processing', 'payment_status' => 'paid']);
        // This depends on the existing order status flow
        Log::info('Placeholder for updating order status for Order ID: ' . $event->order->id . ' to processing/paid.');

        // 2. Create accounting entries for captured payment
        $this->accountingService->createJournalEntriesForCapture($event->order, $event->bnplTransaction, $event->captureDetails);

        // 3. Send notifications (e.g., payment confirmation to customer, notification to admin)
        // Example: Notification::send($event->order->customer, new BnplPaymentCapturedNotification($event->order));
        Log::info('Placeholder for sending notification for captured BNPL payment for Order ID: ' . $event->order->id);

        // 4. Potentially trigger other processes like inventory deduction, fulfillment, etc.
    }
}
