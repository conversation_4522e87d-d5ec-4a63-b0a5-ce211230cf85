@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($account) ? __("Edit Account") : __("Add New Account") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($account) ? route("admin.accounts.update", $account->id) : route("admin.accounts.store") }}" method="POST">
        @csrf
        @if(isset($account))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="name_ar" class="form-label">{{ __("Name (Arabic)") }}</label>
                    <input type="text" class="form-control" id="name_ar" name="name_ar" value="{{ old("name_ar", $account->name_ar ?? "") }}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="name_en" class="form-label">{{ __("Name (English)") }}</label>
                    <input type="text" class="form-control" id="name_en" name="name_en" value="{{ old("name_en", $account->name_en ?? "") }}" required>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="code" class="form-label">{{ __("Account Code") }}</label>
                    <input type="text" class="form-control" id="code" name="code" value="{{ old("code", $account->code ?? "") }}">
                    <small class="form-text text-muted">{{ __("Leave blank for auto-generation (if configured), or enter manually.") }}</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="account_type_id" class="form-label">{{ __("Account Type") }}</label>
                    <select class="form-control" id="account_type_id" name="account_type_id" required>
                        <option value="">{{ __("Select Account Type") }}</option>
                        @foreach($accountTypes as $type)
                            <option value="{{ $type->id }}" {{ (isset($account) && $account->account_type_id == $type->id) || old("account_type_id") == $type->id ? "selected" : "" }}>
                                {{ $type->name_ar }} ({{ $type->name_en }})
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="parent_id" class="form-label">{{ __("Parent Account") }}</label>
            <select class="form-control" id="parent_id" name="parent_id">
                <option value="">{{ __("None (Main Account)") }}</option>
                @foreach($parentAccounts as $parentAccount)
                    <option value="{{ $parentAccount->id }}" {{ (isset($account) && $account->parent_id == $parentAccount->id) || old("parent_id") == $parentAccount->id ? "selected" : "" }}>
                        {{ $parentAccount->name_ar }} ({{ $parentAccount->name_en }}) - [{{ $parentAccount->code }}]
                    </option>
                @endforeach
            </select>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="description_ar" class="form-label">{{ __("Description (Arabic)") }}</label>
                    <textarea class="form-control" id="description_ar" name="description_ar">{{ old("description_ar", $account->description_ar ?? "") }}</textarea>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="description_en" class="form-label">{{ __("Description (English)") }}</label>
                    <textarea class="form-control" id="description_en" name="description_en">{{ old("description_en", $account->description_en ?? "") }}</textarea>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="opening_balance_debit" class="form-label">{{ __("Opening Balance Debit") }}</label>
                    <input type="number" step="0.01" class="form-control" id="opening_balance_debit" name="opening_balance_debit" value="{{ old("opening_balance_debit", $account->opening_balance_debit ?? "0.00") }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="opening_balance_credit" class="form-label">{{ __("Opening Balance Credit") }}</label>
                    <input type="number" step="0.01" class="form-control" id="opening_balance_credit" name="opening_balance_credit" value="{{ old("opening_balance_credit", $account->opening_balance_credit ?? "0.00") }}">
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="opening_balance_date" class="form-label">{{ __("Opening Balance Date") }}</label>
            <input type="date" class="form-control" id="opening_balance_date" name="opening_balance_date" value="{{ old("opening_balance_date", $account->opening_balance_date ?? "") }}">
        </div>

         <div class="mb-3">
            <label for="branch_id" class="form-label">{{ __("Branch (Optional)") }}</label>
            <select class="form-control" id="branch_id" name="branch_id">
                <option value="">{{ __("Select Branch") }}</option>
                @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ (isset($account) && $account->branch_id == $branch->id) || old("branch_id") == $branch->id ? "selected" : "" }}>
                        {{ $branch->name_ar }} ({{ $branch->name_en }})
                    </option>
                @endforeach
            </select>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_control_account" name="is_control_account" value="1" {{ (isset($account) && $account->is_control_account) || old("is_control_account") ? "checked" : "" }}>
                    <label class="form-check-label" for="is_control_account">{{ __("Is Control Account") }}</label>
                    <small class="form-text text-muted d-block">{{ __("If checked, no direct entries allowed; sums up sub-accounts.") }}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="accepts_entries" name="accepts_entries" value="1" {{ (isset($account) && $account->accepts_entries) || old("accepts_entries", 1) ? "checked" : "" }}>
                    <label class="form-check-label" for="accepts_entries">{{ __("Accepts Entries") }}</label>
                     <small class="form-text text-muted d-block">{{ __("If unchecked, it might be a header or inactive.") }}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ (isset($account) && $account->is_active) || old("is_active", 1) ? "checked" : "" }}>
                    <label class="form-check-label" for="is_active">{{ __("Is Active") }}</label>
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-success">{{ isset($account) ? __("Update Account") : __("Save Account") }}</button>
        <a href="{{ route("admin.accounts.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

