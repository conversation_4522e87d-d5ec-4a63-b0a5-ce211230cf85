@extends(\'layouts.app\')

@section(\'content
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.products_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.products_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-boxes me-1"></i>
            {{ __(\'manufacturing.products_list\') }}
            <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_product\') }}</a> {{-- Link to create page --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for products table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.product_code\') }}</th>
                        <th>{{ __(\'manufacturing.product_name\') }}</th>
                        <th>{{ __(\'manufacturing.product_type\') }}</th>
                        <th>{{ __(\'manufacturing.standard_cost\') }}</th>
                        <th>{{ __(\'manufacturing.can_be_manufactured\') }}</th>
                        <th>{{ __(\'manufacturing.can_be_purchased\') }}</th>
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>PROD-001</td>
                        <td>Sample Manufactured Product</td>
                        <td>{{ __(\'manufacturing.type_manufactured\') }}</td>
                        <td>150.00</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td><span class="badge bg-secondary">{{ __(\'general.no\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a> {{-- Link to show/edit page --}}
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a> {{-- Link to edit page --}}
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button> {{-- Form for delete --}}
                        </td>
                    </tr>
                    <tr>
                        <td>RAW-005</td>
                        <td>Sample Raw Material</td>
                        <td>{{ __(\'manufacturing.type_raw_material\') }}</td>
                        <td>25.50</td>
                        <td><span class="badge bg-secondary">{{ __(\'general.no\') }}</span></td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a>
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button>
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

