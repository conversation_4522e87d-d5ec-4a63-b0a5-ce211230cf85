<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_modifiers', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar'); // الاسم بالعربية
            $table->string('name_en'); // الاسم بالإنجليزية
            $table->text('description_ar')->nullable(); // الوصف بالعربية
            $table->text('description_en')->nullable(); // الوصف بالإنجليزية
            $table->enum('type', ['single', 'multiple'])->default('single'); // نوع الاختيار
            $table->boolean('is_required')->default(false); // إجباري أم لا
            $table->integer('min_selections')->default(0); // الحد الأدنى للاختيارات
            $table->integer('max_selections')->nullable(); // الحد الأقصى للاختيارات
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->boolean('is_active')->default(true);
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_modifiers');
    }
};
