<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RestaurantOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'menu_item_id',
        'quantity',
        'unit_price',
        'total_price',
        'notes',
        'special_instructions',
        'status',
        'preparation_started_at',
        'ready_at',
        'served_at',
        'kitchen_station_id',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'preparation_started_at' => 'datetime',
        'ready_at' => 'datetime',
        'served_at' => 'datetime',
    ];

    /**
     * Get the order that owns the order item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(RestaurantOrder::class, 'order_id');
    }

    /**
     * Get the menu item that owns the order item.
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class, 'menu_item_id');
    }

    /**
     * Get the kitchen station that owns the order item.
     */
    public function kitchenStation(): BelongsTo
    {
        return $this->belongsTo(KitchenStation::class, 'kitchen_station_id');
    }

    /**
     * Get the modifiers for the order item.
     */
    public function modifiers(): HasMany
    {
        return $this->hasMany(RestaurantOrderItemModifier::class, 'order_item_id');
    }

    /**
     * Calculate total price including modifiers.
     */
    public function calculateTotalPrice(): void
    {
        $modifiersTotal = $this->modifiers->sum('price_adjustment');
        $this->total_price = ($this->unit_price + $modifiersTotal) * $this->quantity;
        $this->save();
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => '#ffc107',
            'preparing' => '#fd7e14',
            'ready' => '#28a745',
            'served' => '#6f42c1',
            'cancelled' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Get preparation time in minutes.
     */
    public function getPreparationTimeAttribute(): ?int
    {
        if ($this->preparation_started_at && $this->ready_at) {
            return $this->preparation_started_at->diffInMinutes($this->ready_at);
        }
        return null;
    }

    /**
     * Check if item can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending']);
    }

    /**
     * Start preparation.
     */
    public function startPreparation(): void
    {
        $this->update([
            'status' => 'preparing',
            'preparation_started_at' => now(),
        ]);
    }

    /**
     * Mark as ready.
     */
    public function markAsReady(): void
    {
        $this->update([
            'status' => 'ready',
            'ready_at' => now(),
        ]);
    }

    /**
     * Mark as served.
     */
    public function markAsServed(): void
    {
        $this->update([
            'status' => 'served',
            'served_at' => now(),
        ]);
    }

    /**
     * Scope a query to only include pending items.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include preparing items.
     */
    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    /**
     * Scope a query to only include ready items.
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }
}
