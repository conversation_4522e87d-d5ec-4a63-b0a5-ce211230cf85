<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Subscription\TenantSubscription;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $module = null)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        // Get active subscription
        $subscription = TenantSubscription::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->orWhere('status', 'trial')
            ->with('subscriptionPlan')
            ->first();

        if (!$subscription) {
            return redirect()->route('subscription.plans')
                ->with('error', 'يجب عليك اختيار باقة اشتراك للوصول إلى هذه الصفحة.');
        }

        // Check if subscription is expired
        if ($subscription->isExpired()) {
            return redirect()->route('subscription.expired')
                ->with('error', 'انتهت صلاحية اشتراكك. يرجى تجديد الاشتراك للمتابعة.');
        }

        // Check module access if specified
        if ($module && !$subscription->hasModuleAccess($module)) {
            return redirect()->route('admin.dashboard')
                ->with('error', 'هذه الوحدة غير متاحة في باقتك الحالية. يرجى ترقية الباقة للوصول إليها.');
        }

        // Add subscription to request for use in controllers
        $request->attributes->set('subscription', $subscription);

        return $next($request);
    }
}
