<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules;

class RegisterController extends Controller
{
    /**
     * عرض صفحة تسجيل مستأجر جديد
     */
    public function showRegistrationForm()
    {
        $plans = SubscriptionPlan::where('is_active', true)->get();
        return view('tenant.register', compact('plans'));
    }

    /**
     * تسجيل مستأجر جديد
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'company_name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'subscription_plan_id' => ['required', 'exists:subscription_plans,id'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            // إنشاء المستخدم المستأجر
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'is_active' => true,
            ]);

            // تعيين دور المستأجر للمستخدم
            $tenantRole = Role::where('name', 'tenant')->first();
            if ($tenantRole) {
                $user->roles()->attach($tenantRole->id);
            }

            // الحصول على خطة الاشتراك
            $plan = SubscriptionPlan::findOrFail($request->subscription_plan_id);

            // إنشاء اشتراك للمستأجر
            $subscription = Subscription::create([
                'tenant_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'start_date' => now(),
                'end_date' => now()->addDays(30), // فترة تجريبية 30 يوم
                'status' => 'active',
                'auto_renew' => false,
                'current_users_count' => 1,
                'current_branches_count' => 0,
                'price_paid' => 0, // مجاني للفترة التجريبية
                'notes' => 'اشتراك تجريبي',
            ]);

            DB::commit();

            // تسجيل الدخول للمستخدم الجديد
            auth()->login($user);

            // إعادة توجيه المستخدم إلى لوحة التحكم مع رسالة ترحيب
            return redirect()->route('tenant.dashboard')->with('success', 'تم تسجيل حسابك بنجاح! مرحباً بك في نظام المحاسبة.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تسجيل حسابك: ' . $e->getMessage())->withInput();
        }
    }
}
