<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\BnplController;
use App\Http\Controllers\Api\PosApiController; // Added for POS API

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// BNPL Routes
Route::prefix("bnpl")->name("api.bnpl.")->group(function () {
    // Endpoint to initiate a checkout session with a BNPL provider
    Route::post("checkout/initiate", [BnplController::class, "initiateCheckout"])->name("checkout.initiate");
    
    // Endpoint to check payment status
    Route::post("checkout/status", [BnplController::class, "checkPaymentStatus"])->name("checkout.status");

    // Webhook endpoints for Tabby and Tamara
    Route::post("webhook/tabby", [BnplController::class, "handleTabbyWebhook"])->name("webhook.tabby");
    Route::post("webhook/tamara", [BnplController::class, "handleTamaraWebhook"])->name("webhook.tamara");

    // Callback/Redirect URLs from payment gateway
    Route::get("callback/tabby/success", [BnplController::class, "handleTabbySuccessCallback"])->name("callback.tabby.success");
    Route::get("callback/tabby/failure", [BnplController::class, "handleTabbyFailureCallback"])->name("callback.tabby.failure");
    Route::get("callback/tabby/cancel", [BnplController::class, "handleTabbyCancelCallback"])->name("callback.tabby.cancel");

    Route::get("callback/tamara/success", [BnplController::class, "handleTamaraSuccessCallback"])->name("callback.tamara.success");
    Route::get("callback/tamara/failure", [BnplController::class, "handleTamaraFailureCallback"])->name("callback.tamara.failure");
    Route::get("callback/tamara/cancel", [BnplController::class, "handleTamaraCancelCallback"])->name("callback.tamara.cancel");
});

// POS API Routes
Route::prefix("pos")->name("api.pos.")->group(function () {
    Route::post("initiate-network-payment", [PosApiController::class, "initiateNetworkPayment"])->name("initiateNetworkPayment");
    // Add other POS related API endpoints here if needed in the future
});


