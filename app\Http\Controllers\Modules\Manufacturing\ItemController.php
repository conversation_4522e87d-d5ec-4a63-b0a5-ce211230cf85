<?php

namespace App\Http\Controllers\Modules\Manufacturing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Manufacturing\ManufacturingItem;
use App\Models\Modules\Branches\Branch;
use App\Models\Modules\GeneralLedger\Account;
use Illuminate\Http\Request;

class ItemController extends Controller
{
    public function index()
    {
        $items = ManufacturingItem::with(['branch', 'account'])->latest()->paginate(10);
        return view('admin.manufacturing.items.index', compact('items'));
    }

    public function create()
    {
        $branches = Branch::all();
        $accounts = Account::where('is_active', true)->get(); // Assuming you want only active accounts
        return view('admin.manufacturing.items.create', compact('branches', 'accounts'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'item_code' => 'required|string|max:255|unique:manufacturing_items,item_code',
            'description' => 'nullable|string',
            'item_type' => 'required|string|in:raw_material,semi_finished_good,finished_good,service,asset,expense,other',
            'unit_of_measure' => 'required|string|max:50',
            'standard_cost' => 'nullable|numeric|min:0',
            'branch_id' => 'nullable|exists:branches,id',
            'account_id' => 'nullable|exists:accounts,id',
            'is_manufactured' => 'nullable|boolean',
            'is_purchased' => 'nullable|boolean',
            'is_sold' => 'nullable|boolean',
            'min_stock_level' => 'nullable|numeric|min:0',
            'max_stock_level' => 'nullable|numeric|min:0|gte:min_stock_level',
        ]);

        $item = ManufacturingItem::create($validatedData);

        // Here you might want to add logic for inventory and GL integration upon item creation if needed.
        // For example, creating an initial inventory record or a GL entry for standard cost if applicable.

        return redirect()->route('admin.manufacturing.items.index')->with('success', 'Manufacturing item created successfully.');
    }

    public function show(ManufacturingItem $item) // Changed variable name to $item for clarity
    {
        $item->load(['branch', 'account']);
        return view('admin.manufacturing.items.show', compact('item'));
    }

    public function edit(ManufacturingItem $item)
    {
        $branches = Branch::all();
        $accounts = Account::where('is_active', true)->get();
        return view('admin.manufacturing.items.edit', compact('item', 'branches', 'accounts'));
    }

    public function update(Request $request, ManufacturingItem $item)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'item_code' => 'required|string|max:255|unique:manufacturing_items,item_code,' . $item->id,
            'description' => 'nullable|string',
            'item_type' => 'required|string|in:raw_material,semi_finished_good,finished_good,service,asset,expense,other',
            'unit_of_measure' => 'required|string|max:50',
            'standard_cost' => 'nullable|numeric|min:0',
            'branch_id' => 'nullable|exists:branches,id',
            'account_id' => 'nullable|exists:accounts,id',
            'is_manufactured' => 'nullable|boolean',
            'is_purchased' => 'nullable|boolean',
            'is_sold' => 'nullable|boolean',
            'min_stock_level' => 'nullable|numeric|min:0',
            'max_stock_level' => 'nullable|numeric|min:0|gte:min_stock_level',
        ]);

        $item->update($validatedData);

        // Logic for inventory and GL integration upon item update if needed.

        return redirect()->route('admin.manufacturing.items.index')->with('success', 'Manufacturing item updated successfully.');
    }

    public function destroy(ManufacturingItem $item)
    {
        // Add logic here to check if the item is used in BOMs or Work Orders before deletion.
        // For now, a simple delete.
        try {
            $item->delete();
            return redirect()->route('admin.manufacturing.items.index')->with('success', 'Manufacturing item deleted successfully.');
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle potential foreign key constraint violations if item is in use
            return redirect()->route('admin.manufacturing.items.index')->with('error', 'Could not delete item. It might be in use.');
        }
    }
}

