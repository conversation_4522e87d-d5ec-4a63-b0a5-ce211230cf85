<li class="tree-node">
    <div class="tree-node-content @if($account->is_control_account) parent-node @endif">
        @if($account->children && $account->children->count() > 0)
            <span class="tree-node-toggle tree-toggle" data-bs-toggle="collapse" data-bs-target="#account-{{ $account->id }}">
                <i class="bi bi-chevron-down"></i>
            </span>
        @else
            <span class="tree-node-toggle" style="visibility: hidden;">
                <i class="bi bi-chevron-down"></i>
            </span>
        @endif

        <span class="tree-node-icon">
            @if($account->is_control_account)
                <i class="bi bi-folder2" style="color: #f6c23e;"></i>
            @else
                <i class="bi bi-file-earmark-text" style="color: #4e73df;"></i>
            @endif
        </span>

        <div class="tree-node-details">
            <span class="tree-node-code">{{ $account->code }}</span>
            <span class="tree-node-name">
                <span class="type-indicator type-{{ $account->accountType->slug }}"></span>
                {{ $account->name_ar }}
            </span>

            @php
                $balance = $account->opening_balance_debit - $account->opening_balance_credit;
                $balanceClass = $balance < 0 ? 'negative' : '';
            @endphp

            <span class="tree-node-balance {{ $balanceClass }}">
                {{ number_format(abs($balance), 2) }}
                <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
            </span>

            <div class="tree-node-actions">
                <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-info" title="عرض">
                    <i class="bi bi-eye"></i>
                </a>
                <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                    <i class="bi bi-pencil"></i>
                </a>
                @if(!$account->children || $account->children->count() == 0)
                    <form action="{{ route('admin.accounts.destroy', $account->id) }}" method="POST" style="display:inline-block;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    @if($account->children && $account->children->count() > 0)
        <div class="collapse show tree-children" id="account-{{ $account->id }}">
            <ul class="tree">
                @foreach($account->children as $child)
                    @include('admin.accounts._tree_node_new', ['account' => $child])
                @endforeach
            </ul>
        </div>
    @endif
</li>
