@extends('layouts.admin')

@section('title', 'إدارة المناديب')

@section('header')
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">
            <i class="bi bi-person-badge-fill text-primary me-2"></i>
            إدارة المناديب
        </h2>
        <a href="{{ route('admin.sales-representatives.representatives.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-lg me-1"></i>
            إضافة مندوب جديد
        </a>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المناديب</h6>
                            <h3 class="mb-0">{{ $representatives->total() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناديب النشطين</h6>
                            <h3 class="mb-0">{{ $representatives->where('status', 'active')->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناديب المعلقين</h6>
                            <h3 class="mb-0">{{ $representatives->where('status', 'suspended')->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-x-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المناديب غير النشطين</h6>
                            <h3 class="mb-0">{{ $representatives->where('status', 'inactive')->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-dash-fill fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.sales-representatives.representatives.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="اسم المندوب أو كود الموظف">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                            <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>معلق</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select class="form-select" id="branch_id" name="branch_id">
                            <option value="">جميع الفروع</option>
                            @foreach(\App\Models\Modules\Branches\Branch::where('is_active', true)->get() as $branch)
                                <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="commission_type" class="form-label">نوع العمولة</label>
                        <select class="form-select" id="commission_type" name="commission_type">
                            <option value="">جميع الأنواع</option>
                            <option value="percentage" {{ request('commission_type') == 'percentage' ? 'selected' : '' }}>نسبة مئوية</option>
                            <option value="fixed" {{ request('commission_type') == 'fixed' ? 'selected' : '' }}>مبلغ ثابت</option>
                            <option value="tiered" {{ request('commission_type') == 'tiered' ? 'selected' : '' }}>شرائح</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search me-1"></i>بحث
                            </button>
                            <a href="{{ route('admin.sales-representatives.representatives.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المناديب -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-table me-2"></i>
                قائمة المناديب
            </h5>
        </div>
        <div class="card-body">
            @if($representatives->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الصورة</th>
                                <th>كود الموظف</th>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>الفرع</th>
                                <th>الحالة</th>
                                <th>نوع العمولة</th>
                                <th>نسبة العمولة</th>
                                <th>المناطق المعينة</th>
                                <th>تاريخ التوظيف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($representatives as $representative)
                                <tr>
                                    <td>
                                        @if($representative->profile_image)
                                            <img src="{{ asset('storage/' . $representative->profile_image) }}" 
                                                 alt="{{ $representative->name }}" 
                                                 class="rounded-circle" 
                                                 width="40" height="40">
                                        @else
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                {{ substr($representative->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $representative->employee_code }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $representative->name }}</strong>
                                            @if($representative->email)
                                                <br><small class="text-muted">{{ $representative->email }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>{{ $representative->phone }}</td>
                                    <td>{{ $representative->branch->name ?? 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ $representative->status_color }}">
                                            @switch($representative->status)
                                                @case('active') نشط @break
                                                @case('inactive') غير نشط @break
                                                @case('suspended') معلق @break
                                                @default {{ $representative->status }}
                                            @endswitch
                                        </span>
                                    </td>
                                    <td>
                                        @switch($representative->commission_type)
                                            @case('percentage') نسبة مئوية @break
                                            @case('fixed') مبلغ ثابت @break
                                            @case('tiered') شرائح @break
                                            @default {{ $representative->commission_type }}
                                        @endswitch
                                    </td>
                                    <td>
                                        @if($representative->commission_type == 'percentage')
                                            {{ number_format($representative->commission_rate, 2) }}%
                                        @elseif($representative->commission_type == 'fixed')
                                            {{ number_format($representative->commission_rate, 2) }} ريال
                                        @else
                                            متغير
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ $representative->activeSalesAreas->count() }} منطقة
                                        </span>
                                    </td>
                                    <td>{{ $representative->hire_date->format('Y-m-d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.sales-representatives.representatives.show', $representative) }}" 
                                               class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.sales-representatives.representatives.dashboard', $representative) }}" 
                                               class="btn btn-sm btn-outline-primary" title="لوحة التحكم">
                                                <i class="bi bi-speedometer2"></i>
                                            </a>
                                            <a href="{{ route('admin.sales-representatives.representatives.edit', $representative) }}" 
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete({{ $representative->id }})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $representatives->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="bi bi-person-x display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">لا توجد مناديب</h5>
                    <p class="text-muted">لم يتم العثور على أي مناديب مطابقين لمعايير البحث.</p>
                    <a href="{{ route('admin.sales-representatives.representatives.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-lg me-1"></i>
                        إضافة مندوب جديد
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal for Delete Confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف هذا المندوب؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(representativeId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/sales-representatives/representatives/${representativeId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status, #branch_id, #commission_type');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
@endpush
