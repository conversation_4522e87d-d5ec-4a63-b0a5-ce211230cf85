@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($status) ? __("Edit Ticket Status") : __("Create Ticket Status") }}</h1>
    <form action="{{ isset($status) ? route("admin.ticketing.ticket_statuses.update", $status->id) : route("admin.ticketing.ticket_statuses.store") }}" method="POST">
        @csrf
        @if(isset($status))
            @method("PUT")
        @endif
        <div class="form-group">
            <label for="name">{{ __("Name") }}</label>
            <input type="text" name="name" id="name" class="form-control @error("name") is-invalid @enderror" value="{{ old("name", $status->name ?? "") }}" required>
            @error("name")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group">
            <label for="type">{{ __("Type") }}</label>
            <select name="type" id="type" class="form-control @error("type") is-invalid @enderror" required>
                <option value="open" {{ old("type", $status->type ?? "") == "open" ? "selected" : "" }}>{{ __("Open") }}</option>
                <option value="pending" {{ old("type", $status->type ?? "") == "pending" ? "selected" : "" }}>{{ __("Pending") }}</option>
                <option value="closed" {{ old("type", $status->type ?? "") == "closed" ? "selected" : "" }}>{{ __("Closed") }}</option>
            </select>
            @error("type")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group">
            <label for="color">{{ __("Color (e.g., #RRGGBB)") }}</label>
            <input type="text" name="color" id="color" class="form-control @error("color") is-invalid @enderror" value="{{ old("color", $status->color ?? "") }}">
            @error("color")
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_default_new" id="is_default_new" class="form-check-input" value="1" {{ old("is_default_new", $status->is_default_new ?? false) ? "checked" : "" }}>
            <label class="form-check-label" for="is_default_new">{{ __("Default for New Tickets") }}</label>
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_default_closed" id="is_default_closed" class="form-check-input" value="1" {{ old("is_default_closed", $status->is_default_closed ?? false) ? "checked" : "" }}>
            <label class="form-check-label" for="is_default_closed">{{ __("Default for Closed Tickets") }}</label>
        </div>
        <div class="form-group form-check">
            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old("is_active", $status->is_active ?? true) ? "checked" : "" }}>
            <label class="form-check-label" for="is_active">{{ __("Active") }}</label>
        </div>
        <button type="submit" class="btn btn-primary">{{ isset($status) ? __("Update Status") : __("Create Status") }}</button>
        <a href="{{ route("admin.ticketing.ticket_statuses.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

