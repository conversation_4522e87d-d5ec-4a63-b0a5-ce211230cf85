<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class ManufacturingWorkOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'manufacturing_work_orders';

    protected $fillable = [
        'work_order_number',
        'item_id', // Item to be produced
        'bom_id',
        'quantity_to_produce',
        'quantity_produced',
        'quantity_scrapped',
        'status',
        'planned_start_date',
        'planned_end_date',
        'actual_start_date',
        'actual_end_date',
        'notes',
        'branch_id',
        'created_by_id',
        'assigned_to_id',
    ];

    protected $casts = [
        'quantity_to_produce' => 'decimal:4',
        'quantity_produced' => 'decimal:4',
        'quantity_scrapped' => 'decimal:4',
        'planned_start_date' => 'datetime',
        'planned_end_date' => 'datetime',
        'actual_start_date' => 'datetime',
        'actual_end_date' => 'datetime',
    ];

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }

    public function bom()
    {
        return $this->belongsTo(ManufacturingBom::class, 'bom_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to_id');
    }

    public function workOrderItems()
    {
        return $this->hasMany(ManufacturingWorkOrderItem::class, 'work_order_id');
    }

    public function consumedItems()
    {
        return $this->hasMany(ManufacturingWorkOrderConsumedItem::class, 'work_order_id');
    }

    public function productionCosts()
    {
        return $this->hasMany(ManufacturingProductionCost::class, 'work_order_id');
    }
}

