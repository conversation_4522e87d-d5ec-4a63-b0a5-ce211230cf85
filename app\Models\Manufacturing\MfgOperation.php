<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class MfgOperation extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_operations';

    protected $fillable = [
        'op_code',
        'name',
        'description',
        'default_work_center_id',
        'standard_setup_time_hours',
        'standard_run_time_per_unit_hours',
        'standard_cost_per_hour',
        'instructions',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'standard_setup_time_hours' => 'decimal:2',
        'standard_run_time_per_unit_hours' => 'decimal:4',
        'standard_cost_per_hour' => 'decimal:4',
        'is_active' => 'boolean',
    ];

    /**
     * Get the default work center for this operation.
     */
    public function defaultWorkCenter()
    {
        return $this->belongsTo(MfgWorkCenter::class, 'default_work_center_id');
    }

    /**
     * Get the BOM items that consume this operation.
     */
    public function bomItems()
    {
        return $this->hasMany(MfgBomItem::class, 'operation_id');
    }

    /**
     * Get the routing steps that include this operation.
     */
    public function routingOperations()
    {
        return $this->hasMany(MfgRoutingOperation::class, 'mfg_operation_id');
    }

    /**
     * Get the production transactions related to this operation.
     */
    public function productionTransactions()
    {
        return $this->hasMany(MfgProductionTransaction::class, 'mfg_operation_id');
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

