<?php $__env->startSection("content"); ?>
<div class="container">
    <h1>ZATCA E-Invoicing Integration</h1>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <p>Manage and monitor the integration with ZATCA (Zakat, Tax and Customs Authority) for e-invoicing compliance.</p>

    <div class="card mb-3">
        <div class="card-header">Integration Status & Configuration</div>
        <div class="card-body">
            <p><strong>Current Phase:</strong>  Phase 2: Integration</p>
            <p><strong>Onboarding Status:</strong>  Completed</p>
            <p><strong>CSID (Cryptographic Stamp ID):</strong>  ABC123XYZ789...</p>
            <p><strong>Last Successful Submission:</strong>  2023-05-13 16:00:00</p>
            <a href="<?php echo e(route("admin.integrations.zatca.edit")); ?>" class="btn btn-warning btn-sm">Configure ZATCA Settings</a>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">Recent Activity</div>
        <div class="card-body">
            <ul>
                <li>Standard Invoice #INV-2023-005 submitted successfully. (Clearance ID: ZATCA-CLR-001)</li>
                <li>Simplified Invoice #SMI-2023-010 reported successfully.</li>
                <li>Credit Note #CRN-2023-002 for INV-2023-003 submitted successfully.</li>
            </ul>
            <a href="<?php echo e(route("admin.integrations.zatca.show")); ?>" class="btn btn-info btn-sm">View All Invoice Logs</a>
        </div>
    </div>

    

</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/zatca/index.blade.php ENDPATH**/ ?>