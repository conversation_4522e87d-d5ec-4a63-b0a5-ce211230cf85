@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($session) ? "Manage POS Session" : "Start New POS Session" }}</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ isset($session) ? route("admin.pos.sessions.update", $session->id) : route("admin.pos.sessions.store") }}" method="POST">
        @csrf
        @if(isset($session))
            @method("PUT")
        @endif

        <div class="form-group">
            <label for="pos_device_id">POS Device</label>
            <select name="pos_device_id" id="pos_device_id" class="form-control" required {{ isset($session) ? 'disabled' : '' }}>
                {{-- @foreach($devices as $device) --}}
                {{-- Replace with actual data --}}
                <option value="1" {{ old("pos_device_id", isset($session) && $session->pos_device_id == 1 ? "selected" : "") }}>Main Counter POS (ID: 1)</option>
                <option value="2" {{ old("pos_device_id", isset($session) && $session->pos_device_id == 2 ? "selected" : "") }}>Mobile POS 1 (ID: 2)</option>
                {{-- @endforeach --}}
            </select>
            @if(isset($session))
                <input type="hidden" name="pos_device_id" value="{{ $session->pos_device_id }}">
            @endif
        </div>

        <div class="form-group">
            <label for="user_id">User (Cashier)</label>
            <select name="user_id" id="user_id" class="form-control" required {{ isset($session) ? 'disabled' : '' }}>
                {{-- @foreach($users as $user) --}}
                {{-- Replace with actual data --}}
                <option value="1" {{ old("user_id", isset($session) && $session->user_id == 1 ? "selected" : "") }}>Cashier One (ID: 1)</option>
                <option value="2" {{ old("user_id", isset($session) && $session->user_id == 2 ? "selected" : "") }}>Cashier Two (ID: 2)</option>
                {{-- @endforeach --}}
            </select>
            @if(isset($session))
                <input type="hidden" name="user_id" value="{{ $session->user_id }}">
            @endif
        </div>

        <div class="form-group">
            <label for="opening_balance">Opening Balance</label>
            <input type="number" name="opening_balance" id="opening_balance" class="form-control" step="0.01" value="{{ old("opening_balance", isset($session) ? $session->opening_balance : "0.00") }}" required {{ isset($session) ? 'readonly' : '' }}>
        </div>

        @if(isset($session))
            <div class="form-group">
                <label for="closing_balance">Closing Balance (Calculated)</label>
                <input type="number" name="closing_balance" id="closing_balance" class="form-control" step="0.01" value="{{ old("closing_balance", isset($session) ? $session->closing_balance : "") }}" readonly>
            </div>
            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea name="notes" id="notes" class="form-control">{{ old("notes", isset($session) ? $session->notes : "") }}</textarea>
            </div>
        @endif

        <button type="submit" class="btn btn-success mt-3">{{ isset($session) ? "Update Session" : "Start Session" }}</button>
        <a href="{{ route("admin.pos.sessions.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

