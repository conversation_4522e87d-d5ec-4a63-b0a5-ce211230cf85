<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mfg_boms', function (Blueprint $table) {
            $table->id();
            $table->string('bom_code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('mfg_product_id'); // The product this BOM is for
            $table->foreign('mfg_product_id')->references('id')->on('mfg_products')->onDelete('cascade');
            $table->decimal('quantity_to_produce', 15, 4)->default(1.0000); // Default quantity of the parent product this BOM produces
            $table->string('unit_of_measure'); // Unit of measure for the quantity_to_produce
            $table->integer('version')->default(1);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Is this the default BOM for the product?
            $table->date('valid_from')->nullable();
            $table->date('valid_to')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['mfg_product_id', 'version'], 'mfg_product_version_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mfg_boms');
    }
};

