@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($payment) ? "Edit POS Payment" : "Add New POS Payment" }}</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ isset($payment) ? route("admin.pos.payments.update", $payment->id) : route("admin.pos.payments.store") }}" method="POST">
        @csrf
        @if(isset($payment))
            @method("PUT")
        @endif

        <div class="form-group">
            <label for="sale_id">Sale ID</label>
            <input type="number" name="sale_id" id="sale_id" class="form-control" value="{{ old("sale_id", isset($payment) ? $payment->sale_id : request("sale_id")) }}" required {{ isset($payment) ? "readonly" : ""}}>
            {{-- Alternatively, if creating from a sale page, $sale->id would be pre-filled and possibly hidden --}}
        </div>

        <div class="form-group">
            <label for="amount">Amount</label>
            <input type="number" name="amount" id="amount" class="form-control" step="0.01" value="{{ old("amount", isset($payment) ? $payment->amount : "") }}" required>
        </div>

        <div class="form-group">
            <label for="payment_method">Payment Method</label>
            <select name="payment_method" id="payment_method" class="form-control" required>
                <option value="cash" {{ old("payment_method", isset($payment) && $payment->payment_method == "cash" ? "selected" : "") }}>Cash</option>
                <option value="card" {{ old("payment_method", isset($payment) && $payment->payment_method == "card" ? "selected" : "") }}>Card</option>
                <option value="bnpl_tabby" {{ old("payment_method", isset($payment) && $payment->payment_method == "bnpl_tabby" ? "selected" : "") }}>BNPL (Tabby)</option>
                <option value="bnpl_tamara" {{ old("payment_method", isset($payment) && $payment->payment_method == "bnpl_tamara" ? "selected" : "") }}>BNPL (Tamara)</option>
                <option value="other" {{ old("payment_method", isset($payment) && $payment->payment_method == "other" ? "selected" : "") }}>Other</option>
                {{-- Add more specific payment methods as needed --}}
            </select>
        </div>

        <div class="form-group">
            <label for="payment_date">Payment Date</label>
            <input type="datetime-local" name="payment_date" id="payment_date" class="form-control" value="{{ old("payment_date", isset($payment) && $payment->payment_date ? optional($payment->payment_date)->format("Y-m-d\TH:i") : now()->format("Y-m-d\TH:i")) }}" required>
        </div>

        <div class="form-group">
            <label for="reference">Reference (e.g., Transaction ID, Cheque No)</label>
            <input type="text" name="reference" id="reference" class="form-control" value="{{ old("reference", isset($payment) ? $payment->reference : "") }}">
        </div>

        <div class="form-group">
            <label for="notes">Notes</label>
            <textarea name="notes" id="notes" class="form-control">{{ old("notes", isset($payment) ? $payment->notes : "") }}</textarea>
        </div>

        <button type="submit" class="btn btn-success mt-3">{{ isset($payment) ? "Update Payment" : "Save Payment" }}</button>
        <a href="{{ url()->previous() ?? route("admin.pos.payments.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

