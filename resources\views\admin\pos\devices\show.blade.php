@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Device Details</h1>

    {{-- Assuming $device variable is passed to this view --}}
    {{-- @if(!$device)
        <div class="alert alert-danger">Device not found.</div>
        <a href="{{ route("admin.pos.devices.index") }}" class="btn btn-primary">Back to List</a>
    @else --}}
        <table class="table table-bordered">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $device->id --}}1</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{-- $device->name --}}Main Counter POS</td>
                </tr>
                <tr>
                    <th>Type</th>
                    <td>{{-- ucfirst($device->type) --}}Fixed</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>{{-- ucfirst($device->status) --}}Active</td>
                </tr>
                <tr>
                    <th>Location</th>
                    <td>{{-- $device->location ?? 'N/A' --}}Store A</td>
                </tr>
                <tr>
                    <th>IP Address</th>
                    <td>{{-- $device->ip_address ?? 'N/A' --}}*************</td>
                </tr>
                <tr>
                    <th>Serial Number</th>
                    <td>{{-- $device->serial_number ?? 'N/A' --}}SN123456789</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{-- $device->created_at->format('Y-m-d H:i:s') --}}2023-01-15 10:30:00</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{-- $device->updated_at->format('Y-m-d H:i:s') --}}2023-01-16 11:00:00</td>
                </tr>
            </tbody>
        </table>

        <a href="{{ route("admin.pos.devices.edit", /*$device->id*/ 1) }}" class="btn btn-warning">Edit Device</a>
        <a href="{{ route("admin.pos.devices.index") }}" class="btn btn-secondary">Back to List</a>
    {{-- @endif --}}
</div>
@endsection

