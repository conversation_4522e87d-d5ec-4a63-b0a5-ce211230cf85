<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WorkOrder;
use App\Models\WorkOrderItem;
use App\Models\ProductionCost;
use App\Models\Item;
use App\Models\Bom;
use App\Models\Branch;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\JournalEntryDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class WorkOrderController extends Controller
{
    private function generateWorkOrderCode()
    {
        // Generate a unique work order code, e.g., WO-YYYYMMDD-XXXX
        $datePart = Carbon::now()->format("Ymd");
        $latestWorkOrder = WorkOrder::where("work_order_code", "LIKE", "WO-{$datePart}-%")->latest("id")->first();
        $nextNumber = 1;
        if ($latestWorkOrder) {
            $parts = explode("-", $latestWorkOrder->work_order_code);
            $lastNumber = intval(end($parts));
            $nextNumber = $lastNumber + 1;
        }
        return "WO-" . $datePart . "-" . str_pad($nextNumber, 4, "0", STR_PAD_LEFT);
    }

    public function index()
    {
        $workOrders = WorkOrder::with(["item", "bom", "branch"])->latest()->paginate(10);
        return view("admin.work_orders.index", compact("workOrders"));
    }

    public function create()
    {
        $manufacturableItems = Item::where("is_manufactured", true)->where("is_active", true)->with(["boms" => function($query) {
            $query->where("is_active", true);
        }])->get();
        $branches = Branch::where("is_active", true)->get();
        $newWorkOrderCode = $this->generateWorkOrderCode();
        return view("admin.work_orders.form", compact("manufacturableItems", "branches", "newWorkOrderCode"));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "work_order_code" => "required|string|max:255|unique:work_orders,work_order_code",
            "item_id" => "required|exists:items,id",
            "bom_id" => "required|exists:boms,id",
            "quantity_to_produce" => "required|numeric|min:0.0001",
            "branch_id" => "required|exists:branches,id",
            "start_date" => "nullable|date",
            "end_date" => "nullable|date|after_or_equal:start_date",
            "notes" => "nullable|string",
        ]);

        DB::beginTransaction();
        try {
            $workOrderData = $validatedData;
            $workOrderData["status"] = "planned"; // Initial status
            $workOrderData["created_by_user_id"] = Auth::id();

            $workOrder = WorkOrder::create($workOrderData);

            // Populate WorkOrderItems from BOM
            $bom = Bom::with("bomItems.item")->find($validatedData["bom_id"]);
            if ($bom && $bom->bomItems) {
                foreach ($bom->bomItems as $bomItem) {
                    WorkOrderItem::create([
                        "work_order_id" => $workOrder->id,
                        "item_id" => $bomItem->item_id,
                        "quantity_required" => ($bomItem->quantity_required / $bom->quantity_to_produce) * $workOrder->quantity_to_produce * (1 + ($bomItem->scrap_percentage / 100)),
                        "unit_of_measure_ar" => $bomItem->unit_of_measure_ar ?? $bomItem->item->unit_of_measure_ar,
                        "unit_of_measure_en" => $bomItem->unit_of_measure_en ?? $bomItem->item->unit_of_measure_en,
                        "cost" => $bomItem->item->standard_cost, // Or last_purchase_price
                    ]);
                }
            }

            DB::commit();
            return redirect()->route("admin.work_orders.index")->with("success", __("admin_messages.work_order_created_successfully"));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with("error", __("admin_messages.error_creating_work_order") . $e->getMessage());
        }
    }

    public function show(WorkOrder $workOrder)
    {
        $workOrder->load(["item", "bom", "branch", "workOrderItems.item", "productionCosts", "createdBy"]);
        return view("admin.work_orders.show", compact("workOrder"));
    }

    public function edit(WorkOrder $workOrder)
    {
        if ($workOrder->status !== "planned" && $workOrder->status !== "in_progress") {
            return redirect()->route("admin.work_orders.show", $workOrder->id)->with("error", __("admin_messages.work_order_cannot_be_edited"));
        }
        $workOrder->load("workOrderItems.item");
        $manufacturableItems = Item::where("is_manufactured", true)->where("is_active", true)->with(["boms" => function($query) {
            $query->where("is_active", true);
        }])->get();
        $branches = Branch::where("is_active", true)->get();
        return view("admin.work_orders.form", compact("workOrder", "manufacturableItems", "branches"));
    }

    public function update(Request $request, WorkOrder $workOrder)
    {
        if ($workOrder->status !== "planned" && $workOrder->status !== "in_progress" && $workOrder->status !== "completed") {
            return redirect()->route("admin.work_orders.show", $workOrder->id)->with("error", __("admin_messages.work_order_cannot_be_updated_status"));
        }

        $action = $request->input("action");

        DB::beginTransaction();
        try {
            if ($action == "start_production" && $workOrder->status == "planned") {
                $workOrder->update(["status" => "in_progress", "actual_start_date" => Carbon::now()]);
                // Logic for inventory movement (issue raw materials) can be added here
                // For each workOrderItem, decrease stock of item_id by quantity_required from branch_id warehouse
                // This would typically involve an InventoryTransaction model and service

            } elseif ($action == "complete_production" && $workOrder->status == "in_progress") {
                $validatedConsumed = $request->validate([
                    "consumed_items" => "sometimes|array",
                    "consumed_items.*.quantity_consumed" => "required_with:consumed_items|numeric|min:0",
                    "quantity_produced_so_far" => "nullable|numeric|min:0"
                ]);
                if ($request->has("consumed_items")) {
                    foreach($request->consumed_items as $woItemId => $data) {
                        $woItem = WorkOrderItem::find($woItemId);
                        if ($woItem && $woItem->work_order_id == $workOrder->id) {
                            $woItem->update(["quantity_consumed" => $data["quantity_consumed"]]);
                        }
                    }
                }
                $workOrder->update([
                    "status" => "completed", 
                    "actual_end_date" => Carbon::now(),
                    "quantity_produced_so_far" => $request->input("quantity_produced_so_far", $workOrder->quantity_produced_so_far)
                ]);

            } elseif ($action == "close_work_order" && $workOrder->status == "completed") {
                $validatedClose = $request->validate(["actual_quantity_produced" => "required|numeric|min:0"]);
                $workOrder->update([
                    "status" => "closed",
                    "actual_quantity_produced" => $validatedClose["actual_quantity_produced"]
                ]);
                // Post costs and inventory adjustments
                $this->postProductionTransactions($workOrder);

            } else { // Standard update for planned or in_progress (notes, dates etc.)
                $validatedData = $request->validate([
                    "item_id" => "required|exists:items,id", // Only if planned
                    "bom_id" => "required|exists:boms,id", // Only if planned
                    "quantity_to_produce" => "required|numeric|min:0.0001", // Only if planned
                    "branch_id" => "required|exists:branches,id", // Only if planned
                    "start_date" => "nullable|date",
                    "end_date" => "nullable|date|after_or_equal:start_date",
                    "notes" => "nullable|string",
                ]);
                if ($workOrder->status == "planned") {
                     $workOrder->update($validatedData);
                     // If item_id or bom_id or quantity_to_produce changed, re-evaluate WorkOrderItems
                     // For simplicity, this example doesn't rebuild them on edit, but a real system might.
                } else {
                    $workOrder->update($request->only(["notes", "start_date", "end_date"]));
                }
            }
            
            $workOrder->updated_by_user_id = Auth::id();
            $workOrder->save();

            DB::commit();
            return redirect()->route("admin.work_orders.show", $workOrder->id)->with("success", __("admin_messages.work_order_updated_successfully"));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with("error", __("admin_messages.error_updating_work_order") . $e->getMessage());
        }
    }

    protected function postProductionTransactions(WorkOrder $workOrder)
    {
        $workOrder->load("workOrderItems.item", "item.inventoryAccount", "item.cogsAccount", "branch");
        $transactionDate = Carbon::now();
        $totalMaterialCost = 0;

        // 1. Journal Entry for Consumed Materials
        $journalEntryMaterials = JournalEntry::create([
            "branch_id" => $workOrder->branch_id,
            "date" => $transactionDate,
            "narration_ar" => "تكلفة المواد المستخدمة لأمر العمل " . $workOrder->work_order_code,
            "narration_en" => "Material cost for Work Order " . $workOrder->work_order_code,
            "source_document_type" => WorkOrder::class,
            "source_document_id" => $workOrder->id,
            "created_by_user_id" => Auth::id(),
        ]);

        foreach ($workOrder->workOrderItems as $woItem) {
            $consumedCost = $woItem->quantity_consumed * $woItem->cost; // Assuming cost is per unit
            $totalMaterialCost += $consumedCost;

            if ($woItem->item && $woItem->item->inventoryAccount && $woItem->item->cogsAccount) {
                 // Debit: WIP Account (or COGS if direct, but WIP is more accurate for manufacturing)
                // For simplicity, using the produced item's COGS account as a proxy for WIP, or a dedicated WIP account should be configured.
                // Let's assume the produced item's COGS account is the WIP for now.
                JournalEntryDetail::create([
                    "journal_entry_id" => $journalEntryMaterials->id,
                    "account_id" => $workOrder->item->cogsAccount->id, // WIP Account (Debit)
                    "debit" => $consumedCost,
                    "credit" => 0,
                    "description_ar" => "استهلاك " . $woItem->item->name_ar,
                    "description_en" => "Consumption of " . $woItem->item->name_en,
                ]);
                // Credit: Raw Material Inventory Account
                JournalEntryDetail::create([
                    "journal_entry_id" => $journalEntryMaterials->id,
                    "account_id" => $woItem->item->inventoryAccount->id, // Raw Material Inventory (Credit)
                    "debit" => 0,
                    "credit" => $consumedCost,
                    "description_ar" => "إخراج " . $woItem->item->name_ar . " من المخزون",
                    "description_en" => "Issuing " . $woItem->item->name_en . " from inventory",
                ]);
            }
            // Here, also update actual stock levels for $woItem->item_id by -$woItem->quantity_consumed
        }

        // 2. Journal Entry for Finished Goods
        // Assume totalMaterialCost is the cost of goods manufactured for now (excluding labor/overhead for simplicity)
        $costOfGoodsManufactured = $totalMaterialCost; // Add labor & overhead later

        if ($workOrder->item && $workOrder->item->inventoryAccount && $workOrder->item->cogsAccount) {
            $journalEntryFG = JournalEntry::create([
                "branch_id" => $workOrder->branch_id,
                "date" => $transactionDate,
                "narration_ar" => "إنتاج بضاعة تامة الصنع لأمر العمل " . $workOrder->work_order_code,
                "narration_en" => "Finished Goods production for Work Order " . $workOrder->work_order_code,
                "source_document_type" => WorkOrder::class,
                "source_document_id" => $workOrder->id,
                "created_by_user_id" => Auth::id(),
            ]);

            // Debit: Finished Goods Inventory Account
            JournalEntryDetail::create([
                "journal_entry_id" => $journalEntryFG->id,
                "account_id" => $workOrder->item->inventoryAccount->id, // Finished Goods Inventory (Debit)
                "debit" => $costOfGoodsManufactured,
                "credit" => 0,
                "description_ar" => "إثبات " . $workOrder->item->name_ar . " كمنتج تام",
                "description_en" => "Recording " . $workOrder->item->name_en . " as finished good",
            ]);

            // Credit: WIP Account (or COGS if used as proxy)
            JournalEntryDetail::create([
                "journal_entry_id" => $journalEntryFG->id,
                "account_id" => $workOrder->item->cogsAccount->id, // WIP Account (Credit)
                "debit" => 0,
                "credit" => $costOfGoodsManufactured,
                "description_ar" => "تحويل من تحت التشغيل إلى تام الصنع",
                "description_en" => "Transfer from WIP to Finished Goods",
            ]);
            // Here, also update actual stock levels for $workOrder->item_id by +$workOrder->actual_quantity_produced
        }
        // Update work order total cost
        $workOrder->total_material_cost = $totalMaterialCost;
        // $workOrder->total_labor_cost = ... ;
        // $workOrder->total_overhead_cost = ... ;
        $workOrder->total_production_cost = $totalMaterialCost; // + labor + overhead
        $workOrder->save();
    }


    public function destroy(WorkOrder $workOrder)
    {
        if ($workOrder->status !== "planned") {
            return redirect()->route("admin.work_orders.index")->with("error", __("admin_messages.work_order_cannot_be_deleted_status"));
        }
        DB::beginTransaction();
        try {
            $workOrder->workOrderItems()->delete();
            $workOrder->productionCosts()->delete();
            $workOrder->delete();
            DB::commit();
            return redirect()->route("admin.work_orders.index")->with("success", __("admin_messages.work_order_deleted_successfully"));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.work_orders.index")->with("error", __("admin_messages.error_deleting_work_order") . $e->getMessage());
        }
    }
}

