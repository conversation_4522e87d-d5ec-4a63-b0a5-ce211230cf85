<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_targets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sales_representative_id')->constrained('sales_representatives')->onDelete('cascade');
            $table->enum('target_type', ['monthly', 'quarterly', 'yearly', 'custom'])->default('monthly');
            $table->integer('target_year'); // سنة الهدف
            $table->integer('target_month')->nullable(); // شهر الهدف
            $table->integer('target_quarter')->nullable(); // ربع الهدف
            $table->decimal('sales_target', 12, 2)->default(0); // هدف المبيعات
            $table->integer('visits_target')->default(0); // هدف الزيارات
            $table->integer('new_customers_target')->default(0); // هدف العملاء الجدد
            $table->decimal('collection_target', 12, 2)->default(0); // هدف التحصيل
            $table->decimal('achieved_sales', 12, 2)->default(0); // المبيعات المحققة
            $table->integer('achieved_visits')->default(0); // الزيارات المحققة
            $table->integer('achieved_new_customers')->default(0); // العملاء الجدد المحققين
            $table->decimal('achieved_collection', 12, 2)->default(0); // التحصيل المحقق
            $table->decimal('sales_achievement_percentage', 5, 2)->default(0); // نسبة إنجاز المبيعات
            $table->decimal('visits_achievement_percentage', 5, 2)->default(0); // نسبة إنجاز الزيارات
            $table->decimal('collection_achievement_percentage', 5, 2)->default(0); // نسبة إنجاز التحصيل
            $table->decimal('overall_achievement_percentage', 5, 2)->default(0); // نسبة الإنجاز الإجمالية
            $table->decimal('bonus_amount', 10, 2)->default(0); // مبلغ المكافأة
            $table->enum('status', ['active', 'completed', 'cancelled'])->default('active');
            $table->text('target_notes')->nullable(); // ملاحظات الهدف
            $table->date('start_date'); // تاريخ البداية
            $table->date('end_date'); // تاريخ النهاية
            $table->foreignId('set_by')->constrained('users')->onDelete('cascade'); // من وضع الهدف
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['sales_representative_id', 'target_type', 'target_year', 'target_month'], 'unique_rep_target');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_targets');
    }
};
