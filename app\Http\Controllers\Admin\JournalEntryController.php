<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JournalEntry;
use App\Models\JournalEntryDetail;
use App\Models\Account;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class JournalEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $journalEntries = JournalEntry::with(["branch", "createdBy"])->latest()->paginate(15);
        return view("admin.journal_entries.index", compact("journalEntries"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accounts = Account::where("is_active", true)->where("accepts_entries", true)->orderBy("name_" . app()->getLocale())->get();
        $branches = Branch::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        // Generate a new entry number (example: JE-YYYYMMDD-XXXX)
        $newEntryNumber = "JE-" . Carbon::now()->format("Ymd") . "-" . strtoupper(Str::random(4));
        return view("admin.journal_entries.form", compact("accounts", "branches", "newEntryNumber"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "entry_number" => "required|string|max:255|unique:journal_entries,entry_number",
            "entry_date" => "required|date",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "branch_id" => "nullable|exists:branches,id",
            "details" => "required|array|min:1",
            "details.*.account_id" => "required|exists:accounts,id,accepts_entries,1", // Ensure account accepts entries
            "details.*.debit" => "required|numeric|min:0",
            "details.*.credit" => "required|numeric|min:0",
            "details.*.description" => "nullable|string|max:255",
        ]);

        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($validatedData["details"] as $detail) {
            $totalDebit += (float)$detail["debit"];
            $totalCredit += (float)$detail["credit"];
        }

        if (abs($totalDebit - $totalCredit) > 0.005) { // Tolerance for float comparison
            return redirect()->back()->withErrors(["balance" => __("Journal entry does not balance!")])->withInput();
        }
        if ($totalDebit == 0 && $totalCredit == 0) {
             return redirect()->back()->withErrors(["balance" => __("Journal entry totals cannot both be zero.")])->withInput();
        }

        DB::beginTransaction();
        try {
            $journalEntry = JournalEntry::create([
                "entry_number" => $validatedData["entry_number"],
                "entry_date" => $validatedData["entry_date"],
                "description_ar" => $validatedData["description_ar"],
                "description_en" => $validatedData["description_en"],
                "branch_id" => $validatedData["branch_id"],
                "status" => "draft", // Default status
                "created_by_user_id" => Auth::id(),
                "total_debit" => $totalDebit,
                "total_credit" => $totalCredit,
            ]);

            foreach ($validatedData["details"] as $detailData) {
                JournalEntryDetail::create([
                    "journal_entry_id" => $journalEntry->id,
                    "account_id" => $detailData["account_id"],
                    "debit" => (float)$detailData["debit"],
                    "credit" => (float)$detailData["credit"],
                    // Assuming the description from detail form is bilingual or primary
                    "description_ar" => $detailData["description"], 
                    "description_en" => $detailData["description"], 
                ]);
            }

            DB::commit();
            return redirect()->route("admin.journal_entries.index")->with("success", __("Journal entry created successfully as draft."));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with("error", __("Failed to create journal entry: ") . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(JournalEntry $journalEntry)
    {
        $journalEntry->load(["details.account", "branch", "createdBy", "postedBy"]);
        return view("admin.journal_entries.show", compact("journalEntry"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", __("Only draft entries can be edited."));
        }
        $journalEntry->load("details");
        $accounts = Account::where("is_active", true)->where("accepts_entries", true)->orderBy("name_" . app()->getLocale())->get();
        $branches = Branch::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        return view("admin.journal_entries.form", compact("journalEntry", "accounts", "branches"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->back()->with("error", __("Only draft entries can be updated."))->withInput();
        }

        $validatedData = $request->validate([
            // entry_number is not updatable for simplicity, or add logic if needed
            "entry_date" => "required|date",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "branch_id" => "nullable|exists:branches,id",
            "details" => "required|array|min:1",
            "details.*.id" => "nullable|exists:journal_entry_details,id,journal_entry_id," . $journalEntry->id, // For existing details
            "details.*.account_id" => "required|exists:accounts,id,accepts_entries,1",
            "details.*.debit" => "required|numeric|min:0",
            "details.*.credit" => "required|numeric|min:0",
            "details.*.description" => "nullable|string|max:255",
        ]);

        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($validatedData["details"] as $detail) {
            $totalDebit += (float)$detail["debit"];
            $totalCredit += (float)$detail["credit"];
        }

        if (abs($totalDebit - $totalCredit) > 0.005) {
            return redirect()->back()->withErrors(["balance" => __("Journal entry does not balance!")])->withInput();
        }
         if ($totalDebit == 0 && $totalCredit == 0) {
             return redirect()->back()->withErrors(["balance" => __("Journal entry totals cannot both be zero.")])->withInput();
        }

        DB::beginTransaction();
        try {
            $journalEntry->update([
                "entry_date" => $validatedData["entry_date"],
                "description_ar" => $validatedData["description_ar"],
                "description_en" => $validatedData["description_en"],
                "branch_id" => $validatedData["branch_id"],
                "total_debit" => $totalDebit,
                "total_credit" => $totalCredit,
                // created_by_user_id remains the same
            ]);

            $existingDetailIds = $journalEntry->details->pluck("id")->toArray();
            $submittedDetailIds = [];

            foreach ($validatedData["details"] as $detailData) {
                $detailId = $detailData["id"] ?? null;
                if ($detailId) {
                    $submittedDetailIds[] = (int)$detailId;
                    $detailToUpdate = JournalEntryDetail::find($detailId);
                    if ($detailToUpdate) {
                        $detailToUpdate->update([
                            "account_id" => $detailData["account_id"],
                            "debit" => (float)$detailData["debit"],
                            "credit" => (float)$detailData["credit"],
                            "description_ar" => $detailData["description"],
                            "description_en" => $detailData["description"],
                        ]);
                    }
                } else {
                    // New detail
                    JournalEntryDetail::create([
                        "journal_entry_id" => $journalEntry->id,
                        "account_id" => $detailData["account_id"],
                        "debit" => (float)$detailData["debit"],
                        "credit" => (float)$detailData["credit"],
                        "description_ar" => $detailData["description"],
                        "description_en" => $detailData["description"],
                    ]);
                }
            }
            
            // Delete details that were removed from the form
            $detailsToDelete = array_diff($existingDetailIds, $submittedDetailIds);
            if (!empty($detailsToDelete)) {
                JournalEntryDetail::destroy($detailsToDelete);
            }

            DB::commit();
            return redirect()->route("admin.journal_entries.index")->with("success", __("Journal entry updated successfully."));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with("error", __("Failed to update journal entry: ") . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", __("Only draft entries can be deleted."));
        }

        DB::beginTransaction();
        try {
            $journalEntry->details()->delete(); // Delete related details first
            $journalEntry->delete();
            DB::commit();
            return redirect()->route("admin.journal_entries.index")->with("success", __("Journal entry deleted successfully."));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.journal_entries.index")->with("error", __("Failed to delete journal entry: ") . $e->getMessage());
        }
    }

    /**
     * Post a draft journal entry.
     */
    public function postEntry(Request $request, JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", __("Only draft entries can be posted."));
        }

        // Recalculate totals to ensure consistency before posting
        $totalDebit = $journalEntry->details()->sum("debit");
        $totalCredit = $journalEntry->details()->sum("credit");

        if (abs($totalDebit - $totalCredit) > 0.005) {
            return redirect()->route("admin.journal_entries.show", $journalEntry->id)->with("error", __("Cannot post: Journal entry does not balance!"));
        }
        if ($totalDebit == 0 && $totalCredit == 0 && $journalEntry->details()->count() > 0) { // Allow zero total if no details, but not if details exist and sum to zero
             return redirect()->route("admin.journal_entries.show", $journalEntry->id)->with("error", __("Cannot post: Journal entry totals are zero with details present."));
        }
        if ($journalEntry->details()->count() === 0) {
            return redirect()->route("admin.journal_entries.show", $journalEntry->id)->with("error", __("Cannot post: Journal entry has no details."));
        }

        DB::beginTransaction();
        try {
            $journalEntry->status = "posted";
            $journalEntry->posted_by_user_id = Auth::id();
            $journalEntry->posted_at = Carbon::now();
            $journalEntry->total_debit = $totalDebit; // Update totals just in case
            $journalEntry->total_credit = $totalCredit;
            $journalEntry->save();

            // Here you would typically update account balances. This is a complex part
            // and depends on how balances are stored (e.g., a separate balances table, or calculated on the fly).
            // For now, we are just marking the entry as posted.
            // Example (conceptual - actual implementation would be more robust):
            /*
            foreach ($journalEntry->details as $detail) {
                $account = Account::find($detail->account_id);
                // This is a simplified balance update. Real systems have more complex logic for periods, etc.
                // $account->balance += ($detail->debit - $detail->credit); 
                // $account->save();
            }
            */

            DB::commit();
            return redirect()->route("admin.journal_entries.show", $journalEntry->id)->with("success", __("Journal entry posted successfully."));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.journal_entries.show", $journalEntry->id)->with("error", __("Failed to post journal entry: ") . $e->getMessage());
        }
    }
}

