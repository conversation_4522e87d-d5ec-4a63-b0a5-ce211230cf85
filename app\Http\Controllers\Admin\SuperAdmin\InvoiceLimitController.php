<?php

namespace App\Http\Controllers\Admin\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\InvoiceLimit;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use Illuminate\Http\Request;

class InvoiceLimitController extends Controller
{
    /**
     * عرض صفحة حدود الفواتير
     */
    public function index()
    {
        $plans = SubscriptionPlan::with('subscriptions')->get();
        $invoiceLimits = InvoiceLimit::with('subscriptionPlan')->get();
        
        return view('admin.super_admin.invoice_limits.index', compact('plans', 'invoiceLimits'));
    }

    /**
     * تحديث حدود الفواتير
     */
    public function update(Request $request)
    {
        $validatedData = $request->validate([
            'limits' => 'required|array',
            'limits.*.subscription_plan_id' => 'required|exists:subscription_plans,id',
            'limits.*.monthly_invoice_limit' => 'required|integer|min:1',
            'limits.*.total_invoice_limit' => 'nullable|integer|min:0',
            'limits.*.is_active' => 'boolean',
        ]);

        foreach ($validatedData['limits'] as $limitData) {
            $invoiceLimit = InvoiceLimit::where('subscription_plan_id', $limitData['subscription_plan_id'])->first();
            
            if ($invoiceLimit) {
                $invoiceLimit->update([
                    'monthly_invoice_limit' => $limitData['monthly_invoice_limit'],
                    'total_invoice_limit' => $limitData['total_invoice_limit'] ?? null,
                    'is_active' => isset($limitData['is_active']) ? true : false,
                ]);
            } else {
                InvoiceLimit::create([
                    'subscription_plan_id' => $limitData['subscription_plan_id'],
                    'monthly_invoice_limit' => $limitData['monthly_invoice_limit'],
                    'total_invoice_limit' => $limitData['total_invoice_limit'] ?? null,
                    'is_active' => isset($limitData['is_active']) ? true : false,
                ]);
            }
        }

        return redirect()->route('admin.super_admin.invoice_limits.index')
            ->with('success', 'تم تحديث حدود الفواتير بنجاح');
    }
}
