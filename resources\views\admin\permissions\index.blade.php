@extends("layouts.admin")

@section("title", "إدارة الصلاحيات")

@section("header", "إدارة الصلاحيات")

@section("content")
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="admin-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-key text-primary me-2"></i>إدارة الصلاحيات</h5>
                        <a href="{{ route("admin.permissions_system.permissions.create") }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i> إضافة صلاحية جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @if (session("success"))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle-fill fs-4 me-2"></i>
                    <div>{{ session("success") }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session("error"))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                    <div>{{ session("error") }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-12">
                <div class="admin-card">
                    <div class="table-responsive">
                        <table id="permissions-table" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>الاسم</th>
                                    <th>المعرف</th>
                                    <th>المجموعة</th>
                                    <th>الوصف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($permissions as $permission)
                                    <tr>
                                        <td>{{ $permission->id }}</td>
                                        <td>{{ $permission->name }}</td>
                                        <td>{{ $permission->slug }}</td>
                                        <td>{{ $permission->group_name }}</td>
                                        <td>{{ $permission->description }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route("admin.permissions_system.permissions.show", $permission->id) }}" class="btn btn-info btn-sm">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route("admin.permissions_system.permissions.edit", $permission->id) }}" class="btn btn-warning btn-sm">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route("admin.permissions_system.permissions.destroy", $permission->id) }}" method="POST" style="display:inline-block;">
                                                    @csrf
                                                    @method("DELETE")
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد صلاحيات مسجلة.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // تهيئة DataTables بإعدادات بسيطة
        $('#permissions-table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
            },
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": 5 } // تعطيل الترتيب للعمود الإجراءات
            ]
        });
    });
</script>
@endpush

