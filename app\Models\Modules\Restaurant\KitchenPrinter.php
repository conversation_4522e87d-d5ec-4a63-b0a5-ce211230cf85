<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class KitchenPrinter extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'ip_address',
        'port',
        'type',
        'connection_type',
        'driver',
        'paper_width',
        'auto_cut',
        'is_active',
        'settings',
        'kitchen_station_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'port' => 'integer',
        'paper_width' => 'integer',
        'auto_cut' => 'boolean',
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Get the kitchen station that owns the printer.
     */
    public function kitchenStation(): BelongsTo
    {
        return $this->belongsTo(KitchenStation::class, 'kitchen_station_id');
    }

    /**
     * Get the branch that owns the printer.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the printer.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Test printer connection.
     */
    public function testConnection(): bool
    {
        try {
            if ($this->connection_type === 'network') {
                $socket = @fsockopen($this->ip_address, $this->port, $errno, $errstr, 5);
                if ($socket) {
                    fclose($socket);
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Print order to kitchen.
     */
    public function printOrder($orderData): bool
    {
        try {
            // Implementation for printing order
            // This would format the order data and send to printer
            return true;
        } catch (\Exception $e) {
            \Log::error('Kitchen printer error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Scope a query to only include active printers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get printer status color.
     */
    public function getStatusColorAttribute(): string
    {
        return $this->is_active ? '#28a745' : '#dc3545';
    }
}
