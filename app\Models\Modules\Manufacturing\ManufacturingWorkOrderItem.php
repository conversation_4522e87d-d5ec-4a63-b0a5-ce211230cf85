<?php

namespace App\Models\Modules\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManufacturingWorkOrderItem extends Model
{
    use HasFactory;

    protected $table = 'manufacturing_work_order_items';

    protected $fillable = [
        'work_order_id',
        'item_id',
        'item_direction', // 'input' or 'output'
        'quantity_planned',
        'quantity_actual',
        'unit_of_measure',
        'notes',
        // 'work_order_operation_id', // If linking to specific operations
    ];

    protected $casts = [
        'quantity_planned' => 'decimal:4',
        'quantity_actual' => 'decimal:4',
    ];

    public function workOrder()
    {
        return $this->belongsTo(ManufacturingWorkOrder::class, 'work_order_id');
    }

    public function item()
    {
        return $this->belongsTo(ManufacturingItem::class, 'item_id');
    }
}

