<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PermissionGroup;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مجموعات الصلاحيات
        $groups = [
            [
                'name' => 'users',
                'display_name' => 'إدارة المستخدمين',
                'description' => 'صلاحيات إدارة المستخدمين والأدوار والصلاحيات',
                'order' => 1,
            ],
            [
                'name' => 'branches',
                'display_name' => 'إدارة الفروع',
                'description' => 'صلاحيات إدارة الفروع',
                'order' => 2,
            ],
            [
                'name' => 'accounting',
                'display_name' => 'المحاسبة',
                'description' => 'صلاحيات المحاسبة والحسابات',
                'order' => 3,
            ],
            [
                'name' => 'sales',
                'display_name' => 'المبيعات',
                'description' => 'صلاحيات المبيعات والفواتير',
                'order' => 4,
            ],
            [
                'name' => 'purchases',
                'display_name' => 'المشتريات',
                'description' => 'صلاحيات المشتريات والموردين',
                'order' => 5,
            ],
            [
                'name' => 'inventory',
                'display_name' => 'المخزون',
                'description' => 'صلاحيات إدارة المخزون والأصناف',
                'order' => 6,
            ],
            [
                'name' => 'reports',
                'display_name' => 'التقارير',
                'description' => 'صلاحيات عرض وطباعة التقارير',
                'order' => 7,
            ],
            [
                'name' => 'settings',
                'display_name' => 'الإعدادات',
                'description' => 'صلاحيات إدارة إعدادات النظام',
                'order' => 8,
            ],
            [
                'name' => 'subscriptions',
                'display_name' => 'الاشتراكات',
                'description' => 'صلاحيات إدارة الاشتراكات والمدفوعات',
                'order' => 9,
            ],
        ];

        foreach ($groups as $group) {
            PermissionGroup::create($group);
        }

        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            [
                'permission_group_id' => 1,
                'name' => 'users.view',
                'display_name' => 'عرض المستخدمين',
                'description' => 'عرض قائمة المستخدمين وتفاصيلهم',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'users.create',
                'display_name' => 'إضافة مستخدم',
                'description' => 'إضافة مستخدم جديد',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'users.edit',
                'display_name' => 'تعديل مستخدم',
                'description' => 'تعديل بيانات المستخدمين',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'users.delete',
                'display_name' => 'حذف مستخدم',
                'description' => 'حذف المستخدمين',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'roles.view',
                'display_name' => 'عرض الأدوار',
                'description' => 'عرض قائمة الأدوار وتفاصيلها',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'roles.create',
                'display_name' => 'إضافة دور',
                'description' => 'إضافة دور جديد',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'roles.edit',
                'display_name' => 'تعديل دور',
                'description' => 'تعديل بيانات الأدوار',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'roles.delete',
                'display_name' => 'حذف دور',
                'description' => 'حذف الأدوار',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'permissions.view',
                'display_name' => 'عرض الصلاحيات',
                'description' => 'عرض قائمة الصلاحيات وتفاصيلها',
            ],
            [
                'permission_group_id' => 1,
                'name' => 'permissions.manage',
                'display_name' => 'إدارة الصلاحيات',
                'description' => 'إدارة الصلاحيات وتعيينها للأدوار والمستخدمين',
            ],

            // صلاحيات الفروع
            [
                'permission_group_id' => 2,
                'name' => 'branches.view',
                'display_name' => 'عرض الفروع',
                'description' => 'عرض قائمة الفروع وتفاصيلها',
            ],
            [
                'permission_group_id' => 2,
                'name' => 'branches.create',
                'display_name' => 'إضافة فرع',
                'description' => 'إضافة فرع جديد',
            ],
            [
                'permission_group_id' => 2,
                'name' => 'branches.edit',
                'display_name' => 'تعديل فرع',
                'description' => 'تعديل بيانات الفروع',
            ],
            [
                'permission_group_id' => 2,
                'name' => 'branches.delete',
                'display_name' => 'حذف فرع',
                'description' => 'حذف الفروع',
            ],

            // صلاحيات المحاسبة
            [
                'permission_group_id' => 3,
                'name' => 'accounts.view',
                'display_name' => 'عرض الحسابات',
                'description' => 'عرض شجرة الحسابات وتفاصيلها',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'accounts.create',
                'display_name' => 'إضافة حساب',
                'description' => 'إضافة حساب جديد',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'accounts.edit',
                'display_name' => 'تعديل حساب',
                'description' => 'تعديل بيانات الحسابات',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'accounts.delete',
                'display_name' => 'حذف حساب',
                'description' => 'حذف الحسابات',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'journal_entries.view',
                'display_name' => 'عرض القيود المحاسبية',
                'description' => 'عرض القيود المحاسبية وتفاصيلها',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'journal_entries.create',
                'display_name' => 'إضافة قيد محاسبي',
                'description' => 'إضافة قيد محاسبي جديد',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'journal_entries.edit',
                'display_name' => 'تعديل قيد محاسبي',
                'description' => 'تعديل القيود المحاسبية',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'journal_entries.delete',
                'display_name' => 'حذف قيد محاسبي',
                'description' => 'حذف القيود المحاسبية',
            ],
            [
                'permission_group_id' => 3,
                'name' => 'journal_entries.post',
                'display_name' => 'ترحيل القيود المحاسبية',
                'description' => 'ترحيل القيود المحاسبية',
            ],

            // صلاحيات الاشتراكات
            [
                'permission_group_id' => 9,
                'name' => 'subscriptions.view',
                'display_name' => 'عرض الاشتراكات',
                'description' => 'عرض قائمة الاشتراكات وتفاصيلها',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscriptions.create',
                'display_name' => 'إضافة اشتراك',
                'description' => 'إضافة اشتراك جديد',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscriptions.edit',
                'display_name' => 'تعديل اشتراك',
                'description' => 'تعديل بيانات الاشتراكات',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscriptions.delete',
                'display_name' => 'حذف اشتراك',
                'description' => 'حذف الاشتراكات',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscription_plans.manage',
                'display_name' => 'إدارة خطط الاشتراكات',
                'description' => 'إدارة خطط الاشتراكات وأسعارها',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscription_payments.view',
                'display_name' => 'عرض مدفوعات الاشتراكات',
                'description' => 'عرض قائمة مدفوعات الاشتراكات وتفاصيلها',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscription_payments.create',
                'display_name' => 'إضافة مدفوعة اشتراك',
                'description' => 'إضافة مدفوعة اشتراك جديدة',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscription_payments.edit',
                'display_name' => 'تعديل مدفوعة اشتراك',
                'description' => 'تعديل بيانات مدفوعات الاشتراكات',
            ],
            [
                'permission_group_id' => 9,
                'name' => 'subscription_payments.delete',
                'display_name' => 'حذف مدفوعة اشتراك',
                'description' => 'حذف مدفوعات الاشتراكات',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // إنشاء الأدوار
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => 'مدير النظام',
            'description' => 'دور مدير النظام مع كافة الصلاحيات',
            'is_system' => true,
        ]);

        $userRole = Role::create([
            'name' => 'user',
            'display_name' => 'مستخدم',
            'description' => 'دور المستخدم العادي',
            'is_system' => true,
        ]);

        // إعطاء جميع الصلاحيات لدور المدير
        $adminRole->permissions()->sync(Permission::all()->pluck('id')->toArray());

        // إعطاء صلاحيات محددة لدور المستخدم
        $userPermissions = Permission::whereIn('name', [
            'accounts.view',
            'journal_entries.view',
            'branches.view',
            'users.view',
        ])->pluck('id')->toArray();

        $userRole->permissions()->sync($userPermissions);

        // تعيين دور المدير للمستخدم الأول
        $admin = User::first();
        if ($admin) {
            $admin->roles()->sync([$adminRole->id]);
        }
    }
}
