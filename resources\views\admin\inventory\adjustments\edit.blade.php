@extends('layouts.admin')

@section('title', 'تعديل تسوية المخزون')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل تسوية المخزون</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.inventory.adjustments.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <a href="{{ route('admin.inventory.adjustments.show', 1) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.inventory.adjustments.update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_number">رقم التسوية</label>
                                    <input type="text" class="form-control" id="adjustment_number" name="adjustment_number" value="ADJ-2024-001" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_date">تاريخ التسوية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="adjustment_date" name="adjustment_date" value="2024-03-15" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">المستودع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">اختر المستودع</option>
                                        <option value="1" selected>المستودع الرئيسي</option>
                                        <option value="2">مستودع الفرع الثاني</option>
                                        <option value="3">مستودع المواد الخام</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_type">نوع التسوية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="adjustment_type" name="adjustment_type" required>
                                        <option value="">اختر نوع التسوية</option>
                                        <option value="increase" selected>زيادة في المخزون</option>
                                        <option value="decrease">نقص في المخزون</option>
                                        <option value="damage">أصناف تالفة</option>
                                        <option value="expired">أصناف منتهية الصلاحية</option>
                                        <option value="lost">أصناف مفقودة</option>
                                        <option value="found">أصناف موجودة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="reason">سبب التسوية <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="reason" name="reason" rows="3" required>تم العثور على كميات إضافية من الأصناف أثناء الجرد الدوري للمستودع الرئيسي. هذه الكميات لم تكن مسجلة في النظام بسبب خطأ في عملية الاستلام السابقة.</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الأصناف -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>أصناف التسوية</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="adjustmentItemsTable">
                                        <thead>
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية الحالية</th>
                                                <th>الكمية الجديدة</th>
                                                <th>الفرق</th>
                                                <th>السعر</th>
                                                <th>القيمة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[0][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1" selected>لابتوب ديل XPS 13</option>
                                                        <option value="2">ماوس لاسلكي لوجيتك</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][current_quantity]" value="10" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][new_quantity]" value="12" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][difference]" value="2" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][unit_price]" value="3500" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][total_value]" value="7000" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[1][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1">لابتوب ديل XPS 13</option>
                                                        <option value="2" selected>ماوس لاسلكي لوجيتك</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][current_quantity]" value="25" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][new_quantity]" value="28" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][difference]" value="3" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][unit_price]" value="150" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[1][total_value]" value="450" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addRow()">
                                    <i class="fas fa-plus"></i> إضافة صنف
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_value">القيمة الإجمالية</label>
                                    <input type="number" class="form-control" id="total_value" name="total_value" value="7450" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="pending" selected>معلق</option>
                                        <option value="approved">معتمد</option>
                                        <option value="rejected">مرفوض</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('admin.inventory.adjustments.show', 1) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let rowIndex = 2;

function addRow() {
    const tbody = document.querySelector('#adjustmentItemsTable tbody');
    const newRow = `
        <tr>
            <td>
                <select class="form-control" name="items[${rowIndex}][item_id]" required>
                    <option value="">اختر الصنف</option>
                    <option value="1">لابتوب ديل XPS 13</option>
                    <option value="2">ماوس لاسلكي لوجيتك</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][current_quantity]" readonly>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][new_quantity]" required>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][difference]" readonly>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][unit_price]" step="0.01" required>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][total_value]" step="0.01" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', newRow);
    rowIndex++;
}

function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotal();
}

function calculateTotal() {
    let total = 0;
    const totalValueInputs = document.querySelectorAll('input[name*="[total_value]"]');
    totalValueInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    document.getElementById('total_value').value = total.toFixed(2);
}

// حساب الفرق والقيمة عند تغيير الكميات والأسعار
document.addEventListener('input', function(e) {
    if (e.target.name && e.target.name.includes('[new_quantity]')) {
        const row = e.target.closest('tr');
        const currentQty = parseFloat(row.querySelector('input[name*="[current_quantity]"]').value) || 0;
        const newQty = parseFloat(e.target.value) || 0;
        const difference = newQty - currentQty;
        row.querySelector('input[name*="[difference]"]').value = difference;

        const unitPrice = parseFloat(row.querySelector('input[name*="[unit_price]"]').value) || 0;
        const totalValue = Math.abs(difference) * unitPrice;
        row.querySelector('input[name*="[total_value]"]').value = totalValue.toFixed(2);

        calculateTotal();
    }

    if (e.target.name && e.target.name.includes('[unit_price]')) {
        const row = e.target.closest('tr');
        const difference = parseFloat(row.querySelector('input[name*="[difference]"]').value) || 0;
        const unitPrice = parseFloat(e.target.value) || 0;
        const totalValue = Math.abs(difference) * unitPrice;
        row.querySelector('input[name*="[total_value]"]').value = totalValue.toFixed(2);

        calculateTotal();
    }
});

// حساب الإجمالي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>
@endpush
