<?php

namespace App\Http\Controllers\Modules\GeneralLedger;

use App\Http\Controllers\Controller;
use App\Models\Modules\GeneralLedger\JournalEntry;
use App\Models\Modules\GeneralLedger\JournalEntryItem;
use App\Models\Modules\GeneralLedger\Account;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class JournalEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $journalEntries = JournalEntry::with(["branch", "createdBy"])->latest()->paginate(10);
        return view("admin.journal_entries.index", compact("journalEntries"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accounts = Account::where("is_active", true)->orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        $nextEntryNumber = JournalEntry::getNextEntryNumber();
        return view("admin.journal_entries.create", compact("accounts", "branches", "nextEntryNumber"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "entry_number" => "required|string|unique:journal_entries,entry_number",
            "entry_date" => "required|date",
            "branch_id" => "nullable|exists:branches,id",
            "description" => "nullable|string",
            "items" => "required|array|min:2", // Must have at least two items for a balanced entry
            "items.*.account_id" => "required|exists:accounts,id",
            "items.*.debit_amount" => "required|numeric|min:0",
            "items.*.credit_amount" => "required|numeric|min:0",
            "items.*.description" => "nullable|string",
        ]);

        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($request->items as $item) {
            $totalDebit += (float)$item["debit_amount"];
            $totalCredit += (float)$item["credit_amount"];
        }

        if (round($totalDebit, 2) !== round($totalCredit, 2)) {
            return redirect()->back()->withInput()->withErrors(["balance" => "Total debit and credit amounts must be equal."]);
        }
        if (round($totalDebit, 2) == 0) {
             return redirect()->back()->withInput()->withErrors(["balance" => "Total debit and credit amounts cannot be zero."]);
        }

        DB::beginTransaction();
        try {
            $journalEntry = JournalEntry::create([
                "entry_number" => $request->entry_number,
                "entry_date" => $request->entry_date,
                "branch_id" => $request->branch_id,
                "description" => $request->description,
                "status" => "draft",
                "created_by_id" => Auth::id(),
            ]);

            foreach ($request->items as $itemData) {
                if (((float)$itemData["debit_amount"] > 0 || (float)$itemData["credit_amount"] > 0)) {
                    $journalEntry->items()->create([
                        "account_id" => $itemData["account_id"],
                        "debit_amount" => (float)$itemData["debit_amount"],
                        "credit_amount" => (float)$itemData["credit_amount"],
                        "description" => $itemData["description"],
                    ]);
                }
            }

            DB::commit();
            return redirect()->route("admin.journal_entries.index")
                             ->with("success", "Journal entry created successfully as draft.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->withErrors(["error" => "Failed to create journal entry: " . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(JournalEntry $journalEntry)
    {
        $journalEntry->load(["items.account", "branch", "createdBy", "postedBy"]);
        return view("admin.journal_entries.show", compact("journalEntry"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", "Only draft entries can be edited.");
        }
        $accounts = Account::where("is_active", true)->orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        $journalEntry->load("items");
        return view("admin.journal_entries.edit", compact("journalEntry", "accounts", "branches"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", "Only draft entries can be updated.");
        }

        $request->validate([
            "entry_date" => "required|date",
            "branch_id" => "nullable|exists:branches,id",
            "description" => "nullable|string",
            "items" => "required|array|min:2",
            "items.*.account_id" => "required|exists:accounts,id",
            "items.*.debit_amount" => "required|numeric|min:0",
            "items.*.credit_amount" => "required|numeric|min:0",
            "items.*.description" => "nullable|string",
        ]);

        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($request->items as $item) {
            $totalDebit += (float)$item["debit_amount"];
            $totalCredit += (float)$item["credit_amount"];
        }

        if (round($totalDebit, 2) !== round($totalCredit, 2)) {
            return redirect()->back()->withInput()->withErrors(["balance" => "Total debit and credit amounts must be equal."]);
        }
        if (round($totalDebit, 2) == 0) {
             return redirect()->back()->withInput()->withErrors(["balance" => "Total debit and credit amounts cannot be zero."]);
        }

        DB::beginTransaction();
        try {
            $journalEntry->update([
                "entry_date" => $request->entry_date,
                "branch_id" => $request->branch_id,
                "description" => $request->description,
            ]);

            // Remove old items and add new ones
            $journalEntry->items()->delete();
            foreach ($request->items as $itemData) {
                 if (((float)$itemData["debit_amount"] > 0 || (float)$itemData["credit_amount"] > 0)) {
                    $journalEntry->items()->create([
                        "account_id" => $itemData["account_id"],
                        "debit_amount" => (float)$itemData["debit_amount"],
                        "credit_amount" => (float)$itemData["credit_amount"],
                        "description" => $itemData["description"],
                    ]);
                }
            }

            DB::commit();
            return redirect()->route("admin.journal_entries.index")
                             ->with("success", "Journal entry updated successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->withErrors(["error" => "Failed to update journal entry: " . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", "Only draft entries can be deleted.");
        }
        DB::beginTransaction();
        try {
            $journalEntry->items()->delete();
            $journalEntry->delete();
            DB::commit();
            return redirect()->route("admin.journal_entries.index")
                             ->with("success", "Journal entry deleted successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.journal_entries.index")
                             ->with("error", "Failed to delete journal entry: " . $e->getMessage());
        }
    }

    /**
     * Post a draft journal entry.
     */
    public function postEntry(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "draft") {
            return redirect()->route("admin.journal_entries.index")->with("error", "Only draft entries can be posted.");
        }

        DB::beginTransaction();
        try {
            // Update account balances
            foreach ($journalEntry->items as $item) {
                $account = Account::find($item->account_id);
                if ($account) {
                    $account->balance += ($item->debit_amount - $item->credit_amount);
                    $account->save();
                }
            }

            $journalEntry->status = "posted";
            $journalEntry->posted_by_id = Auth::id();
            $journalEntry->posted_at = now();
            $journalEntry->save();

            DB::commit();
            return redirect()->route("admin.journal_entries.index")
                             ->with("success", "Journal entry posted successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.journal_entries.index")
                             ->with("error", "Failed to post journal entry: " . $e->getMessage());
        }
    }

     /**
     * Cancel a posted journal entry.
     */
    public function cancelEntry(JournalEntry $journalEntry)
    {
        if ($journalEntry->status !== "posted") {
            return redirect()->route("admin.journal_entries.index")->with("error", "Only posted entries can be cancelled.");
        }

        // Check for subsequent transactions or period closing before allowing cancellation - Advanced logic needed here
        // For now, a simple cancellation is implemented

        DB::beginTransaction();
        try {
            // Revert account balances
            foreach ($journalEntry->items as $item) {
                $account = Account::find($item->account_id);
                if ($account) {
                    $account->balance -= ($item->debit_amount - $item->credit_amount); // Revert the original impact
                    $account->save();
                }
            }

            $journalEntry->status = "cancelled";
            // Optionally, store who cancelled and when
            // $journalEntry->cancelled_by_id = Auth::id();
            // $journalEntry->cancelled_at = now();
            $journalEntry->save();

            DB::commit();
            return redirect()->route("admin.journal_entries.index")
                             ->with("success", "Journal entry cancelled successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.journal_entries.index")
                             ->with("error", "Failed to cancel journal entry: " . $e->getMessage());
        }
    }
}

