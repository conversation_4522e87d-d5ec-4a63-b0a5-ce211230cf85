<?php

namespace App\Http\Controllers\Modules\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PurchaseReportController extends Controller
{
    /**
     * Display purchase reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.reports.purchases.index');
    }

    /**
     * Generate purchase summary report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseSummary(Request $request)
    {
        return view('admin.reports.purchases.summary');
    }

    /**
     * Generate purchase by supplier report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseBySupplier(Request $request)
    {
        return view('admin.reports.purchases.by_supplier');
    }

    /**
     * Generate purchase by product report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseByProduct(Request $request)
    {
        return view('admin.reports.purchases.by_product');
    }

    /**
     * Generate purchase by period report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseByPeriod(Request $request)
    {
        return view('admin.reports.purchases.by_period');
    }

    /**
     * Generate top suppliers report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function topSuppliers(Request $request)
    {
        return view('admin.reports.purchases.top_suppliers');
    }

    /**
     * Generate purchase returns report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseReturns(Request $request)
    {
        return view('admin.reports.purchases.returns');
    }

    /**
     * Generate purchase orders report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchaseOrders(Request $request)
    {
        return view('admin.reports.purchases.orders');
    }
}
