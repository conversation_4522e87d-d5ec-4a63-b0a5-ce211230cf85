<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturing_work_order_consumed_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('work_order_id')->constrained('manufacturing_work_orders')->onDelete('cascade');
            $table->foreignId('item_id')->constrained('manufacturing_items')->comment('Raw material or component consumed');
            $table->decimal('quantity_consumed', 15, 4);
            $table->string('unit_of_measure');
            $table->timestamp('consumption_date')->useCurrent();
            $table->foreignId('issued_by_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            // Link to a specific work order item if this consumption is directly tied to an input line
            // $table->foreignId('work_order_item_id')->nullable()->constrained('manufacturing_work_order_items')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturing_work_order_consumed_items');
    }
};
