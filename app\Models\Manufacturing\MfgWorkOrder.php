<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
// use App\Models\Sales\SalesOrder; // If SalesOrder model exists
// use App\Models\Planning\Forecast; // If Forecast model exists

class MfgWorkOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_work_orders';

    protected $fillable = [
        'wo_number',
        'description',
        'mfg_product_id',
        'quantity_to_produce',
        'unit_of_measure',
        'mfg_bom_id',
        'mfg_routing_id',
        'scheduled_start_date',
        'scheduled_end_date',
        'actual_start_date',
        'actual_end_date',
        'status',
        'priority',
        'quantity_produced',
        'quantity_scrapped',
        'source_sales_order_id',
        'source_forecast_id',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'quantity_to_produce' => 'decimal:4',
        'quantity_produced' => 'decimal:4',
        'quantity_scrapped' => 'decimal:4',
        'scheduled_start_date' => 'datetime',
        'scheduled_end_date' => 'datetime',
        'actual_start_date' => 'datetime',
        'actual_end_date' => 'datetime',
    ];

    /**
     * Get the manufacturing product to be produced.
     */
    public function mfgProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'mfg_product_id');
    }

    /**
     * Get the Bill of Material used for this work order.
     */
    public function mfgBom()
    {
        return $this->belongsTo(MfgBom::class, 'mfg_bom_id');
    }

    /**
     * Get the Routing used for this work order.
     */
    public function mfgRouting()
    {
        return $this->belongsTo(MfgRouting::class, 'mfg_routing_id');
    }

    /**
     * Get the production transactions for this work order.
     */
    public function productionTransactions()
    {
        return $this->hasMany(MfgProductionTransaction::class, 'mfg_work_order_id');
    }

    /**
     * Get the material allocations for this work order.
     */
    public function materialAllocations()
    {
        return $this->hasMany(MfgMaterialAllocation::class, 'work_order_id');
    }

    // /**
    //  * Get the source sales order if applicable.
    //  */
    // public function salesOrder()
    // {
    //     return $this->belongsTo(SalesOrder::class, 'source_sales_order_id');
    // }

    // /**
    //  * Get the source forecast if applicable.
    //  */
    // public function forecast()
    // {
    //     return $this->belongsTo(Forecast::class, 'source_forecast_id');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

