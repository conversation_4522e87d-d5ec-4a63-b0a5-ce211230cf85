<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("accounts", function (Blueprint $table) {
            $table->id();
            $table->string("name_ar");
            $table->string("name_en");
            $table->string("code")->unique()->nullable(); // Account code, can be auto-generated or manual
            $table->foreignId("account_type_id")->constrained("account_types");
            $table->foreignId("parent_id")->nullable()->constrained("accounts")->onDelete("cascade"); // For hierarchical chart of accounts
            $table->text("description_ar")->nullable();
            $table->text("description_en")->nullable();
            $table->boolean("is_control_account")->default(false); // If true, no direct entries, sums up sub-accounts
            $table->boolean("accepts_entries")->default(true); // If false, it might be a header or inactive
            $table->decimal("opening_balance_debit", 15, 2)->default(0.00);
            $table->decimal("opening_balance_credit", 15, 2)->default(0.00);
            $table->date("opening_balance_date")->nullable();
            $table->foreignId("branch_id")->nullable()->constrained("branches")->onDelete("set null"); // Optional: Link account to a specific branch
            $table->boolean("is_active")->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("accounts");
    }
};

