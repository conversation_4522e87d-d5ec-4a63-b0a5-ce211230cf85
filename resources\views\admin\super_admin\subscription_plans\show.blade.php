@extends('layouts.admin')

@section('title', 'تفاصيل باقة الاشتراك')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل باقة: {{ $subscriptionPlan->name }}</h3>
                    <div>
                        <a href="{{ route('admin.super_admin.subscription_plans.edit', $subscriptionPlan) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.super_admin.subscription_plans.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">معلومات الباقة الأساسية</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $subscriptionPlan->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الكود</th>
                                            <td><code>{{ $subscriptionPlan->code }}</code></td>
                                        </tr>
                                        <tr>
                                            <th>الوصف</th>
                                            <td>{{ $subscriptionPlan->description ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>السعر</th>
                                            <td>{{ $subscriptionPlan->price }} ريال</td>
                                        </tr>
                                        <tr>
                                            <th>دورة الفوترة</th>
                                            <td>
                                                @switch($subscriptionPlan->billing_cycle)
                                                    @case('monthly')
                                                        شهري
                                                        @break
                                                    @case('quarterly')
                                                        ربع سنوي
                                                        @break
                                                    @case('semi_annually')
                                                        نصف سنوي
                                                        @break
                                                    @case('annually')
                                                        سنوي
                                                        @break
                                                    @default
                                                        {{ $subscriptionPlan->billing_cycle }}
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if($subscriptionPlan->is_active)
                                                    <span class="badge bg-success">نشط</span>
                                                @else
                                                    <span class="badge bg-danger">غير نشط</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $subscriptionPlan->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $subscriptionPlan->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">حدود وميزات الباقة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-body">
                                                    <h5 class="card-title">حدود الاستخدام</h5>
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            الحد الأقصى للمستخدمين
                                                            <span class="badge bg-primary rounded-pill">{{ $subscriptionPlan->max_users }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            الحد الأقصى للفروع
                                                            <span class="badge bg-primary rounded-pill">{{ $subscriptionPlan->max_branches }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            حد الفواتير الشهري
                                                            <span class="badge bg-primary rounded-pill">{{ $invoiceLimit->monthly_invoice_limit ?? 'غير محدد' }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            حد الفواتير الإجمالي
                                                            <span class="badge bg-primary rounded-pill">{{ $invoiceLimit->total_invoice_limit ?? 'غير محدد' }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            مساحة التخزين
                                                            <span class="badge bg-primary rounded-pill">{{ $subscriptionPlan->storage_space_gb }} GB</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-body">
                                                    <h5 class="card-title">الوحدات المتاحة</h5>
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            نقاط البيع
                                                            @if($subscriptionPlan->has_pos)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            إدارة المخزون
                                                            @if($subscriptionPlan->has_inventory)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            المحاسبة
                                                            @if($subscriptionPlan->has_accounting)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            التصنيع
                                                            @if($subscriptionPlan->has_manufacturing)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            الموارد البشرية
                                                            @if($subscriptionPlan->has_hr)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">وحدات إضافية</h5>
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            إدارة علاقات العملاء
                                                            @if($subscriptionPlan->has_crm)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            المشتريات
                                                            @if($subscriptionPlan->has_purchases)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            المبيعات
                                                            @if($subscriptionPlan->has_sales)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            التقارير
                                                            @if($subscriptionPlan->has_reports)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            الوصول إلى API
                                                            @if($subscriptionPlan->has_api_access)
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">المشتركين في هذه الباقة</h5>
                                </div>
                                <div class="card-body">
                                    @if($subscriptionPlan->subscriptions->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>العميل</th>
                                                        <th>رقم الاشتراك</th>
                                                        <th>تاريخ البدء</th>
                                                        <th>تاريخ الانتهاء</th>
                                                        <th>الحالة</th>
                                                        <th>السعر المدفوع</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($subscriptionPlan->subscriptions as $subscription)
                                                        <tr>
                                                            <td>{{ $subscription->id }}</td>
                                                            <td>{{ $subscription->tenant->name ?? 'غير محدد' }}</td>
                                                            <td>{{ $subscription->subscription_number }}</td>
                                                            <td>{{ $subscription->start_date->format('Y-m-d') }}</td>
                                                            <td>{{ $subscription->end_date->format('Y-m-d') }}</td>
                                                            <td>
                                                                <span class="badge {{ $subscription->status == 'active' ? 'bg-success' : 'bg-warning' }}">
                                                                    {{ $subscription->status_arabic }}
                                                                </span>
                                                            </td>
                                                            <td>{{ $subscription->price_paid }} ريال</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا يوجد مشتركين في هذه الباقة حالياً</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
