@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($device) ? "Edit POS Device" : "Add New POS Device" }}</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{ isset($device) ? route("admin.pos.devices.update", $device->id) : route("admin.pos.devices.store") }}" method="POST">
        @csrf
        @if(isset($device))
            @method("PUT")
        @endif

        <div class="form-group">
            <label for="name">Device Name</label>
            <input type="text" name="name" id="name" class="form-control" value="{{ old("name", isset($device) ? $device->name : "") }}" required>
        </div>

        <div class="form-group">
            <label for="type">Device Type</label>
            <select name="type" id="type" class="form-control" required>
                <option value="fixed" {{ old("type", isset($device) && $device->type == "fixed" ? "selected" : "") }}>Fixed</option>
                <option value="mobile" {{ old("type", isset($device) && $device->type == "mobile" ? "selected" : "") }}>Mobile</option>
            </select>
        </div>

        <div class="form-group">
            <label for="status">Status</label>
            <select name="status" id="status" class="form-control" required>
                <option value="active" {{ old("status", isset($device) && $device->status == "active" ? "selected" : "") }}>Active</option>
                <option value="inactive" {{ old("status", isset($device) && $device->status == "inactive" ? "selected" : "") }}>Inactive</option>
                <option value="maintenance" {{ old("status", isset($device) && $device->status == "maintenance" ? "selected" : "") }}>Maintenance</option>
            </select>
        </div>

        <div class="form-group">
            <label for="location">Location (Optional)</label>
            <input type="text" name="location" id="location" class="form-control" value="{{ old("location", isset($device) ? $device->location : "") }}">
        </div>

        <div class="form-group">
            <label for="ip_address">IP Address (Optional)</label>
            <input type="text" name="ip_address" id="ip_address" class="form-control" value="{{ old("ip_address", isset($device) ? $device->ip_address : "") }}">
        </div>

        <div class="form-group">
            <label for="serial_number">Serial Number (Optional)</label>
            <input type="text" name="serial_number" id="serial_number" class="form-control" value="{{ old("serial_number", isset($device) ? $device->serial_number : "") }}">
        </div>

        <button type="submit" class="btn btn-success mt-3">{{ isset($device) ? "Update Device" : "Save Device" }}</button>
        <a href="{{ route("admin.pos.devices.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

