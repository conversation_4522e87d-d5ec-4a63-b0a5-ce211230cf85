<?php

namespace App\Listeners\Bnpl;

use App\Events\Bnpl\BnplPaymentFailedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
// Import necessary models or services for notifications

class HandleBnplPaymentFailed // implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  BnplPaymentFailedEvent  $event
     * @return void
     */
    public function handle(BnplPaymentFailedEvent $event): void
    {
        Log::info('BNPL Payment Failed Event Received for Order ID: ' . $event->order->id . '. Reason: ' . $event->reason);

        // 1. Update Order Status to 'payment_failed' or a similar status
        // Example: $event->order->update([\'status\' => \'payment_failed\', \'payment_status\' => \'failed\']);
        // This depends on the existing order status flow

        // 2. Log the failure reason and BNPL transaction details if available
        if ($event->bnplTransaction) {
            Log::error('BNPL Payment Failed for Order ID: ' . $event->order->id . ', BNPL Transaction ID: ' . $event->bnplTransaction->id . ', Reason: ' . $event->reason);
        } else {
            Log::error('BNPL Payment Failed for Order ID: ' . $event->order->id . ', Reason: ' . $event->reason . '. No BNPL transaction object available.');
        }

        // 3. Send notifications (e.g., to customer about payment failure, to admin for investigation)
        // Example: Notification::send($event->order->customer, new BnplPaymentFailedNotification($event->order, $event->reason));
        Log::info('Placeholder for sending notification for failed BNPL payment for Order ID: ' . $event->order->id);

        // 4. Potentially reverse any provisional actions taken (e.g., stock reservation)
    }
}

