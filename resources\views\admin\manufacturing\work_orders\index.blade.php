@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Manufacturing Work Orders</h1>
        <a href="{{ route("admin.manufacturing.work_orders.create") }}" class="btn btn-primary">Add New Work Order</a>
        <table class="table mt-3">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Work Order Number</th>
                    <th>Item to Produce</th>
                    <th>Quantity to Produce</th>
                    <th>Status</th>
                    <th>Planned Start Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {{-- @foreach ($workOrders as $workOrder) --}}
                {{-- Example Row --}}
                <tr>
                    <td>1</td>
                    <td>WO-2025-001</td>
                    <td>Product A (P001)</td>
                    <td>100.00</td>
                    <td>Pending</td>
                    <td>2025-06-01</td>
                    <td>
                        <a href="#" class="btn btn-sm btn-info">View</a>
                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                        <form action="#" method="POST" style="display:inline-block;">
                            @csrf
                            @method("DELETE")
                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
                {{-- @endforeach --}}
            </tbody>
        </table>
    </div>
@endsection
