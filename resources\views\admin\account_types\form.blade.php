@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($accountType) ? __("Edit Account Type") : __("Add New Account Type") }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($accountType) ? route("admin.account_types.update", $accountType->id) : route("admin.account_types.store") }}" method="POST">
        @csrf
        @if(isset($accountType))
            @method("PUT")
        @endif

        <div class="mb-3">
            <label for="name_ar" class="form-label">{{ __("Name (Arabic)") }}</label>
            <input type="text" class="form-control" id="name_ar" name="name_ar" value="{{ old("name_ar", $accountType->name_ar ?? "") }}" required>
        </div>

        <div class="mb-3">
            <label for="name_en" class="form-label">{{ __("Name (English)") }}</label>
            <input type="text" class="form-control" id="name_en" name="name_en" value="{{ old("name_en", $accountType->name_en ?? "") }}" required>
        </div>

        <div class="mb-3">
            <label for="slug" class="form-label">{{ __("Slug") }}</label>
            <input type="text" class="form-control" id="slug" name="slug" value="{{ old("slug", $accountType->slug ?? "") }}" required>
            <small class="form-text text-muted">{{ __("e.g., assets, liabilities, equity, income, expenses. Used for programmatic access.") }}</small>
        </div>

        <div class="mb-3">
            <label for="description_ar" class="form-label">{{ __("Description (Arabic)") }}</label>
            <textarea class="form-control" id="description_ar" name="description_ar">{{ old("description_ar", $accountType->description_ar ?? "") }}</textarea>
        </div>

        <div class="mb-3">
            <label for="description_en" class="form-label">{{ __("Description (English)") }}</label>
            <textarea class="form-control" id="description_en" name="description_en">{{ old("description_en", $accountType->description_en ?? "") }}</textarea>
        </div>

        <div class="mb-3">
            <label for="parent_id" class="form-label">{{ __("Parent Account Type") }}</label>
            <select class="form-control" id="parent_id" name="parent_id">
                <option value="">{{ __("None (It is a primary type)") }}</option>
                @foreach($parentAccountTypes as $parentType)
                    <option value="{{ $parentType->id }}" {{ (isset($accountType) && $accountType->parent_id == $parentType->id) || old("parent_id") == $parentType->id ? "selected" : "" }}>
                        {{ $parentType->name_ar }} ({{ $parentType->name_en }})
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="is_primary" name="is_primary" value="1" {{ (isset($accountType) && $accountType->is_primary) || old("is_primary") ? "checked" : "" }}>
            <label class="form-check-label" for="is_primary">{{ __("Is Primary Type (e.g., Assets, Liabilities)") }}</label>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ (isset($accountType) && $accountType->is_active) || old("is_active", 1) ? "checked" : "" }}>
            <label class="form-check-label" for="is_active">{{ __("Is Active") }}</label>
        </div>

        <button type="submit" class="btn btn-success">{{ isset($accountType) ? __("Update Account Type") : __("Save Account Type") }}</button>
        <a href="{{ route("admin.account_types.index") }}" class="btn btn-secondary">{{ __("Cancel") }}</a>
    </form>
</div>
@endsection

