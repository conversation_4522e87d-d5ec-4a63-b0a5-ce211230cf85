<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Bom;
use App\Models\BomItem;
use App\Models\Item;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $boms = Bom::with(["item", "branch"])->latest()->paginate(10);
        return view("admin.boms.index", compact("boms"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Items that can be produced (Finished Goods or Semi-Finished Goods)
        $items = Item::whereIn("type", ["finished_good", "semi_finished_good"])->where("is_active", true)->get();
        // Items that can be components (Raw Materials or Semi-Finished Goods)
        $rawMaterialsAndSemiFinished = Item::whereIn("type", ["raw_material", "semi_finished_good"])->where("is_active", true)->get();
        $branches = Branch::where("is_active", true)->get();
        return view("admin.boms.form", compact("items", "branches", "rawMaterialsAndSemiFinished"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "bom_code" => "nullable|string|max:255|unique:boms,bom_code",
            "item_id" => "required|exists:items,id",
            "quantity_to_produce" => "required|numeric|min:0.0001",
            "unit_of_measure_ar" => "nullable|string|max:255",
            "unit_of_measure_en" => "nullable|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "branch_id" => "nullable|exists:branches,id",
            "is_default" => "boolean",
            "is_active" => "boolean",
            "valid_from" => "nullable|date",
            "valid_to" => "nullable|date|after_or_equal:valid_from",
            "bom_items" => "nullable|array",
            "bom_items.*.item_id" => "required_with:bom_items|exists:items,id",
            "bom_items.*.quantity_required" => "required_with:bom_items|numeric|min:0.0001",
            "bom_items.*.unit_of_measure_ar" => "nullable|string|max:255",
            "bom_items.*.scrap_percentage" => "nullable|numeric|min:0|max:100",
        ]);

        DB::beginTransaction();
        try {
            $bomData = $request->only(["name_ar", "name_en", "bom_code", "item_id", "quantity_to_produce", "unit_of_measure_ar", "unit_of_measure_en", "description_ar", "description_en", "branch_id", "valid_from", "valid_to"]);
            $bomData["is_default"] = $request->has("is_default");
            $bomData["is_active"] = $request->has("is_active");
            $bomData["created_by_user_id"] = Auth::id();

            $bom = Bom::create($bomData);

            if ($request->has("bom_items")) {
                foreach ($request->bom_items as $bomItemData) {
                    $bom->bomItems()->create([
                        "item_id" => $bomItemData["item_id"],
                        "quantity_required" => $bomItemData["quantity_required"],
                        "unit_of_measure_ar" => $bomItemData["unit_of_measure_ar"] ?? null,
                        "scrap_percentage" => $bomItemData["scrap_percentage"] ?? 0,
                    ]);
                }
            }
            DB::commit();
            return redirect()->route("admin.boms.index")->with("success", __("admin_messages.bom_created_successfully"));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with("error", __("admin_messages.error_creating_bom") . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Bom $bom)
    {
        $bom->load(["item", "branch", "bomItems.item"]);
        return view("admin.boms.show", compact("bom"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Bom $bom)
    {
        $bom->load("bomItems.item");
        $items = Item::whereIn("type", ["finished_good", "semi_finished_good"])->where("is_active", true)->get();
        $rawMaterialsAndSemiFinished = Item::whereIn("type", ["raw_material", "semi_finished_good"])->where("is_active", true)->get();
        $branches = Branch::where("is_active", true)->get();
        return view("admin.boms.form", compact("bom", "items", "branches", "rawMaterialsAndSemiFinished"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Bom $bom)
    {
         $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "bom_code" => "nullable|string|max:255|unique:boms,bom_code," . $bom->id,
            "item_id" => "required|exists:items,id",
            "quantity_to_produce" => "required|numeric|min:0.0001",
            "unit_of_measure_ar" => "nullable|string|max:255",
            "unit_of_measure_en" => "nullable|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "branch_id" => "nullable|exists:branches,id",
            "is_default" => "boolean",
            "is_active" => "boolean",
            "valid_from" => "nullable|date",
            "valid_to" => "nullable|date|after_or_equal:valid_from",
            "bom_items" => "nullable|array",
            "bom_items.*.id" => "nullable|exists:bom_items,id", // For existing items
            "bom_items.*.item_id" => "required_with:bom_items|exists:items,id",
            "bom_items.*.quantity_required" => "required_with:bom_items|numeric|min:0.0001",
            "bom_items.*.unit_of_measure_ar" => "nullable|string|max:255",
            "bom_items.*.scrap_percentage" => "nullable|numeric|min:0|max:100",
        ]);

        DB::beginTransaction();
        try {
            $bomData = $request->only(["name_ar", "name_en", "bom_code", "item_id", "quantity_to_produce", "unit_of_measure_ar", "unit_of_measure_en", "description_ar", "description_en", "branch_id", "valid_from", "valid_to"]);
            $bomData["is_default"] = $request->has("is_default");
            $bomData["is_active"] = $request->has("is_active");
            $bomData["updated_by_user_id"] = Auth::id();

            $bom->update($bomData);

            // Handle BOM Items: Update existing, delete removed, add new
            $existingItemIds = [];
            if ($request->has("bom_items")) {
                foreach ($request->bom_items as $bomItemData) {
                    if (isset($bomItemData["id"]) && $bomItemData["id"]) {
                        // Update existing item
                        $bomItem = BomItem::find($bomItemData["id"]);
                        if ($bomItem && $bomItem->bom_id == $bom->id) { // Ensure it belongs to this BOM
                            $bomItem->update([
                                "item_id" => $bomItemData["item_id"],
                                "quantity_required" => $bomItemData["quantity_required"],
                                "unit_of_measure_ar" => $bomItemData["unit_of_measure_ar"] ?? null,
                                "scrap_percentage" => $bomItemData["scrap_percentage"] ?? 0,
                            ]);
                            $existingItemIds[] = $bomItem->id;
                        }
                    } else {
                        // Add new item
                        $newItem = $bom->bomItems()->create([
                            "item_id" => $bomItemData["item_id"],
                            "quantity_required" => $bomItemData["quantity_required"],
                            "unit_of_measure_ar" => $bomItemData["unit_of_measure_ar"] ?? null,
                            "scrap_percentage" => $bomItemData["scrap_percentage"] ?? 0,
                        ]);
                        $existingItemIds[] = $newItem->id;
                    }
                }
            }
            // Delete items not present in the submission
            $bom->bomItems()->whereNotIn("id", $existingItemIds)->delete();

            DB::commit();
            return redirect()->route("admin.boms.index")->with("success", __("admin_messages.bom_updated_successfully"));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->with("error", __("admin_messages.error_updating_bom") . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Bom $bom)
    {
        // Add checks here if BOM is used in Work Orders etc. before deleting
        DB::beginTransaction();
        try {
            $bom->bomItems()->delete(); // Delete related bom items first
            $bom->delete();
            DB::commit();
            return redirect()->route("admin.boms.index")->with("success", __("admin_messages.bom_deleted_successfully"));
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            return redirect()->route("admin.boms.index")->with("error", __("admin_messages.bom_delete_failed_constraint"));
        }
    }
}

