@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("Ticket Status Details") }}</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ $status->name }}</h4>
        </div>
        <div class="card-body">
            <p><strong>{{ __("ID:") }}</strong> {{ $status->id }}</p>
            <p><strong>{{ __("Name:") }}</strong> {{ $status->name }}</p>
            <p><strong>{{ __("Type:") }}</strong> {{ ucfirst($status->type) }}</p>
            <p><strong>{{ __("Color:") }}</strong>
                <span style="display:inline-block; width: 20px; height: 20px; background-color: {{ $status->color ?? '#FFFFFF' }}; border: 1px solid #ccc;"></span>
                {{ $status->color ?? __("N/A") }}
            </p>
            <p><strong>{{ __("Default for New Tickets:") }}</strong>
                @if($status->is_default_new)
                    <span class="badge badge-info">{{ __("Yes") }}</span>
                @else
                    {{ __("No") }}
                @endif
            </p>
            <p><strong>{{ __("Default for Closed Tickets:") }}</strong>
                @if($status->is_default_closed)
                    <span class="badge badge-secondary">{{ __("Yes") }}</span>
                @else
                    {{ __("No") }}
                @endif
            </p>
            <p><strong>{{ __("Active:") }}</strong>
                @if($status->is_active)
                    <span class="badge badge-success">{{ __("Yes") }}</span>
                @else
                    <span class="badge badge-danger">{{ __("No") }}</span>
                @endif
            </p>
            <p><strong>{{ __("Created At:") }}</strong> {{ $status->created_at->format("Y-m-d H:i:s") }}</p>
            <p><strong>{{ __("Updated At:") }}</strong> {{ $status->updated_at->format("Y-m-d H:i:s") }}</p>
        </div>
        <div class="card-footer">
            <a href="{{ route("admin.ticketing.ticket_statuses.edit", $status->id) }}" class="btn btn-warning">{{ __("Edit") }}</a>
            <a href="{{ route("admin.ticketing.ticket_statuses.index") }}" class="btn btn-secondary">{{ __("Back to List") }}</a>
        </div>
    </div>
</div>
@endsection

