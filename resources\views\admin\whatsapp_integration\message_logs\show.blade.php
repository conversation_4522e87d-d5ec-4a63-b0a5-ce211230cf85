@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>WhatsApp Message Log Details</h1>

    <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Message SID:</strong>
                {{ $messageLog->message_sid }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Recipient User:</strong>
                {{ $messageLog->user->name ?? "N/A" }} (ID: {{ $messageLog->user_id ?? "N/A" }})
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Recipient Phone Number:</strong>
                {{ $messageLog->recipient_phone_number }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Template Used:</strong>
                {{ $messageLog->template->name ?? "N/A" }} (ID: {{ $messageLog->template_id ?? "N/A" }})
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Message Content:</strong>
                <pre>{{ $messageLog->message_content }}</pre>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Message Type:</strong>
                {{ $messageLog->message_type }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Status:</strong>
                {{ $messageLog->status }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Direction:</strong>
                {{ $messageLog->direction }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Error Code:</strong>
                {{ $messageLog->error_code ?? "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Error Message:</strong>
                <pre>{{ $messageLog->error_message ?? "N/A" }}</pre>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Sent At:</strong>
                {{ $messageLog->sent_at ? $messageLog->sent_at->format("Y-m-d H:i:s") : "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Delivered At:</strong>
                {{ $messageLog->delivered_at ? $messageLog->delivered_at->format("Y-m-d H:i:s") : "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Read At:</strong>
                {{ $messageLog->read_at ? $messageLog->read_at->format("Y-m-d H:i:s") : "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Related Model Type:</strong>
                {{ $messageLog->related_model_type ?? "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Related Model ID:</strong>
                {{ $messageLog->related_model_id ?? "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Log Created At:</strong>
                {{ $messageLog->created_at->format("Y-m-d H:i:s") }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Log Updated At:</strong>
                {{ $messageLog->updated_at->format("Y-m-d H:i:s") }}
            </div>
        </div>
    </div>
    <div class="pull-right">
        <a class="btn btn-primary" href="{{ route("admin.whatsapp_integration.message_logs.index") }}"> Back</a>
    </div>
</div>
@endsection

