<?php $__env->startSection("content"); ?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><?php echo e(__("Ticket Priorities")); ?></h1>
        <a href="<?php echo e(route("admin.ticketing.ticket_priorities.create")); ?>" class="btn btn-primary"><?php echo e(__("Create New Priority")); ?></a>
    </div>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th><?php echo e(__("ID")); ?></th>
                <th><?php echo e(__("Name")); ?></th>
                <th><?php echo e(__("Level")); ?></th>
                <th><?php echo e(__("Color")); ?></th>
                <th><?php echo e(__("Active")); ?></th>
                <th><?php echo e(__("Actions")); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $priority): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($priority->id); ?></td>
                    <td><?php echo e($priority->name); ?></td>
                    <td><?php echo e($priority->level); ?></td>
                    <td>
                        <span style="display:inline-block; width: 20px; height: 20px; background-color: <?php echo e($priority->color ?? '#FFFFFF'); ?>; border: 1px solid #ccc;"></span>
                        <?php echo e($priority->color); ?>

                    </td>
                    <td>
                        <?php if($priority->is_active): ?>
                            <span class="badge badge-success"><?php echo e(__("Yes")); ?></span>
                        <?php else: ?>
                            <span class="badge badge-danger"><?php echo e(__("No")); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a href="<?php echo e(route("admin.ticketing.ticket_priorities.show", $priority->id)); ?>" class="btn btn-sm btn-info"><?php echo e(__("View")); ?></a>
                        <a href="<?php echo e(route("admin.ticketing.ticket_priorities.edit", $priority->id)); ?>" class="btn btn-sm btn-warning"><?php echo e(__("Edit")); ?></a>
                        <form action="<?php echo e(route("admin.ticketing.ticket_priorities.destroy", $priority->id)); ?>" method="POST" style="display: inline-block;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field("DELETE"); ?>
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("<?php echo e(__("Are you sure you want to delete this priority?")); ?>")"><?php echo e(__("Delete")); ?></button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="6" class="text-center"><?php echo e(__("No ticket priorities found.")); ?></td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
    <?php echo e($priorities->links()); ?>

</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/ticketing/priorities/index.blade.php ENDPATH**/ ?>