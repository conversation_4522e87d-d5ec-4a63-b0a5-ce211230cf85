@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ __("Kitchen Display System") }}</h1>
                <div>
                    <button class="btn btn-success" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt"></i> {{ __("Refresh") }}
                    </button>
                    <button class="btn btn-info ms-2" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> {{ __("Fullscreen") }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Kitchen Stations Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills" id="stationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all-orders"
                            type="button" role="tab" onclick="setActiveStation('')">
                        <i class="fas fa-list"></i> {{ __("All Orders") }}
                        <span class="badge bg-primary ms-1" id="all-count">0</span>
                    </button>
                </li>
                @foreach($kitchenStations as $station)
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="station-{{ $station->id }}-tab" data-bs-toggle="pill"
                                data-bs-target="#station-{{ $station->id }}" type="button" role="tab"
                                onclick="setActiveStation({{ $station->id }})"
                                style="border-color: {{ $station->color }}; color: {{ $station->color }};">
                            <i class="fas fa-gear"></i> {{ $station->name }}
                            <span class="badge ms-1" style="background-color: {{ $station->color }}" id="station-{{ $station->id }}-count">0</span>
                        </button>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>

    <!-- Orders Display -->
    <div class="row" id="ordersContainer">
        <!-- Orders will be loaded here via JavaScript -->
    </div>

    <!-- Auto-refresh indicator -->
    <div class="position-fixed bottom-0 end-0 p-3">
        <div class="toast" id="refreshToast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-sync-alt text-primary me-2"></i>
                <strong class="me-auto">{{ __("Auto Refresh") }}</strong>
                <small class="text-muted" id="lastUpdate">{{ __("Just now") }}</small>
            </div>
            <div class="toast-body">
                {{ __("Orders updated automatically") }}
            </div>
        </div>
    </div>
</div>

<!-- Order Item Modal -->
<div class="modal fade" id="orderItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __("Update Order Item Status") }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="orderItemDetails"></div>
                <div class="mt-3">
                    <label class="form-label">{{ __("New Status") }}</label>
                    <select class="form-select" id="newStatus">
                        <option value="pending">{{ __("Pending") }}</option>
                        <option value="preparing">{{ __("Preparing") }}</option>
                        <option value="ready">{{ __("Ready") }}</option>
                        <option value="served">{{ __("Served") }}</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __("Cancel") }}</button>
                <button type="button" class="btn btn-primary" onclick="updateItemStatus()">{{ __("Update Status") }}</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.kitchen-order-card {
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
    margin-bottom: 20px;
}

.kitchen-order-card.status-pending {
    border-left-color: #ffc107;
    background-color: #fff3cd;
}

.kitchen-order-card.status-preparing {
    border-left-color: #fd7e14;
    background-color: #fff0e6;
}

.kitchen-order-card.status-ready {
    border-left-color: #28a745;
    background-color: #d4edda;
}

.kitchen-order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.order-timer {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.order-timer.urgent {
    color: #dc3545;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.nav-pills .nav-link.active {
    background-color: var(--bs-primary);
}

.status-buttons .btn {
    margin: 2px;
}

.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    overflow-y: auto;
}
</style>
@endpush

@push('scripts')
<script>
let currentStation = '';
let refreshInterval;
let selectedOrderItemId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadOrders();
    startAutoRefresh();
});

function setActiveStation(stationId) {
    currentStation = stationId;
    loadOrders();
}

function filterByStation(stationId) {
    currentStation = stationId;

    // Update active tab
    document.querySelectorAll('.nav-link').forEach(tab => tab.classList.remove('active'));
    if (stationId === '') {
        document.getElementById('all-tab').classList.add('active');
    } else {
        document.getElementById(`station-${stationId}-tab`).classList.add('active');
    }

    loadOrders();
}

function loadOrders() {
    const url = new URL('/admin/restaurant/kitchen-display/orders', window.location.origin);
    if (currentStation) {
        url.searchParams.append('station_id', currentStation);
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrders(data.data);
                updateOrderCounts(data.data);
            }
        })
        .catch(error => {
            console.error('Error loading orders:', error);
        });
}

function displayOrders(orders) {
    const container = document.getElementById('ordersContainer');

    if (orders.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h4 class="text-muted">{{ __("No pending orders") }}</h4>
                    <p class="text-muted">{{ __("All orders are completed!") }}</p>
                </div>
            </div>
        `;
        return;
    }

    const ordersHtml = orders.map(order => `
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card kitchen-order-card status-${order.status}" onclick="showOrderDetails(${order.id})">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <strong>#${order.order_number}</strong>
                        <small class="text-muted d-block">${order.table_name}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${getStatusColor(order.status)}">${order.status}</span>
                        <div class="order-timer ${getTimerClass(order.created_at)}">${getElapsedTime(order.created_at)}</div>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${order.menu_item_name}</h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">Qty: ${order.quantity}</span>
                        ${order.modifiers ? `<small class="text-muted">${order.modifiers}</small>` : ''}
                    </div>
                    ${order.notes ? `<p class="card-text"><small class="text-muted">${order.notes}</small></p>` : ''}
                    ${order.special_instructions ? `<p class="card-text"><small class="text-warning">${order.special_instructions}</small></p>` : ''}
                </div>
                <div class="card-footer">
                    <div class="status-buttons">
                        ${getStatusButtons(order)}
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = ordersHtml;
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'preparing': 'info',
        'ready': 'success',
        'served': 'secondary'
    };
    return colors[status] || 'primary';
}

function getTimerClass(createdAt) {
    const elapsed = getElapsedMinutes(createdAt);
    return elapsed > 15 ? 'urgent' : '';
}

function getElapsedTime(createdAt) {
    const elapsed = getElapsedMinutes(createdAt);
    return `${elapsed}m`;
}

function getElapsedMinutes(createdAt) {
    const now = new Date();
    const created = new Date(createdAt);
    return Math.floor((now - created) / (1000 * 60));
}

function getStatusButtons(order) {
    const buttons = [];

    if (order.status === 'pending') {
        buttons.push(`<button class="btn btn-sm btn-warning" onclick="updateOrderItemStatus(${order.id}, 'preparing')">{{ __("Start") }}</button>`);
    }

    if (order.status === 'preparing') {
        buttons.push(`<button class="btn btn-sm btn-success" onclick="updateOrderItemStatus(${order.id}, 'ready')">{{ __("Ready") }}</button>`);
    }

    if (order.status === 'ready') {
        buttons.push(`<button class="btn btn-sm btn-secondary" onclick="updateOrderItemStatus(${order.id}, 'served')">{{ __("Served") }}</button>`);
    }

    return buttons.join(' ');
}

function updateOrderItemStatus(itemId, status) {
    fetch(`/admin/restaurant/kitchen-display/items/${itemId}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadOrders();
            showToast('{{ __("Order status updated successfully") }}');
        } else {
            alert('{{ __("Error updating order status") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("Error updating order status") }}');
    });
}

function updateOrderCounts(orders) {
    // Update all orders count
    document.getElementById('all-count').textContent = orders.length;

    // Update station counts
    @foreach($kitchenStations as $station)
        const station{{ $station->id }}Count = orders.filter(order => order.kitchen_station_id === {{ $station->id }}).length;
        const station{{ $station->id }}Badge = document.getElementById('station-{{ $station->id }}-count');
        if (station{{ $station->id }}Badge) {
            station{{ $station->id }}Badge.textContent = station{{ $station->id }}Count;
        }
    @endforeach
}

function refreshOrders() {
    loadOrders();
    showToast('{{ __("Orders refreshed") }}');
}

function startAutoRefresh() {
    refreshInterval = setInterval(() => {
        loadOrders();
        updateLastUpdateTime();
    }, 30000); // Refresh every 30 seconds
}

function updateLastUpdateTime() {
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
}

function showToast(message) {
    const toast = document.getElementById('refreshToast');
    toast.querySelector('.toast-body').textContent = message;
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
@endpush
