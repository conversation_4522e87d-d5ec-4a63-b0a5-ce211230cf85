<?php $__env->startSection("title", "نقطة البيع الرئيسية"); ?>

<?php $__env->startPush("styles"); ?>
<style>
    /* Styles specific to main.blade.php, complementing app.blade.php styles */
    .products-section .form-control-sm,
    .products-section .form-select-sm {
        margin-bottom: 0.75rem;
    }

    .cart-items {
        flex-grow: 1;
        min-height: 150px; /* Ensure cart has some min height */
    }

    .cart-summary .badge {
        font-size: 0.9em;
    }

    /* Responsive adjustments for product grid if needed, though app.blade.php handles main layout */
    @media (max-width: 576px) {
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
            gap: 0.5rem;
        }
        .product-card {
            padding: 0.5rem;
        }
        .product-card img {
            height: 60px;
        }
        .product-card strong {
            font-size: 0.8rem;
        }
        .cart-section h5 {
            font-size: 1.1rem;
        }
        .cart-summary span {
            font-size: 0.9rem;
        }
        .cart-summary .fs-5 {
            font-size: 1.1rem !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection("pos_header"); ?>
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="h5 mb-0">نقطة البيع الذكية</h1>
        <div>
            <span class="me-2 me-md-3 small">المستخدم:  مندوب مبيعات</span>
            <a href="" onclick="event.preventDefault(); document.getElementById("logout-form").submit();" class="btn btn-danger btn-sm">تسجيل الخروج</a>
            <form id="logout-form" action="" method="POST" style="display: none;">
                <?php echo csrf_field(); ?>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection("pos_toolbar"); ?>
<div class="pos-toolbar d-flex align-items-center">
    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#customerModal"><i class="bi bi-person-plus-fill me-1"></i> العميل</button>
    <button class="btn btn-sm btn-warning" onclick="holdCurrentSale()"><i class="bi bi-pause-circle-fill me-1"></i> تعليق</button>
    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#heldSalesModal" onclick="displayHeldSales()"><i class="bi bi-arrow-down-left-circle-fill me-1"></i> استرجاع</button>
    <button class="btn btn-sm btn-danger" onclick="clearCart()"><i class="bi bi-x-circle-fill me-1"></i> إلغاء</button>
    
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection("content"); ?>
<div class="pos-main-layout">
    <div class="products-section">
        <div class="row g-2 mb-2">
            <div class="col-md-7 col-12">
                <input type="text" id="product-search" class="form-control form-control-sm" placeholder="بحث بالاسم أو الباركود...">
            </div>
            <div class="col-md-5 col-12">
                <select id="product-category" class="form-select form-select-sm">
                    <option selected value="all">جميع الفئات</option>
                    <option value="1">مشروبات</option>
                    <option value="2">مأكولات</option>
                    <option value="3">حلويات</option>
                    
                </select>
            </div>
        </div>
        <div class="product-grid" id="product-grid-container">
            
            <p class="text-muted text-center w-100">جاري تحميل المنتجات...</p>
        </div>
    </div>
    <div class="cart-section d-flex flex-column">
        <h5 class="mb-3">سلة المشتريات <span id="selected-customer-name" class="badge bg-primary ms-2"></span></h5>
        <div class="cart-items flex-grow-1" id="cart-items-container">
            <p class="text-muted text-center mt-4">السلة فارغة</p>
        </div>
        <div class="cart-summary mt-auto">
            <div class="d-flex justify-content-between">
                <span>الإجمالي الفرعي:</span>
                <span id="cart-subtotal">0.00 ريال</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>الضريبة (15%):</span>
                <span id="cart-tax">0.00 ريال</span>
            </div>
            
            <div class="mt-2 mb-2">
                <label for="delivery-option" class="form-label visually-hidden">خيار التوصيل:</label>
                <select class="form-select form-select-sm" id="delivery-option" onchange="updateDeliveryCost()">
                    <option value="none" selected>استلام من المتجر</option>
                    <option value="smsa">سمسا</option>
                    <option value="aramex">أرامكس</option>
                </select>
            </div>
            <div class="d-flex justify-content-between" id="delivery-cost-line" style="display: none;">
                <span>تكلفة التوصيل:</span>
                <span id="cart-delivery-cost">0.00 ريال</span>
            </div>

            <hr class="my-2">
            <div class="d-flex justify-content-between fw-bold fs-5 mt-1">
                <span>الإجمالي الكلي:</span>
                <span id="cart-total">0.00 ريال</span>
            </div>
            <div class="d-grid mt-3">
                <button class="btn btn-success btn-lg" id="proceed-to-payment-btn" data-bs-toggle="modal" data-bs-target="#paymentModal" disabled><i class="bi bi-credit-card-fill me-2"></i>الدفع</button>
            </div>
        </div>
    </div>
</div>


<?php echo $__env->make("pos_frontend.partials.payment_modal", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make("pos_frontend.partials.customer_modal", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make("pos_frontend.partials.held_sales_modal", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make("pos_frontend.partials.receipt_modal", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush("scripts"); ?>
<script>
    // Existing JavaScript from the original file, may need adjustments based on UI changes.
    // For brevity, assuming the JS logic for cart, products, modals remains largely the same
    // unless specific UI elements it interacts with have changed IDs or fundamental structure.

    let cart = [];
    let products = []; 
    let selectedCustomer = null;
    let heldSales = []; 
    let deliveryCost = 0;

    const dummyProducts = [
        <?php for($i = 1; $i <= 20; $i++): ?>
        { id: <?php echo e($i); ?>, name: "منتج رقم <?php echo e($i); ?>", price: <?php echo e(rand(5, 150)); ?>, category: <?php echo e(rand(1,3)); ?>, image: "https://picsum.photos/seed/product<?php echo e($i); ?>/100/70" },
        <?php endfor; ?>
    ];

    function initializeProducts() {
        products = dummyProducts; 
        renderProducts();
    }

    function renderProducts(filteredProducts = null) {
        const productContainer = document.getElementById("product-grid-container");
        const prodsToRender = filteredProducts || products;
        if (prodsToRender.length === 0) {
            productContainer.innerHTML = "<p class=\"text-muted text-center w-100\">لا توجد منتجات تطابق البحث.</p>";
        } else {
            productContainer.innerHTML = prodsToRender.map(product => `
                <div class="product-card" onclick="addToCart(${product.id})" title="${product.name} - ${product.price.toFixed(2)} ريال">
                    <img src="${product.image}" alt="${product.name}" loading="lazy">
                    <strong>${product.name}</strong>
                    <div>${product.price.toFixed(2)} ريال</div>
                </div>
            `).join("");
        }
    }

    document.getElementById("product-search").addEventListener("input", function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const category = document.getElementById("product-category").value;
        filterAndRenderProducts(searchTerm, category);
    });

    document.getElementById("product-category").addEventListener("change", function(e) {
        const category = e.target.value;
        const searchTerm = document.getElementById("product-search").value.toLowerCase();
        filterAndRenderProducts(searchTerm, category);
    });

    function filterAndRenderProducts(searchTerm, category) {
        let filtered = products;
        if (category !== "all") {
            filtered = filtered.filter(p => p.category == category);
        }
        if (searchTerm) {
            filtered = filtered.filter(p => 
                p.name.toLowerCase().includes(searchTerm) || 
                p.id.toString().includes(searchTerm) // Basic barcode simulation by ID
            );
        }
        renderProducts(filtered);
    }

    function findProductById(productId) {
        return products.find(p => p.id === productId);
    }

    function addToCart(productId) {
        const product = findProductById(productId);
        if (!product) return;

        const existingProductIndex = cart.findIndex(item => item.id === product.id);
        if (existingProductIndex > -1) {
            cart[existingProductIndex].quantity++;
        } else {
            cart.push({ ...product, quantity: 1 });
        }
        renderCart();
    }

    function removeFromCart(productId) {
        cart = cart.filter(item => item.id !== productId);
        renderCart();
    }

    function updateQuantity(productId, change) {
        const productIndex = cart.findIndex(item => item.id === productId);
        if (productIndex > -1) {
            cart[productIndex].quantity += change;
            if (cart[productIndex].quantity <= 0) {
                removeFromCart(productId);
            } else {
                renderCart();
            }
        }
    }
    
    function clearCart() {
        cart = [];
        selectedCustomer = null;
        document.getElementById("selected-customer-name").textContent = "";
        document.getElementById("delivery-option").value = "none";
        updateDeliveryCost();
        renderCart();
        const amountTenderedInput = document.getElementById("amount-tendered");
        if(amountTenderedInput) amountTenderedInput.value = "";
        const changeDueElement = document.getElementById("change-due");
        if(changeDueElement) changeDueElement.textContent = "0.00 ريال";
    }

    function renderCart() {
        const cartContainer = document.getElementById("cart-items-container");
        const paymentButton = document.getElementById("proceed-to-payment-btn");

        if (cart.length === 0) {
            cartContainer.innerHTML = "<p class=\"text-muted text-center mt-4\">السلة فارغة</p>";
            paymentButton.disabled = true;
        } else {
            cartContainer.innerHTML = cart.map(item => `
                <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                    <div class="flex-grow-1 me-2">
                        <strong class="d-block text-truncate">${item.name}</strong>
                        <small>${item.price.toFixed(2)} ريال x ${item.quantity}</small>
                    </div>
                    <div class="d-flex align-items-center flex-shrink-0 me-2">
                        <button class="btn btn-sm btn-outline-secondary px-2" onclick="updateQuantity(${item.id}, -1)" aria-label="Decrease quantity">-</button>
                        <span class="mx-2">${item.quantity}</span>
                        <button class="btn btn-sm btn-outline-secondary px-2" onclick="updateQuantity(${item.id}, 1)" aria-label="Increase quantity">+</button>
                    </div>
                    <div class="fw-bold flex-shrink-0 me-2">${(item.price * item.quantity).toFixed(2)}</div>
                    <button class="btn btn-sm btn-outline-danger px-1 py-0 flex-shrink-0" onclick="removeFromCart(${item.id})" aria-label="Remove item"><i class="bi bi-trash"></i></button>
                </div>
            `).join("");
            paymentButton.disabled = false;
        }
        updateCartSummary();
    }

    function updateCartSummary() {
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const taxRate = 0.15; 
        const tax = subtotal * taxRate;
        const total = subtotal + tax + deliveryCost;

        document.getElementById("cart-subtotal").textContent = `${subtotal.toFixed(2)} ريال`;
        document.getElementById("cart-tax").textContent = `${tax.toFixed(2)} ريال`;
        document.getElementById("cart-delivery-cost").textContent = `${deliveryCost.toFixed(2)} ريال`;
        document.getElementById("cart-total").textContent = `${total.toFixed(2)} ريال`;
        
        const paymentModalTotalElement = document.getElementById("payment-modal-total");
        if (paymentModalTotalElement) {
            paymentModalTotalElement.textContent = total.toFixed(2);
        }
        const amountTenderedInput = document.getElementById("amount-tendered");
        if (amountTenderedInput) {
             amountTenderedInput.value = ""; 
        }
        const changeDueElement = document.getElementById("change-due");
        if(changeDueElement) changeDueElement.textContent = "0.00 ريال";
    }

    function updateDeliveryCost() {
        const deliveryOption = document.getElementById("delivery-option").value;
        const deliveryCostLine = document.getElementById("delivery-cost-line");
        
        if (deliveryOption === "smsa") {
            deliveryCost = 25.00;
            deliveryCostLine.style.display = "flex";
        } else if (deliveryOption === "aramex") {
            deliveryCost = 30.00;
            deliveryCostLine.style.display = "flex";
        } else {
            deliveryCost = 0;
            deliveryCostLine.style.display = "none";
        }
        updateCartSummary();
    }

    function selectCustomer(customerId, customerName) {
        selectedCustomer = { id: customerId, name: customerName };
        document.getElementById("selected-customer-name").textContent = customerName;
        var customerModalEl = document.getElementById("customerModal");
        if (customerModalEl) {
            var customerModalInstance = bootstrap.Modal.getInstance(customerModalEl);
            if (customerModalInstance) customerModalInstance.hide();
        }
    }

    function holdCurrentSale() {
        if (cart.length === 0) {
            alert("السلة فارغة لتعليقها!");
            return;
        }
        const saleToHold = {
            id: Date.now(), 
            cart: JSON.parse(JSON.stringify(cart)), // Deep copy
            customer: selectedCustomer ? JSON.parse(JSON.stringify(selectedCustomer)) : null, 
            deliveryOption: document.getElementById("delivery-option").value,
            deliveryCost: deliveryCost,
            timestamp: new Date()
        };
        heldSales.push(saleToHold);
        saveHeldSalesToStorage();
        clearCart();
        alert("تم تعليق الفاتورة الحالية بنجاح.");
        // displayHeldSales(); // Optionally show held sales modal immediately
    }

    function retrieveHeldSale(saleId) {
        const saleIndex = heldSales.findIndex(s => s.id === saleId);
        if (saleIndex > -1) {
            const saleToRetrieve = heldSales[saleIndex];
            cart = JSON.parse(JSON.stringify(saleToRetrieve.cart));
            selectedCustomer = saleToRetrieve.customer ? JSON.parse(JSON.stringify(saleToRetrieve.customer)) : null;
            
            document.getElementById("selected-customer-name").textContent = selectedCustomer ? selectedCustomer.name : "";
            document.getElementById("delivery-option").value = saleToRetrieve.deliveryOption || "none";
            // deliveryCost is part of saleToRetrieve, so set it before calling updateCartSummary via renderCart
            deliveryCost = saleToRetrieve.deliveryCost || 0;
            if (deliveryCost > 0) {
                 document.getElementById("delivery-cost-line").style.display = "flex";
            } else {
                 document.getElementById("delivery-cost-line").style.display = "none";
            }
            renderCart(); 
            
            heldSales.splice(saleIndex, 1);
            saveHeldSalesToStorage();
            displayHeldSales(); // Refresh modal list
            var heldModalEl = document.getElementById("heldSalesModal");
            if(heldModalEl){
                var heldModalInstance = bootstrap.Modal.getInstance(heldModalEl);
                if (heldModalInstance) heldModalInstance.hide();
            }
        } else {
            console.error("Sale not found for ID:", saleId);
        }
    }

    function deleteHeldSale(saleId) {
        const saleIndex = heldSales.findIndex(s => s.id === saleId);
        if (saleIndex > -1) {
            heldSales.splice(saleIndex, 1);
            saveHeldSalesToStorage();
            displayHeldSales(); // Refresh modal list
        } else {
             console.error("Sale not found for ID:", saleId);
        }
    }
    
    function loadHeldSalesFromStorage() {
        const storedSales = localStorage.getItem("posHeldSales");
        if (storedSales) {
            heldSales = JSON.parse(storedSales);
        }
    }

    function saveHeldSalesToStorage() {
        localStorage.setItem("posHeldSales", JSON.stringify(heldSales));
    }

    function displayHeldSales() {
        const container = document.getElementById("held-sales-list");
        if (!container) return;
        if (heldSales.length === 0) {
            container.innerHTML = "<p class=\"text-muted text-center\">لا توجد فواتير معلقة حالياً.</p>";
            return;
        }
        container.innerHTML = heldSales.map((sale, index) => `
            <li class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <strong>فاتورة #${sale.id}</strong> (${new Date(sale.timestamp).toLocaleTimeString("ar-SA")})<br>
                    <small>العميل: ${sale.customer ? sale.customer.name : "بدون عميل"} | المنتجات: ${sale.cart.length}</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="retrieveHeldSale(${sale.id})">استرجاع</button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteHeldSale(${sale.id})">حذف</button>
                </div>
            </li>
        `).join("");
    }

    // Initial setup
    document.addEventListener("DOMContentLoaded", () => {
        initializeProducts();
        renderCart();
        loadHeldSalesFromStorage(); // Load any previously held sales

        // Update held sales display when modal is shown
        var heldSalesModalEl = document.getElementById("heldSalesModal");
        if (heldSalesModalEl) {
            heldSalesModalEl.addEventListener("show.bs.modal", displayHeldSales);
        }
    });

</script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make("pos_frontend.layouts.app", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/pos_frontend/main/main.blade.php ENDPATH**/ ?>