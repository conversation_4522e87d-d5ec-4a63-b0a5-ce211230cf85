<?php

namespace App\Helpers;

class CurrencyHelper
{
    /**
     * تنسيق المبلغ مع رمز العملة
     *
     * @param float $amount المبلغ
     * @param string|null $currency كود العملة (اختياري)
     * @param bool $showSymbol إظهار رمز العملة
     * @return string
     */
    public static function format($amount, $currency = null, $showSymbol = true)
    {
        $currency = $currency ?: config('currency.default', 'SAR');
        $currencyConfig = config("currency.supported_currencies.{$currency}");
        
        if (!$currencyConfig) {
            $currencyConfig = config('currency.supported_currencies.SAR');
        }

        $decimalPlaces = $currencyConfig['decimal_places'] ?? 2;
        $thousandsSeparator = $currencyConfig['thousands_separator'] ?? ',';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
        $symbol = $currencyConfig['symbol'] ?? '﷼';
        $symbolPosition = $currencyConfig['symbol_position'] ?? 'after';

        $formattedAmount = number_format($amount, $decimalPlaces, $decimalSeparator, $thousandsSeparator);

        if (!$showSymbol) {
            return $formattedAmount;
        }

        if ($symbolPosition === 'before') {
            return $symbol . ' ' . $formattedAmount;
        } else {
            return $formattedAmount . ' ' . $symbol;
        }
    }

    /**
     * تنسيق المبلغ بالريال السعودي
     *
     * @param float $amount المبلغ
     * @param bool $showSymbol إظهار رمز العملة
     * @return string
     */
    public static function formatSAR($amount, $showSymbol = true)
    {
        return self::format($amount, 'SAR', $showSymbol);
    }

    /**
     * الحصول على رمز العملة
     *
     * @param string|null $currency كود العملة
     * @return string
     */
    public static function getSymbol($currency = null)
    {
        $currency = $currency ?: config('currency.default', 'SAR');
        return config("currency.supported_currencies.{$currency}.symbol", '﷼');
    }

    /**
     * الحصول على اسم العملة
     *
     * @param string|null $currency كود العملة
     * @param string $locale اللغة (ar/en)
     * @return string
     */
    public static function getName($currency = null, $locale = 'ar')
    {
        $currency = $currency ?: config('currency.default', 'SAR');
        $key = $locale === 'en' ? 'name_en' : 'name';
        return config("currency.supported_currencies.{$currency}.{$key}", 'ريال سعودي');
    }

    /**
     * الحصول على قائمة العملات المدعومة
     *
     * @param string $locale اللغة (ar/en)
     * @return array
     */
    public static function getSupportedCurrencies($locale = 'ar')
    {
        $currencies = config('currency.supported_currencies', []);
        $result = [];

        foreach ($currencies as $code => $config) {
            $nameKey = $locale === 'en' ? 'name_en' : 'name';
            $result[$code] = [
                'code' => $code,
                'name' => $config[$nameKey] ?? $config['name'],
                'symbol' => $config['symbol'],
            ];
        }

        return $result;
    }

    /**
     * تحويل النص إلى رقم
     *
     * @param string $amount النص
     * @param string|null $currency العملة
     * @return float
     */
    public static function parseAmount($amount, $currency = null)
    {
        $currency = $currency ?: config('currency.default', 'SAR');
        $currencyConfig = config("currency.supported_currencies.{$currency}");
        
        if (!$currencyConfig) {
            $currencyConfig = config('currency.supported_currencies.SAR');
        }

        $thousandsSeparator = $currencyConfig['thousands_separator'] ?? ',';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';

        // إزالة رمز العملة والمسافات
        $amount = preg_replace('/[^\d' . preg_quote($thousandsSeparator) . preg_quote($decimalSeparator) . '-]/', '', $amount);
        
        // استبدال فاصل الآلاف
        $amount = str_replace($thousandsSeparator, '', $amount);
        
        // استبدال فاصل العشرية
        if ($decimalSeparator !== '.') {
            $amount = str_replace($decimalSeparator, '.', $amount);
        }

        return (float) $amount;
    }

    /**
     * التحقق من صحة كود العملة
     *
     * @param string $currency كود العملة
     * @return bool
     */
    public static function isValidCurrency($currency)
    {
        return array_key_exists($currency, config('currency.supported_currencies', []));
    }

    /**
     * الحصول على العملة الافتراضية
     *
     * @return string
     */
    public static function getDefaultCurrency()
    {
        return config('currency.default', 'SAR');
    }

    /**
     * تنسيق المبلغ للعرض في الجداول
     *
     * @param float $amount المبلغ
     * @param string|null $currency العملة
     * @return string
     */
    public static function formatForTable($amount, $currency = null)
    {
        return self::format($amount, $currency, true);
    }

    /**
     * تنسيق المبلغ للإدخال في النماذج
     *
     * @param float $amount المبلغ
     * @param string|null $currency العملة
     * @return string
     */
    public static function formatForInput($amount, $currency = null)
    {
        return self::format($amount, $currency, false);
    }

    /**
     * تحويل المبلغ إلى كلمات (باللغة العربية)
     *
     * @param float $amount المبلغ
     * @param string|null $currency العملة
     * @return string
     */
    public static function toWords($amount, $currency = null)
    {
        $currency = $currency ?: config('currency.default', 'SAR');
        $currencyName = self::getName($currency, 'ar');
        
        // هذه دالة مبسطة - يمكن تطويرها لتحويل أكثر دقة
        $integerPart = (int) $amount;
        $decimalPart = round(($amount - $integerPart) * 100);
        
        $words = self::numberToArabicWords($integerPart);
        
        if ($decimalPart > 0) {
            $words .= ' و ' . self::numberToArabicWords($decimalPart) . ' هللة';
        }
        
        return $words . ' ' . $currencyName;
    }

    /**
     * تحويل الرقم إلى كلمات عربية (مبسط)
     *
     * @param int $number الرقم
     * @return string
     */
    private static function numberToArabicWords($number)
    {
        if ($number == 0) return 'صفر';
        
        $ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
        $tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
        $teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
        
        if ($number < 10) {
            return $ones[$number];
        } elseif ($number < 20) {
            return $teens[$number - 10];
        } elseif ($number < 100) {
            $ten = intval($number / 10);
            $one = $number % 10;
            return $tens[$ten] . ($one > 0 ? ' ' . $ones[$one] : '');
        } elseif ($number < 1000) {
            $hundred = intval($number / 100);
            $remainder = $number % 100;
            $result = $ones[$hundred] . ' مائة';
            if ($remainder > 0) {
                $result .= ' ' . self::numberToArabicWords($remainder);
            }
            return $result;
        } else {
            // للأرقام الأكبر يمكن إضافة المزيد من المنطق
            return number_format($number);
        }
    }
}
