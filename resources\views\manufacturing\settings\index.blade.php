@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.settings_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.settings_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-cog me-1"></i>
            {{ __(\'manufacturing.general_settings\') }}
        </div>
        <div class="card-body">
            {{-- Placeholder for settings form --}}
            <form>
                {{-- Work Order Numbering --}}
                <div class="mb-3 row">
                    <label for="wo_prefix" class="col-sm-3 col-form-label">{{ __(\'manufacturing.setting_wo_prefix\') }}</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="wo_prefix" value="WO-">
                    </div>
                </div>
                <div class="mb-3 row">
                    <label for="wo_next_number" class="col-sm-3 col-form-label">{{ __(\'manufacturing.setting_wo_next_number\') }}</label>
                    <div class="col-sm-9">
                        <input type="number" class="form-control" id="wo_next_number" value="1001">
                    </div>
                </div>

                {{-- Default Costing Method --}}
                <div class="mb-3 row">
                    <label for="default_costing_method" class="col-sm-3 col-form-label">{{ __(\'manufacturing.setting_default_costing_method\') }}</label>
                    <div class="col-sm-9">
                        <select class="form-select" id="default_costing_method">
                            <option value="standard">{{ __(\'manufacturing.costing_method_standard\') }}</option>
                            <option value="average">{{ __(\'manufacturing.costing_method_average\') }}</option>
                            <option value="fifo">{{ __(\'manufacturing.costing_method_fifo\') }}</option>
                            <option value="lifo">{{ __(\'manufacturing.costing_method_lifo\') }}</option>
                        </select>
                    </div>
                </div>
                
                {{-- Add more settings as defined in mfg_settings table and UI/UX design --}}

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">{{ __(\'general.save_settings\') }}</button>
                </div>
            </form>
        </div>
    </div>

    {{-- Potentially other setting groups can be in separate cards --}}

</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page --}}
@endpush

