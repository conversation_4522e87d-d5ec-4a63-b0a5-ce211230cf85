<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الخاصة بالمستأجر
     */
    public function index()
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى لوحة تحكم المستأجر.');
        }
        
        // الحصول على معلومات الاشتراك
        $subscription = Subscription::where('tenant_id', $user->id)
            ->where('status', 'active')
            ->orderBy('end_date', 'desc')
            ->first();
        
        // الحصول على عدد المستخدمين التابعين للمستأجر
        $usersCount = $user->tenantUsers()->count();
        
        // إحصائيات أخرى يمكن إضافتها حسب احتياجات النظام
        
        return view('tenant.dashboard', compact('user', 'subscription', 'usersCount'));
    }
    
    /**
     * عرض صفحة إدارة الاشتراك
     */
    public function subscription()
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة الاشتراك.');
        }
        
        // الحصول على معلومات الاشتراك
        $subscription = Subscription::where('tenant_id', $user->id)
            ->orderBy('end_date', 'desc')
            ->first();
        
        // الحصول على سجل المدفوعات
        $payments = $subscription ? $subscription->payments()->latest()->get() : collect();
        
        return view('tenant.subscription', compact('user', 'subscription', 'payments'));
    }
    
    /**
     * عرض صفحة إدارة المستخدمين التابعين للمستأجر
     */
    public function users()
    {
        $user = Auth::user();
        
        // التحقق من أن المستخدم هو مستأجر
        if (!$user->hasRole('tenant')) {
            return redirect()->route('home')->with('error', 'ليس لديك صلاحية الوصول إلى إدارة المستخدمين.');
        }
        
        // الحصول على المستخدمين التابعين للمستأجر
        $users = $user->tenantUsers()->with('roles')->get();
        
        // الحصول على معلومات الاشتراك لمعرفة الحد الأقصى للمستخدمين
        $subscription = Subscription::where('tenant_id', $user->id)
            ->where('status', 'active')
            ->orderBy('end_date', 'desc')
            ->first();
        
        $maxUsers = $subscription ? $subscription->plan->max_users : 0;
        
        return view('tenant.users', compact('user', 'users', 'subscription', 'maxUsers'));
    }
}
