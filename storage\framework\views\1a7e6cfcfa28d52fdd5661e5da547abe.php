<?php $__env->startSection("content"); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0"><?php echo e(__("Restaurant Reports")); ?></h1>
                <div>
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> <?php echo e(__("Print")); ?>

                    </button>
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> <?php echo e(__("Export")); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($todayStats['orders_count']); ?></h4>
                            <p class="mb-0"><?php echo e(__("Today's Orders")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-receipt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($todayStats['revenue'], 2)); ?></h4>
                            <p class="mb-0"><?php echo e(__("Today's Revenue")); ?> (ريال)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($todayStats['avg_order_value'], 2)); ?></h4>
                            <p class="mb-0"><?php echo e(__("Avg Order Value")); ?> (ريال)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($monthStats['orders_count']); ?></h4>
                            <p class="mb-0"><?php echo e(__("This Month's Orders")); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Available Reports")); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo e(route('admin.restaurant.reports.sales')); ?>" class="text-decoration-none">
                                <div class="card report-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                                        <h6 class="card-title"><?php echo e(__("Sales Reports")); ?></h6>
                                        <p class="card-text text-muted"><?php echo e(__("Detailed sales analysis and trends")); ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo e(route('admin.restaurant.reports.menu_performance')); ?>" class="text-decoration-none">
                                <div class="card report-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-utensils fa-3x text-success mb-3"></i>
                                        <h6 class="card-title"><?php echo e(__("Menu Performance")); ?></h6>
                                        <p class="card-text text-muted"><?php echo e(__("Best selling items and categories")); ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo e(route('admin.restaurant.reports.table_turnover')); ?>" class="text-decoration-none">
                                <div class="card report-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-table fa-3x text-info mb-3"></i>
                                        <h6 class="card-title"><?php echo e(__("Table Turnover")); ?></h6>
                                        <p class="card-text text-muted"><?php echo e(__("Table utilization and efficiency")); ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo e(route('admin.restaurant.reports.kitchen_performance')); ?>" class="text-decoration-none">
                                <div class="card report-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-fire fa-3x text-warning mb-3"></i>
                                        <h6 class="card-title"><?php echo e(__("Kitchen Performance")); ?></h6>
                                        <p class="card-text text-muted"><?php echo e(__("Preparation times and efficiency")); ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Monthly Revenue Trend")); ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Recent Orders")); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><?php echo e(__("Order")); ?></th>
                                    <th><?php echo e(__("Table")); ?></th>
                                    <th><?php echo e(__("Amount")); ?></th>
                                    <th><?php echo e(__("Status")); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Recent orders will be loaded here -->
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        <?php echo e(__("Loading recent orders...")); ?>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__("Top Menu Items")); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><?php echo e(__("Item")); ?></th>
                                    <th><?php echo e(__("Orders")); ?></th>
                                    <th><?php echo e(__("Revenue")); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Top items will be loaded here -->
                                <tr>
                                    <td colspan="3" class="text-center text-muted">
                                        <?php echo e(__("Loading top items...")); ?>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.report-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.card {
    border-radius: 12px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeRevenueChart();
    loadRecentOrders();
    loadTopItems();
});

function initializeRevenueChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    // Sample data - replace with actual data from backend
    const data = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: '<?php echo e(__("Revenue")); ?> (ريال)',
            data: [12000, 15000, 18000, 14000, 20000, 22000],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ريال';
                        }
                    }
                }
            }
        }
    });
}

function loadRecentOrders() {
    // Simulate loading recent orders
    setTimeout(() => {
        const tbody = document.querySelector('.table tbody');
        tbody.innerHTML = `
            <tr>
                <td>#ORD001</td>
                <td>Table 5</td>
                <td>125.50 ريال</td>
                <td><span class="badge bg-success">Completed</span></td>
            </tr>
            <tr>
                <td>#ORD002</td>
                <td>Table 3</td>
                <td>89.25 ريال</td>
                <td><span class="badge bg-warning">Preparing</span></td>
            </tr>
            <tr>
                <td>#ORD003</td>
                <td>Table 1</td>
                <td>156.75 ريال</td>
                <td><span class="badge bg-info">Ready</span></td>
            </tr>
        `;
    }, 1000);
}

function loadTopItems() {
    // Simulate loading top items
    setTimeout(() => {
        const tbody = document.querySelectorAll('.table tbody')[1];
        tbody.innerHTML = `
            <tr>
                <td>Grilled Chicken</td>
                <td>45</td>
                <td>2,250 ريال</td>
            </tr>
            <tr>
                <td>Caesar Salad</td>
                <td>32</td>
                <td>960 ريال</td>
            </tr>
            <tr>
                <td>Beef Burger</td>
                <td>28</td>
                <td>1,400 ريال</td>
            </tr>
        `;
    }, 1200);
}

function exportToExcel() {
    // Implement Excel export functionality
    alert('<?php echo e(__("Excel export functionality will be implemented")); ?>');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/restaurant/reports/index.blade.php ENDPATH**/ ?>