@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>{{ __("Tickets") }}</h1>
        <a href="{{ route("admin.ticketing.tickets.create") }}" class="btn btn-primary">{{ __("Create New Ticket") }}</a>
    </div>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover">
                <thead>
                    <tr>
                        <th>{{ __("Ticket #") }}</th>
                        <th>{{ __("Subject") }}</th>
                        <th>{{ __("User") }}</th>
                        <th>{{ __("Category") }}</th>
                        <th>{{ __("Priority") }}</th>
                        <th>{{ __("Status") }}</th>
                        <th>{{ __("Assigned To") }}</th>
                        <th>{{ __("Last Reply") }}</th>
                        <th>{{ __("Created At") }}</th>
                        <th>{{ __("Actions") }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($tickets as $ticket)
                        <tr>
                            <td><a href="{{ route("admin.ticketing.tickets.show", $ticket->id) }}">{{ $ticket->ticket_number }}</a></td>
                            <td>{{ Str::limit($ticket->subject, 40) }}</td>
                            <td>{{ $ticket->user->name }}</td>
                            <td><span class="badge" style="background-color: {{ $ticket->category->color ?? '#ddd' }}">{{ $ticket->category->name }}</span></td>
                            <td><span class="badge" style="background-color: {{ $ticket->priority->color ?? '#ddd' }}">{{ $ticket->priority->name }}</span></td>
                            <td><span class="badge" style="background-color: {{ $ticket->status->color ?? '#ddd' }}">{{ $ticket->status->name }}</span></td>
                            <td>{{ $ticket->assignedTo->name ?? __("Unassigned") }}</td>
                            <td>{{ $ticket->last_reply_at ? $ticket->last_reply_at->diffForHumans() : __("No replies yet") }}</td>
                            <td>{{ $ticket->created_at->format("Y-m-d H:i") }}</td>
                            <td>
                                <a href="{{ route("admin.ticketing.tickets.show", $ticket->id) }}" class="btn btn-sm btn-info">{{ __("View") }}</a>
                                <a href="{{ route("admin.ticketing.tickets.edit", $ticket->id) }}" class="btn btn-sm btn-warning">{{ __("Edit") }}</a>
                                <form action="{{ route("admin.ticketing.tickets.destroy", $ticket->id) }}" method="POST" style="display: inline-block;">
                                    @csrf
                                    @method("DELETE")
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("{{ __("Are you sure you want to delete this ticket?") }}")">{{ __("Delete") }}</button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" class="text-center">{{ __("No tickets found.") }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            {{ $tickets->links() }}
        </div>
    </div>
</div>
@endsection

