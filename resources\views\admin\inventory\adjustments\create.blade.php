@extends('layouts.admin')

@section('title', 'إضافة تسوية مخزون جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة تسوية مخزون جديدة</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.inventory.adjustments.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.inventory.adjustments.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_number">رقم التسوية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="adjustment_number" name="adjustment_number" value="ADJ-{{ date('Y-m-d') }}-{{ str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT) }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_date">تاريخ التسوية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="adjustment_date" name="adjustment_date" value="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">المستودع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">اختر المستودع</option>
                                        <option value="1">المستودع الرئيسي</option>
                                        <option value="2">مستودع الفرع الثاني</option>
                                        <option value="3">مستودع المواد الخام</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_type">نوع التسوية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="adjustment_type" name="adjustment_type" required>
                                        <option value="">اختر نوع التسوية</option>
                                        <option value="increase">زيادة في المخزون</option>
                                        <option value="decrease">نقص في المخزون</option>
                                        <option value="damage">أصناف تالفة</option>
                                        <option value="expired">أصناف منتهية الصلاحية</option>
                                        <option value="lost">أصناف مفقودة</option>
                                        <option value="found">أصناف موجودة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="reason">سبب التسوية <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="reason" name="reason" rows="3" required placeholder="اكتب سبب التسوية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الأصناف -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>أصناف التسوية</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="adjustmentItemsTable">
                                        <thead>
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية الحالية</th>
                                                <th>الكمية الجديدة</th>
                                                <th>الفرق</th>
                                                <th>السعر</th>
                                                <th>القيمة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="items[0][item_id]" required>
                                                        <option value="">اختر الصنف</option>
                                                        <option value="1">صنف تجريبي 1</option>
                                                        <option value="2">صنف تجريبي 2</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][current_quantity]" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][new_quantity]" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][difference]" readonly>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][unit_price]" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="items[0][total_value]" step="0.01" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addRow()">
                                    <i class="fas fa-plus"></i> إضافة صنف
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_value">القيمة الإجمالية</label>
                                    <input type="number" class="form-control" id="total_value" name="total_value" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="pending">معلق</option>
                                        <option value="approved">معتمد</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التسوية
                            </button>
                            <a href="{{ route('admin.inventory.adjustments.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let rowIndex = 1;

function addRow() {
    const tbody = document.querySelector('#adjustmentItemsTable tbody');
    const newRow = `
        <tr>
            <td>
                <select class="form-control" name="items[${rowIndex}][item_id]" required>
                    <option value="">اختر الصنف</option>
                    <option value="1">صنف تجريبي 1</option>
                    <option value="2">صنف تجريبي 2</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][current_quantity]" readonly>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][new_quantity]" required>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][difference]" readonly>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][unit_price]" step="0.01" required>
            </td>
            <td>
                <input type="number" class="form-control" name="items[${rowIndex}][total_value]" step="0.01" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', newRow);
    rowIndex++;
}

function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotal();
}

function calculateTotal() {
    let total = 0;
    const totalValueInputs = document.querySelectorAll('input[name*="[total_value]"]');
    totalValueInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    document.getElementById('total_value').value = total.toFixed(2);
}

// حساب الفرق والقيمة عند تغيير الكميات والأسعار
document.addEventListener('input', function(e) {
    if (e.target.name && e.target.name.includes('[new_quantity]')) {
        const row = e.target.closest('tr');
        const currentQty = parseFloat(row.querySelector('input[name*="[current_quantity]"]').value) || 0;
        const newQty = parseFloat(e.target.value) || 0;
        const difference = newQty - currentQty;
        row.querySelector('input[name*="[difference]"]').value = difference;
        
        const unitPrice = parseFloat(row.querySelector('input[name*="[unit_price]"]').value) || 0;
        const totalValue = Math.abs(difference) * unitPrice;
        row.querySelector('input[name*="[total_value]"]').value = totalValue.toFixed(2);
        
        calculateTotal();
    }
    
    if (e.target.name && e.target.name.includes('[unit_price]')) {
        const row = e.target.closest('tr');
        const difference = parseFloat(row.querySelector('input[name*="[difference]"]').value) || 0;
        const unitPrice = parseFloat(e.target.value) || 0;
        const totalValue = Math.abs(difference) * unitPrice;
        row.querySelector('input[name*="[total_value]"]').value = totalValue.toFixed(2);
        
        calculateTotal();
    }
});
</script>
@endpush
