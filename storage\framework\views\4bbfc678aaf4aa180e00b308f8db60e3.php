<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'لوحة التحكم'); ?> - نظام المحاسبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #2ecc71;
            --text-light: #ecf0f1;
            --text-dark: #2c3e50;
            --sidebar-width: 280px;
            --transition-speed: 0.3s;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all var(--transition-speed);
        }

        /* Modern Sidebar Styles */
        .sidebar {
            min-height: 100vh;
            max-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1a2530 100%);
            color: var(--text-light);
            position: fixed;
            width: var(--sidebar-width);
            box-shadow: var(--box-shadow);
            z-index: 1000;
            transition: all var(--transition-speed);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
        }

        .sidebar::-webkit-scrollbar {
            width: 8px;
            display: block;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            border: 2px solid rgba(0, 0, 0, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            background: rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .sidebar-toggle {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: var(--text-light);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-speed);
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Collapsed Sidebar */
        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar.collapsed .sidebar-header h4,
        .sidebar.collapsed .sidebar-header p,
        .sidebar.collapsed .sidebar-menu span,
        .sidebar.collapsed .menu-category {
            display: none;
        }

        .sidebar.collapsed .sidebar-toggle i {
            transform: rotate(180deg);
        }

        .sidebar.collapsed .sidebar-menu a {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar.collapsed .sidebar-menu i {
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar.collapsed + .main-content {
            margin-right: 70px;
        }

        .sidebar-header h4 {
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--text-light);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .sidebar-header p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .sidebar-menu {
            padding: 20px 10px;
            max-height: calc(100vh - 100px);
            overflow-y: auto;
        }

        .sidebar-menu a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: all 0.5s;
        }

        .sidebar-menu a:hover:before {
            right: 100%;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .sidebar-menu a.active {
            background: linear-gradient(90deg, var(--secondary-color), rgba(52, 152, 219, 0.7));
            border-right: 4px solid var(--accent-color);
        }

        .sidebar-menu i {
            margin-left: 12px;
            font-size: 1.2rem;
            min-width: 25px;
            text-align: center;
            transition: all var(--transition-speed);
        }

        .sidebar-menu a:hover i, .sidebar-menu a.active i {
            transform: scale(1.2);
        }

        /* Menu Category Styles */
        .menu-category {
            position: relative;
            padding: 0 15px;
        }

        .menu-category:after {
            content: '';
            position: absolute;
            right: 15px;
            top: 50%;
            width: 70%;
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
        }

        .menu-category small {
            font-size: 0.75rem;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.5) !important;
            position: relative;
            z-index: 1;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1a2530 100%);
            padding-left: 10px;
        }

        /* Main Content Styles */
        .main-content {
            margin-right: var(--sidebar-width);
            padding: 25px;
            transition: all var(--transition-speed);
        }

        .admin-card {
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 25px;
            margin-bottom: 25px;
            transition: all var(--transition-speed);
        }

        .admin-card:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-3px);
        }

        .page-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .navbar {
            background-color: #fff;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }

        .dropdown-menu {
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        /* Sidebar Dropdown Styles */
        .sidebar-menu .dropdown {
            position: relative;
        }

        .sidebar-menu .dropdown-toggle::after {
            display: inline-block;
            margin-right: 5px;
            vertical-align: middle;
            content: "";
            border-top: 0.3em solid;
            border-left: 0.3em solid transparent;
            border-right: 0.3em solid transparent;
            position: absolute;
            left: 15px;
        }

        .sidebar-menu .dropdown-menu {
            background-color: #1a2530;
            border: none;
            border-radius: 8px;
            margin-top: 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            padding: 8px;
            min-width: 200px;
        }

        .sidebar-menu .dropdown-item {
            color: var(--text-light);
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 4px;
        }

        .sidebar-menu .dropdown-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Avatar Styles */
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            font-size: 1rem;
            font-weight: bold;
        }

        /* Alert Styles */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        /* Badge Styles */
        .badge {
            font-weight: 500;
        }

        /* Button Styles */
        .btn {
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4>نظام المحاسبة</h4>
            <p class="mb-0">لوحة تحكم الإدارة</p>
            <button id="sidebarToggle" class="sidebar-toggle">
                <i class="bi bi-chevron-right"></i>
            </button>
        </div>
        <?php echo $__env->make('partials.sidebar-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>

    <div class="main-content">
        <nav class="navbar navbar-expand-lg mb-4">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <h4 class="mb-0 text-primary fw-bold"><?php echo $__env->yieldContent('header', 'لوحة التحكم'); ?></h4>
                    <div class="badge bg-primary bg-opacity-10 text-primary ms-2 py-2 px-3 rounded-pill">
                        <i class="bi bi-calendar3"></i> <?php echo e(date('Y-m-d')); ?>

                    </div>
                </div>
                <div class="ms-auto d-flex align-items-center">
                    <div class="position-relative me-3">
                        <a href="#" class="btn btn-light position-relative rounded-circle p-2">
                            <i class="bi bi-bell-fill text-muted"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                2
                            </span>
                        </a>
                    </div>
                    <div class="dropdown">
                        <a class="btn btn-light dropdown-toggle d-flex align-items-center" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="avatar-circle me-2 bg-primary text-white d-flex align-items-center justify-content-center">
                                <?php echo e(Auth::check() ? substr(Auth::user()->name, 0, 1) : 'م'); ?>

                            </div>
                            <span><?php echo e(Auth::check() ? Auth::user()->name : 'المستخدم'); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="bi bi-person-circle me-2"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="bi bi-gear-fill me-2"></i> الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo e(route('logout.confirm')); ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <?php if(session('is_switched_tenant')): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                        <div>أنت الآن تستخدم حساب المستأجر: <strong><?php echo e(session('tenant_name')); ?></strong></div>
                    </div>
                    <a href="<?php echo e(route('tenant_switch.back')); ?>" class="btn btn-sm btn-primary">
                        <i class="bi bi-box-arrow-right me-1"></i>العودة إلى حساب السوبر أدمن
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle-fill fs-4 me-2"></i>
                    <div><?php echo e(session('success')); ?></div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                    <div><?php echo e(session('error')); ?></div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
    // حماية عامة من أخطاء DataTables
    $(document).ready(function() {
        // التحقق من وجود صفحة الحسابات
        if (window.location.pathname.includes('/accounts/new') || window.location.pathname.includes('/accounts')) {
            // منع تطبيق DataTables على جداول الحسابات
            $('.modern-accounts-table, .accounts-table').removeClass('table').addClass('accounts-custom-table');

            // تعطيل DataTables على هذه الجداول
            var originalDataTable = $.fn.DataTable;
            $.fn.DataTable = function(options) {
                if ($(this).hasClass('modern-accounts-table') || $(this).hasClass('accounts-table') || $(this).hasClass('accounts-custom-table') || $(this).attr('data-no-datatables')) {
                    console.log('DataTables منع على جدول الحسابات');
                    return this;
                }
                return originalDataTable.call(this, options);
            };
        }

        // حماية عامة من أخطاء DataTables
        var originalDataTableInit = $.fn.DataTable;
        $.fn.DataTable = function(options) {
            try {
                // التحقق من وجود الجدول
                var table = $(this);
                if (table.length === 0) {
                    console.warn('لا يوجد جدول للتطبيق عليه DataTables');
                    return this;
                }

                // التحقق من عدد الأعمدة
                var headerCols = table.find('thead tr:first th').length;
                var bodyRows = table.find('tbody tr');
                var bodyCols = 0;

                // البحث عن أول صف يحتوي على بيانات حقيقية (ليس رسالة فارغة)
                bodyRows.each(function() {
                    var row = $(this);
                    if (!row.find('td[colspan]').length) {
                        bodyCols = row.find('td').length;
                        return false; // توقف عند أول صف صحيح
                    }
                });

                // إذا لم توجد بيانات، لا تطبق DataTables
                if (bodyCols === 0) {
                    console.log('لا توجد بيانات في الجدول، تم تخطي DataTables');
                    return this;
                }

                if (headerCols !== bodyCols) {
                    console.warn('تحذير: عدم تطابق الأعمدة في الجدول. Header: ' + headerCols + ', Body: ' + bodyCols);
                    // تطبيق إعدادات آمنة
                    options = options || {};
                    options.destroy = true;
                    options.columnDefs = options.columnDefs || [];

                    // تعطيل الترتيب للعمود الأخير (عادة الإجراءات)
                    if (headerCols > 0) {
                        options.columnDefs.push({ "orderable": false, "targets": [headerCols - 1] });
                    }
                }

                return originalDataTableInit.call(this, options);
            } catch (error) {
                console.error('خطأ في تهيئة DataTables:', error);
                return this;
            }
        };
    });
    </script>
    <script src="<?php echo e(asset('js/logout.js')); ?>"></script>
    <script>
        // Toggle Sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mainContent = document.querySelector('.main-content');

            // Check if sidebar state is saved in localStorage
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');

                // Save state to localStorage
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });

            // Add hover effect for collapsed sidebar
            sidebar.addEventListener('mouseenter', function() {
                if (sidebar.classList.contains('collapsed')) {
                    sidebar.style.width = 'var(--sidebar-width)';

                    // Show hidden elements
                    const hiddenElements = sidebar.querySelectorAll('.sidebar-header h4, .sidebar-header p, .sidebar-menu span, .menu-category');
                    hiddenElements.forEach(el => el.style.display = 'block');

                    // Adjust menu items
                    const menuItems = sidebar.querySelectorAll('.sidebar-menu a');
                    menuItems.forEach(item => {
                        item.style.justifyContent = 'flex-start';
                        item.style.padding = '12px 15px';
                    });

                    // Adjust icons
                    const icons = sidebar.querySelectorAll('.sidebar-menu i');
                    icons.forEach(icon => {
                        icon.style.margin = '0 12px 0 0';
                        icon.style.fontSize = '1.2rem';
                    });
                }
            });

            sidebar.addEventListener('mouseleave', function() {
                if (sidebar.classList.contains('collapsed')) {
                    sidebar.style.width = '70px';

                    // Hide elements again
                    const hiddenElements = sidebar.querySelectorAll('.sidebar-header h4, .sidebar-header p, .sidebar-menu span, .menu-category');
                    hiddenElements.forEach(el => el.style.display = 'none');

                    // Reset menu items
                    const menuItems = sidebar.querySelectorAll('.sidebar-menu a');
                    menuItems.forEach(item => {
                        item.style.justifyContent = 'center';
                        item.style.padding = '15px 0';
                    });

                    // Reset icons
                    const icons = sidebar.querySelectorAll('.sidebar-menu i');
                    icons.forEach(icon => {
                        icon.style.margin = '0';
                        icon.style.fontSize = '1.5rem';
                    });
                }
            });

            // Add submenu functionality
            const menuItems = document.querySelectorAll('.sidebar-menu a');
            menuItems.forEach(item => {
                // Add tooltip for collapsed sidebar
                item.setAttribute('title', item.querySelector('span').textContent);
            });
        });
    </script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/layouts/admin.blade.php ENDPATH**/ ?>