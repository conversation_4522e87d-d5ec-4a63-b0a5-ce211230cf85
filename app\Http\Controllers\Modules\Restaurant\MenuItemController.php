<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\MenuItem;
use App\Models\Modules\Restaurant\MenuCategory;
use App\Models\Modules\Restaurant\KitchenStation;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MenuItemController extends Controller
{
    public function index()
    {
        $menuItems = MenuItem::with(['category', 'kitchenStation', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(20);

        return view('admin.restaurant.menu-items.index', compact('menuItems'));
    }

    public function create()
    {
        $categories = MenuCategory::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $kitchenStations = KitchenStation::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.menu-items.form', compact('categories', 'kitchenStations', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'sku' => 'nullable|string|max:100|unique:menu_items,sku',
            'barcode' => 'nullable|string|max:100',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preparation_time' => 'nullable|integer|min:0',
            'calories' => 'nullable|integer|min:0',
            'category_id' => 'required|exists:menu_categories,id',
            'kitchen_station_id' => 'nullable|exists:kitchen_stations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_spicy' => 'boolean',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('image')) {
            $validatedData['image'] = $request->file('image')->store('menu-items', 'public');
        }

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_spicy'] = $request->has('is_spicy');
        $validatedData['is_vegetarian'] = $request->has('is_vegetarian');
        $validatedData['is_vegan'] = $request->has('is_vegan');
        $validatedData['is_gluten_free'] = $request->has('is_gluten_free');
        $validatedData['is_available'] = $request->has('is_available');
        $validatedData['is_active'] = $request->has('is_active');

        MenuItem::create($validatedData);

        return redirect()->route('admin.restaurant.menu-items.index')
            ->with('success', __('Menu item created successfully.'));
    }

    public function show(MenuItem $menuItem)
    {
        $menuItem->load(['category', 'kitchenStation', 'branch', 'modifiers.options']);
        return view('admin.restaurant.menu-items.show', compact('menuItem'));
    }

    public function edit(MenuItem $menuItem)
    {
        $categories = MenuCategory::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $kitchenStations = KitchenStation::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.menu-items.form', compact('menuItem', 'categories', 'kitchenStations', 'branches'));
    }

    public function update(Request $request, MenuItem $menuItem)
    {
        $validatedData = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'sku' => 'nullable|string|max:100|unique:menu_items,sku,' . $menuItem->id,
            'barcode' => 'nullable|string|max:100',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preparation_time' => 'nullable|integer|min:0',
            'calories' => 'nullable|integer|min:0',
            'category_id' => 'required|exists:menu_categories,id',
            'kitchen_station_id' => 'nullable|exists:kitchen_stations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_spicy' => 'boolean',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('image')) {
            if ($menuItem->image) {
                Storage::disk('public')->delete($menuItem->image);
            }
            $validatedData['image'] = $request->file('image')->store('menu-items', 'public');
        }

        $validatedData['is_spicy'] = $request->has('is_spicy');
        $validatedData['is_vegetarian'] = $request->has('is_vegetarian');
        $validatedData['is_vegan'] = $request->has('is_vegan');
        $validatedData['is_gluten_free'] = $request->has('is_gluten_free');
        $validatedData['is_available'] = $request->has('is_available');
        $validatedData['is_active'] = $request->has('is_active');

        $menuItem->update($validatedData);

        return redirect()->route('admin.restaurant.menu-items.index')
            ->with('success', __('Menu item updated successfully.'));
    }

    public function destroy(MenuItem $menuItem)
    {
        if ($menuItem->image) {
            Storage::disk('public')->delete($menuItem->image);
        }

        $menuItem->delete();

        return redirect()->route('admin.restaurant.menu-items.index')
            ->with('success', __('Menu item deleted successfully.'));
    }

    public function updateAvailability(Request $request, MenuItem $menuItem)
    {
        $request->validate([
            'is_available' => 'required|boolean'
        ]);

        $menuItem->update(['is_available' => $request->is_available]);

        return response()->json([
            'success' => true,
            'message' => __('Menu item availability updated successfully.'),
            'is_available' => $menuItem->is_available
        ]);
    }
}
