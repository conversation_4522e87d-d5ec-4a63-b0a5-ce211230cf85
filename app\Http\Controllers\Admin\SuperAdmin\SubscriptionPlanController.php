<?php

namespace App\Http\Controllers\Admin\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\InvoiceLimit;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SubscriptionPlanController extends Controller
{
    /**
     * عرض قائمة الباقات
     */
    public function index()
    {
        $plans = SubscriptionPlan::withCount('subscriptions')->with('invoiceLimit')->latest()->get();
        return view('admin.super_admin.subscription_plans.index', compact('plans'));
    }

    /**
     * عرض نموذج إنشاء باقة جديدة
     */
    public function create()
    {
        return view('admin.super_admin.subscription_plans.form');
    }

    /**
     * حفظ باقة جديدة
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:subscription_plans,code',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,semi_annually,annually',
            'max_users' => 'required|integer|min:1',
            'max_branches' => 'required|integer|min:1',
            'has_pos' => 'boolean',
            'has_inventory' => 'boolean',
            'has_accounting' => 'boolean',
            'has_manufacturing' => 'boolean',
            'has_hr' => 'boolean',
            'has_crm' => 'boolean',
            'has_purchases' => 'boolean',
            'has_sales' => 'boolean',
            'has_reports' => 'boolean',
            'has_api_access' => 'boolean',
            'storage_space_gb' => 'required|integer|min:1',
            'is_active' => 'boolean',
            'monthly_invoice_limit' => 'required|integer|min:1',
            'total_invoice_limit' => 'nullable|integer|min:0',
        ]);

        // تحويل القيم البوليانية
        foreach (['has_pos', 'has_inventory', 'has_accounting', 'has_manufacturing', 'has_hr', 'has_crm', 'has_purchases', 'has_sales', 'has_reports', 'has_api_access', 'is_active'] as $field) {
            $validatedData[$field] = isset($validatedData[$field]) ? true : false;
        }

        // إنشاء كود إذا لم يتم توفيره
        if (empty($validatedData['code'])) {
            $validatedData['code'] = Str::slug($validatedData['name']) . '-' . Str::random(5);
        }

        // إنشاء الباقة
        $plan = SubscriptionPlan::create($validatedData);

        // إنشاء حد الفواتير للباقة
        InvoiceLimit::create([
            'subscription_plan_id' => $plan->id,
            'monthly_invoice_limit' => $validatedData['monthly_invoice_limit'],
            'total_invoice_limit' => $validatedData['total_invoice_limit'],
            'is_active' => true,
        ]);

        return redirect()->route('admin.super_admin.subscription_plans.index')
            ->with('success', 'تم إنشاء الباقة بنجاح');
    }

    /**
     * عرض تفاصيل الباقة
     */
    public function show(SubscriptionPlan $subscriptionPlan)
    {
        $subscriptionPlan->load(['subscriptions.tenant']);
        $invoiceLimit = InvoiceLimit::where('subscription_plan_id', $subscriptionPlan->id)->first();

        return view('admin.super_admin.subscription_plans.show', compact('subscriptionPlan', 'invoiceLimit'));
    }

    /**
     * عرض نموذج تعديل الباقة
     */
    public function edit(SubscriptionPlan $subscriptionPlan)
    {
        $invoiceLimit = InvoiceLimit::where('subscription_plan_id', $subscriptionPlan->id)->first();

        return view('admin.super_admin.subscription_plans.form', compact('subscriptionPlan', 'invoiceLimit'));
    }

    /**
     * تحديث الباقة
     */
    public function update(Request $request, SubscriptionPlan $subscriptionPlan)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:subscription_plans,code,' . $subscriptionPlan->id,
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,semi_annually,annually',
            'max_users' => 'required|integer|min:1',
            'max_branches' => 'required|integer|min:1',
            'has_pos' => 'boolean',
            'has_inventory' => 'boolean',
            'has_accounting' => 'boolean',
            'has_manufacturing' => 'boolean',
            'has_hr' => 'boolean',
            'has_crm' => 'boolean',
            'has_purchases' => 'boolean',
            'has_sales' => 'boolean',
            'has_reports' => 'boolean',
            'has_api_access' => 'boolean',
            'storage_space_gb' => 'required|integer|min:1',
            'is_active' => 'boolean',
            'monthly_invoice_limit' => 'required|integer|min:1',
            'total_invoice_limit' => 'nullable|integer|min:0',
        ]);

        // تحويل القيم البوليانية
        foreach (['has_pos', 'has_inventory', 'has_accounting', 'has_manufacturing', 'has_hr', 'has_crm', 'has_purchases', 'has_sales', 'has_reports', 'has_api_access', 'is_active'] as $field) {
            $validatedData[$field] = isset($validatedData[$field]) ? true : false;
        }

        // تحديث الباقة
        $subscriptionPlan->update($validatedData);

        // تحديث حد الفواتير للباقة
        $invoiceLimit = InvoiceLimit::where('subscription_plan_id', $subscriptionPlan->id)->first();

        if ($invoiceLimit) {
            $invoiceLimit->update([
                'monthly_invoice_limit' => $validatedData['monthly_invoice_limit'],
                'total_invoice_limit' => $validatedData['total_invoice_limit'],
            ]);
        } else {
            InvoiceLimit::create([
                'subscription_plan_id' => $subscriptionPlan->id,
                'monthly_invoice_limit' => $validatedData['monthly_invoice_limit'],
                'total_invoice_limit' => $validatedData['total_invoice_limit'],
                'is_active' => true,
            ]);
        }

        return redirect()->route('admin.super_admin.subscription_plans.index')
            ->with('success', 'تم تحديث الباقة بنجاح');
    }

    /**
     * حذف الباقة
     */
    public function destroy(SubscriptionPlan $subscriptionPlan)
    {
        // التحقق من عدم وجود اشتراكات نشطة للباقة
        if ($subscriptionPlan->subscriptions()->where('status', 'active')->count() > 0) {
            return redirect()->route('admin.super_admin.subscription_plans.index')
                ->with('error', 'لا يمكن حذف الباقة لأنها مرتبطة باشتراكات نشطة');
        }

        // حذف حد الفواتير للباقة
        InvoiceLimit::where('subscription_plan_id', $subscriptionPlan->id)->delete();

        // حذف الباقة
        $subscriptionPlan->delete();

        return redirect()->route('admin.super_admin.subscription_plans.index')
            ->with('success', 'تم حذف الباقة بنجاح');
    }
}
