@extends('layouts.admin')

@section('title', 'إدارة عروض الأسعار')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة عروض الأسعار</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.sales.quotations.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إنشاء عرض سعر جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم العرض</th>
                                    <th>العميل</th>
                                    <th>تاريخ العرض</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد عروض أسعار مسجلة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        $('.table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.22/i18n/Arabic.json"
            }
        });
    });
</script>
@endpush
