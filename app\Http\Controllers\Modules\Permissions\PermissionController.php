<?php

namespace App\Http\Controllers\Modules\Permissions;

use App\Http\Controllers\Controller;
use App\Models\Modules\Permissions\Permission;
use App\Models\PermissionGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $permissions = Permission::with('roles')->latest()->get();
        return view("admin.permissions_system.permissions.index", compact("permissions"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view("admin.permissions_system.permissions.create");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => "required|string|max:255|unique:permissions,name",
            "display_name" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:permissions,slug",
            "group_name" => "nullable|string|max:255",
            "description" => "nullable|string",
        ]);

        Permission::create([
            "name" => $request->name,
            "display_name" => $request->display_name,
            "slug" => Str::slug($request->slug),
            "group_name" => $request->group_name,
            "description" => $request->description,
            "permission_group_id" => null, // قيمة افتراضية
            "module" => $request->module ?? 'permissions_system',
        ]);

        return redirect()->route("admin.permissions_system.permissions.index")
                         ->with("success", "تم إنشاء الصلاحية بنجاح.");
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        $permission->load('roles');
        return view("admin.permissions_system.permissions.show", compact("permission"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permission $permission)
    {
        return view("admin.permissions_system.permissions.edit", compact("permission"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            "name" => "required|string|max:255|unique:permissions,name," . $permission->id,
            "display_name" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:permissions,slug," . $permission->id,
            "group_name" => "nullable|string|max:255",
            "description" => "nullable|string",
        ]);

        $permission->update([
            "name" => $request->name,
            "display_name" => $request->display_name,
            "slug" => Str::slug($request->slug),
            "group_name" => $request->group_name,
            "description" => $request->description,
            "module" => $request->module ?? $permission->module ?? 'permissions_system',
        ]);

        return redirect()->route("admin.permissions_system.permissions.index")
                         ->with("success", "تم تحديث الصلاحية بنجاح.");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission)
    {
        // التحقق مما إذا كانت الصلاحية مرتبطة بأي أدوار قبل الحذف
        if ($permission->roles()->count() > 0) {
            return redirect()->route("admin.permissions_system.permissions.index")
                             ->with("error", "لا يمكن حذف هذه الصلاحية لأنها مرتبطة بأدوار.");
        }

        try {
            $permission->delete();
            return redirect()->route("admin.permissions_system.permissions.index")
                             ->with("success", "تم حذف الصلاحية بنجاح.");
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.permissions_system.permissions.index")
                             ->with("error", "لا يمكن حذف الصلاحية. قد تكون مرتبطة بسجلات أخرى.");
        }
    }
}

