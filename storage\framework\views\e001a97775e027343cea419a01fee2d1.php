<?php $__env->startSection("content"); ?>
<div class="container">
    <h1>ZATCA E-Invoicing Logs & Details</h1>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <div class="mb-3">
        <a href="<?php echo e(route("admin.integrations.zatca.index")); ?>" class="btn btn-sm btn-primary">Back to ZATCA Overview</a>
        <a href="<?php echo e(route("admin.integrations.zatca.edit")); ?>" class="btn btn-sm btn-warning">Edit Configuration</a>
        
    </div>

    <div class="card mb-3">
        <div class="card-header">Current Configuration Summary</div>
        <div class="card-body">
            <p><strong>Phase:</strong>  Phase 2: Integration</p>
            <p><strong>Environment:</strong>  Simulation</p>
            <p><strong>Onboarding Status:</strong>  Onboarded</p>
            <p><strong>Production CSID:</strong>  Set</p>
            <p><strong>Simulation CSID:</strong>  Set</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Invoice Submission Logs (Last 100 Entries)</div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route("admin.integrations.zatca.show")); ?>" class="mb-3">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" name="invoice_number" class="form-control form-control-sm" placeholder="Invoice Number" value="<?php echo e(request("invoice_number")); ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">All Statuses</option>
                            <option value="cleared" <?php echo e(request("status") == "cleared" ? "selected" : ""); ?>>Cleared</option>
                            <option value="reported" <?php echo e(request("status") == "reported" ? "selected" : ""); ?>>Reported</option>
                            <option value="rejected" <?php echo e(request("status") == "rejected" ? "selected" : ""); ?>>Rejected</option>
                            <option value="pending" <?php echo e(request("status") == "pending" ? "selected" : ""); ?>>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="submission_date" class="form-control form-control-sm" value="<?php echo e(request("submission_date")); ?>">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-info btn-sm">Filter Logs</button>
                        <a href="<?php echo e(route("admin.integrations.zatca.show")); ?>" class="btn btn-secondary btn-sm">Clear Filters</a>
                    </div>
                </div>
            </form>

            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Invoice No.</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>ZATCA Status</th>
                        <th>Clearance/Reporting ID</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    
                    
                    <tr>
                        <td>2023-05-13 16:00:00</td>
                        <td><a href="#">INV-2023-005</a></td>
                        <td>Standard Tax Invoice</td>
                        <td>575.00 SAR</td>
                        <td><span class="badge bg-success">Cleared</span></td>
                        <td>ZATCA-CLR-001</td>
                        <td><a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 15:30:00</td>
                        <td><a href="#">SMI-2023-010</a></td>
                        <td>Simplified Tax Invoice</td>
                        <td>80.00 SAR</td>
                        <td><span class="badge bg-success">Reported</span></td>
                        <td>N/A</td>
                        <td><a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 14:00:00</td>
                        <td><a href="#">INV-2023-004</a></td>
                        <td>Standard Tax Invoice</td>
                        <td>1200.00 SAR</td>
                        <td><span class="badge bg-danger">Rejected</span></td>
                        <td>N/A</td>
                        <td><a href="#" class="btn btn-xs btn-outline-danger">View Errors</a> <a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    
                </tbody>
            </table>
            
            
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/zatca/show.blade.php ENDPATH**/ ?>