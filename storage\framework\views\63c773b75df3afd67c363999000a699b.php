<?php $__env->startSection("content"); ?>
<div class="container">
    <h1>BNPL Provider Details & Logs: <?php echo e(ucfirst($provider)); ?></h1>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <div class="card mb-3">
        <div class="card-header">Configuration Summary for <?php echo e(ucfirst($provider)); ?></div>
        <div class="card-body">
            <p><strong>Status:</strong>  Enabled</p>
            <p><strong>Environment:</strong>  Sandbox</p>
            <?php if($provider == "tabby"): ?>
                <p><strong>Public Key:</strong>  pk_test_xxxx</p>
                <p><strong>Merchant ID:</strong>  TB12345</p>
            <?php elseif($provider == "tamara"): ?>
                <p><strong>API Token:</strong>  tamara_test_xxxx</p>
            <?php endif; ?>
            <a href="<?php echo e(route("admin.integrations.bnpl.edit", ["provider" => $provider])); ?>" class="btn btn-sm btn-warning">Edit Configuration</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Transaction Logs (Last 50 Entries for <?php echo e(ucfirst($provider)); ?>)</div>
        <div class="card-body">
            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Sale ID</th>
                        <th>Order ID (<?php echo e(ucfirst($provider)); ?>)</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    
                    
                    <tr>
                        <td>2023-05-13 14:00:00</td>
                        <td><a href="#">S1003</a></td>
                        <td>TABBY-ORDER-XYZ</td>
                        <td>250.00</td>
                        <td><span class="badge bg-success">Captured</span></td>
                        <td><a href="#" class="btn btn-xs btn-info">View Details</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-12 16:30:00</td>
                        <td><a href="#">S1000</a></td>
                        <td>TAMARA-ORDER-ABC</td>
                        <td>180.75</td>
                        <td><span class="badge bg-warning">Authorized</span></td>
                        <td><a href="#" class="btn btn-xs btn-info">View Details</a></td>
                    </tr>
                    
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-3">
        <a href="<?php echo e(route("admin.integrations.bnpl.index")); ?>" class="btn btn-secondary">Back to BNPL Integrations</a>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/bnpl/show.blade.php ENDPATH**/ ?>