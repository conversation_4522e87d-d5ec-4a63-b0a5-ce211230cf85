<?php

namespace App\Http\Controllers\Modules\GeneralLedger;

use App\Http\Controllers\Controller;
use App\Models\Modules\GeneralLedger\Account;
use App\Models\Modules\GeneralLedger\AccountType;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get account types
        $accountTypes = AccountType::where('is_active', true)
                                  ->orderBy('id')
                                  ->get();

        // Get root accounts (accounts with no parent) grouped by account type
        $rootAccounts = [];

        foreach ($accountTypes as $type) {
            $accounts = Account::with(['children'])
                              ->where('account_type_id', $type->id)
                              ->whereNull('parent_id')
                              ->orderBy('code')
                              ->get();

            if ($accounts->count() > 0) {
                // Calculate total balance for this account type
                $typeBalance = 0;
                foreach ($accounts as $account) {
                    $accountBalance = $account->opening_balance_debit - $account->opening_balance_credit;
                    $typeBalance += $accountBalance;
                }

                $rootAccounts[$type->id] = [
                    'type' => $type,
                    'accounts' => $accounts,
                    'balance' => $typeBalance
                ];
            }
        }

        // Get all accounts for the table view
        $allAccounts = Account::with(["accountType"])
                            ->orderBy("account_type_id")
                            ->orderBy("code")
                            ->get();

        return view("admin.accounts.index_new", compact('rootAccounts', 'allAccounts', 'accountTypes'));
    }

    /**
     * Display the tree view of accounts.
     */
    public function tree(Request $request)
    {
        // Get account types
        $accountTypes = AccountType::where('is_active', true)
                                  ->orderBy('id')
                                  ->get();

        // Get root accounts (accounts with no parent) grouped by account type
        $rootAccounts = [];

        foreach ($accountTypes as $type) {
            $accounts = Account::with(['children'])
                              ->where('account_type_id', $type->id)
                              ->whereNull('parent_id')
                              ->orderBy('code')
                              ->get();

            if ($accounts->count() > 0) {
                $rootAccounts[$type->id] = [
                    'type' => $type,
                    'accounts' => $accounts,
                    'balance' => $this->calculateTypeBalance($accounts)
                ];
            }
        }

        // Get all accounts for the table view
        $allAccounts = Account::with(["accountType"])
                            ->orderBy("account_type_id")
                            ->orderBy("code")
                            ->get();

        return view("admin.accounts.index_new", compact('rootAccounts', 'allAccounts', 'accountTypes'));
    }

    /**
     * Calculate the total balance for an account type
     */
    private function calculateTypeBalance($accounts)
    {
        $balance = 0;

        foreach ($accounts as $account) {
            $accountBalance = $account->opening_balance_debit - $account->opening_balance_credit;
            $balance += $accountBalance;

            // Add balances from children recursively
            if ($account->children && $account->children->count() > 0) {
                $balance += $this->calculateTypeBalance($account->children);
            }
        }

        return $balance;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accountTypes = AccountType::orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        $parentAccounts = Account::where("is_active", true)->orderBy("name_ar")->get(); // For parent selection
        return view("admin.accounts.create", compact("accountTypes", "branches", "parentAccounts"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "code" => "required|string|max:50|unique:accounts,code",
            "account_type_id" => "required|exists:account_types,id",
            "branch_id" => "nullable|exists:branches,id",
            "parent_id" => "nullable|exists:accounts,id",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "is_control_account" => "required|boolean",
            "accepts_entries" => "required|boolean",
            "opening_balance_debit" => "required|numeric|min:0",
            "opening_balance_credit" => "required|numeric|min:0",
            "opening_balance_date" => "required|date",
            "is_active" => "required|boolean",
        ]);

        $data = $request->all();

        Account::create($data);

        return redirect()->route("admin.accounts.index")
                         ->with("success", "Account created successfully.");
    }

    /**
     * Display the specified resource.
     */
    public function show(Account $account)
    {
        $account->load(["accountType", "branch", "parent", "children"]);
        return view("admin.accounts.show", compact("account"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Account $account)
    {
        $accountTypes = AccountType::orderBy("name")->get();
        $branches = Branch::orderBy("name")->get();
        $parentAccounts = Account::where("is_active", true)->where("id", "!=", $account->id)->orderBy("name_ar")->get(); // Exclude self
        $account->load(["accountType", "branch", "parent"]);
        return view("admin.accounts.edit", compact("account", "accountTypes", "branches", "parentAccounts"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Account $account)
    {
        $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "code" => "required|string|max:50|unique:accounts,code," . $account->id,
            "account_type_id" => "required|exists:account_types,id",
            "branch_id" => "nullable|exists:branches,id",
            "parent_id" => "nullable|exists:accounts,id",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "is_control_account" => "required|boolean",
            "accepts_entries" => "required|boolean",
            "opening_balance_debit" => "required|numeric|min:0",
            "opening_balance_credit" => "required|numeric|min:0",
            "opening_balance_date" => "required|date",
            "is_active" => "required|boolean",
        ]);

        $data = $request->all();

        $account->update($data);

        return redirect()->route("admin.accounts.index")
                         ->with("success", "Account updated successfully.");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Account $account)
    {
        if ($account->journalEntryItems()->exists()) {
            return redirect()->route("admin.accounts.index")
                             ->with("error", "Cannot delete account with existing journal entries.");
        }
        if ($account->children()->exists()) {
            return redirect()->route("admin.accounts.index")
                             ->with("error", "Cannot delete account with child accounts. Please remove or re-assign child accounts first.");
        }
        $account->delete();

        return redirect()->route("admin.accounts.index")
                         ->with("success", "Account deleted successfully.");
    }
}

