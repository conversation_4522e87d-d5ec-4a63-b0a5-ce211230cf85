<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesTarget;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SalesTargetController extends Controller
{
    public function index()
    {
        $targets = SalesTarget::with(['salesRepresentative', 'setBy', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->latest()
            ->paginate(15);

        return view('admin.sales-representatives.targets.index', compact('targets'));
    }

    public function create()
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.targets.form', compact('representatives', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'target_type' => 'required|in:monthly,quarterly,yearly,custom',
            'target_year' => 'required|integer|min:2020',
            'target_month' => 'nullable|integer|min:1|max:12',
            'target_quarter' => 'nullable|integer|min:1|max:4',
            'sales_target' => 'required|numeric|min:0',
            'visits_target' => 'required|integer|min:0',
            'new_customers_target' => 'required|integer|min:0',
            'collection_target' => 'required|numeric|min:0',
            'bonus_amount' => 'nullable|numeric|min:0',
            'target_notes' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'branch_id' => 'required|exists:branches,id',
        ]);

        // Check for duplicate targets
        $existingTarget = SalesTarget::where('sales_representative_id', $validatedData['sales_representative_id'])
            ->where('target_type', $validatedData['target_type'])
            ->where('target_year', $validatedData['target_year'])
            ->where('target_month', $validatedData['target_month'])
            ->where('status', 'active')
            ->first();

        if ($existingTarget) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('Target already exists for this representative and period.'));
        }

        $validatedData['status'] = 'active';
        $validatedData['set_by'] = Auth::id();
        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();

        SalesTarget::create($validatedData);

        return redirect()->route('admin.sales-representatives.targets.index')
            ->with('success', __('Sales target created successfully.'));
    }

    public function show(SalesTarget $target)
    {
        $target->load(['salesRepresentative', 'setBy', 'branch']);

        // Update achievements
        $target->updateAchievements();

        return view('admin.sales-representatives.targets.show', compact('target'));
    }

    public function edit(SalesTarget $target)
    {
        if ($target->status === 'completed') {
            return redirect()->route('admin.sales-representatives.targets.index')
                ->with('error', __('Cannot edit completed target.'));
        }

        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.targets.form', compact('target', 'representatives', 'branches'));
    }

    public function update(Request $request, SalesTarget $target)
    {
        if ($target->status === 'completed') {
            return redirect()->route('admin.sales-representatives.targets.index')
                ->with('error', __('Cannot update completed target.'));
        }

        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'target_type' => 'required|in:monthly,quarterly,yearly,custom',
            'target_year' => 'required|integer|min:2020',
            'target_month' => 'nullable|integer|min:1|max:12',
            'target_quarter' => 'nullable|integer|min:1|max:4',
            'sales_target' => 'required|numeric|min:0',
            'visits_target' => 'required|integer|min:0',
            'new_customers_target' => 'required|integer|min:0',
            'collection_target' => 'required|numeric|min:0',
            'bonus_amount' => 'nullable|numeric|min:0',
            'target_notes' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'branch_id' => 'required|exists:branches,id',
        ]);

        // Check for duplicate targets (excluding current)
        $existingTarget = SalesTarget::where('sales_representative_id', $validatedData['sales_representative_id'])
            ->where('target_type', $validatedData['target_type'])
            ->where('target_year', $validatedData['target_year'])
            ->where('target_month', $validatedData['target_month'])
            ->where('status', 'active')
            ->where('id', '!=', $target->id)
            ->first();

        if ($existingTarget) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('Target already exists for this representative and period.'));
        }

        $target->update($validatedData);

        return redirect()->route('admin.sales-representatives.targets.index')
            ->with('success', __('Sales target updated successfully.'));
    }

    public function destroy(SalesTarget $target)
    {
        if ($target->status === 'completed') {
            return redirect()->route('admin.sales-representatives.targets.index')
                ->with('error', __('Cannot delete completed target.'));
        }

        $target->delete();

        return redirect()->route('admin.sales-representatives.targets.index')
            ->with('success', __('Sales target deleted successfully.'));
    }

    public function updateAchievement(Request $request, SalesTarget $target)
    {
        $target->updateAchievements();

        return response()->json([
            'success' => true,
            'message' => __('Target achievement updated successfully.'),
            'data' => [
                'sales_achievement_percentage' => $target->sales_achievement_percentage,
                'visits_achievement_percentage' => $target->visits_achievement_percentage,
                'collection_achievement_percentage' => $target->collection_achievement_percentage,
                'overall_achievement_percentage' => $target->overall_achievement_percentage,
            ]
        ]);
    }

    public function dashboard()
    {
        $tenantId = Auth::user()->tenant_id ?? Auth::id();
        
        // Current month targets
        $currentTargets = SalesTarget::where('tenant_id', $tenantId)
            ->where('target_year', now()->year)
            ->where('target_month', now()->month)
            ->where('status', 'active')
            ->with('salesRepresentative')
            ->get()
            ->each(function($target) {
                $target->updateAchievements();
            });

        // Achievement statistics
        $achievementStats = [
            'total_targets' => $currentTargets->count(),
            'achieved_targets' => $currentTargets->where('overall_achievement_percentage', '>=', 100)->count(),
            'average_achievement' => $currentTargets->avg('overall_achievement_percentage'),
            'total_sales_target' => $currentTargets->sum('sales_target'),
            'total_achieved_sales' => $currentTargets->sum('achieved_sales'),
            'total_visits_target' => $currentTargets->sum('visits_target'),
            'total_achieved_visits' => $currentTargets->sum('achieved_visits'),
        ];

        // Top performers
        $topPerformers = $currentTargets->sortByDesc('overall_achievement_percentage')->take(5);

        // Achievement trends (last 6 months)
        $achievementTrends = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthTargets = SalesTarget::where('tenant_id', $tenantId)
                ->where('target_year', $date->year)
                ->where('target_month', $date->month)
                ->get();

            $achievementTrends[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('M Y'),
                'average_achievement' => $monthTargets->avg('overall_achievement_percentage') ?? 0,
                'targets_count' => $monthTargets->count(),
                'achieved_count' => $monthTargets->where('overall_achievement_percentage', '>=', 100)->count(),
            ];
        }

        return view('admin.sales-representatives.targets.dashboard', compact(
            'currentTargets', 'achievementStats', 'topPerformers', 'achievementTrends'
        ));
    }

    public function getTargetsByRepresentative(Request $request)
    {
        $representativeId = $request->get('representative_id');
        $year = $request->get('year', now()->year);

        $targets = SalesTarget::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('sales_representative_id', $representativeId)
            ->where('target_year', $year)
            ->orderBy('target_month')
            ->get()
            ->each(function($target) {
                $target->updateAchievements();
            });

        return response()->json([
            'success' => true,
            'data' => $targets
        ]);
    }

    public function bulkCreate(Request $request)
    {
        $request->validate([
            'representative_ids' => 'required|array',
            'representative_ids.*' => 'exists:sales_representatives,id',
            'target_type' => 'required|in:monthly,quarterly,yearly,custom',
            'target_year' => 'required|integer|min:2020',
            'target_month' => 'nullable|integer|min:1|max:12',
            'target_quarter' => 'nullable|integer|min:1|max:4',
            'sales_target' => 'required|numeric|min:0',
            'visits_target' => 'required|integer|min:0',
            'new_customers_target' => 'required|integer|min:0',
            'collection_target' => 'required|numeric|min:0',
            'bonus_amount' => 'nullable|numeric|min:0',
            'target_notes' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $createdCount = 0;
        $skippedCount = 0;

        foreach ($request->representative_ids as $representativeId) {
            // Check if target already exists
            $existingTarget = SalesTarget::where('sales_representative_id', $representativeId)
                ->where('target_type', $request->target_type)
                ->where('target_year', $request->target_year)
                ->where('target_month', $request->target_month)
                ->where('status', 'active')
                ->first();

            if ($existingTarget) {
                $skippedCount++;
                continue;
            }

            SalesTarget::create([
                'sales_representative_id' => $representativeId,
                'target_type' => $request->target_type,
                'target_year' => $request->target_year,
                'target_month' => $request->target_month,
                'target_quarter' => $request->target_quarter,
                'sales_target' => $request->sales_target,
                'visits_target' => $request->visits_target,
                'new_customers_target' => $request->new_customers_target,
                'collection_target' => $request->collection_target,
                'bonus_amount' => $request->bonus_amount,
                'target_notes' => $request->target_notes,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => 'active',
                'set_by' => Auth::id(),
                'branch_id' => $request->branch_id,
                'tenant_id' => Auth::user()->tenant_id ?? Auth::id(),
            ]);

            $createdCount++;
        }

        return response()->json([
            'success' => true,
            'message' => __('Bulk targets created successfully.'),
            'created_count' => $createdCount,
            'skipped_count' => $skippedCount
        ]);
    }
}
