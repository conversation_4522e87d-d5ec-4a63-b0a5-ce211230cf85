<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Zatca\ZatcaInvoiceService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TestFullZatcaIntegration extends Command
{
    protected $signature = 'test:zatca-full {--type=simplified : Invoice type to test (simplified or standard)} {--otp= : Optional OTP for compliance CSID request}';
    protected $description = 'Tests the full ZATCA integration flow: CSR, CSID, XML, Sign, Report/Clear.';

    protected ZatcaInvoiceService $zatcaService;

    public function __construct(ZatcaInvoiceService $zatcaService)
    {
        parent::__construct();
        $this->zatcaService = $zatcaService;
    }

    public function handle()
    {
        $this->info('Starting Full ZATCA Integration Test...');

        if (!Storage::disk("local")->exists("zatca")) {
            Storage::disk("local")->makeDirectory("zatca");
            $this->comment('Created zatca directory in storage/app.');
        }

        $this->info("Step 1: Generating CSR...");

        $vatRegistrationNumber = config("zatca.vat_registration_number", "310123456789013");
        $sellerName = config("zatca.seller_name", "Test Seller Name");
        $sellerStreet = config("zatca.seller_street", "Test Street");
        $sellerCountryCode = config("zatca.seller_country_code", "SA");

        if (empty($vatRegistrationNumber)) {
            $this->error('ZATCA VAT Registration Number (zatca.vat_registration_number) is not configured.');
            return 1;
        }
        if (empty($sellerName)) {
            $this->error('ZATCA Seller Name (zatca.seller_name) is not configured.');
            return 1;
        }

        $csrData = $this->zatcaService->generateCsr(
            config("zatca.csr_common_name", $vatRegistrationNumber),
            $vatRegistrationNumber, 
            $sellerName,
            config("zatca.csr_organization_unit", "IT Department"),
            $sellerCountryCode,
            config("zatca.csr_invoice_type", "1100"),
            $sellerStreet,
            config("zatca.csr_business_category", "Retail")
        );
        $this->comment("CSR generated. CSR file at: " . $csrData["csr_path"] . ", Private key at: " . $csrData["private_key_path"]);
        // CSR content is already saved by generateCsr in ZatcaInvoiceService if using generateAndSave
        // $this->comment("CSR content saved to storage/app/zatca/latest_csr.pem"); // This line might be redundant if generateCsr saves it.

        $this->info("Step 2: Requesting Compliance CSID...");
        $otp = $this->option('otp') ?? (string) config("zatca.compliance_otp", "");
        
        // Determine the relative path for storage checks
        $csidCertRelativePath = str_replace(storage_path('app/'), '', $this->zatcaService->getCsidCertificatePath());
        $csidKeyRelativePath = str_replace(storage_path('app/'), '', $this->zatcaService->getCsidPrivateKeyPath());

        if (empty($otp) && (!Storage::disk("local")->exists($csidCertRelativePath) || !Storage::disk("local")->exists($csidKeyRelativePath)) ) {
            $this->error("OTP for compliance CSID is not provided and existing CSID files not found. Please provide OTP or place CSID files.");
            $this->comment("Expected CSID certificate at: " . $this->zatcaService->getCsidCertificatePath());
            $this->comment("Expected CSID private key at: " . $this->zatcaService->getCsidPrivateKeyPath());
            return 1;
        }

        if (!empty($otp)) {
            // Pass the CSR file path, not its content
            $csidResponse = $this->zatcaService->requestComplianceCsid($csrData["csr_path"], $otp);
            if (isset($csidResponse["error"]) && $csidResponse["error"]) {
                $this->error("Failed to obtain CSID: " . $csidResponse["message"]);
                if(isset($csidResponse["details"])) $this->line("Details: " . json_encode($csidResponse["details"]));
                if(isset($csidResponse["details_api"])) $this->line("API Details: " . json_encode($csidResponse["details_api"]));
                return 1;
            }
            $this->comment("Compliance CSID obtained and saved.");
            $this->comment("Certificate at: " . ($csidResponse["csid_certificate_path"] ?? 'N/A'));
            $this->comment("Private Key (Secret) at: " . ($csidResponse["csid_private_key_path"] ?? 'N/A'));
        } else {
            $this->info("OTP not provided, proceeding with existing CSID files (if they exist).");
             if (!Storage::disk("local")->exists($csidCertRelativePath) || !Storage::disk("local")->exists($csidKeyRelativePath)) {
                $this->error("CSID files not found at expected locations. Cannot proceed without OTP or existing CSID.");
                return 1;
            }
        }

        $this->info("Step 3: Preparing Invoice Data...");
        $invoiceType = $this->option('type');
        $invoiceId = Str::uuid()->toString();
        $previousHash = "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==";

        $invoiceData = [
            "id" => $invoiceId,
            "items" => [
                ["id" => 1, "name" => "Test Product 1", "quantity" => 2, "price_before_vat" => 100.00, "vat_rate" => 0.15],
                ["id" => 2, "name" => "Test Product 2", "quantity" => 1, "price_before_vat" => 50.00, "vat_rate" => 0.15],
            ]
        ];

        $this->info("Step 4: Generating Invoice XML...");
        $invoiceXml = null;
        if ($invoiceType === 'simplified') {
            $invoiceData["buyer_name"] = "Cash Customer";
            $invoiceXml = $this->zatcaService->generateSimplifiedInvoiceXml($invoiceData, $previousHash);
            $this->comment("Generated Simplified Invoice XML.");
        } elseif ($invoiceType === 'standard') {
            $invoiceData["buyer"] = [
                "name" => "Test Business Buyer",
                "vat_id" => "300000000000003",
                "street" => "Buyer Street",
                "building_number" => "4321",
                "plot_identification" => "4321",
                "city_subdivision" => "Buyer District",
                "city" => "Riyadh",
                "postal_zone" => "54321",
                "country_code" => "SA"
            ];
            $invoiceXml = $this->zatcaService->generateStandardInvoiceXml($invoiceData, $previousHash);
            $this->comment("Generated Standard Invoice XML.");
        } else {
            $this->error("Invalid invoice type specified. Use 'simplified' or 'standard'.");
            return 1;
        }
        Storage::disk("local")->put("zatca/test_invoice_{$invoiceId}.xml", $invoiceXml);
        $this->comment("Invoice XML saved to storage/app/zatca/test_invoice_{$invoiceId}.xml");

        $this->info("Step 5: Signing Invoice XML...");
        $signedXml = $this->zatcaService->signInvoiceXml($invoiceXml);
        if (!$signedXml) {
            $this->error("Failed to sign invoice XML. Check CSID certificate and private key.");
            return 1;
        }
        Storage::disk("local")->put("zatca/test_invoice_signed_{$invoiceId}.xml", $signedXml);
        $this->comment("Signed Invoice XML saved to storage/app/zatca/test_invoice_signed_{$invoiceId}.xml");

        $this->info("Step 6: Reporting/Clearing Invoice with ZATCA...");
        $apiResponse = null;
        if ($invoiceType === 'simplified') {
            $apiResponse = $this->zatcaService->reportSimplifiedInvoice($signedXml, $invoiceId);
            $this->comment("Simplified Invoice Reporting API called.");
        } elseif ($invoiceType === 'standard') {
            $apiResponse = $this->zatcaService->clearStandardInvoice($signedXml, $invoiceId);
            $this->comment("Standard Invoice Clearance API called.");
        }

        if ($apiResponse && (!isset($apiResponse["error"]) || !$apiResponse["error"])) {
            $this->info("ZATCA API Call Successful!");
            $this->line("Response: " . json_encode($apiResponse["response"] ?? $apiResponse, JSON_PRETTY_PRINT));
        } else {
            $this->error("ZATCA API Call Failed.");
            $this->line("Message: " . ($apiResponse["message"] ?? "Unknown error"));
            if(isset($apiResponse["details"])) $this->line("Details: " . json_encode($apiResponse["details"], JSON_PRETTY_PRINT));
            if(isset($apiResponse["details_api"])) $this->line("API Details: " . json_encode($apiResponse["details_api"], JSON_PRETTY_PRINT));
            if(isset($apiResponse["response"]["messages"])){
                $this->error("Validation Messages from ZATCA:");
                foreach($apiResponse["response"]["messages"] as $msg){
                    $this->line("- Type: {$msg['type']}, Code: {$msg['code']}, Message: {$msg['message']}");
                }
            }
        }

        $this->info("Full ZATCA Integration Test Finished.");
        return 0;
    }
}

