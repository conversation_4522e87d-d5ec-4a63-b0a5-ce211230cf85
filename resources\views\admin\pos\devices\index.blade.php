@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Devices</h1>
    <a href="{{ route("admin.pos.devices.create") }}" class="btn btn-primary mb-3">Add New Device</a>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {{-- @foreach($devices as $device) --}}
            {{-- Replace with actual data when available --}}
            <tr>
                <td>1</td>
                <td>Main Counter POS</td>
                <td>Fixed</td>
                <td>Active</td>
                <td>
                    <a href="{{-- route("admin.pos.devices.show", $device->id) --}}" class="btn btn-info btn-sm">View</a>
                    <a href="{{-- route("admin.pos.devices.edit", $device->id) --}}" class="btn btn-warning btn-sm">Edit</a>
                    <form action="{{-- route("admin.pos.devices.destroy", $device->id) --}}" method="POST" style="display:inline-block;">
                        @csrf
                        @method("DELETE")
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure?")">Delete</button>
                    </form>
                </td>
            </tr>
            {{-- @endforeach --}}
        </tbody>
    </table>
</div>
@endsection

