
<div class="modal fade" id="customerModal" tabindex="-1" aria-labelledby="customerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalLabel"><i class="bi bi-people-fill me-2"></i>إدارة العملاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-pills nav-fill mb-3" id="customerTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="search-customer-tab" data-bs-toggle="tab" data-bs-target="#search-customer-pane" type="button" role="tab" aria-controls="search-customer-pane" aria-selected="true"><i class="bi bi-search me-1"></i>بحث عن عميل</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="add-customer-tab" data-bs-toggle="tab" data-bs-target="#add-customer-pane" type="button" role="tab" aria-controls="add-customer-pane" aria-selected="false"><i class="bi bi-person-plus-fill me-1"></i>إضافة عميل جديد</button>
                    </li>
                </ul>
                <div class="tab-content" id="customerTabContent">
                    <div class="tab-pane fade show active p-3 border rounded" id="search-customer-pane" role="tabpanel" aria-labelledby="search-customer-tab">
                        <div class="mb-3">
                            <label for="customer-search-input" class="form-label visually-hidden">بحث عن عميل</label>
                            <input type="text" class="form-control form-control-lg" id="customer-search-input" placeholder="ابحث بالاسم أو رقم الجوال أو الرقم الضريبي...">
                        </div>
                        <div class="list-group" id="customer-search-results" style="max-height: 300px; overflow-y: auto;">
                            
                            <p class="list-group-item text-muted text-center">أدخل بيانات البحث أعلاه.</p>
                        </div>
                        
                        <div id="dummy-customer-data" style="display:none;">
                            <button type="button" class="list-group-item list-group-item-action" onclick="selectCustomer(1, 'عميل افتراضي 1', '0500000001', '<EMAIL>', '123456789012345')">
                                <strong>عميل افتراضي 1</strong><br><small>0500000001 | <EMAIL> | VAT: 123456789012345</small>
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" onclick="selectCustomer(2, 'عميل افتراضي 2', '0500000002', '<EMAIL>', '543210987654321')">
                                <strong>عميل افتراضي 2</strong><br><small>0500000002 | <EMAIL> | VAT: 543210987654321</small>
                            </button>
                             <button type="button" class="list-group-item list-group-item-action" onclick="selectCustomer(3, 'شركة الأمل للتجارة', '0555123456', '<EMAIL>', '300123456700003')">
                                <strong>شركة الأمل للتجارة</strong><br><small>0555123456 | <EMAIL> | VAT: 300123456700003</small>
                            </button>
                        </div>
                    </div>
                    <div class="tab-pane fade p-3 border rounded" id="add-customer-pane" role="tabpanel" aria-labelledby="add-customer-tab">
                        <form id="add-customer-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new-customer-name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-lg" id="new-customer-name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="new-customer-phone" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control form-control-lg" id="new-customer-phone" required inputmode="tel">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new-customer-email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control form-control-lg" id="new-customer-email" inputmode="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="new-customer-vat" class="form-label">الرقم الضريبي (VAT)</label>
                                    <input type="text" class="form-control form-control-lg" id="new-customer-vat" inputmode="numeric">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new-customer-address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="new-customer-address" rows="2"></textarea>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary btn-lg"><i class="bi bi-person-plus-fill me-2"></i>إضافة العميل</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush("scripts"); ?>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const customerSearchInput = document.getElementById("customer-search-input");
        const customerSearchResults = document.getElementById("customer-search-results");
        const dummyCustomerData = document.getElementById("dummy-customer-data").innerHTML;
        const addCustomerForm = document.getElementById("add-customer-form");

        if (customerSearchInput) {
            customerSearchInput.addEventListener("input", function() {
                const searchTerm = this.value.trim().toLowerCase();
                if (searchTerm.length > 1) { 
                    // Simulate API call by filtering dummy data
                    const allDummies = document.createElement('div');
                    allDummies.innerHTML = dummyCustomerData;
                    const filteredDummies = Array.from(allDummies.children).filter(el => {
                        return el.textContent.toLowerCase().includes(searchTerm);
                    });
                    if (filteredDummies.length > 0) {
                        customerSearchResults.innerHTML = "";
                        filteredDummies.forEach(el => customerSearchResults.appendChild(el));
                    } else {
                        customerSearchResults.innerHTML = '<p class="list-group-item text-muted text-center">لا توجد نتائج تطابق بحثك.</p>';
                    }
                } else if (searchTerm.length === 0) {
                     customerSearchResults.innerHTML = '<p class="list-group-item text-muted text-center">أدخل بيانات البحث أعلاه.</p>';
                } else {
                    customerSearchResults.innerHTML = '<p class="list-group-item text-muted text-center">أدخل حرفين على الأقل للبحث.</p>';
                }
            });
        }

        if (addCustomerForm) {
            addCustomerForm.addEventListener("submit", function(event) {
                event.preventDefault();
                const customerName = document.getElementById("new-customer-name").value;
                const customerPhone = document.getElementById("new-customer-phone").value;
                const customerEmail = document.getElementById("new-customer-email").value;
                const customerVat = document.getElementById("new-customer-vat").value;
                const customerAddress = document.getElementById("new-customer-address").value;

                // Basic validation example
                if (!customerName || !customerPhone) {
                    alert("يرجى إدخال اسم العميل ورقم الجوال.");
                    return;
                }

                // In a real app, send this data to the server
                console.log("New Customer Data:", {customerName, customerPhone, customerEmail, customerVat, customerAddress});
                alert(`تمت إضافة العميل: ${customerName} (محاكاة).`);
                
                // Select this new customer (function defined in main.blade.php)
                if (typeof selectCustomer === "function") {
                    // Pass all relevant data for the new customer
                    selectCustomer(Date.now(), customerName, customerPhone, customerEmail, customerVat, customerAddress);
                } else {
                    console.error("selectCustomer function is not defined in the global scope.");
                }
                
                this.reset();
                var customerModalEl = document.getElementById("customerModal");
                if (customerModalEl) {
                    var customerModalInstance = bootstrap.Modal.getInstance(customerModalEl);
                    if (customerModalInstance) customerModalInstance.hide();
                }
            });
        }
    });

    // Make sure selectCustomer is globally available if it's called from here
    // It's better to have it defined in main.blade.php and ensure it handles all necessary customer data
    // Example of how it might be defined in main.blade.php:
    /*
    function selectCustomer(id, name, phone, email, vat, address) {
        selectedCustomer = { id, name, phone, email, vat, address };
        document.getElementById("selected-customer-name").textContent = name;
        // Potentially pre-fill email/phone for receipt options in payment modal
        const receiptCustomerEmail = document.getElementById("receipt-customer-email");
        if(receiptCustomerEmail && email) receiptCustomerEmail.value = email;
        const receiptCustomerWhatsapp = document.getElementById("receipt-customer-whatsapp");
        if(receiptCustomerWhatsapp && phone) receiptCustomerWhatsapp.value = phone; // Assuming phone is WhatsApp compatible

        var customerModalEl = document.getElementById("customerModal");
        if (customerModalEl) {
            var customerModalInstance = bootstrap.Modal.getInstance(customerModalEl);
            if (customerModalInstance) customerModalInstance.hide();
        }
    }
    */
</script>
<?php $__env->stopPush(); ?>

<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/pos_frontend/partials/customer_modal.blade.php ENDPATH**/ ?>