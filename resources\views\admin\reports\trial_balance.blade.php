@extends("layouts.admin")

@section("main-content")
    <div class="container-fluid">
        <h1 class="h3 mb-4 text-gray-800">{{ __("Trial Balance") }}</h1>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{ __("Trial Balance as of") }} {{ now()->format("Y-m-d") }}</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{ __("Account Code") }}</th>
                                <th>{{ __("Account Name") }}</th>
                                <th class="text-right">{{ __("Debit") }}</th>
                                <th class="text-right">{{ __("Credit") }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $calculatedTotalDebit = 0;
                                $calculatedTotalCredit = 0;
                            @endphp
                            @forelse ($accounts as $account)
                                @php
                                    $debit = 0;
                                    $credit = 0;
                                    if ($account->accountType) {
                                        if ($account->accountType->is_debit_balance) {
                                            if ($account->balance >= 0) {
                                                $debit = $account->balance;
                                            } else {
                                                $credit = abs($account->balance);
                                            }
                                        } else {
                                            if ($account->balance >= 0) {
                                                $credit = $account->balance;
                                            } else {
                                                $debit = abs($account->balance);
                                            }
                                        }
                                    }
                                    $calculatedTotalDebit += $debit;
                                    $calculatedTotalCredit += $credit;
                                @endphp
                                <tr>
                                    <td>{{ $account->code }}</td>
                                    <td>{{ $account->name }}</td>
                                    <td class="text-right">{{ $debit > 0 ? number_format($debit, 2) : "-" }}</td>
                                    <td class="text-right">{{ $credit > 0 ? number_format($credit, 2) : "-" }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center">{{ __("No accounts found or balances are zero.") }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="2" class="text-right">{{ __("Totals") }}</th>
                                <th class="text-right">{{ number_format($calculatedTotalDebit, 2) }}</th>
                                <th class="text-right">{{ number_format($calculatedTotalCredit, 2) }}</th>
                            </tr>
                            @if (round($calculatedTotalDebit, 2) !== round($calculatedTotalCredit, 2))
                            <tr>
                                <th colspan="4" class="text-center text-danger">{{ __("Warning: Totals do not match!") }}</th>
                            </tr>
                            @endif
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

