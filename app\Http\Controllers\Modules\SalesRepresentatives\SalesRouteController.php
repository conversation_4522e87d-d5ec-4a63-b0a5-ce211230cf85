<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesRoute;
use App\Models\Modules\SalesRepresentatives\SalesArea;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SalesRouteController extends Controller
{
    public function index()
    {
        $routes = SalesRoute::with(['salesRepresentative', 'salesArea', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.sales-representatives.routes.index', compact('routes'));
    }

    public function create()
    {
        $salesAreas = SalesArea::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.routes.form', compact('salesAreas', 'representatives', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:sales_routes,code',
            'description' => 'nullable|string',
            'route_type' => 'required|in:daily,weekly,monthly,custom',
            'schedule_days' => 'nullable|array',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'estimated_duration' => 'nullable|integer|min:1',
            'estimated_distance' => 'nullable|numeric|min:0',
            'fuel_allowance' => 'nullable|numeric|min:0',
            'max_customers' => 'nullable|integer|min:1',
            'priority_level' => 'required|integer|min:1|max:5',
            'route_points' => 'nullable|json',
            'special_instructions' => 'nullable|string',
            'requires_vehicle' => 'boolean',
            'sales_representative_id' => 'nullable|exists:sales_representatives,id',
            'sales_area_id' => 'required|exists:sales_areas,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['requires_vehicle'] = $request->has('requires_vehicle');
        $validatedData['is_active'] = $request->has('is_active');

        // Handle route points
        if ($request->has('route_points') && $request->route_points) {
            $validatedData['route_points'] = json_decode($request->route_points, true);
        }

        SalesRoute::create($validatedData);

        return redirect()->route('admin.sales-representatives.routes.index')
            ->with('success', __('Sales route created successfully.'));
    }

    public function show(SalesRoute $route)
    {
        $route->load([
            'salesRepresentative', 
            'salesArea', 
            'branch', 
            'salesVisits' => function($query) {
                $query->latest()->take(10);
            },
            'customers'
        ]);

        // Calculate route statistics
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $stats = [
            'total_customers' => $route->customers()->count(),
            'monthly_visits' => $route->calculateVisits($currentMonth, $endOfMonth),
            'completion_rate' => $route->calculateCompletionRate($currentMonth, $endOfMonth),
            'is_scheduled_today' => $route->isScheduledForToday(),
            'next_scheduled_date' => $route->getNextScheduledDate(),
        ];

        return view('admin.sales-representatives.routes.show', compact('route', 'stats'));
    }

    public function edit(SalesRoute $route)
    {
        $salesAreas = SalesArea::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.routes.form', compact('route', 'salesAreas', 'representatives', 'branches'));
    }

    public function update(Request $request, SalesRoute $route)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:sales_routes,code,' . $route->id,
            'description' => 'nullable|string',
            'route_type' => 'required|in:daily,weekly,monthly,custom',
            'schedule_days' => 'nullable|array',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'estimated_duration' => 'nullable|integer|min:1',
            'estimated_distance' => 'nullable|numeric|min:0',
            'fuel_allowance' => 'nullable|numeric|min:0',
            'max_customers' => 'nullable|integer|min:1',
            'priority_level' => 'required|integer|min:1|max:5',
            'route_points' => 'nullable|json',
            'special_instructions' => 'nullable|string',
            'requires_vehicle' => 'boolean',
            'sales_representative_id' => 'nullable|exists:sales_representatives,id',
            'sales_area_id' => 'required|exists:sales_areas,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['requires_vehicle'] = $request->has('requires_vehicle');
        $validatedData['is_active'] = $request->has('is_active');

        // Handle route points
        if ($request->has('route_points') && $request->route_points) {
            $validatedData['route_points'] = json_decode($request->route_points, true);
        }

        $route->update($validatedData);

        return redirect()->route('admin.sales-representatives.routes.index')
            ->with('success', __('Sales route updated successfully.'));
    }

    public function destroy(SalesRoute $route)
    {
        // Check if route has visits
        if ($route->salesVisits()->count() > 0) {
            return redirect()->route('admin.sales-representatives.routes.index')
                ->with('error', __('Cannot delete route with existing visits.'));
        }

        // Check if route has customers
        if ($route->customers()->count() > 0) {
            return redirect()->route('admin.sales-representatives.routes.index')
                ->with('error', __('Cannot delete route with assigned customers.'));
        }

        $route->delete();

        return redirect()->route('admin.sales-representatives.routes.index')
            ->with('success', __('Sales route deleted successfully.'));
    }

    public function assignRepresentative(Request $request, SalesRoute $route)
    {
        $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
        ]);

        $route->update([
            'sales_representative_id' => $request->sales_representative_id
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Representative assigned successfully.')
        ]);
    }

    public function optimize(SalesRoute $route)
    {
        // This would implement route optimization logic
        // For now, we'll return a simple response
        
        $optimizedRoute = [
            'original_distance' => $route->estimated_distance,
            'optimized_distance' => $route->estimated_distance * 0.85, // 15% improvement
            'time_saved' => 30, // minutes
            'fuel_saved' => $route->fuel_allowance * 0.15,
            'suggestions' => [
                'إعادة ترتيب نقاط الزيارة لتقليل المسافة',
                'تجميع الزيارات في نفس المنطقة',
                'تجنب ساعات الذروة المرورية',
                'استخدام طرق بديلة أقل ازدحاماً'
            ]
        ];

        return view('admin.sales-representatives.routes.optimize', compact('route', 'optimizedRoute'));
    }

    public function getRoutesByArea(Request $request)
    {
        $areaId = $request->get('area_id');
        
        $routes = SalesRoute::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('sales_area_id', $areaId)
            ->where('is_active', true)
            ->ordered()
            ->get(['id', 'name', 'code']);

        return response()->json([
            'success' => true,
            'data' => $routes
        ]);
    }
}
