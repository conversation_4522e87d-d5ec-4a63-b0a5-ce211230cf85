<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Customer;

class SalesRepresentative extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_code',
        'name',
        'phone',
        'email',
        'national_id',
        'birth_date',
        'address',
        'hire_date',
        'base_salary',
        'commission_rate',
        'target_amount',
        'status',
        'commission_type',
        'commission_tiers',
        'vehicle_type',
        'vehicle_number',
        'license_number',
        'license_expiry',
        'notes',
        'profile_image',
        'documents',
        'is_active',
        'manager_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'hire_date' => 'date',
        'license_expiry' => 'date',
        'base_salary' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'target_amount' => 'decimal:2',
        'commission_tiers' => 'array',
        'documents' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the manager that supervises the representative.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the branch that owns the representative.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the representative.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the sales areas assigned to the representative.
     */
    public function salesAreas(): BelongsToMany
    {
        return $this->belongsToMany(SalesArea::class, 'representative_area_assignments')
            ->withPivot(['assigned_date', 'effective_from', 'effective_to', 'assignment_type', 'commission_override', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get the active sales areas assigned to the representative.
     */
    public function activeSalesAreas(): BelongsToMany
    {
        return $this->salesAreas()
            ->wherePivot('is_active', true)
            ->wherePivot('effective_from', '<=', now())
            ->where(function($query) {
                $query->wherePivot('effective_to', '>=', now())
                      ->orWherePivot('effective_to', null);
            });
    }

    /**
     * Get the sales routes for the representative.
     */
    public function salesRoutes(): HasMany
    {
        return $this->hasMany(SalesRoute::class);
    }

    /**
     * Get the sales visits for the representative.
     */
    public function salesVisits(): HasMany
    {
        return $this->hasMany(SalesVisit::class);
    }

    /**
     * Get the sales commissions for the representative.
     */
    public function salesCommissions(): HasMany
    {
        return $this->hasMany(SalesCommission::class);
    }

    /**
     * Get the sales targets for the representative.
     */
    public function salesTargets(): HasMany
    {
        return $this->hasMany(SalesTarget::class);
    }

    /**
     * Get the sales expenses for the representative.
     */
    public function salesExpenses(): HasMany
    {
        return $this->hasMany(SalesExpense::class);
    }

    /**
     * Get the customers assigned to the representative.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'sales_representative_id');
    }

    /**
     * Calculate total commission for a period.
     */
    public function calculateCommission($startDate, $endDate): float
    {
        return $this->salesCommissions()
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('payment_status', '!=', 'cancelled')
            ->sum('net_commission');
    }

    /**
     * Calculate total sales for a period.
     */
    public function calculateSales($startDate, $endDate): float
    {
        return $this->salesCommissions()
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('commission_type', 'sales')
            ->sum('base_amount');
    }

    /**
     * Get current month target.
     */
    public function getCurrentMonthTarget()
    {
        return $this->salesTargets()
            ->where('target_type', 'monthly')
            ->where('target_year', now()->year)
            ->where('target_month', now()->month)
            ->where('status', 'active')
            ->first();
    }

    /**
     * Calculate achievement percentage for current month.
     */
    public function getCurrentMonthAchievement(): array
    {
        $target = $this->getCurrentMonthTarget();
        if (!$target) {
            return ['sales' => 0, 'visits' => 0, 'collection' => 0];
        }

        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $achievedSales = $this->calculateSales($startOfMonth, $endOfMonth);
        $achievedVisits = $this->salesVisits()
            ->whereBetween('visit_date', [$startOfMonth, $endOfMonth])
            ->where('visit_status', 'completed')
            ->count();

        $achievedCollection = $this->salesCommissions()
            ->whereBetween('transaction_date', [$startOfMonth, $endOfMonth])
            ->where('commission_type', 'collection')
            ->sum('base_amount');

        return [
            'sales' => $target->sales_target > 0 ? ($achievedSales / $target->sales_target) * 100 : 0,
            'visits' => $target->visits_target > 0 ? ($achievedVisits / $target->visits_target) * 100 : 0,
            'collection' => $target->collection_target > 0 ? ($achievedCollection / $target->collection_target) * 100 : 0,
        ];
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => '#28a745',
            'inactive' => '#6c757d',
            'suspended' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Get full name with employee code.
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' (' . $this->employee_code . ')';
    }

    /**
     * Check if license is expired or expiring soon.
     */
    public function isLicenseExpiringSoon($days = 30): bool
    {
        if (!$this->license_expiry) {
            return false;
        }

        return $this->license_expiry->diffInDays(now()) <= $days;
    }

    /**
     * Scope a query to only include active representatives.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    /**
     * Scope a query to order by name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('name');
    }
}
