<?php

namespace App\Http\Controllers\Modules\GeneralLedger;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Modules\GeneralLedger\FiscalYear;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class FiscalYearController extends Controller
{
    /**
     * Display a listing of the fiscal years.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $fiscalYears = FiscalYear::orderBy('start_date', 'desc')->get();
        return view('admin.fiscal_years.index', compact('fiscalYears'));
    }

    /**
     * Show the form for creating a new fiscal year.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.fiscal_years.create');
    }

    /**
     * Store a newly created fiscal year in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_locked' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check for overlapping fiscal years
        $overlapping = FiscalYear::where(function($query) use ($request) {
            $query->whereBetween('start_date', [$request->start_date, $request->end_date])
                ->orWhereBetween('end_date', [$request->start_date, $request->end_date])
                ->orWhere(function($q) use ($request) {
                    $q->where('start_date', '<=', $request->start_date)
                      ->where('end_date', '>=', $request->end_date);
                });
        })->exists();

        if ($overlapping) {
            return redirect()->back()
                ->withErrors(['overlap' => 'هذه السنة المالية تتداخل مع سنة مالية موجودة بالفعل.'])
                ->withInput();
        }

        $fiscalYear = new FiscalYear();
        $fiscalYear->name = $request->name;
        $fiscalYear->start_date = $request->start_date;
        $fiscalYear->end_date = $request->end_date;
        $fiscalYear->is_active = $request->has('is_active');
        $fiscalYear->is_locked = $request->has('is_locked');
        $fiscalYear->save();

        return redirect()->route('admin.fiscal_years.index')
            ->with('success', 'تم إنشاء السنة المالية بنجاح.');
    }

    /**
     * Display the specified fiscal year.
     *
     * @param  \App\Models\Modules\GeneralLedger\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function show(FiscalYear $fiscalYear)
    {
        return view('admin.fiscal_years.show', compact('fiscalYear'));
    }

    /**
     * Show the form for editing the specified fiscal year.
     *
     * @param  \App\Models\Modules\GeneralLedger\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function edit(FiscalYear $fiscalYear)
    {
        return view('admin.fiscal_years.edit', compact('fiscalYear'));
    }

    /**
     * Update the specified fiscal year in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Modules\GeneralLedger\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FiscalYear $fiscalYear)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_locked' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check for overlapping fiscal years (excluding the current one)
        $overlapping = FiscalYear::where('id', '!=', $fiscalYear->id)
            ->where(function($query) use ($request) {
                $query->whereBetween('start_date', [$request->start_date, $request->end_date])
                    ->orWhereBetween('end_date', [$request->start_date, $request->end_date])
                    ->orWhere(function($q) use ($request) {
                        $q->where('start_date', '<=', $request->start_date)
                          ->where('end_date', '>=', $request->end_date);
                    });
            })->exists();

        if ($overlapping) {
            return redirect()->back()
                ->withErrors(['overlap' => 'هذه السنة المالية تتداخل مع سنة مالية موجودة بالفعل.'])
                ->withInput();
        }

        $fiscalYear->name = $request->name;
        $fiscalYear->start_date = $request->start_date;
        $fiscalYear->end_date = $request->end_date;
        $fiscalYear->is_active = $request->has('is_active');
        $fiscalYear->is_locked = $request->has('is_locked');
        $fiscalYear->save();

        return redirect()->route('admin.fiscal_years.index')
            ->with('success', 'تم تحديث السنة المالية بنجاح.');
    }

    /**
     * Remove the specified fiscal year from storage.
     *
     * @param  \App\Models\Modules\GeneralLedger\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function destroy(FiscalYear $fiscalYear)
    {
        // Check if there are any journal entries associated with this fiscal year
        $hasJournalEntries = DB::table('journal_entries')
            ->where('fiscal_year_id', $fiscalYear->id)
            ->exists();

        if ($hasJournalEntries) {
            return redirect()->route('admin.fiscal_years.index')
                ->with('error', 'لا يمكن حذف هذه السنة المالية لأنها تحتوي على قيود محاسبية.');
        }

        $fiscalYear->delete();

        return redirect()->route('admin.fiscal_years.index')
            ->with('success', 'تم حذف السنة المالية بنجاح.');
    }
}
