<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User; // For created_by_id, posted_by_id
use App\Models\Modules\Branches\Branch;

class JournalEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        "entry_number",
        "entry_date",
        "branch_id",
        "description",
        "status", // e.g., draft, posted, cancelled
        "created_by_id",
        "posted_by_id",
        "posted_at",
    ];

    protected $casts = [
        "entry_date" => "date",
        "posted_at" => "datetime",
    ];

    public function items()
    {
        return $this->hasMany(JournalEntryItem::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, "created_by_id");
    }

    public function postedBy()
    {
        return $this->belongsTo(User::class, "posted_by_id");
    }

    public static function getNextEntryNumber()
    {
        // Basic sequential number, can be made more complex (e.g., per branch, per year)
        $lastEntry = self::orderBy("id", "desc")->first();
        $number = $lastEntry ? (int)substr($lastEntry->entry_number, 3) + 1 : 1;
        return "JE-" . str_pad($number, 6, "0", STR_PAD_LEFT);
    }

    // Accessor for total debit
    public function getTotalDebitAttribute()
    {
        return $this->items->sum("debit_amount");
    }

    // Accessor for total credit
    public function getTotalCreditAttribute()
    {
        return $this->items->sum("credit_amount");
    }

    // Accessor for status color (for UI)
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case "posted":
                return "success";
            case "draft":
                return "warning";
            case "cancelled":
                return "danger";
            default:
                return "secondary";
        }
    }
}

