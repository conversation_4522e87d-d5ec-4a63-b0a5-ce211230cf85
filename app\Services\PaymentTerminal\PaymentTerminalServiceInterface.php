<?php

namespace App\Services\PaymentTerminal;

interface PaymentTerminalServiceInterface
{
    /**
     * Set the configuration for the payment terminal provider.
     *
     * @param array $config
     * @return void
     */
    public function setConfig(array $config): void;

    /**
     * Initiate a payment transaction.
     *
     * @param float $amount The amount to be charged.
     * @param string $currency The currency code (e.g., SAR).
     * @param array $options Additional options for the transaction (e.g., invoice_id, customer_details).
     * @return array The response from the payment terminal provider, including transaction ID and status.
     */
    public function initiatePayment(float $amount, string $currency, array $options = []): array;

    /**
     * Check the status of a specific payment transaction.
     *
     * @param string $transactionId The unique identifier of the transaction.
     * @return array The status of the transaction (e.g., pending, success, failed) and other details.
     */
    public function checkPaymentStatus(string $transactionId): array;

    /**
     * Process a refund for a specific transaction.
     *
     * @param string $transactionId The unique identifier of the original transaction.
     * @param float $amount The amount to be refunded.
     * @param string $currency The currency code.
     * @param array $options Additional options for the refund.
     * @return array The response from the payment terminal provider regarding the refund.
     */
    public function refundPayment(string $transactionId, float $amount, string $currency, array $options = []): array;

    /**
     * Test the connection to the payment terminal or provider's API.
     *
     * @return array Connection status and any relevant messages.
     */
    public function testConnection(): array;
}

