# دليل تكامل هيئة الزكاة والضريبة والجمارك (ZATCA)

## نظرة عامة

يحتوي هذا المشروع على تكامل شامل مع هيئة الزكاة والضريبة والجمارك (ZATCA) للفوترة الإلكترونية (فاتورة) في المملكة العربية السعودية.

## 🔧 الإعداد والتكوين

### 1. إعدادات البيئة (.env)

تم إضافة الإعدادات التالية إلى ملف `.env`:

```env
# إعدادات هيئة الزكاة والضريبة والجمارك (ZATCA)
ZATCA_ENVIRONMENT=sandbox
ZATCA_ENABLED=true
ZATCA_CURRENT_PHASE=2

# معلومات الشركة للفوترة الإلكترونية
ZATCA_SELLER_NAME="شركة المحاسبة المتقدمة"
ZATCA_VAT_REGISTRATION_NUMBER=310123456789013
ZATCA_SELLER_STREET="شارع الملك فهد"
ZATCA_SELLER_BUILDING_NUMBER=1234
ZATCA_SELLER_CITY="الرياض"
ZATCA_SELLER_POSTAL_ZONE=12345
ZATCA_SELLER_COUNTRY_CODE=SA

# إعدادات الفوترة الإلكترونية
ZATCA_DIGITAL_SIGNATURE_ENABLED=true
ZATCA_AUTO_SUBMIT=false
ZATCA_QR_CODE_ENABLED=true
ZATCA_DEFAULT_VAT_RATE=15

# OTP للحصول على شهادة الامتثال
ZATCA_COMPLIANCE_OTP=
```

### 2. ملف التكوين (config/zatca.php)

تم إنشاء ملف تكوين شامل يحتوي على:
- إعدادات البيئة (sandbox/production)
- معلومات البائع
- إعدادات الشهادات والتوقيع الرقمي
- روابط API الخاصة بـ ZATCA
- إعدادات الضرائب والفوترة

## 🚀 المكونات الرئيسية

### 1. خدمة ZATCA (ZatcaInvoiceService)

الموقع: `app/Services/Zatca/ZatcaInvoiceService.php`

**الوظائف الرئيسية:**
- إنشاء شهادة CSR
- طلب شهادة الامتثال (CSID)
- إنشاء فواتير XML متوافقة مع ZATCA
- توقيع الفواتير رقمياً
- إرسال الفواتير إلى ZATCA (تقرير/تصريح)
- إنشاء QR Code

### 2. وحدة التحكم (ZatcaIntegrationController)

الموقع: `app/Http/Controllers/Modules/Integrations/ZatcaIntegrationController.php`

**الصفحات المتاحة:**
- `/admin/integrations/zatca` - لوحة تحكم ZATCA
- `/admin/integrations/zatca/edit` - إعدادات التكامل
- `/admin/integrations/zatca/logs` - سجلات الفواتير

### 3. أمر الاختبار

```bash
php artisan test:zatca-full --type=simplified --otp=YOUR_OTP
```

## 📋 خطوات التفعيل

### المرحلة 1: إعداد الشهادات

1. **تحديث معلومات الشركة** في ملف `.env`
2. **إنشاء شهادة CSR:**
   ```bash
   php artisan test:zatca-full --type=simplified
   ```

3. **الحصول على OTP** من بوابة فاتورة
4. **طلب شهادة الامتثال:**
   ```bash
   php artisan test:zatca-full --otp=YOUR_OTP_HERE
   ```

### المرحلة 2: اختبار التكامل

1. **اختبار إنشاء فاتورة مبسطة:**
   ```bash
   php artisan test:zatca-full --type=simplified
   ```

2. **اختبار إنشاء فاتورة عادية:**
   ```bash
   php artisan test:zatca-full --type=standard
   ```

## 🔗 التكامل مع نقاط البيع

يتم تكامل ZATCA تلقائياً مع نظام نقاط البيع:
- إنشاء فواتير مبسطة للمبيعات النقدية
- إنشاء QR Code تلقائياً
- إرسال الفواتير إلى ZATCA (اختياري)

## 📁 هيكل الملفات

```
app/
├── Services/Zatca/
│   └── ZatcaInvoiceService.php
├── Http/Controllers/Modules/Integrations/
│   └── ZatcaIntegrationController.php
└── Console/Commands/
    └── TestFullZatcaIntegration.php

config/
└── zatca.php

resources/views/admin/integrations/zatca/
├── index.blade.php
├── form.blade.php
└── show.blade.php

storage/app/zatca/
├── csr.pem
├── csr_private_key.pem
├── csid_certificate.pem
└── csid_private_key.pem
```

## 🔐 الأمان

- جميع الشهادات والمفاتيح الخاصة محفوظة في `storage/app/zatca/`
- يتم تشفير المفاتيح الخاصة بكلمات مرور
- جميع الاتصالات مع ZATCA مشفرة بـ HTTPS

## 📊 المراقبة والسجلات

- سجلات مفصلة لجميع العمليات
- تتبع حالة الفواتير المرسلة
- تقارير الأخطاء والتحذيرات

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في شهادة CSR:**
   - تأكد من صحة معلومات الشركة
   - تحقق من رقم التسجيل الضريبي

2. **فشل في الحصول على CSID:**
   - تأكد من صحة OTP
   - تحقق من انتهاء صلاحية OTP

3. **خطأ في إرسال الفاتورة:**
   - تأكد من وجود شهادة CSID صالحة
   - تحقق من تنسيق XML للفاتورة

## 📞 الدعم

للحصول على الدعم:
1. راجع سجلات النظام في `storage/logs/laravel.log`
2. تحقق من سجلات ZATCA في لوحة التحكم
3. راجع وثائق ZATCA الرسمية

## 🔄 التحديثات المستقبلية

- دعم الفواتير المختلطة
- تكامل مع أنظمة ERP إضافية
- تحسينات في واجهة المستخدم
- دعم المزيد من أنواع الفواتير
