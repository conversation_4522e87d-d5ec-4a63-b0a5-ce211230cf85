@extends('layouts.admin')

@section('title', 'تفاصيل السنة المالية')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل السنة المالية: {{ $fiscalYear->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.fiscal_years.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات السنة المالية</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $fiscalYear->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ البداية</th>
                                            <td>{{ $fiscalYear->formatted_start_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ النهاية</th>
                                            <td>{{ $fiscalYear->formatted_end_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>المدة</th>
                                            <td>{{ $fiscalYear->duration_in_months }} شهر</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if ($fiscalYear->is_active)
                                                    <span class="badge badge-success">نشطة</span>
                                                @else
                                                    <span class="badge badge-secondary">غير نشطة</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>حالة القفل</th>
                                            <td>
                                                @if ($fiscalYear->is_locked)
                                                    <span class="badge badge-danger">مقفلة</span>
                                                @else
                                                    <span class="badge badge-info">مفتوحة</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $fiscalYear->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $fiscalYear->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">إحصائيات السنة المالية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-info"><i class="fas fa-file-invoice"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">عدد القيود المحاسبية</span>
                                                    <span class="info-box-number">{{ $fiscalYear->journalEntries->count() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">القيود المرحلة</span>
                                                    <span class="info-box-number">{{ $fiscalYear->journalEntries->where('is_posted', true)->count() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">القيود المعلقة</span>
                                                    <span class="info-box-number">{{ $fiscalYear->journalEntries->where('is_posted', false)->count() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-danger"><i class="fas fa-ban"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">القيود الملغاة</span>
                                                    <span class="info-box-number">{{ $fiscalYear->journalEntries->where('is_cancelled', true)->count() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">الإجراءات</h5>
                                </div>
                                <div class="card-body">
                                    <a href="{{ route('admin.fiscal_years.edit', $fiscalYear) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <form action="{{ route('admin.fiscal_years.destroy', $fiscalYear) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه السنة المالية؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
