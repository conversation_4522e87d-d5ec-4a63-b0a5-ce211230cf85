@extends('layouts.admin')

@section('title', 'إعدادات نقاط البيع')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إعدادات نقاط البيع</h3>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="row">
                        <!-- الإعدادات العامة -->
                        <div class="col-md-4 mb-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-gear-fill text-primary" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">الإعدادات العامة</h5>
                                    <p class="card-text">إعدادات عامة لنقاط البيع</p>
                                    <a href="{{ route('admin.pos.settings.index') }}" class="btn btn-primary">
                                        <i class="fas fa-cog"></i> إدارة الإعدادات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الفواتير -->
                        <div class="col-md-4 mb-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-receipt text-success" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">إعدادات الفواتير</h5>
                                    <p class="card-text">تخصيص شكل ومحتوى الفواتير</p>
                                    <a href="{{ route('admin.pos.settings.receipt') }}" class="btn btn-success">
                                        <i class="fas fa-receipt"></i> إعدادات الفواتير
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- طرق الدفع -->
                        <div class="col-md-4 mb-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="bi bi-credit-card-fill text-info" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">طرق الدفع</h5>
                                    <p class="card-text">إدارة طرق الدفع المتاحة</p>
                                    <a href="{{ route('admin.pos.settings.payment_methods') }}" class="btn btn-info">
                                        <i class="fas fa-credit-card"></i> طرق الدفع
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الضرائب -->
                        <div class="col-md-4 mb-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-percent text-warning" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">إعدادات الضرائب</h5>
                                    <p class="card-text">تكوين الضرائب والرسوم</p>
                                    <a href="{{ route('admin.pos.settings.tax') }}" class="btn btn-warning">
                                        <i class="fas fa-percentage"></i> إعدادات الضرائب
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج الإعدادات العامة -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>الإعدادات العامة</h4>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('admin.pos.settings.update') }}" method="POST">
                                        @csrf
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="default_currency">العملة الافتراضية</label>
                                                    <select class="form-control" id="default_currency" name="default_currency">
                                                        <option value="SAR">ريال سعودي (SAR)</option>
                                                        <option value="USD">دولار أمريكي (USD)</option>
                                                        <option value="EUR">يورو (EUR)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="default_language">اللغة الافتراضية</label>
                                                    <select class="form-control" id="default_language" name="default_language">
                                                        <option value="ar">العربية</option>
                                                        <option value="en">الإنجليزية</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="auto_print_receipt" name="auto_print_receipt">
                                                        <label class="form-check-label" for="auto_print_receipt">
                                                            طباعة الفاتورة تلقائياً
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="require_customer_info" name="require_customer_info">
                                                        <label class="form-check-label" for="require_customer_info">
                                                            طلب معلومات العميل إجبارياً
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> حفظ الإعدادات
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
