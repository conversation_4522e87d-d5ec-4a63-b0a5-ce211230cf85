@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>User Details: {{ $user->name }}</h2>

        <table class="table table-bordered">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{ $user->id }}</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{ $user->name }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ $user->email }}</td>
                </tr>
                <tr>
                    <th>Email Verified At</th>
                    <td>{{ $user->email_verified_at ?? "Not verified" }}</td>
                </tr>
                <tr>
                    <th>Branch</th>
                    <td>{{ $user->branch->name ?? "N/A" }}</td>
                </tr>
                <tr>
                    <th>Roles</th>
                    <td>
                        @if($user->roles->isNotEmpty())
                            @foreach($user->roles as $role)
                                <span class="badge badge-primary">{{ $role->name }}</span>
                            @endforeach
                        @else
                            No roles assigned.
                        @endif
                    </td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{ $user->created_at }}</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{ $user->updated_at }}</td>
                </tr>
            </tbody>
        </table>

        <a href="{{ route("admin.users.edit", $user->id) }}" class="btn btn-warning">Edit User</a>
        <a href="{{ route("admin.users.index") }}" class="btn btn-secondary">Back to List</a>
    </div>
@endsection

