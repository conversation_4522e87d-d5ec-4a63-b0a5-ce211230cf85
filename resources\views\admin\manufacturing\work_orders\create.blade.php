@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Add New Work Order</h1>
        <form action="{{ route("admin.manufacturing.work_orders.store") }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="work_order_number">Work Order Number</label>
                <input type="text" name="work_order_number" id="work_order_number" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="item_id">Item to Produce</label>
                <select name="item_id" id="item_id" class="form-control" required>
                    {{-- @foreach($manufacturedItems as $item) --}}
                    {{-- <option value="{{ $item->id }}">{{ $item->name }} ({{ $item->item_code }})</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Item to Produce</option>
                </select>
            </div>
            <div class="form-group">
                <label for="bom_id">Bill of Materials (BOM)</label>
                <select name="bom_id" id="bom_id" class="form-control">
                    {{-- @foreach($boms as $bom) --}}
                    {{-- <option value="{{ $bom->id }}">{{ $bom->name }} - v{{$bom->version}} (for {{ $bom->item->name }})</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select BOM (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="quantity_to_produce">Quantity to Produce</label>
                <input type="number" step="0.0001" name="quantity_to_produce" id="quantity_to_produce" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="status">Status</label>
                <select name="status" id="status" class="form-control">
                    <option value="pending" selected>Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="on_hold">On Hold</option>
                </select>
            </div>
            <div class="form-group">
                <label for="planned_start_date">Planned Start Date</label>
                <input type="datetime-local" name="planned_start_date" id="planned_start_date" class="form-control">
            </div>
            <div class="form-group">
                <label for="planned_end_date">Planned End Date</label>
                <input type="datetime-local" name="planned_end_date" id="planned_end_date" class="form-control">
            </div>
            <div class="form-group">
                <label for="branch_id">Branch</label>
                <select name="branch_id" id="branch_id" class="form-control">
                    {{-- @foreach($branches as $branch) --}}
                    {{-- <option value="{{ $branch->id }}">{{ $branch->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Branch (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="assigned_to_id">Assigned To</label>
                <select name="assigned_to_id" id="assigned_to_id" class="form-control">
                    {{-- @foreach($users as $user) --}}
                    {{-- <option value="{{ $user->id }}">{{ $user->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select User (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea name="notes" id="notes" class="form-control"></textarea>
            </div>

            {{-- Section for Work Order Items (Components to be consumed / Output items) - Can be dynamic --}}
            {{-- This might be more complex and could be handled on the edit/show view or via a separate process --}}

            <button type="submit" class="btn btn-success mt-3">Save Work Order</button>
        </form>
    </div>
@endsection
