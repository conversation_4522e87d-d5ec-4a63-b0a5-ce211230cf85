<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Modules\Branches\Branch;
use App\Models\Customer;
use App\Models\User;

class RestaurantOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'type',
        'status',
        'table_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'delivery_address',
        'guests_count',
        'subtotal',
        'tax_amount',
        'service_charge',
        'delivery_charge',
        'discount_amount',
        'total_amount',
        'notes',
        'special_instructions',
        'order_time',
        'estimated_ready_time',
        'ready_time',
        'served_time',
        'waiter_id',
        'cashier_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'guests_count' => 'integer',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'service_charge' => 'decimal:2',
        'delivery_charge' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'order_time' => 'datetime',
        'estimated_ready_time' => 'datetime',
        'ready_time' => 'datetime',
        'served_time' => 'datetime',
    ];

    /**
     * Get the table that owns the order.
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(RestaurantTable::class, 'table_id');
    }

    /**
     * Get the customer that owns the order.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the waiter that serves the order.
     */
    public function waiter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiter_id');
    }

    /**
     * Get the cashier that processes the order.
     */
    public function cashier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    /**
     * Get the branch that owns the order.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the order.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the order items for the order.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(RestaurantOrderItem::class, 'order_id');
    }

    /**
     * Generate unique order number.
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $date = now()->format('Ymd');
        $lastOrder = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = $lastOrder ? (int)substr($lastOrder->order_number, -4) + 1 : 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate order totals.
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->orderItems->sum(function ($item) {
            return $item->total_price;
        });

        $this->tax_amount = $this->subtotal * ($this->tax_rate ?? 0) / 100;
        $this->total_amount = $this->subtotal + $this->tax_amount + $this->service_charge + $this->delivery_charge - $this->discount_amount;
        
        $this->save();
    }

    /**
     * Get status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => '#ffc107',
            'confirmed' => '#17a2b8',
            'preparing' => '#fd7e14',
            'ready' => '#28a745',
            'served' => '#6f42c1',
            'completed' => '#28a745',
            'cancelled' => '#dc3545',
            default => '#6c757d'
        };
    }

    /**
     * Check if order can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if order is active.
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Scope a query to only include active orders.
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope a query to only include today's orders.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }
}
