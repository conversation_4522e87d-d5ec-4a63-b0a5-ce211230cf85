@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("Ticket Priority Details") }}</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ $priority->name }}</h4>
        </div>
        <div class="card-body">
            <p><strong>{{ __("ID:") }}</strong> {{ $priority->id }}</p>
            <p><strong>{{ __("Name:") }}</strong> {{ $priority->name }}</p>
            <p><strong>{{ __("Level:") }}</strong> {{ $priority->level }}</p>
            <p><strong>{{ __("Color:") }}</strong> 
                <span style="display:inline-block; width: 20px; height: 20px; background-color: {{ $priority->color ?? '#FFFFFF' }}; border: 1px solid #ccc;"></span>
                {{ $priority->color ?? __("N/A") }}
            </p>
            <p><strong>{{ __("Active:") }}</strong> 
                @if($priority->is_active)
                    <span class="badge badge-success">{{ __("Yes") }}</span>
                @else
                    <span class="badge badge-danger">{{ __("No") }}</span>
                @endif
            </p>
            <p><strong>{{ __("Created At:") }}</strong> {{ $priority->created_at->format("Y-m-d H:i:s") }}</p>
            <p><strong>{{ __("Updated At:") }}</strong> {{ $priority->updated_at->format("Y-m-d H:i:s") }}</p>
        </div>
        <div class="card-footer">
            <a href="{{ route("admin.ticketing.priorities.edit", $priority->id) }}" class="btn btn-warning">{{ __("Edit") }}</a>
            <a href="{{ route("admin.ticketing.priorities.index") }}" class="btn btn-secondary">{{ __("Back to List") }}</a>
        </div>
    </div>
</div>
@endsection

