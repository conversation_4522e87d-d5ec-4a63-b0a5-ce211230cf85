<?php

namespace App\Providers;

use App\Events\Bnpl\BnplPaymentAuthorizedEvent;
use App\Events\Bnpl\BnplPaymentCapturedEvent;
use App\Events\Bnpl\BnplPaymentFailedEvent;
use App\Events\Bnpl\BnplRefundProcessedEvent;
use App\Listeners\Bnpl\HandleBnplPaymentAuthorized;
use App\Listeners\Bnpl\HandleBnplPaymentCaptured;
use App\Listeners\Bnpl\HandleBnplPaymentFailed;
use App\Listeners\Bnpl\HandleBnplRefundProcessed;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        BnplPaymentAuthorizedEvent::class => [
            HandleBnplPaymentAuthorized::class,
        ],
        BnplPaymentCapturedEvent::class => [
            HandleBnplPaymentCaptured::class,
        ],
        BnplPaymentFailedEvent::class => [
            HandleBnplPaymentFailed::class,
        ],
        BnplRefundProcessedEvent::class => [
            HandleBnplRefundProcessed::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
