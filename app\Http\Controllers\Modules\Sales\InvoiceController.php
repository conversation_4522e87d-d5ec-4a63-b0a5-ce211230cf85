<?php

namespace App\Http\Controllers\Modules\Sales;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Modules\Sales\Invoice;
use App\Models\Modules\Sales\Customer;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the invoices.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $invoices = Invoice::with('customer')->get();
        return view('admin.sales.invoices.index', compact('invoices'));
    }

    /**
     * Show the form for creating a new invoice.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $customers = Customer::active()->get();
        return view('admin.sales.invoices.create', compact('customers'));
    }

    /**
     * Store a newly created invoice in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'invoice_number' => 'required|string|max:50|unique:invoices',
            'reference' => 'nullable|string|max:100',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.tax_rate' => 'nullable|numeric|min:0',
            'items.*.discount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms' => 'nullable|string|max:1000',
            'status' => 'required|in:draft,sent,paid,partially_paid,overdue,cancelled',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create invoice
        $invoice = new Invoice();
        $invoice->customer_id = $request->customer_id;
        $invoice->invoice_date = $request->invoice_date;
        $invoice->due_date = $request->due_date;
        $invoice->invoice_number = $request->invoice_number;
        $invoice->reference = $request->reference;
        $invoice->notes = $request->notes;
        $invoice->terms = $request->terms;
        $invoice->status = $request->status;

        // Calculate totals
        $subtotal = 0;
        $tax_total = 0;
        $discount_total = 0;

        foreach ($request->items as $item) {
            $line_total = $item['quantity'] * $item['price'];
            $line_discount = isset($item['discount']) ? $item['discount'] : 0;
            $line_tax = isset($item['tax_rate']) ? ($line_total - $line_discount) * ($item['tax_rate'] / 100) : 0;

            $subtotal += $line_total;
            $discount_total += $line_discount;
            $tax_total += $line_tax;
        }

        $invoice->subtotal = $subtotal;
        $invoice->discount_total = $discount_total;
        $invoice->tax_total = $tax_total;
        $invoice->total_amount = $subtotal - $discount_total + $tax_total;
        $invoice->due_amount = $invoice->total_amount; // Initially, the full amount is due

        $invoice->save();

        // Save invoice items
        foreach ($request->items as $item) {
            $invoice->items()->create([
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'tax_rate' => isset($item['tax_rate']) ? $item['tax_rate'] : 0,
                'discount' => isset($item['discount']) ? $item['discount'] : 0,
                'line_total' => ($item['quantity'] * $item['price']) -
                                (isset($item['discount']) ? $item['discount'] : 0) +
                                (isset($item['tax_rate']) ? ($item['quantity'] * $item['price'] -
                                (isset($item['discount']) ? $item['discount'] : 0)) *
                                ($item['tax_rate'] / 100) : 0)
            ]);
        }

        return redirect()->route('admin.sales.invoices.index')
            ->with('success', 'تم إنشاء الفاتورة بنجاح');
    }

    /**
     * Display the specified invoice.
     *
     * @param  \App\Models\Modules\Sales\Invoice  $invoice
     * @return \Illuminate\Http\Response
     */
    public function show(Invoice $invoice)
    {
        $invoice->load('customer', 'items.product');
        return view('admin.sales.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice.
     *
     * @param  \App\Models\Modules\Sales\Invoice  $invoice
     * @return \Illuminate\Http\Response
     */
    public function edit(Invoice $invoice)
    {
        $customers = Customer::active()->get();
        $invoice->load('items.product');
        return view('admin.sales.invoices.edit', compact('invoice', 'customers'));
    }

    /**
     * Update the specified invoice in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Modules\Sales\Invoice  $invoice
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Invoice $invoice)
    {
        // Similar validation as store method
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'invoice_number' => 'required|string|max:50|unique:invoices,invoice_number,' . $invoice->id,
            'reference' => 'nullable|string|max:100',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.tax_rate' => 'nullable|numeric|min:0',
            'items.*.discount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'terms' => 'nullable|string|max:1000',
            'status' => 'required|in:draft,sent,paid,partially_paid,overdue,cancelled',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update invoice
        $invoice->customer_id = $request->customer_id;
        $invoice->invoice_date = $request->invoice_date;
        $invoice->due_date = $request->due_date;
        $invoice->invoice_number = $request->invoice_number;
        $invoice->reference = $request->reference;
        $invoice->notes = $request->notes;
        $invoice->terms = $request->terms;
        $invoice->status = $request->status;

        // Calculate totals (similar to store method)
        $subtotal = 0;
        $tax_total = 0;
        $discount_total = 0;

        foreach ($request->items as $item) {
            $line_total = $item['quantity'] * $item['price'];
            $line_discount = isset($item['discount']) ? $item['discount'] : 0;
            $line_tax = isset($item['tax_rate']) ? ($line_total - $line_discount) * ($item['tax_rate'] / 100) : 0;

            $subtotal += $line_total;
            $discount_total += $line_discount;
            $tax_total += $line_tax;
        }

        $invoice->subtotal = $subtotal;
        $invoice->discount_total = $discount_total;
        $invoice->tax_total = $tax_total;
        $invoice->total_amount = $subtotal - $discount_total + $tax_total;

        // Only update due_amount if status is not paid
        if ($invoice->status != 'paid') {
            // Calculate due amount based on payments
            $paid_amount = $invoice->payments()->sum('amount');
            $invoice->due_amount = $invoice->total_amount - $paid_amount;
        } else {
            $invoice->due_amount = 0; // If status is paid, due amount is 0
        }

        $invoice->save();

        // Delete existing items and create new ones
        $invoice->items()->delete();

        foreach ($request->items as $item) {
            $invoice->items()->create([
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'tax_rate' => isset($item['tax_rate']) ? $item['tax_rate'] : 0,
                'discount' => isset($item['discount']) ? $item['discount'] : 0,
                'line_total' => ($item['quantity'] * $item['price']) -
                                (isset($item['discount']) ? $item['discount'] : 0) +
                                (isset($item['tax_rate']) ? ($item['quantity'] * $item['price'] -
                                (isset($item['discount']) ? $item['discount'] : 0)) *
                                ($item['tax_rate'] / 100) : 0)
            ]);
        }

        return redirect()->route('admin.sales.invoices.index')
            ->with('success', 'تم تحديث الفاتورة بنجاح');
    }

    /**
     * Remove the specified invoice from storage.
     *
     * @param  \App\Models\Modules\Sales\Invoice  $invoice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Invoice $invoice)
    {
        // Check if invoice can be deleted (e.g., no payments, not sent to customer)
        if ($invoice->payments()->exists()) {
            return redirect()->route('admin.sales.invoices.index')
                ->with('error', 'لا يمكن حذف الفاتورة لأنها مرتبطة بمدفوعات');
        }

        // Delete invoice items first
        $invoice->items()->delete();

        // Delete the invoice
        $invoice->delete();

        return redirect()->route('admin.sales.invoices.index')
            ->with('success', 'تم حذف الفاتورة بنجاح');
    }

    /**
     * Display sales reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function reports()
    {
        // Logic to display sales reports
        return view('admin.sales.reports.index');
    }
}
