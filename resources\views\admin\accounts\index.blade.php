@extends("layouts.admin")

@section('styles')
<style>
    /* ===== COMMON STYLES ===== */
    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .btn-icon {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    .btn-icon i {
        margin-right: 5px;
    }

    /* ===== TABS STYLING ===== */
    .nav-tabs {
        border-bottom: 1px solid #e3e6f0;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6e707e;
        font-weight: 600;
        padding: 0.75rem 1rem;
        border-bottom: 3px solid transparent;
        transition: all 0.2s;
    }

    .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #4e73df;
    }

    .nav-tabs .nav-link.active {
        color: #4e73df;
        background-color: transparent;
        border-bottom: 3px solid #4e73df;
    }

    .nav-tabs .nav-link i {
        margin-right: 5px;
    }

    /* ===== TREE VIEW STYLES ===== */
    .accounts-tree {
        margin-top: 1rem;
    }

    .account-type-section {
        margin-bottom: 1rem;
        border-radius: 0.35rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.05);
    }

    .account-type-header {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        cursor: pointer;
        transition: all 0.2s;
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .account-type-header:hover {
        background-color: #eaecf4;
    }

    .account-type-header .toggle-icon {
        margin-left: 0.5rem;
        transition: transform 0.2s;
        color: #4e73df;
    }

    .account-type-header.collapsed .toggle-icon {
        transform: rotate(-90deg);
    }

    .account-type-header .type-icon {
        margin-right: 0.5rem;
        width: 24px;
        text-align: center;
    }

    .account-type-header .type-name {
        font-weight: 600;
        color: #5a5c69;
    }

    .account-type-header .type-balance {
        margin-right: auto;
        font-size: 0.875rem;
        color: #5a5c69;
    }

    .account-type-header .type-balance.negative {
        color: #e74a3b;
    }

    .account-type-content {
        padding: 0;
        background-color: #fff;
    }

    /* Tree structure */
    .tree-container {
        padding: 0.5rem 0;
    }

    .tree {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tree-node {
        position: relative;
        padding: 0;
    }

    .tree-node-content {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        transition: all 0.2s;
        border-radius: 0.25rem;
        margin: 0.125rem 0;
    }

    .tree-node-content:hover {
        background-color: #f8f9fc;
    }

    .tree-node-toggle {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #4e73df;
        transition: transform 0.2s;
    }

    .tree-node-toggle.collapsed {
        transform: rotate(-90deg);
    }

    .tree-node-icon {
        margin: 0 0.5rem;
        width: 20px;
        text-align: center;
    }

    .tree-node-details {
        display: flex;
        flex: 1;
        align-items: center;
    }

    .tree-node-code {
        font-family: monospace;
        color: #858796;
        margin-right: 0.5rem;
        font-size: 0.875rem;
    }

    .tree-node-name {
        flex: 1;
    }

    .tree-node-balance {
        margin-right: 1rem;
        font-size: 0.875rem;
        color: #5a5c69;
    }

    .tree-node-balance.negative {
        color: #e74a3b;
    }

    .tree-node-actions {
        display: none;
    }

    .tree-node-content:hover .tree-node-actions {
        display: flex;
    }

    .tree-node-actions .btn {
        padding: 0.25rem;
        margin-right: 0.25rem;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .tree-children {
        padding-right: 1.5rem;
        position: relative;
    }

    .tree-children:before {
        content: '';
        position: absolute;
        top: 0;
        right: 12px;
        bottom: 0;
        width: 1px;
        background-color: #e3e6f0;
    }

    .tree-node-content.parent-node {
        font-weight: 600;
    }

    /* Type indicators */
    .type-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .type-assets { background-color: #4e73df; }
    .type-liabilities { background-color: #1cc88a; }
    .type-equity { background-color: #f6c23e; }
    .type-revenue { background-color: #36b9cc; }
    .type-expenses { background-color: #e74a3b; }

    /* ===== TABLE VIEW STYLES ===== */
    .accounts-table {
        width: 100%;
    }

    .accounts-table th {
        background-color: #f8f9fc;
        color: #5a5c69;
        font-weight: 600;
        text-align: right;
    }

    .accounts-table td {
        vertical-align: middle;
    }

    .accounts-table .account-type-cell {
        display: flex;
        align-items: center;
    }

    .accounts-table .account-actions {
        white-space: nowrap;
    }

    .accounts-table .account-balance.negative {
        color: #e74a3b;
    }

    /* Search and filters */
    .search-box {
        position: relative;
    }

    .search-box .form-control {
        padding-right: 2.5rem;
    }

    .search-box .search-icon {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #d1d3e2;
    }

    .account-info {
        display: flex;
        flex: 1;
        align-items: center;
    }

    .account-code {
        font-weight: bold;
        min-width: 80px;
        margin-left: 15px;
        color: #5a5c69;
        font-family: 'Courier New', monospace;
    }

    .account-name {
        flex: 1;
        font-weight: 500;
    }

    .account-balance {
        margin: 0 15px;
        font-weight: 600;
        min-width: 120px;
        text-align: left;
        color: #2e59d9;
    }

    .account-balance.negative {
        color: #e74a3b;
    }

    .account-actions {
        display: flex;
        gap: 8px;
    }

    .account-actions .btn {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        transition: all 0.2s;
    }

    .account-actions .btn:hover {
        transform: translateY(-2px);
    }

    .has-children {
        font-weight: bold;
    }

    /* Account Type Colors */
    .account-type-assets {
        border-right: none;
        border-top: 4px solid #4e73df;
    }

    .account-type-liabilities {
        border-right: none;
        border-top: 4px solid #1cc88a;
    }

    .account-type-equity {
        border-right: none;
        border-top: 4px solid #f6c23e;
    }

    .account-type-revenue {
        border-right: none;
        border-top: 4px solid #36b9cc;
    }

    .account-type-expenses {
        border-right: none;
        border-top: 4px solid #e74a3b;
    }

    .account-type-closing {
        border-right: none;
        border-top: 4px solid #6f42c1;
    }

    /* Badge Colors */
    .badge-assets {
        background-color: #4e73df;
        color: white;
    }

    .badge-liabilities {
        background-color: #1cc88a;
        color: white;
    }

    .badge-equity {
        background-color: #f6c23e;
        color: #444;
    }

    .badge-revenue {
        background-color: #36b9cc;
        color: white;
    }

    .badge-expenses {
        background-color: #e74a3b;
        color: white;
    }

    .badge-closing {
        background-color: #6f42c1;
        color: white;
    }

    /* Table Styles */
    .accounts-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .accounts-table thead th {
        background-color: #f8f9fc;
        color: #5a5c69;
        font-weight: 600;
        text-align: right;
        padding: 15px;
        border-bottom: 2px solid #e3e6f0;
    }

    .accounts-table tbody tr {
        transition: all 0.2s;
    }

    .accounts-table tbody tr:hover {
        background-color: #f8f9fc;
    }

    .accounts-table tbody td {
        padding: 12px 15px;
        border-bottom: 1px solid #e3e6f0;
        vertical-align: middle;
    }

    .accounts-table .account-type-cell {
        display: flex;
        align-items: center;
    }

    .accounts-table .account-type-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-left: 8px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .account-info {
            flex-wrap: wrap;
        }

        .account-balance {
            margin-top: 8px;
            width: 100%;
            text-align: right;
        }

        .account-actions {
            margin-top: 8px;
        }

        .accounts-table {
            display: block;
            overflow-x: auto;
        }
    }

    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .tree-node {
        animation: fadeIn 0.3s ease-out forwards;
    }

    /* Tab Styling */
    .nav-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #5a5c69;
        font-weight: 600;
        padding: 10px 20px;
        transition: all 0.2s;
    }

    .nav-tabs .nav-link.active {
        color: #4e73df;
        border-bottom-color: #4e73df;
        background-color: transparent;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom-color: #e3e6f0;
    }

    /* Control Buttons */
    .tree-controls {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .tree-controls .btn {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .tree-controls .btn:hover {
        transform: translateY(-2px);
    }
</style>
@endsection



@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle tree nodes with animation
        $(document).on('click', '.tree-toggle', function(e) {
            e.stopPropagation();
            $(this).find('i').toggleClass('fa-chevron-left fa-chevron-down');

            // Get the target collapse element
            const target = $($(this).data('target'));

            // Toggle the clicked node with smooth animation
            if (target.hasClass('show')) {
                target.removeClass('show');
            } else {
                target.addClass('show');
            }
        });

        // Toggle account type sections
        $('.account-type-header').on('click', function() {
            $(this).find('i:first').toggleClass('fa-chevron-down fa-chevron-right');

            // Toggle the target
            const target = $($(this).data('target'));
            target.toggleClass('show');
        });

        // Expand all button
        $('#expandAll').on('click', function() {
            // Expand all account type sections
            $('.account-type-header').each(function() {
                const target = $($(this).data('target'));
                target.addClass('show');
                $(this).find('i:first').removeClass('fa-chevron-right').addClass('fa-chevron-down');
            });

            // Expand all tree nodes
            $('.tree-toggle').each(function() {
                const target = $($(this).data('target'));
                target.addClass('show');
                $(this).find('i').removeClass('fa-chevron-left').addClass('fa-chevron-down');
            });
        });

        // Collapse all button
        $('#collapseAll').on('click', function() {
            // Collapse all tree nodes
            $('.tree-toggle').each(function() {
                const target = $($(this).data('target'));
                target.removeClass('show');
                $(this).find('i').removeClass('fa-chevron-down').addClass('fa-chevron-left');
            });

            // Keep account type sections open
            $('.account-type-header i:first-child').removeClass('fa-chevron-right').addClass('fa-chevron-down');
        });

        // Filter accounts by type
        $('.filter-accounts').on('click', function(e) {
            e.preventDefault();
            const type = $(this).data('type');

            if (type === 'all') {
                $('.account-type-section').show();
                $('#filterDropdown').html('<i class="fas fa-filter"></i> تصفية');
            } else {
                $('.account-type-section').hide();
                $(`.account-type-section .account-type-${type}`).closest('.account-type-section').show();
                $('#filterDropdown').html(`<i class="fas fa-filter"></i> ${$(this).text()}`);
            }
        });

        // Add negative class to balances
        $('.account-balance').each(function() {
            const text = $(this).text().trim();
            if (text.includes('دائن')) {
                $(this).addClass('negative');
            }
        });

        // Initialize tooltips
        $('[title]').tooltip();

        // Tab switching with animation
        $('#accountTabs a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // Handle tab switching via URL hash
        if (window.location.hash) {
            const hash = window.location.hash;
            if (hash === '#table') {
                $('#table-tab').tab('show');
            }
        }

        // Update URL hash on tab change
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            const target = $(e.target).attr('href');
            if (history.pushState) {
                history.pushState(null, null, target);
            } else {
                location.hash = target;
            }
        });

        // Tree View Search
        $('#treeSearch').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            if (searchTerm.length > 0) {
                // Hide all account type sections first
                $('.account-type-section').each(function() {
                    const section = $(this);
                    let hasMatch = false;

                    // Check each node in this section
                    section.find('.tree-node').each(function() {
                        const node = $(this);
                        const accountName = node.find('.tree-node-name').text().toLowerCase();
                        const accountCode = node.find('.tree-node-code').text().toLowerCase();

                        if (accountName.includes(searchTerm) || accountCode.includes(searchTerm)) {
                            hasMatch = true;
                            node.show();

                            // Expand parent nodes
                            let parent = node.parent().closest('.tree-node');
                            while (parent.length) {
                                parent.show();
                                const collapseId = parent.find('.tree-node-toggle').data('target');
                                $(collapseId).addClass('show');
                                parent = parent.parent().closest('.tree-node');
                            }
                        } else {
                            node.hide();
                        }
                    });

                    // Show/hide the entire section based on matches
                    if (hasMatch) {
                        section.show();
                        section.find('.collapse').addClass('show');
                    } else {
                        section.hide();
                    }
                });

                // Highlight search terms
                $('.tree-node-name').each(function() {
                    const text = $(this).html();
                    const highlightedText = text.replace(
                        new RegExp(searchTerm, 'gi'),
                        match => `<span class="highlight" style="background-color: yellow;">${match}</span>`
                    );
                    $(this).html(highlightedText);
                });
            } else {
                // Show all when search is cleared
                $('.account-type-section').show();
                $('.tree-node').show();

                // Remove highlighting
                $('.tree-node-name').each(function() {
                    const text = $(this).html();
                    $(this).html(text.replace(/<span class="highlight"[^>]*>(.*?)<\/span>/gi, '$1'));
                });
            }
        });

        // Table View Search
        $('#tableSearch').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            if (searchTerm.length > 0) {
                $('.accounts-table tbody tr').each(function() {
                    const row = $(this);
                    const code = row.find('td:first-child').text().toLowerCase();
                    const name = row.find('td:nth-child(2)').text().toLowerCase();
                    const type = row.find('td:nth-child(3)').text().toLowerCase();

                    if (code.includes(searchTerm) || name.includes(searchTerm) || type.includes(searchTerm)) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });
            } else {
                $('.accounts-table tbody tr').show();
            }
        });

        // Filter accounts in table view
        $('.filter-accounts').on('click', function(e) {
            e.preventDefault();
            const type = $(this).data('type');

            if (type === 'all') {
                $('.accounts-table tbody tr').show();
                $('#filterDropdown').html('<i class="fas fa-filter"></i> تصفية حسب النوع');
            } else {
                $('.accounts-table tbody tr').hide();
                $('.accounts-table tbody tr').each(function() {
                    const typeCell = $(this).find('td:nth-child(3)').text().toLowerCase();
                    if (typeCell.includes(type)) {
                        $(this).show();
                    }
                });
                $('#filterDropdown').html(`<i class="fas fa-filter"></i> ${$(this).text().trim()}`);
            }
        });
    });
</script>
@endsection
@section("content")
    <div class="container-fluid">
        <h1 class="h3 mb-4 text-gray-800">دليل الحسابات</h1>

        @if (session("success"))
            <div class="alert alert-success border-left-success alert-dismissible fade show" role="alert">
                {{ session("success") }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if (session("error"))
            <div class="alert alert-danger border-left-danger alert-dismissible fade show" role="alert">
                {{ session("error") }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger border-left-danger" role="alert">
                <ul class="pl-4 my-2">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">جميع الحسابات</h6>
                <a href="{{ route("admin.accounts.create") }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle"></i> إضافة حساب جديد
                </a>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs mb-4" id="accountTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="tree-tab" data-toggle="tab" href="#tree" role="tab" aria-controls="tree" aria-selected="true">
                            <i class="fas fa-sitemap"></i> عرض شجري
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="table-tab" data-toggle="tab" href="#table" role="tab" aria-controls="table" aria-selected="false">
                            <i class="fas fa-table"></i> عرض جدولي
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="accountTabsContent">
                    <!-- عرض شجري -->
                    <div class="tab-pane fade show active" id="tree" role="tabpanel" aria-labelledby="tree-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex">
                                <button id="expandAll" class="btn btn-sm btn-outline-primary mr-2">
                                    <i class="fas fa-expand-alt"></i> توسيع الكل
                                </button>
                                <button id="collapseAll" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-compress-alt"></i> طي الكل
                                </button>
                            </div>
                            <div class="search-box">
                                <input type="text" id="treeSearch" class="form-control form-control-sm" placeholder="بحث في الحسابات...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>

                        @include('admin.accounts._tree_view')
                    </div>

                    <!-- عرض جدولي -->
                    <div class="tab-pane fade" id="table" role="tabpanel" aria-labelledby="table-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-filter"></i> تصفية حسب النوع
                                </button>
                                <div class="dropdown-menu" aria-labelledby="filterDropdown">
                                    <a class="dropdown-item filter-accounts" href="#" data-type="all">جميع الحسابات</a>
                                    <div class="dropdown-divider"></div>
                                    @foreach($accountTypes as $type)
                                        <a class="dropdown-item filter-accounts" href="#" data-type="{{ $type->slug }}">
                                            <span class="type-indicator type-{{ $type->slug }} mr-2"></span>
                                            {{ $type->name_ar }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            <div class="search-box">
                                <input type="text" id="tableSearch" class="form-control form-control-sm" placeholder="بحث في الحسابات...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>

                        @include('admin.accounts._table_view')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

