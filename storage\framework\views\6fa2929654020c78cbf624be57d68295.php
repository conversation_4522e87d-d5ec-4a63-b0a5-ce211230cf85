
<div class="sidebar-menu">
    <!-- لوحة التحكم -->
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="<?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
        <i class="bi bi-speedometer2"></i>
        <span>لوحة التحكم</span>
    </a>

    <?php if(Auth::check() && Auth::user()->hasRole('super_admin')): ?>
    <!-- قسم السوبر أدمن -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">السوبر أدمن</small>
    </div>

    <a href="<?php echo e(route('admin.super_admin.dashboard')); ?>" class="<?php echo e(request()->routeIs('admin.super_admin.dashboard') ? 'active' : ''); ?>">
        <i class="bi bi-speedometer"></i>
        <span>لوحة تحكم السوبر أدمن</span>
    </a>

    <a href="<?php echo e(route('admin.super_admin.subscription_plans.index')); ?>" class="<?php echo e(request()->routeIs('admin.super_admin.subscription_plans.*') ? 'active' : ''); ?>">
        <i class="bi bi-list-stars"></i>
        <span>إدارة الباقات</span>
    </a>

    <a href="<?php echo e(route('admin.super_admin.invoice_limits.index')); ?>" class="<?php echo e(request()->routeIs('admin.super_admin.invoice_limits.*') ? 'active' : ''); ?>">
        <i class="bi bi-file-earmark-ruled"></i>
        <span>حدود الفواتير</span>
    </a>
    <?php endif; ?>

    <!-- قسم المحاسبة والمالية -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المحاسبة والمالية</small>
    </div>

    <!-- دليل الحسابات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.accounts.*') || request()->routeIs('admin.account_types.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-journal-bookmark-fill"></i>
            <span>دليل الحسابات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.accounts.new')); ?>">
                <i class="bi bi-journal-text me-2"></i> الحسابات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.account_types.index')); ?>">
                <i class="bi bi-tags me-2"></i> أنواع الحسابات
            </a>
        </div>
    </div>

    <a href="<?php echo e(route('admin.journal_entries.index')); ?>" class="<?php echo e(request()->routeIs('admin.journal_entries.*') ? 'active' : ''); ?>">
        <i class="bi bi-journal-plus"></i>
        <span>القيود المحاسبية</span>
    </a>

    <a href="<?php echo e(route('admin.fiscal_years.index')); ?>" class="<?php echo e(request()->routeIs('admin.fiscal_years.*') ? 'active' : ''); ?>">
        <i class="bi bi-calendar-range-fill"></i>
        <span>السنوات المالية</span>
    </a>

    <a href="<?php echo e(route('admin.taxes.index')); ?>" class="<?php echo e(request()->routeIs('admin.taxes.*') ? 'active' : ''); ?>">
        <i class="bi bi-percent"></i>
        <span>الضرائب والرسوم</span>
    </a>

    <!-- قسم المبيعات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المبيعات</small>
    </div>

    <a href="<?php echo e(route('admin.sales.customers.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales.customers.*') ? 'active' : ''); ?>">
        <i class="bi bi-person-badge-fill"></i>
        <span>العملاء</span>
    </a>

    <a href="<?php echo e(route('admin.sales.invoices.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales.invoices.*') ? 'active' : ''); ?>">
        <i class="bi bi-receipt"></i>
        <span>فواتير المبيعات</span>
    </a>

    <a href="<?php echo e(route('admin.sales.quotations.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales.quotations.*') ? 'active' : ''); ?>">
        <i class="bi bi-file-earmark-text-fill"></i>
        <span>عروض الأسعار</span>
    </a>

    <a href="<?php echo e(route('admin.sales.returns.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales.returns.*') ? 'active' : ''); ?>">
        <i class="bi bi-arrow-return-left"></i>
        <span>مرتجعات المبيعات</span>
    </a>

    <!-- قسم المشتريات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المشتريات</small>
    </div>

    <a href="<?php echo e(route('admin.purchases.suppliers.index')); ?>" class="<?php echo e(request()->routeIs('admin.purchases.suppliers.*') ? 'active' : ''); ?>">
        <i class="bi bi-truck"></i>
        <span>الموردين</span>
    </a>

    <a href="<?php echo e(route('admin.purchases.invoices.index')); ?>" class="<?php echo e(request()->routeIs('admin.purchases.invoices.*') ? 'active' : ''); ?>">
        <i class="bi bi-bag-fill"></i>
        <span>فواتير المشتريات</span>
    </a>

    <a href="<?php echo e(route('admin.purchases.orders.index')); ?>" class="<?php echo e(request()->routeIs('admin.purchases.orders.*') ? 'active' : ''); ?>">
        <i class="bi bi-cart-plus-fill"></i>
        <span>طلبات الشراء</span>
    </a>

    <a href="<?php echo e(route('admin.purchases.returns.index')); ?>" class="<?php echo e(request()->routeIs('admin.purchases.returns.*') ? 'active' : ''); ?>">
        <i class="bi bi-arrow-return-right"></i>
        <span>مرتجعات المشتريات</span>
    </a>

    <!-- قسم المخزون والمنتجات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المخزون والمنتجات</small>
    </div>

    <!-- إدارة الأصناف -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.items.*') || request()->routeIs('admin.inventory.categories.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-box-seam-fill"></i>
            <span>إدارة الأصناف</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.items.index')); ?>">
                <i class="bi bi-box me-2"></i> الأصناف
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.inventory.categories.index')); ?>">
                <i class="bi bi-tags me-2"></i> التصنيفات
            </a>
        </div>
    </div>

    <!-- إدارة المخزون -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.inventory.warehouses.*') || request()->routeIs('admin.inventory.adjustments.*') || request()->routeIs('admin.inventory.transfers.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-building-fill-check"></i>
            <span>إدارة المخزون</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.inventory.warehouses.index')); ?>">
                <i class="bi bi-building me-2"></i> المستودعات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.inventory.adjustments.index')); ?>">
                <i class="bi bi-arrow-up-down me-2"></i> تسويات المخزون
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.inventory.transfers.index')); ?>">
                <i class="bi bi-arrow-left-right me-2"></i> نقل المخزون
            </a>
        </div>
    </div>

    <!-- قسم التصنيع -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التصنيع</small>
    </div>

    <!-- إدارة الإنتاج -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.manufacturing.boms.*') || request()->routeIs('admin.manufacturing.work_orders.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-gear-wide-connected"></i>
            <span>إدارة الإنتاج</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.manufacturing.boms.index')); ?>">
                <i class="bi bi-diagram-3 me-2"></i> قوائم المواد
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.manufacturing.work_orders.index')); ?>">
                <i class="bi bi-clipboard-check me-2"></i> أوامر العمل
            </a>
        </div>
    </div>

    <a href="<?php echo e(route('admin.manufacturing.operations.index')); ?>" class="<?php echo e(request()->routeIs('admin.manufacturing.operations.*') ? 'active' : ''); ?>">
        <i class="bi bi-gear-fill"></i>
        <span>العمليات</span>
    </a>

    <!-- قسم نقاط البيع -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نقاط البيع</small>
    </div>

    <a href="<?php echo e(route('pos.main')); ?>" target="_blank" class="text-success">
        <i class="bi bi-display"></i>
        <span>فتح نقطة البيع</span>
    </a>

    <!-- إدارة نقاط البيع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.pos.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-pc-display"></i>
            <span>إدارة نقاط البيع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.pos.terminals.index')); ?>">
                <i class="bi bi-pc me-2"></i> أجهزة نقاط البيع
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.pos.sessions.index')); ?>">
                <i class="bi bi-clock-history me-2"></i> جلسات البيع
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.pos.settings.index')); ?>">
                <i class="bi bi-sliders me-2"></i> إعدادات نقاط البيع
            </a>
        </div>
    </div>

    <!-- قسم المطاعم -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نظام المطاعم</small>
    </div>

    <!-- نقطة بيع المطعم -->
    <a href="<?php echo e(route('admin.restaurant.pos')); ?>" target="_blank" class="text-success">
        <i class="bi bi-shop"></i>
        <span>نقطة بيع المطعم</span>
    </a>

    <!-- شاشة المطبخ -->
    <a href="<?php echo e(route('admin.restaurant.kitchen_display')); ?>" target="_blank" class="text-warning">
        <i class="bi bi-tv"></i>
        <span>شاشة المطبخ</span>
    </a>

    <!-- إدارة الطاولات والمناطق -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.restaurant.areas.*') || request()->routeIs('admin.restaurant.tables.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-grid-3x3-gap-fill"></i>
            <span>الطاولات والمناطق</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.areas.index')); ?>">
                <i class="bi bi-map me-2"></i> المناطق
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.tables.index')); ?>">
                <i class="bi bi-table me-2"></i> الطاولات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.floor_plan')); ?>">
                <i class="bi bi-diagram-3 me-2"></i> مخطط المطعم
            </a>
        </div>
    </div>

    <!-- إدارة القوائم -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.restaurant.menu-categories.*') || request()->routeIs('admin.restaurant.menu-items.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-journal-text"></i>
            <span>إدارة القوائم</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.menu-categories.index')); ?>">
                <i class="bi bi-tags me-2"></i> فئات القائمة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.menu-items.index')); ?>">
                <i class="bi bi-card-list me-2"></i> أصناف القائمة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.modifiers.index')); ?>">
                <i class="bi bi-plus-circle me-2"></i> المعدلات والإضافات
            </a>
        </div>
    </div>

    <!-- إدارة المطبخ -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.restaurant.kitchen-stations.*') || request()->routeIs('admin.restaurant.kitchen-printers.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-house-gear-fill"></i>
            <span>إدارة المطبخ</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.kitchen-stations.index')); ?>">
                <i class="bi bi-gear me-2"></i> محطات المطبخ
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.kitchen-printers.index')); ?>">
                <i class="bi bi-printer me-2"></i> طابعات المطبخ
            </a>
        </div>
    </div>

    <!-- إدارة الطلبات -->
    <a href="<?php echo e(route('admin.restaurant.orders.index')); ?>" class="<?php echo e(request()->routeIs('admin.restaurant.orders.*') ? 'active' : ''); ?>">
        <i class="bi bi-receipt-cutoff"></i>
        <span>إدارة الطلبات</span>
    </a>

    <!-- أجهزة نقاط البيع -->
    <a href="<?php echo e(route('admin.restaurant.pos-terminals.index')); ?>" class="<?php echo e(request()->routeIs('admin.restaurant.pos-terminals.*') ? 'active' : ''); ?>">
        <i class="bi bi-pc-display-horizontal"></i>
        <span>أجهزة نقاط البيع</span>
    </a>

    <!-- تقارير المطعم -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.restaurant.reports.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-graph-up"></i>
            <span>تقارير المطعم</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.reports.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.reports.sales')); ?>">
                <i class="bi bi-graph-up me-2"></i> تقارير المبيعات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.reports.menu_performance')); ?>">
                <i class="bi bi-bar-chart me-2"></i> أداء القائمة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.reports.table_turnover')); ?>">
                <i class="bi bi-arrow-repeat me-2"></i> دوران الطاولات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.restaurant.reports.kitchen_performance')); ?>">
                <i class="bi bi-stopwatch me-2"></i> أداء المطبخ
            </a>
        </div>
    </div>

    <!-- قسم المناديب -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نظام المناديب</small>
    </div>

    <!-- إدارة المناديب -->
    <a href="<?php echo e(route('admin.sales-representatives.representatives.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales-representatives.representatives.*') ? 'active' : ''); ?>">
        <i class="bi bi-person-badge-fill"></i>
        <span>إدارة المناديب</span>
    </a>

    <!-- إدارة المناطق -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales-representatives.areas.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-geo-alt-fill"></i>
            <span>المناطق وخطوط السير</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.areas.index')); ?>">
                <i class="bi bi-map me-2"></i> المناطق
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.routes.index')); ?>">
                <i class="bi bi-signpost me-2"></i> خطوط السير
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.areas.map')); ?>">
                <i class="bi bi-globe me-2"></i> خريطة المناطق
            </a>
        </div>
    </div>

    <!-- إدارة الزيارات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales-representatives.visits.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-calendar-check-fill"></i>
            <span>الزيارات والجولات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.visits.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> جميع الزيارات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.visits.calendar')); ?>">
                <i class="bi bi-calendar3 me-2"></i> تقويم الزيارات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.visits.map')); ?>">
                <i class="bi bi-geo-alt me-2"></i> خريطة الزيارات
            </a>
        </div>
    </div>

    <!-- إدارة العمولات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales-representatives.commissions.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-cash-coin"></i>
            <span>العمولات والمكافآت</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.commissions.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> جميع العمولات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.commissions.calculate')); ?>">
                <i class="bi bi-calculator me-2"></i> حساب العمولات
            </a>
        </div>
    </div>

    <!-- إدارة الأهداف -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales-representatives.targets.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-bullseye"></i>
            <span>الأهداف والإنجازات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.targets.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> جميع الأهداف
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.targets.dashboard')); ?>">
                <i class="bi bi-speedometer me-2"></i> لوحة الأهداف
            </a>
        </div>
    </div>

    <!-- إدارة المصروفات -->
    <a href="<?php echo e(route('admin.sales-representatives.expenses.index')); ?>" class="<?php echo e(request()->routeIs('admin.sales-representatives.expenses.*') ? 'active' : ''); ?>">
        <i class="bi bi-receipt-cutoff"></i>
        <span>مصروفات المناديب</span>
    </a>

    <!-- تقارير المناديب -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales-representatives.reports.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-graph-up-arrow"></i>
            <span>تقارير المناديب</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.representatives_performance')); ?>">
                <i class="bi bi-person-check me-2"></i> أداء المناديب
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.areas_performance')); ?>">
                <i class="bi bi-geo me-2"></i> أداء المناطق
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.routes_efficiency')); ?>">
                <i class="bi bi-speedometer me-2"></i> كفاءة خطوط السير
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.visits_analysis')); ?>">
                <i class="bi bi-calendar-check me-2"></i> تحليل الزيارات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.commissions_summary')); ?>">
                <i class="bi bi-cash me-2"></i> ملخص العمولات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.targets_achievement')); ?>">
                <i class="bi bi-trophy me-2"></i> إنجاز الأهداف
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.sales-representatives.reports.expenses_analysis')); ?>">
                <i class="bi bi-receipt me-2"></i> تحليل المصروفات
            </a>
        </div>
    </div>

    <!-- قسم الموارد البشرية -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الموارد البشرية</small>
    </div>

    <a href="<?php echo e(route('admin.hr.employees.index')); ?>" class="<?php echo e(request()->routeIs('admin.hr.employees.*') ? 'active' : ''); ?>">
        <i class="bi bi-person-vcard-fill"></i>
        <span>الموظفين</span>
    </a>

    <a href="<?php echo e(route('admin.hr.departments.index')); ?>" class="<?php echo e(request()->routeIs('admin.hr.departments.*') ? 'active' : ''); ?>">
        <i class="bi bi-diagram-2-fill"></i>
        <span>الأقسام</span>
    </a>

    <a href="<?php echo e(route('admin.hr.payroll.index')); ?>" class="<?php echo e(request()->routeIs('admin.hr.payroll.*') ? 'active' : ''); ?>">
        <i class="bi bi-cash-stack"></i>
        <span>الرواتب</span>
    </a>

    <!-- قسم التكاملات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التكاملات</small>
    </div>

    <!-- تكامل ZATCA -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.integrations.zatca.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-shield-check text-success"></i>
            <span>هيئة الزكاة والضريبة</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.zatca.index')); ?>">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.zatca.edit')); ?>">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.zatca.show')); ?>">
                <i class="bi bi-file-text me-2"></i> سجلات الفواتير
            </a>
        </div>
    </div>

    <!-- تكامل واتساب -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.whatsapp.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-whatsapp text-success"></i>
            <span>تكامل واتساب</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.whatsapp.settings.index')); ?>">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.whatsapp.messages.index')); ?>">
                <i class="bi bi-chat-dots me-2"></i> الرسائل
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.whatsapp.templates.index')); ?>">
                <i class="bi bi-file-text me-2"></i> القوالب
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.whatsapp.opt_ins.index')); ?>">
                <i class="bi bi-person-check me-2"></i> الموافقات
            </a>
        </div>
    </div>

    <!-- تكامل BNPL -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.integrations.bnpl.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-credit-card-2-front text-primary"></i>
            <span>اشتري الآن وادفع لاحقاً</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.bnpl.index')); ?>">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.bnpl.settings')); ?>">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.bnpl.transactions.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> المعاملات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.bnpl.providers.index')); ?>">
                <i class="bi bi-building me-2"></i> مقدمي الخدمة
            </a>
        </div>
    </div>

    <!-- تكامل أجهزة الدفع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.integrations.payment_terminals.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-credit-card text-info"></i>
            <span>أجهزة الدفع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.payment_terminals.index')); ?>">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.payment_terminals.settings')); ?>">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.payment_terminals.transactions.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> المعاملات
            </a>
        </div>
    </div>

    <!-- تكامل التوصيل -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.integrations.delivery.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-truck text-warning"></i>
            <span>خدمات التوصيل</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.delivery.index')); ?>">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.delivery.settings')); ?>">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.delivery.orders.index')); ?>">
                <i class="bi bi-box me-2"></i> طلبات التوصيل
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.integrations.delivery.tracking.index')); ?>">
                <i class="bi bi-geo-alt me-2"></i> تتبع الطلبات
            </a>
        </div>
    </div>

    <!-- قسم التقارير -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التقارير</small>
    </div>

    <!-- التقارير المالية -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.reports.financial.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-file-earmark-bar-graph-fill"></i>
            <span>التقارير المالية</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.reports.financial.index')); ?>">
                <i class="bi bi-graph-up me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.financial_reports.index')); ?>">
                <i class="bi bi-file-earmark-spreadsheet me-2"></i> التقارير المحاسبية
            </a>
        </div>
    </div>

    <!-- تقارير المبيعات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.sales.reports.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-cart-check-fill"></i>
            <span>تقارير المبيعات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.sales.reports.index')); ?>">
                <i class="bi bi-graph-up me-2"></i> تقارير عامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.reports.sales.index')); ?>">
                <i class="bi bi-bar-chart me-2"></i> تحليل المبيعات
            </a>
        </div>
    </div>

    <!-- تقارير المشتريات -->
    <a href="<?php echo e(route('admin.reports.purchases.index')); ?>" class="<?php echo e(request()->routeIs('admin.reports.purchases.*') ? 'active' : ''); ?>">
        <i class="bi bi-bag-check-fill"></i>
        <span>تقارير المشتريات</span>
    </a>

    <!-- تقارير المخزون -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.reports.inventory.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-boxes"></i>
            <span>تقارير المخزون</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.reports.inventory.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.reports.inventory.stock_levels')); ?>">
                <i class="bi bi-bar-chart me-2"></i> مستويات المخزون
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.reports.inventory.low_stock')); ?>">
                <i class="bi bi-exclamation-triangle me-2"></i> المخزون المنخفض
            </a>
        </div>
    </div>

    <!-- تقارير التصنيع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.manufacturing.reports.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-gear-wide"></i>
            <span>تقارير التصنيع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.manufacturing.reports.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.manufacturing.reports.production_summary')); ?>">
                <i class="bi bi-graph-up me-2"></i> ملخص الإنتاج
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.manufacturing.reports.material_consumption')); ?>">
                <i class="bi bi-box me-2"></i> استهلاك المواد
            </a>
        </div>
    </div>

    <!-- قسم نظام التذاكر -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نظام التذاكر</small>
    </div>

    <!-- إدارة التذاكر -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.ticketing.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-ticket-perforated-fill"></i>
            <span>إدارة التذاكر</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.tickets.index')); ?>">
                <i class="bi bi-ticket me-2"></i> التذاكر
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.ticket_categories.index')); ?>">
                <i class="bi bi-tags me-2"></i> التصنيفات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.ticket_priorities.index')); ?>">
                <i class="bi bi-exclamation-circle me-2"></i> الأولويات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.ticket_statuses.index')); ?>">
                <i class="bi bi-check-circle me-2"></i> الحالات
            </a>
        </div>
    </div>

    <!-- تقارير التذاكر -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.ticketing.reports.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-graph-up"></i>
            <span>تقارير التذاكر</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.reports.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.ticketing.reports.performance')); ?>">
                <i class="bi bi-speedometer me-2"></i> تقرير الأداء
            </a>
        </div>
    </div>

    <!-- قسم الإدارة والإعدادات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الإدارة والإعدادات</small>
    </div>

    <a href="<?php echo e(route('admin.users.index')); ?>" class="<?php echo e(request()->routeIs('admin.users.*') ? 'active' : ''); ?>">
        <i class="bi bi-people-fill"></i>
        <span>المستخدمين</span>
    </a>

    <a href="<?php echo e(route('admin.branches.index')); ?>" class="<?php echo e(request()->routeIs('admin.branches.*') ? 'active' : ''); ?>">
        <i class="bi bi-building-fill"></i>
        <span>الفروع</span>
    </a>

    <!-- نظام الصلاحيات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.permissions_system.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-shield-lock-fill"></i>
            <span>نظام الصلاحيات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.permissions_system.roles.index')); ?>">
                <i class="bi bi-person-badge me-2"></i> الأدوار
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.permissions_system.permissions.index')); ?>">
                <i class="bi bi-key me-2"></i> الصلاحيات
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.permissions_system.groups.index')); ?>">
                <i class="bi bi-folder me-2"></i> مجموعات الصلاحيات
            </a>
        </div>
    </div>

    <a href="<?php echo e(route('admin.settings.index')); ?>" class="<?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>">
        <i class="bi bi-gear-fill"></i>
        <span>إعدادات النظام</span>
    </a>

    <!-- قسم النظام -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">النظام</small>
    </div>

    <!-- النسخ الاحتياطية -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle <?php echo e(request()->routeIs('admin.system.backup.*') ? 'active' : ''); ?>" data-bs-toggle="dropdown">
            <i class="bi bi-cloud-arrow-up-fill"></i>
            <span>النسخ الاحتياطية</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="<?php echo e(route('admin.system.backup.index')); ?>">
                <i class="bi bi-list-ul me-2"></i> إدارة النسخ
            </a>
            <a class="dropdown-item" href="<?php echo e(route('admin.system.backup.settings')); ?>">
                <i class="bi bi-gear me-2"></i> إعدادات النسخ
            </a>
        </div>
    </div>

    <!-- سجلات النظام -->
    <a href="<?php echo e(route('admin.system.logs.index')); ?>" class="<?php echo e(request()->routeIs('admin.system.logs.*') ? 'active' : ''); ?>">
        <i class="bi bi-file-text-fill"></i>
        <span>سجلات النظام</span>
    </a>

    <!-- اختبار العملة -->
    <a href="<?php echo e(route('admin.currency.test')); ?>" class="<?php echo e(request()->routeIs('admin.currency.test') ? 'active' : ''); ?>">
        <i class="bi bi-currency-exchange"></i>
        <span>اختبار العملة</span>
    </a>

    <!-- تسجيل الخروج -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الجلسة</small>
    </div>

    <a href="<?php echo e(route('logout.confirm')); ?>" class="text-danger">
        <i class="bi bi-box-arrow-right"></i>
        <span>تسجيل الخروج</span>
    </a>
</div>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/partials/sidebar-menu.blade.php ENDPATH**/ ?>