@extends('layouts.admin')

@section('title', 'إدارة الرواتب')

@push('styles')
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة الرواتب</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.hr.payroll.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إنشاء كشف راتب
                        </a>
                        <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#generateMonthlyModal">
                            <i class="fas fa-calendar"></i> إنشاء رواتب شهرية
                        </button>
                        <a href="{{ route('admin.hr.payroll.summary') }}" class="btn btn-sm btn-info">
                            <i class="fas fa-chart-bar"></i> ملخص الرواتب
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في كشوف الرواتب...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="monthFilter">
                                <option value="">جميع الشهور</option>
                                <option value="2024-01">يناير 2024</option>
                                <option value="2024-02">فبراير 2024</option>
                                <option value="2024-03">مارس 2024</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="draft">مسودة</option>
                                <option value="approved">معتمد</option>
                                <option value="paid">مدفوع</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>الشهر</th>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الخصومات</th>
                                    <th>صافي الراتب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مارس 2024</td>
                                    <td>8,000 ريال</td>
                                    <td>1,200 ريال</td>
                                    <td>500 ريال</td>
                                    <td>8,700 ريال</td>
                                    <td><span class="badge badge-success">مدفوع</span></td>
                                    <td>2024-03-01</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.hr.payroll.show', 1) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.hr.payroll.export', 1) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <a href="{{ route('admin.hr.payroll.edit', 1) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>فاطمة أحمد سالم</td>
                                    <td>مارس 2024</td>
                                    <td>6,500 ريال</td>
                                    <td>800 ريال</td>
                                    <td>300 ريال</td>
                                    <td>7,000 ريال</td>
                                    <td><span class="badge badge-warning">معتمد</span></td>
                                    <td>2024-03-01</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.hr.payroll.show', 2) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.hr.payroll.export', 2) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <a href="{{ route('admin.hr.payroll.edit', 2) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإنشاء الرواتب الشهرية -->
<div class="modal fade" id="generateMonthlyModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء رواتب شهرية</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.hr.payroll.generate_monthly') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="month">الشهر</label>
                        <input type="month" class="form-control" id="month" name="month" required>
                    </div>
                    <div class="form-group">
                        <label for="department">القسم (اختياري)</label>
                        <select class="form-control" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            <option value="1">قسم المبيعات</option>
                            <option value="2">قسم المحاسبة</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء الرواتب</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة الرواتب
        console.log('تم تعطيل DataTables في صفحة الرواتب');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة الرواتب');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة الرواتب');
                return this;
            };
        }
        
        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });
</script>
@endpush
