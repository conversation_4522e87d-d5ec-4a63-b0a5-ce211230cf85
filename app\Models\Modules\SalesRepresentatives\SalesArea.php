<?php

namespace App\Models\Modules\SalesRepresentatives;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use App\Models\Customer;

class SalesArea extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'city',
        'district',
        'boundaries',
        'coordinates',
        'postal_code',
        'area_type',
        'priority_level',
        'travel_allowance',
        'estimated_visit_time',
        'special_instructions',
        'requires_approval',
        'is_active',
        'region_manager_id',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'priority_level' => 'integer',
        'travel_allowance' => 'decimal:2',
        'estimated_visit_time' => 'integer',
        'requires_approval' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the region manager for the area.
     */
    public function regionManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'region_manager_id');
    }

    /**
     * Get the branch that owns the area.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the area.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the sales representatives assigned to this area.
     */
    public function salesRepresentatives(): BelongsToMany
    {
        return $this->belongsToMany(SalesRepresentative::class, 'representative_area_assignments')
            ->withPivot(['assigned_date', 'effective_from', 'effective_to', 'assignment_type', 'commission_override', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get the active sales representatives assigned to this area.
     */
    public function activeSalesRepresentatives(): BelongsToMany
    {
        return $this->salesRepresentatives()
            ->wherePivot('is_active', true)
            ->wherePivot('effective_from', '<=', now())
            ->where(function($query) {
                $query->wherePivot('effective_to', '>=', now())
                      ->orWherePivot('effective_to', null);
            });
    }

    /**
     * Get the primary sales representative for this area.
     */
    public function primaryRepresentative()
    {
        return $this->activeSalesRepresentatives()
            ->wherePivot('assignment_type', 'primary')
            ->first();
    }

    /**
     * Get the sales routes in this area.
     */
    public function salesRoutes(): HasMany
    {
        return $this->hasMany(SalesRoute::class);
    }

    /**
     * Get the customers in this area.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'sales_area_id');
    }

    /**
     * Get the sales visits in this area.
     */
    public function salesVisits(): HasMany
    {
        return $this->hasMany(SalesVisit::class, 'sales_area_id');
    }

    /**
     * Calculate total sales for this area in a period.
     */
    public function calculateSales($startDate, $endDate): float
    {
        return $this->customers()
            ->join('orders', 'customers.id', '=', 'orders.customer_id')
            ->whereBetween('orders.order_date', [$startDate, $endDate])
            ->sum('orders.total_amount');
    }

    /**
     * Get area type color.
     */
    public function getAreaTypeColorAttribute(): string
    {
        return match($this->area_type) {
            'urban' => '#007bff',
            'suburban' => '#28a745',
            'rural' => '#ffc107',
            default => '#6c757d'
        };
    }

    /**
     * Get priority level badge.
     */
    public function getPriorityBadgeAttribute(): string
    {
        return match($this->priority_level) {
            1 => 'badge-danger',
            2 => 'badge-warning',
            3 => 'badge-info',
            default => 'badge-secondary'
        };
    }

    /**
     * Get priority level text.
     */
    public function getPriorityTextAttribute(): string
    {
        return match($this->priority_level) {
            1 => 'عالية',
            2 => 'متوسطة',
            3 => 'منخفضة',
            default => 'غير محدد'
        };
    }

    /**
     * Check if area requires approval for visits.
     */
    public function requiresApproval(): bool
    {
        return $this->requires_approval;
    }

    /**
     * Scope a query to only include active areas.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by priority and name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('priority_level')->orderBy('name');
    }

    /**
     * Scope a query to filter by city.
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope a query to filter by area type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('area_type', $type);
    }
}
