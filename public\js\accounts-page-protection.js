// حماية صفحة الحسابات من تطبيق DataTables التلقائي
(function() {
    'use strict';
    
    // تعطيل DataTables فوراً
    window.accountsPageDataTablesDisabled = true;
    
    // حفظ الدالة الأصلية
    var originalDataTable = null;
    var originalDataTableLower = null;
    
    // دالة لتعطيل DataTables
    function disableDataTables() {
        if (window.jQuery && window.jQuery.fn) {
            // حفظ الدوال الأصلية
            if (window.jQuery.fn.DataTable && !originalDataTable) {
                originalDataTable = window.jQuery.fn.DataTable;
            }
            if (window.jQuery.fn.dataTable && !originalDataTableLower) {
                originalDataTableLower = window.jQuery.fn.dataTable;
            }
            
            // استبدال DataTable
            if (window.jQuery.fn.DataTable) {
                window.jQuery.fn.DataTable = function(options) {
                    console.log('تم منع تطبيق DataTable على صفحة الحسابات');
                    return this;
                };
            }
            
            // استبدال dataTable
            if (window.jQuery.fn.dataTable) {
                window.jQuery.fn.dataTable = function(options) {
                    console.log('تم منع تطبيق dataTable على صفحة الحسابات');
                    return this;
                };
            }
        }
    }
    
    // دالة لإزالة classes
    function removeTableClasses() {
        if (window.jQuery) {
            window.jQuery('.modern-accounts-table, .accounts-table').removeClass('table').addClass('accounts-custom-table');
            
            // إزالة أي DataTables موجود
            window.jQuery('.modern-accounts-table, .accounts-table, .accounts-custom-table').each(function() {
                if (window.jQuery.fn.DataTable && window.jQuery.fn.DataTable.isDataTable(this)) {
                    window.jQuery(this).DataTable().destroy();
                }
            });
        }
    }
    
    // تطبيق الحماية فوراً
    disableDataTables();
    
    // تطبيق الحماية عند تحميل jQuery
    if (window.jQuery) {
        removeTableClasses();
        window.jQuery(document).ready(function() {
            removeTableClasses();
            disableDataTables();
        });
    } else {
        // انتظار تحميل jQuery
        var checkJQuery = setInterval(function() {
            if (window.jQuery) {
                clearInterval(checkJQuery);
                removeTableClasses();
                disableDataTables();
                window.jQuery(document).ready(function() {
                    removeTableClasses();
                    disableDataTables();
                });
            }
        }, 100);
    }
    
    // تطبيق الحماية عند تحميل DOM
    document.addEventListener('DOMContentLoaded', function() {
        removeTableClasses();
        disableDataTables();
    });
    
    // تطبيق الحماية عند تحميل النافذة
    window.addEventListener('load', function() {
        removeTableClasses();
        disableDataTables();
    });
    
})();
