<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'نظام المحاسبة')</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            background-color: #2c3e50;
            padding-top: 20px;
            overflow-y: auto;
            padding-bottom: 50px;
            box-shadow: -3px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .sidebar a {
            padding: 10px 15px;
            text-decoration: none;
            color: #ecf0f1;
            display: block;
            font-size: 0.95rem;
            transition: all 0.3s;
            border-radius: 5px;
            margin: 2px 8px;
        }
        .sidebar a:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }
        .sidebar a.active {
            color: #fff;
            background-color: #3498db;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        .sidebar h6 {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 15px;
            color: #3498db;
            font-weight: 600;
        }
        .sidebar .module-section {
            margin-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding-bottom: 10px;
        }
        .sidebar .logo-container {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar .logo-container h5 {
            font-weight: 600;
            letter-spacing: 1px;
        }
        .content {
            margin-right: 280px;
            padding: 20px;
        }
        
        /* أنماط إضافية للوحة التحكم */
        .page-title {
            font-weight: 600;
            color: #2c3e50;
            position: relative;
            padding-bottom: 10px;
        }
        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background-color: #3498db;
            border-radius: 3px;
        }
        
        .bg-gradient-blue {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
        }
        
        .bg-gradient-green {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            border: none;
        }
        
        .bg-gradient-purple {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            border: none;
        }
        
        .card-icon-lg {
            width: 60px;
            height: 60px;
            font-size: 1.8rem;
        }
        
        .dashboard-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: none;
        }
        
        .dashboard-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        
        .dashboard-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 15px 20px;
        }
        
        .dashboard-card .card-body {
            padding: 20px;
        }
        
        @yield('custom-styles')
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo-container">
            <h5 class="text-white">نظام المحاسبة</h5>
            <small class="text-white-50 d-block">إدارة الأعمال المالية</small>
        </div>
        <a href="{{ url('/admin/dashboard') }}" class="{{ request()->is('admin/dashboard') ? 'active' : '' }}">
            <i class="bi bi-speedometer2 me-2"></i> لوحة التحكم
        </a>

        <!-- الدفتر العام -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-journal-bookmark me-1"></i> الدفتر العام</h6>
            <a href="{{ route('admin.accounts.index') }}" class="{{ request()->routeIs('admin.accounts.*') ? 'active' : '' }}">
                <i class="bi bi-journal-text me-2"></i> الحسابات
            </a>
            <a href="{{ route('admin.account_types.index') }}" class="{{ request()->routeIs('admin.account_types.*') ? 'active' : '' }}">
                <i class="bi bi-tags me-2"></i> أنواع الحسابات
            </a>
            <a href="{{ route('admin.journal_entries.index') }}" class="{{ request()->routeIs('admin.journal_entries.*') ? 'active' : '' }}">
                <i class="bi bi-journal-plus me-2"></i> القيود المحاسبية
            </a>
            <a href="{{ route('admin.financial_reports.index') }}" class="{{ request()->routeIs('admin.financial_reports.*') ? 'active' : '' }}">
                <i class="bi bi-file-earmark-text me-2"></i> التقارير المالية
            </a>
        </div>

        <!-- المبيعات -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-cart-check me-1"></i> المبيعات</h6>
            <a href="{{ route('admin.sales.invoices.index') }}" class="{{ request()->routeIs('admin.sales.invoices.*') ? 'active' : '' }}">
                <i class="bi bi-receipt me-2"></i> الفواتير
            </a>
            <a href="{{ route('admin.sales.customers.index') }}" class="{{ request()->routeIs('admin.sales.customers.*') ? 'active' : '' }}">
                <i class="bi bi-people me-2"></i> العملاء
            </a>
            <a href="{{ route('admin.sales.quotations.index') }}" class="{{ request()->routeIs('admin.sales.quotations.*') ? 'active' : '' }}">
                <i class="bi bi-file-earmark-text me-2"></i> عروض الأسعار
            </a>
            <a href="{{ route('admin.sales.returns.index') }}" class="{{ request()->routeIs('admin.sales.returns.*') ? 'active' : '' }}">
                <i class="bi bi-arrow-return-left me-2"></i> المرتجعات
            </a>
            <a href="{{ route('admin.sales.reports.index') }}" class="{{ request()->routeIs('admin.sales.reports.*') ? 'active' : '' }}">
                <i class="bi bi-graph-up me-2"></i> تقارير المبيعات
            </a>
        </div>

        <!-- نقاط البيع -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-shop me-1"></i> نقاط البيع</h6>
            <a href="{{ route('admin.pos.index') }}" class="{{ request()->routeIs('admin.pos.*') ? 'active' : '' }}">
                <i class="bi bi-cash-register me-2"></i> نقطة البيع
            </a>
            <a href="{{ route('admin.pos_transactions.index') }}" class="{{ request()->routeIs('admin.pos_transactions.*') ? 'active' : '' }}">
                <i class="bi bi-receipt-cutoff me-2"></i> المعاملات
            </a>
        </div>

        <!-- التصنيع -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-tools me-1"></i> التصنيع</h6>
            <a href="{{ route('admin.manufacturing.items.index') }}" class="{{ request()->routeIs('admin.manufacturing.items.*') ? 'active' : '' }}">
                <i class="bi bi-box me-2"></i> المنتجات
            </a>
            <a href="{{ route('admin.manufacturing.boms.index') }}" class="{{ request()->routeIs('admin.manufacturing.boms.*') ? 'active' : '' }}">
                <i class="bi bi-list-check me-2"></i> قوائم المواد
            </a>
            <a href="{{ route('admin.manufacturing.work_orders.index') }}" class="{{ request()->routeIs('admin.manufacturing.work_orders.*') ? 'active' : '' }}">
                <i class="bi bi-gear me-2"></i> أوامر الإنتاج
            </a>
        </div>

        <!-- الفروع -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-diagram-3 me-1"></i> الفروع</h6>
            <a href="{{ route('admin.branches.index') }}" class="{{ request()->routeIs('admin.branches.*') ? 'active' : '' }}">
                <i class="bi bi-building me-2"></i> إدارة الفروع
            </a>
        </div>

        <!-- نظام التذاكر -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-ticket-perforated me-1"></i> نظام التذاكر</h6>
            <a href="{{ route('admin.ticketing.tickets.index') }}" class="{{ request()->routeIs('admin.ticketing.tickets.*') ? 'active' : '' }}">
                <i class="bi bi-ticket-detailed me-2"></i> التذاكر
            </a>
            <a href="{{ route('admin.ticketing.ticket_categories.index') }}" class="{{ request()->routeIs('admin.ticketing.ticket_categories.*') ? 'active' : '' }}">
                <i class="bi bi-tag me-2"></i> الفئات
            </a>
        </div>

        <!-- المستخدمين والصلاحيات -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-people-fill me-1"></i> المستخدمين والصلاحيات</h6>
            <a href="{{ route('admin.users.index') }}" class="{{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                <i class="bi bi-person me-2"></i> المستخدمين
            </a>
            <a href="{{ route('admin.roles.index') }}" class="{{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                <i class="bi bi-shield-lock me-2"></i> الأدوار والصلاحيات
            </a>
        </div>

        <!-- الاشتراكات -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-credit-card-2-front me-1"></i> الاشتراكات</h6>
            <a href="{{ route('admin.subscriptions.subscriptions.index') }}" class="{{ request()->routeIs('admin.subscriptions.subscriptions.*') ? 'active' : '' }}">
                <i class="bi bi-credit-card me-2"></i> إدارة الاشتراكات
            </a>
            <a href="{{ route('admin.subscriptions.payments.index') }}" class="{{ request()->routeIs('admin.subscriptions.payments.*') ? 'active' : '' }}">
                <i class="bi bi-clock-history me-2"></i> سجل المدفوعات
            </a>
        </div>

        <!-- تكامل واتساب -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-whatsapp me-1"></i> تكامل واتساب</h6>
            <a href="{{ route('admin.whatsapp_settings.index') }}" class="{{ request()->routeIs('admin.whatsapp_settings.*') ? 'active' : '' }}">
                <i class="bi bi-gear-wide-connected me-2"></i> إعدادات واتساب
            </a>
            <a href="{{ route('admin.whatsapp_messages.index') }}" class="{{ request()->routeIs('admin.whatsapp_messages.*') ? 'active' : '' }}">
                <i class="bi bi-chat-dots me-2"></i> الرسائل
            </a>
        </div>

        <!-- الإعدادات -->
        <div class="mt-3 mb-2 module-section">
            <h6 class="px-3 mb-2"><i class="bi bi-sliders me-1"></i> الإعدادات</h6>
            <a href="{{ route('admin.settings.index') }}" class="{{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                <i class="bi bi-gear-fill me-2"></i> إعدادات النظام
            </a>
            <a href="{{ route('admin.profile.index') }}" class="{{ request()->routeIs('admin.profile.*') ? 'active' : '' }}">
                <i class="bi bi-person-circle me-2"></i> الملف الشخصي
            </a>
        </div>
    </div>

    <div class="content">
        <div class="container">
            @yield('content')
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    @yield('scripts')
</body>
</html>
