<?php

namespace App\Http\Controllers\Modules\Manufacturing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Manufacturing\ManufacturingBom;
use App\Models\Modules\Manufacturing\ManufacturingItem;
use App\Models\Modules\Manufacturing\ManufacturingBomItem;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BomController extends Controller
{
    public function index()
    {
        $boms = ManufacturingBom::with(["item", "branch"])->latest()->paginate(10);
        return view("admin.manufacturing.boms.index", compact("boms"));
    }

    public function create()
    {
        // Items that can be produced (finished goods or semi-finished goods)
        $manufacturedItems = ManufacturingItem::where("is_manufactured", true)->orWhere("item_type", "finished_good")->orWhere("item_type", "semi_finished_good")->get();
        // Items that can be components (raw materials or semi-finished goods)
        $componentItems = ManufacturingItem::whereIn("item_type", ["raw_material", "semi_finished_good"])->get();
        $branches = Branch::all();
        return view("admin.manufacturing.boms.create", compact("manufacturedItems", "componentItems", "branches"));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255",
            "description" => "nullable|string",
            "item_id" => "required|exists:manufacturing_items,id",
            "version" => "required|string|max:50",
            "quantity_produced" => "required|numeric|min:0.0001",
            "is_active" => "nullable|boolean",
            "valid_from" => "nullable|date",
            "valid_to" => "nullable|date|after_or_equal:valid_from",
            "branch_id" => "nullable|exists:branches,id",
            "bom_items" => "required|array|min:1",
            "bom_items.*.item_id" => "required|exists:manufacturing_items,id",
            "bom_items.*.quantity" => "required|numeric|min:0.0001",
            "bom_items.*.unit_of_measure" => "required|string|max:50",
        ]);

        // Ensure unique combination of item_id, version, and branch_id if branch_id is present
        $uniqueRule = ["item_id" => $validatedData["item_id"], "version" => $validatedData["version"]];
        if (isset($validatedData["branch_id"])) {
            $uniqueRule["branch_id"] = $validatedData["branch_id"];
        } else {
            $uniqueRule["branch_id"] = null;
        }
        if (ManufacturingBom::where($uniqueRule)->exists()) {
            return back()->withInput()->withErrors(["version" => "This BOM version already exists for the selected item and branch."]);
        }

        DB::beginTransaction();
        try {
            $bom = ManufacturingBom::create([
                "name" => $validatedData["name"],
                "description" => $validatedData["description"],
                "item_id" => $validatedData["item_id"],
                "version" => $validatedData["version"],
                "quantity_produced" => $validatedData["quantity_produced"],
                "is_active" => $request->has("is_active"),
                "valid_from" => $validatedData["valid_from"],
                "valid_to" => $validatedData["valid_to"],
                "branch_id" => $validatedData["branch_id"],
                "created_by_id" => auth()->id(), // Assuming authenticated user
            ]);

            foreach ($validatedData["bom_items"] as $bomItemData) {
                $bom->bomItems()->create($bomItemData);
            }

            DB::commit();
            return redirect()->route("admin.manufacturing.boms.index")->with("success", "BOM created successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with("error", "Error creating BOM: " . $e->getMessage());
        }
    }

    public function show(ManufacturingBom $bom)
    {
        $bom->load(["item.unitOfMeasure", "branch", "bomItems.item.unitOfMeasure", "createdBy", "updatedBy"]);
        return view("admin.manufacturing.boms.show", compact("bom"));
    }

    public function edit(ManufacturingBom $bom)
    {
        $manufacturedItems = ManufacturingItem::where("is_manufactured", true)->orWhere("item_type", "finished_good")->orWhere("item_type", "semi_finished_good")->get();
        $componentItems = ManufacturingItem::whereIn("item_type", ["raw_material", "semi_finished_good"])->get();
        $branches = Branch::all();
        $bom->load("bomItems"); // Eager load bomItems for the form
        return view("admin.manufacturing.boms.edit", compact("bom", "manufacturedItems", "componentItems", "branches"));
    }

    public function update(Request $request, ManufacturingBom $bom)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255",
            "description" => "nullable|string",
            "item_id" => "required|exists:manufacturing_items,id",
            "version" => "required|string|max:50",
            "quantity_produced" => "required|numeric|min:0.0001",
            "is_active" => "nullable|boolean",
            "valid_from" => "nullable|date",
            "valid_to" => "nullable|date|after_or_equal:valid_from",
            "branch_id" => "nullable|exists:branches,id",
            "bom_items" => "required|array|min:1",
            "bom_items.*.id" => "nullable|exists:manufacturing_bom_items,id", // For existing items
            "bom_items.*.item_id" => "required|exists:manufacturing_items,id",
            "bom_items.*.quantity" => "required|numeric|min:0.0001",
            "bom_items.*.unit_of_measure" => "required|string|max:50",
        ]);

        $uniqueRule = ["item_id" => $validatedData["item_id"], "version" => $validatedData["version"]];
        if (isset($validatedData["branch_id"])) {
            $uniqueRule["branch_id"] = $validatedData["branch_id"];
        } else {
            $uniqueRule["branch_id"] = null;
        }
        if (ManufacturingBom::where($uniqueRule)->where("id", "!=", $bom->id)->exists()) {
            return back()->withInput()->withErrors(["version" => "This BOM version already exists for the selected item and branch."]);
        }

        DB::beginTransaction();
        try {
            $bom->update([
                "name" => $validatedData["name"],
                "description" => $validatedData["description"],
                "item_id" => $validatedData["item_id"],
                "version" => $validatedData["version"],
                "quantity_produced" => $validatedData["quantity_produced"],
                "is_active" => $request->has("is_active"),
                "valid_from" => $validatedData["valid_from"],
                "valid_to" => $validatedData["valid_to"],
                "branch_id" => $validatedData["branch_id"],
                "updated_by_id" => auth()->id(),
            ]);

            $existingItemIds = [];
            foreach ($validatedData["bom_items"] as $bomItemData) {
                if (isset($bomItemData["id"])) {
                    // Update existing item
                    $bomItem = ManufacturingBomItem::find($bomItemData["id"]);
                    if ($bomItem && $bomItem->bom_id == $bom->id) { // Ensure it belongs to this BOM
                        $bomItem->update($bomItemData);
                        $existingItemIds[] = $bomItem->id;
                    }
                } else {
                    // Create new item
                    $newItem = $bom->bomItems()->create($bomItemData);
                    $existingItemIds[] = $newItem->id;
                }
            }
            // Delete items not present in the request
            $bom->bomItems()->whereNotIn("id", $existingItemIds)->delete();

            DB::commit();
            return redirect()->route("admin.manufacturing.boms.index")->with("success", "BOM updated successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with("error", "Error updating BOM: " . $e->getMessage());
        }
    }

    public function destroy(ManufacturingBom $bom)
    {
        // Check if BOM is used in Work Orders
        if ($bom->workOrders()->exists()) {
            return redirect()->route("admin.manufacturing.boms.index")->with("error", "Cannot delete BOM. It is used in active Work Orders.");
        }

        DB::beginTransaction();
        try {
            $bom->bomItems()->delete(); // Delete all associated BOM items
            $bom->delete();
            DB::commit();
            return redirect()->route("admin.manufacturing.boms.index")->with("success", "BOM deleted successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.manufacturing.boms.index")->with("error", "Error deleting BOM: " . $e->getMessage());
        }
    }
}

