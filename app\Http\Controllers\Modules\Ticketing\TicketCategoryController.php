<?php

namespace App\Http\Controllers\Modules\Ticketing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Ticketing\TicketCategory;
use Illuminate\Http\Request;

class TicketCategoryController extends Controller
{
    public function index()
    {
        $categories = TicketCategory::latest()->paginate(15);
        return view("admin.ticketing.categories.index", compact("categories"));
    }

    public function create()
    {
        return view("admin.ticketing.categories.form");
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_categories,name",
            "description" => "nullable|string",
            "is_active" => "boolean",
        ]);
        $validatedData["is_active"] = $request->has("is_active");

        TicketCategory::create($validatedData);
        return redirect()->route("admin.ticketing.categories.index")->with("success", __("Ticket category created successfully."));
    }

    public function show(TicketCategory $category)
    {
        return view("admin.ticketing.categories.show", compact("category"));
    }

    public function edit(TicketCategory $category)
    {
        return view("admin.ticketing.categories.form", compact("category"));
    }

    public function update(Request $request, TicketCategory $category)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_categories,name," . $category->id,
            "description" => "nullable|string",
            "is_active" => "boolean",
        ]);
        $validatedData["is_active"] = $request->has("is_active");

        $category->update($validatedData);
        return redirect()->route("admin.ticketing.categories.index")->with("success", __("Ticket category updated successfully."));
    }

    public function destroy(TicketCategory $category)
    {
        if ($category->tickets()->count() > 0) {
            return redirect()->route("admin.ticketing.categories.index")->with("error", __("Cannot delete category with associated tickets."));
        }
        $category->delete();
        return redirect()->route("admin.ticketing.categories.index")->with("success", __("Ticket category deleted successfully."));
    }
}

