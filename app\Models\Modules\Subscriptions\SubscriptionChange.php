<?php

namespace App\Models\Modules\Subscriptions;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionChange extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'user_id',
        'change_type',
        'change_details',
        'old_plan_id',
        'new_plan_id',
        'effective_date',
        'price_difference',
    ];

    protected $casts = [
        'effective_date' => 'date',
        'price_difference' => 'decimal:2',
    ];

    /**
     * Get the subscription for this change.
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the user who made this change.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the old plan for this change.
     */
    public function oldPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'old_plan_id');
    }

    /**
     * Get the new plan for this change.
     */
    public function newPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'new_plan_id');
    }

    /**
     * Get the formatted price difference.
     */
    public function getFormattedPriceDifferenceAttribute()
    {
        $prefix = $this->price_difference >= 0 ? '+' : '';
        return $prefix . number_format($this->price_difference, 2) . ' ريال';
    }

    /**
     * Get the change type in Arabic.
     */
    public function getChangeTypeArabicAttribute()
    {
        return match($this->change_type) {
            'upgrade' => 'ترقية',
            'downgrade' => 'تخفيض',
            'renew' => 'تجديد',
            'cancel' => 'إلغاء',
            default => $this->change_type,
        };
    }
}
