@extends('layouts.admin')

@section('title', 'إضافة عملية تصنيع جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة عملية تصنيع جديدة</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.manufacturing.operations.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.manufacturing.operations.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_number">رقم العملية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="operation_number" name="operation_number" value="MFG-{{ date('Y-m-d') }}-{{ str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT) }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_name">اسم العملية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="operation_name" name="operation_name" required placeholder="أدخل اسم العملية">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_type">نوع العملية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="operation_type" name="operation_type" required>
                                        <option value="">اختر نوع العملية</option>
                                        <option value="assembly">تجميع</option>
                                        <option value="production">إنتاج</option>
                                        <option value="packaging">تعبئة وتغليف</option>
                                        <option value="quality_check">فحص جودة</option>
                                        <option value="maintenance">صيانة</option>
                                        <option value="testing">اختبار</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">المنتج <span class="text-danger">*</span></label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">اختر المنتج</option>
                                        <option value="1">لابتوب ديل XPS 13</option>
                                        <option value="2">ماوس لاسلكي لوجيتك</option>
                                        <option value="3">كيبورد ميكانيكي</option>
                                        <option value="4">شاشة سامسونج 24 بوصة</option>
                                        <option value="5">سماعات بلوتوث</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="required_quantity">الكمية المطلوبة <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="required_quantity" name="required_quantity" required min="1" placeholder="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="produced_quantity">الكمية المنتجة</label>
                                    <input type="number" class="form-control" id="produced_quantity" name="produced_quantity" value="0" min="0" placeholder="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="unit_cost">تكلفة الوحدة</label>
                                    <input type="number" class="form-control" id="unit_cost" name="unit_cost" step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">تاريخ الانتهاء المتوقع <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="responsible_person">المسؤول عن العملية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="responsible_person" name="responsible_person" required>
                                        <option value="">اختر المسؤول</option>
                                        <option value="1">أحمد محمد علي</option>
                                        <option value="2">فاطمة أحمد خالد</option>
                                        <option value="3">محمد علي حسن</option>
                                        <option value="4">سارة خالد محمد</option>
                                        <option value="5">عبدالله أحمد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">حالة العملية</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="planned">مخطط</option>
                                        <option value="in_progress">قيد التنفيذ</option>
                                        <option value="completed">مكتمل</option>
                                        <option value="on_hold">معلق</option>
                                        <option value="cancelled">ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">وصف العملية</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" placeholder="اكتب وصفاً تفصيلياً للعملية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- المواد المطلوبة -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>المواد المطلوبة للعملية</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="materialsTable">
                                        <thead>
                                            <tr>
                                                <th>المادة</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>الوحدة</th>
                                                <th>التكلفة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="materials[0][material_id]">
                                                        <option value="">اختر المادة</option>
                                                        <option value="1">مكونات إلكترونية</option>
                                                        <option value="2">بلاستيك</option>
                                                        <option value="3">معادن</option>
                                                        <option value="4">مواد تعبئة</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[0][quantity]" min="0" step="0.01">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="materials[0][unit]">
                                                        <option value="piece">قطعة</option>
                                                        <option value="kg">كيلوجرام</option>
                                                        <option value="meter">متر</option>
                                                        <option value="liter">لتر</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[0][cost]" step="0.01" min="0">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addMaterialRow()">
                                    <i class="fas fa-plus"></i> إضافة مادة
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_cost">التكلفة الإجمالية</label>
                                    <input type="number" class="form-control" id="total_cost" name="total_cost" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">الأولوية</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low">منخفضة</option>
                                        <option value="medium" selected>متوسطة</option>
                                        <option value="high">عالية</option>
                                        <option value="urgent">عاجلة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ العملية
                            </button>
                            <a href="{{ route('admin.manufacturing.operations.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let materialRowIndex = 1;

function addMaterialRow() {
    const tbody = document.querySelector('#materialsTable tbody');
    const newRow = `
        <tr>
            <td>
                <select class="form-control" name="materials[${materialRowIndex}][material_id]">
                    <option value="">اختر المادة</option>
                    <option value="1">مكونات إلكترونية</option>
                    <option value="2">بلاستيك</option>
                    <option value="3">معادن</option>
                    <option value="4">مواد تعبئة</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="materials[${materialRowIndex}][quantity]" min="0" step="0.01">
            </td>
            <td>
                <select class="form-control" name="materials[${materialRowIndex}][unit]">
                    <option value="piece">قطعة</option>
                    <option value="kg">كيلوجرام</option>
                    <option value="meter">متر</option>
                    <option value="liter">لتر</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="materials[${materialRowIndex}][cost]" step="0.01" min="0">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', newRow);
    materialRowIndex++;
}

function removeMaterialRow(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotalCost();
}

function calculateTotalCost() {
    let total = 0;
    const costInputs = document.querySelectorAll('input[name*="[cost]"]');
    const unitCost = parseFloat(document.getElementById('unit_cost').value) || 0;
    const requiredQuantity = parseFloat(document.getElementById('required_quantity').value) || 0;
    
    // حساب تكلفة المواد
    costInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    
    // إضافة تكلفة الإنتاج
    total += (unitCost * requiredQuantity);
    
    document.getElementById('total_cost').value = total.toFixed(2);
}

// حساب التكلفة عند تغيير القيم
document.addEventListener('input', function(e) {
    if (e.target.name && (e.target.name.includes('[cost]') || e.target.id === 'unit_cost' || e.target.id === 'required_quantity')) {
        calculateTotalCost();
    }
});

// التحقق من تاريخ الانتهاء
document.getElementById('start_date').addEventListener('change', function() {
    const startDate = new Date(this.value);
    const endDateInput = document.getElementById('end_date');
    const endDate = new Date(endDateInput.value);
    
    if (endDate <= startDate) {
        endDateInput.value = '';
        alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
    }
});

document.getElementById('end_date').addEventListener('change', function() {
    const endDate = new Date(this.value);
    const startDate = new Date(document.getElementById('start_date').value);
    
    if (endDate <= startDate) {
        this.value = '';
        alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
    }
});
</script>
@endpush
