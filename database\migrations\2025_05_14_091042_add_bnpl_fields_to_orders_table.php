<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('payment_method_detail')->nullable()->after('payment_method'); // Or choose a suitable position
            $table->foreignId('bnpl_transaction_id')->nullable()->after('id')->constrained('bnpl_transactions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            if (Schema::hasColumn('orders', 'bnpl_transaction_id')) {
                // Attempt to drop foreign key. Laravel default naming is tableName_columnName_foreign
                // However, dropConstrainedForeignId is more robust if the convention was followed.
                // $table->dropForeign(['bnpl_transaction_id']); // This might fail if the name is different
                // It's safer to use dropConstrainedForeignId if you used ->constrained() initially.
                // Or find the exact foreign key name from your DB schema if issues persist.
                $table->dropConstrainedForeignId('bnpl_transaction_id');
             //   $table->dropColumn('bnpl_transaction_id'); // dropConstrainedForeignId handles column drop too
            }
            if (Schema::hasColumn('orders', 'payment_method_detail')) {
                $table->dropColumn('payment_method_detail');
            }
        });
    }
};

