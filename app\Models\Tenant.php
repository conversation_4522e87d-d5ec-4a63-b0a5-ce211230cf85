<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Role;
use App\Models\Modules\Subscriptions\Subscription;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * Create a new Eloquent model instance.
     *
     * @param  array  $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // تطبيق نطاق المستأجرين فقط
        static::addGlobalScope('tenant_role', function ($query) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'tenant');
            });
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the owner of the tenant (which is the tenant itself).
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'id', 'id');
    }

    /**
     * Get the users that belong to this tenant.
     */
    public function users()
    {
        return $this->hasMany(User::class, 'tenant_id');
    }

    /**
     * Get the subscriptions for this tenant.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'tenant_id');
    }

    /**
     * Get the active subscription for this tenant.
     */
    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class, 'tenant_id')
            ->where('status', 'active')
            ->orderBy('end_date', 'desc');
    }

    /**
     * The roles that belong to the tenant.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, "role_user", "tenant_id", "role_id");
    }

    /**
     * Check if the tenant has a specific role.
     * @param string|array $role
     * @return bool
     */
    public function hasRole($role): bool
    {
        if (is_string($role)) {
            return $this->roles->contains("name", $role) || $this->roles->contains("slug", $role);
        }
        if (is_array($role)) {
            foreach ($role as $r) {
                if ($this->hasRole($r)) {
                    return true;
                }
            }
            return false;
        }
        return $this->roles->intersect(Role::whereIn("name", (array)$role)->orWhereIn("slug", (array)$role)->get())->isNotEmpty();
    }
}
