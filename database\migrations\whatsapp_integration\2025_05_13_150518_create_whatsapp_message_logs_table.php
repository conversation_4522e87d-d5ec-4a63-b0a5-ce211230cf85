<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("whatsapp_message_logs", function (Blueprint $table) {
            $table->id();
            $table->string("message_sid")->unique()->comment("Message SID from WhatsApp provider, e.g., Twilio");
            $table->foreignId("user_id")->nullable()->constrained("users")->onDelete("set null")->comment("Recipient user if applicable");
            $table->string("recipient_phone_number", 30);
            $table->foreignId("template_id")->nullable()->constrained("whatsapp_message_templates")->onDelete("set null");
            $table->text("message_content")->comment("Actual message content sent (after filling placeholders)");
            $table->string("message_type", 50)->comment("Message type: TEMPLATE, TEXT, MEDIA");
            $table->string("status", 50)->comment("Send status: QUEUED, SENT, FAILED, DELIVERED, READ, UNDELIVERED");
            $table->string("direction", 10)->default("outbound")->comment("Message direction: outbound, inbound");
            $table->string("error_code", 50)->nullable();
            $table->text("error_message")->nullable();
            $table->timestamp("sent_at")->nullable();
            $table->timestamp("delivered_at")->nullable();
            $table->timestamp("read_at")->nullable();
            $table->nullableMorphs("related_model"); // For polymorphic relation to Invoice, Ticket, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("whatsapp_message_logs");
    }
};
