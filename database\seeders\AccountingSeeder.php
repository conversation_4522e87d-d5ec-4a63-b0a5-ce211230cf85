<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Modules\GeneralLedger\AccountType;
use App\Models\Modules\GeneralLedger\Account;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AccountingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تفريغ الجداول قبل إضافة البيانات الجديدة
        // استخدام طريقة متوافقة مع SQLite
        Schema::disableForeignKeyConstraints();
        Account::truncate();
        AccountType::truncate();
        Schema::enableForeignKeyConstraints();

        // إنشاء أنواع الحسابات الرئيسية
        $assetType = AccountType::create([
            'name_ar' => 'الأصول',
            'name_en' => 'Assets',
            'slug' => 'assets',
            'description_ar' => 'الأصول والممتلكات',
            'description_en' => 'Assets and Properties',
            'is_primary' => true,
            'is_active' => true,
        ]);

        $liabilityType = AccountType::create([
            'name_ar' => 'الخصوم',
            'name_en' => 'Liabilities',
            'slug' => 'liabilities',
            'description_ar' => 'الالتزامات والديون',
            'description_en' => 'Liabilities and Debts',
            'is_primary' => true,
            'is_active' => true,
        ]);

        $equityType = AccountType::create([
            'name_ar' => 'حقوق الملكية',
            'name_en' => 'Equity',
            'slug' => 'equity',
            'description_ar' => 'حقوق الملكية ورأس المال',
            'description_en' => 'Equity and Capital',
            'is_primary' => true,
            'is_active' => true,
        ]);

        $revenueType = AccountType::create([
            'name_ar' => 'الإيرادات',
            'name_en' => 'Revenue',
            'slug' => 'revenue',
            'description_ar' => 'الإيرادات والدخل',
            'description_en' => 'Revenue and Income',
            'is_primary' => true,
            'is_active' => true,
        ]);

        $expenseType = AccountType::create([
            'name_ar' => 'المصروفات',
            'name_en' => 'Expenses',
            'slug' => 'expenses',
            'description_ar' => 'المصروفات والنفقات',
            'description_en' => 'Expenses and Costs',
            'is_primary' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الرئيسية للأصول
        $assets = Account::create([
            'name_ar' => 'الأصول',
            'name_en' => 'Assets',
            'code' => '1000',
            'account_type_id' => $assetType->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للأصول
        $currentAssets = Account::create([
            'name_ar' => 'الأصول المتداولة',
            'name_en' => 'Current Assets',
            'code' => '1100',
            'account_type_id' => $assetType->id,
            'parent_id' => $assets->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        $fixedAssets = Account::create([
            'name_ar' => 'الأصول الثابتة',
            'name_en' => 'Fixed Assets',
            'code' => '1200',
            'account_type_id' => $assetType->id,
            'parent_id' => $assets->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للأصول المتداولة
        Account::create([
            'name_ar' => 'النقد في الصندوق',
            'name_en' => 'Cash on Hand',
            'code' => '1110',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'البنك',
            'name_en' => 'Bank',
            'code' => '1120',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'المخزون',
            'name_en' => 'Inventory',
            'code' => '1130',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'العملاء',
            'name_en' => 'Accounts Receivable',
            'code' => '1140',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للأصول الثابتة
        Account::create([
            'name_ar' => 'الأراضي',
            'name_en' => 'Land',
            'code' => '1210',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'المباني',
            'name_en' => 'Buildings',
            'code' => '1220',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الأثاث والتجهيزات',
            'name_en' => 'Furniture and Fixtures',
            'code' => '1230',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'المعدات والآلات',
            'name_en' => 'Equipment and Machinery',
            'code' => '1240',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'السيارات',
            'name_en' => 'Vehicles',
            'code' => '1250',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الرئيسية للخصوم
        $liabilities = Account::create([
            'name_ar' => 'الخصوم',
            'name_en' => 'Liabilities',
            'code' => '2000',
            'account_type_id' => $liabilityType->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للخصوم
        $currentLiabilities = Account::create([
            'name_ar' => 'الخصوم المتداولة',
            'name_en' => 'Current Liabilities',
            'code' => '2100',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $liabilities->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        $longTermLiabilities = Account::create([
            'name_ar' => 'الخصوم طويلة الأجل',
            'name_en' => 'Long-term Liabilities',
            'code' => '2200',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $liabilities->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للخصوم المتداولة
        Account::create([
            'name_ar' => 'الموردون',
            'name_en' => 'Accounts Payable',
            'code' => '2110',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الرواتب المستحقة',
            'name_en' => 'Accrued Salaries',
            'code' => '2120',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'ضريبة القيمة المضافة',
            'name_en' => 'VAT',
            'code' => '2130',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للخصوم طويلة الأجل
        Account::create([
            'name_ar' => 'قروض طويلة الأجل',
            'name_en' => 'Long-term Loans',
            'code' => '2210',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $longTermLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الرئيسية لحقوق الملكية
        $equity = Account::create([
            'name_ar' => 'حقوق الملكية',
            'name_en' => 'Equity',
            'code' => '3000',
            'account_type_id' => $equityType->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية لحقوق الملكية
        Account::create([
            'name_ar' => 'رأس المال',
            'name_en' => 'Capital',
            'code' => '3100',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الأرباح المحتجزة',
            'name_en' => 'Retained Earnings',
            'code' => '3200',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الرئيسية للإيرادات
        $revenue = Account::create([
            'name_ar' => 'الإيرادات',
            'name_en' => 'Revenue',
            'code' => '4000',
            'account_type_id' => $revenueType->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للإيرادات
        Account::create([
            'name_ar' => 'إيرادات المبيعات',
            'name_en' => 'Sales Revenue',
            'code' => '4100',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'إيرادات الخدمات',
            'name_en' => 'Service Revenue',
            'code' => '4200',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'إيرادات أخرى',
            'name_en' => 'Other Revenue',
            'code' => '4300',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الرئيسية للمصروفات
        $expenses = Account::create([
            'name_ar' => 'المصروفات',
            'name_en' => 'Expenses',
            'code' => '5000',
            'account_type_id' => $expenseType->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        // إنشاء الحسابات الفرعية للمصروفات
        Account::create([
            'name_ar' => 'تكلفة البضاعة المباعة',
            'name_en' => 'Cost of Goods Sold',
            'code' => '5100',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الرواتب والأجور',
            'name_en' => 'Salaries and Wages',
            'code' => '5200',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الإيجار',
            'name_en' => 'Rent',
            'code' => '5300',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'المرافق',
            'name_en' => 'Utilities',
            'code' => '5400',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات التسويق والإعلان',
            'name_en' => 'Marketing and Advertising',
            'code' => '5500',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات أخرى',
            'name_en' => 'Other Expenses',
            'code' => '5600',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // إضافة حسابات إضافية للأنشطة التجارية المختلفة

        // حسابات إضافية للأصول المتداولة
        Account::create([
            'name_ar' => 'الاستثمارات قصيرة الأجل',
            'name_en' => 'Short-term Investments',
            'code' => '1150',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'المصروفات المدفوعة مقدماً',
            'name_en' => 'Prepaid Expenses',
            'code' => '1160',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'أوراق القبض',
            'name_en' => 'Notes Receivable',
            'code' => '1170',
            'account_type_id' => $assetType->id,
            'parent_id' => $currentAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للمخزون
        $inventory = Account::create([
            'name_ar' => 'حسابات المخزون',
            'name_en' => 'Inventory Accounts',
            'code' => '1300',
            'account_type_id' => $assetType->id,
            'parent_id' => $assets->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مخزون المواد الخام',
            'name_en' => 'Raw Materials Inventory',
            'code' => '1310',
            'account_type_id' => $assetType->id,
            'parent_id' => $inventory->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مخزون الإنتاج تحت التشغيل',
            'name_en' => 'Work in Progress Inventory',
            'code' => '1320',
            'account_type_id' => $assetType->id,
            'parent_id' => $inventory->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مخزون البضائع الجاهزة',
            'name_en' => 'Finished Goods Inventory',
            'code' => '1330',
            'account_type_id' => $assetType->id,
            'parent_id' => $inventory->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للأصول الثابتة
        Account::create([
            'name_ar' => 'أجهزة الكمبيوتر والإلكترونيات',
            'name_en' => 'Computers and Electronics',
            'code' => '1260',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'البرمجيات والتراخيص',
            'name_en' => 'Software and Licenses',
            'code' => '1270',
            'account_type_id' => $assetType->id,
            'parent_id' => $fixedAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // الأصول غير الملموسة
        $intangibleAssets = Account::create([
            'name_ar' => 'الأصول غير الملموسة',
            'name_en' => 'Intangible Assets',
            'code' => '1400',
            'account_type_id' => $assetType->id,
            'parent_id' => $assets->id,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الشهرة',
            'name_en' => 'Goodwill',
            'code' => '1410',
            'account_type_id' => $assetType->id,
            'parent_id' => $intangibleAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'براءات الاختراع',
            'name_en' => 'Patents',
            'code' => '1420',
            'account_type_id' => $assetType->id,
            'parent_id' => $intangibleAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'العلامات التجارية',
            'name_en' => 'Trademarks',
            'code' => '1430',
            'account_type_id' => $assetType->id,
            'parent_id' => $intangibleAssets->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للخصوم المتداولة
        Account::create([
            'name_ar' => 'أوراق الدفع',
            'name_en' => 'Notes Payable',
            'code' => '2140',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الإيرادات المقبوضة مقدماً',
            'name_en' => 'Unearned Revenue',
            'code' => '2150',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الضرائب المستحقة',
            'name_en' => 'Taxes Payable',
            'code' => '2160',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $currentLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للخصوم طويلة الأجل
        Account::create([
            'name_ar' => 'رهون عقارية',
            'name_en' => 'Mortgages Payable',
            'code' => '2220',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $longTermLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'سندات الدفع',
            'name_en' => 'Bonds Payable',
            'code' => '2230',
            'account_type_id' => $liabilityType->id,
            'parent_id' => $longTermLiabilities->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية لحقوق الملكية
        Account::create([
            'name_ar' => 'رأس المال المدفوع',
            'name_en' => 'Paid-in Capital',
            'code' => '3300',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الأسهم العادية',
            'name_en' => 'Common Stock',
            'code' => '3400',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'الأسهم الممتازة',
            'name_en' => 'Preferred Stock',
            'code' => '3500',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'توزيعات الأرباح',
            'name_en' => 'Dividends',
            'code' => '3600',
            'account_type_id' => $equityType->id,
            'parent_id' => $equity->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للإيرادات
        Account::create([
            'name_ar' => 'إيرادات الاستثمار',
            'name_en' => 'Investment Income',
            'code' => '4400',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'إيرادات الفوائد',
            'name_en' => 'Interest Income',
            'code' => '4500',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'إيرادات الإيجار',
            'name_en' => 'Rental Income',
            'code' => '4600',
            'account_type_id' => $revenueType->id,
            'parent_id' => $revenue->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات إضافية للمصروفات
        Account::create([
            'name_ar' => 'مصروفات الصيانة والإصلاح',
            'name_en' => 'Maintenance and Repairs',
            'code' => '5700',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات التأمين',
            'name_en' => 'Insurance Expense',
            'code' => '5800',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات السفر',
            'name_en' => 'Travel Expense',
            'code' => '5900',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات الاستهلاك',
            'name_en' => 'Depreciation Expense',
            'code' => '6000',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات الفوائد',
            'name_en' => 'Interest Expense',
            'code' => '6100',
            'account_type_id' => $expenseType->id,
            'parent_id' => $expenses->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات خاصة بالمطاعم
        $restaurantAccounts = Account::create([
            'name_ar' => 'حسابات المطاعم',
            'name_en' => 'Restaurant Accounts',
            'code' => '7000',
            'account_type_id' => $expenseType->id,
            'parent_id' => null,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مشتريات المواد الغذائية',
            'name_en' => 'Food Purchases',
            'code' => '7100',
            'account_type_id' => $expenseType->id,
            'parent_id' => $restaurantAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مشتريات المشروبات',
            'name_en' => 'Beverage Purchases',
            'code' => '7200',
            'account_type_id' => $expenseType->id,
            'parent_id' => $restaurantAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات التوصيل',
            'name_en' => 'Delivery Expenses',
            'code' => '7300',
            'account_type_id' => $expenseType->id,
            'parent_id' => $restaurantAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات خاصة بالتجارة الإلكترونية
        $ecommerceAccounts = Account::create([
            'name_ar' => 'حسابات التجارة الإلكترونية',
            'name_en' => 'E-commerce Accounts',
            'code' => '8000',
            'account_type_id' => $expenseType->id,
            'parent_id' => null,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات الشحن',
            'name_en' => 'Shipping Expenses',
            'code' => '8100',
            'account_type_id' => $expenseType->id,
            'parent_id' => $ecommerceAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'عمولات منصات البيع',
            'name_en' => 'Marketplace Commissions',
            'code' => '8200',
            'account_type_id' => $expenseType->id,
            'parent_id' => $ecommerceAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات التغليف',
            'name_en' => 'Packaging Expenses',
            'code' => '8300',
            'account_type_id' => $expenseType->id,
            'parent_id' => $ecommerceAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        // حسابات خاصة بالمقاولات
        $contractingAccounts = Account::create([
            'name_ar' => 'حسابات المقاولات',
            'name_en' => 'Contracting Accounts',
            'code' => '9000',
            'account_type_id' => $expenseType->id,
            'parent_id' => null,
            'is_control_account' => true,
            'accepts_entries' => false,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مواد البناء',
            'name_en' => 'Building Materials',
            'code' => '9100',
            'account_type_id' => $expenseType->id,
            'parent_id' => $contractingAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'أجور العمال',
            'name_en' => 'Labor Wages',
            'code' => '9200',
            'account_type_id' => $expenseType->id,
            'parent_id' => $contractingAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);

        Account::create([
            'name_ar' => 'مصروفات المعدات',
            'name_en' => 'Equipment Expenses',
            'code' => '9300',
            'account_type_id' => $expenseType->id,
            'parent_id' => $contractingAccounts->id,
            'is_control_account' => false,
            'accepts_entries' => true,
            'is_active' => true,
        ]);
    }
}
