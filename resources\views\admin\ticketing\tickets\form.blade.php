@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($ticket) ? __("Edit Ticket") : __("Create Ticket") }}</h1>
    <form action="{{ isset($ticket) ? route("admin.ticketing.tickets.update", $ticket->id) : route("admin.ticketing.tickets.store") }}" method="POST" enctype="multipart/form-data">
        @csrf
        @if(isset($ticket))
            @method("PUT")
        @endif

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">{{ __("Ticket Details") }}</div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="subject">{{ __("Subject") }}</label>
                            <input type="text" name="subject" id="subject" class="form-control @error("subject") is-invalid @enderror" value="{{ old("subject", $ticket->subject ?? "") }}" required>
                            @error("subject")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">{{ __("Description") }}</label>
                            <textarea name="description" id="description" class="form-control @error("description") is-invalid @enderror" rows="5" required>{{ old("description", $ticket->description ?? "") }}</textarea>
                            @error("description")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        @if(isset($ticket))
                            <div class="form-group">
                                <label for="reply_body">{{ __("Add Reply") }}</label>
                                <textarea name="reply_body" id="reply_body" class="form-control @error("reply_body") is-invalid @enderror" rows="3"></textarea>
                                @error("reply_body")
                                    <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                @enderror
                            </div>
                             <div class="form-group form-check">
                                <input type="checkbox" name="is_internal_note" id="is_internal_note" class="form-check-input" value="1" {{ old("is_internal_note") ? "checked" : "" }}>
                                <label class="form-check-label" for="is_internal_note">{{ __("Internal Note (visible to support team only)") }}</label>
                            </div>
                        @endif

                        <div class="form-group">
                            <label for="attachments">{{ __("Attachments") }}</label>
                            <input type="file" name="attachments[]" id="attachments" class="form-control-file @error("attachments.*") is-invalid @enderror" multiple>
                            @error("attachments.*")
                                <span class="invalid-feedback d-block" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        @if(isset($ticket) && $ticket->attachments->count() > 0)
                            <div class="mt-3">
                                <h5>{{ __("Current Attachments:") }}</h5>
                                <ul>
                                    @foreach($ticket->attachments as $attachment)
                                        <li><a href="{{ Storage::url($attachment->file_path) }}" target="_blank">{{ $attachment->file_name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                         @if(isset($ticket) && $ticket->replies->count() > 0)
                            <hr>
                            <h5>{{ __("Replies") }}</h5>
                            @foreach($ticket->replies()->latest()->get() as $reply)
                                <div class="card mb-2 {{ $reply->is_internal_note ? 'bg-light' : '' }}">
                                    <div class="card-body">
                                        <p class="card-text">{{ $reply->body }}</p>
                                        <small class="text-muted">
                                            {{ __("By:") }} {{ $reply->user->name }} {{ __("on") }} {{ $reply->created_at->format("Y-m-d H:i") }}
                                            @if($reply->is_internal_note)
                                                <span class="badge badge-warning">{{ __("Internal Note") }}</span>
                                            @endif
                                        </small>
                                        @if($reply->attachments->count() > 0)
                                            <br><small>{{ __("Attachments:") }}</small>
                                            <ul>
                                                @foreach($reply->attachments as $attachment)
                                                    <li><a href="{{ Storage::url($attachment->file_path) }}" target="_blank">{{ $attachment->file_name }}</a></li>
                                                @endforeach
                                            </ul>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">{{ __("Ticket Properties") }}</div>
                    <div class="card-body">
                        @if(!isset($ticket) || auth()->user()->isAdmin()) {{-- Allow user selection only on create or for admins --}}
                        <div class="form-group">
                            <label for="user_id">{{ __("User (Customer)") }}</label>
                            <select name="user_id" id="user_id" class="form-control @error("user_id") is-invalid @enderror" required>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old("user_id", $ticket->user_id ?? auth()->id()) == $user->id ? "selected" : "" }}>{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                            @error("user_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                        @else
                            <p><strong>{{ __("User (Customer):") }}</strong> {{ $ticket->user->name }}</p>
                            <input type="hidden" name="user_id" value="{{ $ticket->user_id }}">
                        @endif

                        <div class="form-group">
                            <label for="ticket_category_id">{{ __("Category") }}</label>
                            <select name="ticket_category_id" id="ticket_category_id" class="form-control @error("ticket_category_id") is-invalid @enderror" required>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old("ticket_category_id", $ticket->ticket_category_id ?? "") == $category->id ? "selected" : "" }}>{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error("ticket_category_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="ticket_priority_id">{{ __("Priority") }}</label>
                            <select name="ticket_priority_id" id="ticket_priority_id" class="form-control @error("ticket_priority_id") is-invalid @enderror" required>
                                @foreach($priorities as $priority)
                                    <option value="{{ $priority->id }}" {{ old("ticket_priority_id", $ticket->ticket_priority_id ?? "") == $priority->id ? "selected" : "" }}>{{ $priority->name }}</option>
                                @endforeach
                            </select>
                            @error("ticket_priority_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="ticket_status_id">{{ __("Status") }}</label>
                            <select name="ticket_status_id" id="ticket_status_id" class="form-control @error("ticket_status_id") is-invalid @enderror" required>
                                @foreach($statuses as $status)
                                    <option value="{{ $status->id }}" {{ old("ticket_status_id", $ticket->ticket_status_id ?? "") == $status->id ? "selected" : "" }}>{{ $status->name }}</option>
                                @endforeach
                            </select>
                            @error("ticket_status_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="assigned_to_user_id">{{ __("Assigned To (Support Agent)") }}</label>
                            <select name="assigned_to_user_id" id="assigned_to_user_id" class="form-control @error("assigned_to_user_id") is-invalid @enderror">
                                <option value="">{{ __("-- Unassigned --") }}</option>
                                @foreach($agents as $agent) {{-- Assuming $agents is passed to the view --}}
                                    <option value="{{ $agent->id }}" {{ old("assigned_to_user_id", $ticket->assigned_to_user_id ?? "") == $agent->id ? "selected" : "" }}>{{ $agent->name }}</option>
                                @endforeach
                            </select>
                            @error("assigned_to_user_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="branch_id">{{ __("Branch (Optional)") }}</label>
                            <select name="branch_id" id="branch_id" class="form-control @error("branch_id") is-invalid @enderror">
                                <option value="">{{ __("-- Select Branch --") }}</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" {{ old("branch_id", $ticket->branch_id ?? "") == $branch->id ? "selected" : "" }}>{{ $branch->name }}</option>
                                @endforeach
                            </select>
                            @error("branch_id")
                                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class=
