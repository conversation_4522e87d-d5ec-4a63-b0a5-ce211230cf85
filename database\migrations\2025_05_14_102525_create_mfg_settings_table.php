<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_settings", function (Blueprint $table) {
            $table->id();
            $table->string("setting_key")->unique();
            $table->text("setting_value")->nullable();
            $table->string("setting_group")->nullable();
            $table->string("type")->default("string"); // e.g., string, integer, boolean, json
            $table->text("description")->nullable();
            $table->boolean("is_enabled")->default(true);
            $table->unsignedBigInteger("created_by")->nullable();
            $table->foreign("created_by")->references("id")->on("users")->onDelete("set null");
            $table->unsignedBigInteger("updated_by")->nullable();
            $table->foreign("updated_by")->references("id")->on("users")->onDelete("set null");
            $table->timestamps();
        });

        // Seed with some default settings
        // This can be moved to a seeder class later if preferred
        $defaultSettings = [
            [
                "setting_key" => "default_wip_account_id",
                "setting_value" => null, // To be set by user
                "setting_group" => "accounting",
                "type" => "integer",
                "description" => "Default Work-In-Progress (WIP) Account ID from chart_of_accounts."
            ],
            [
                "setting_key" => "default_finished_goods_account_id",
                "setting_value" => null, // To be set by user
                "setting_group" => "accounting",
                "type" => "integer",
                "description" => "Default Finished Goods Account ID from chart_of_accounts."
            ],
            [
                "setting_key" => "default_scrap_account_id",
                "setting_value" => null, // To be set by user
                "setting_group" => "accounting",
                "type" => "integer",
                "description" => "Default Scrap Account ID from chart_of_accounts."
            ],
            [
                "setting_key" => "allow_overproduction",
                "setting_value" => "false",
                "setting_group" => "work_order",
                "type" => "boolean",
                "description" => "Allow work orders to report more quantity produced than scheduled."
            ],
            [
                "setting_key" => "auto_issue_components",
                "setting_value" => "true",
                "setting_group" => "work_order",
                "type" => "boolean",
                "description" => "Automatically issue components when a work order operation is started or completed (backflush)."
            ],
             [
                "setting_key" => "work_order_prefix",
                "setting_value" => "WO-",
                "setting_group" => "general",
                "type" => "string",
                "description" => "Prefix for automatically generated Work Order numbers."
            ]
        ];

        foreach ($defaultSettings as $setting) {
            DB::table("mfg_settings")->insert([
                "setting_key" => $setting["setting_key"],
                "setting_value" => $setting["setting_value"],
                "setting_group" => $setting["setting_group"],
                "type" => $setting["type"],
                "description" => $setting["description"],
                "created_at" => now(),
                "updated_at" => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_settings");
    }
};

