<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kitchen_printers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الطابعة
            $table->string('ip_address'); // عنوان IP
            $table->integer('port')->default(9100); // منفذ الطابعة
            $table->enum('type', ['thermal', 'impact', 'laser'])->default('thermal'); // نوع الطابعة
            $table->enum('connection_type', ['network', 'usb', 'serial'])->default('network'); // نوع الاتصال
            $table->string('driver')->default('ESC/POS'); // تعريف الطابعة
            $table->integer('paper_width')->default(80); // عرض الورق بالمم
            $table->boolean('auto_cut')->default(true); // قطع تلقائي
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable(); // إعدادات إضافية
            $table->foreignId('kitchen_station_id')->constrained('kitchen_stations')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kitchen_printers');
    }
};
