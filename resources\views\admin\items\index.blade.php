@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __('admin_views.items_list') }}</h1>
    <a href="{{ route('admin.items.create') }}" class="btn btn-primary mb-3">{{ __('admin_views.add_new_item') }}</a>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>{{ __('admin_views.name_ar') }}</th>
                <th>{{ __('admin_views.name_en') }}</th>
                <th>{{ __('admin_views.code') }}</th>
                <th>{{ __('admin_views.type') }}</th>
                <th>{{ __('admin_views.branch') }}</th>
                <th>{{ __('admin_views.actions') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($items as $item)
                <tr>
                    <td>{{ $item->id }}</td>
                    <td>{{ $item->name_ar }}</td>
                    <td>{{ $item->name_en }}</td>
                    <td>{{ $item->code }}</td>
                    <td>{{ $item->type }}</td>
                    <td>{{ $item->branch ? $item->branch->name_ar : '-' }}</td>
                    <td>
                        <a href="{{ route('admin.items.show', $item->id) }}" class="btn btn-info btn-sm">{{ __('admin_views.view') }}</a>
                        <a href="{{ route('admin.items.edit', $item->id) }}" class="btn btn-warning btn-sm">{{ __('admin_views.edit') }}</a>
                        <form action="{{ route('admin.items.destroy', $item->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('{{ __('admin_views.confirm_delete') }}')">{{ __('admin_views.delete') }}</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center">{{ __('admin_views.no_items_found') }}</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    {{ $items->links() }} 
</div>
@endsection

