@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>ZATCA E-Invoicing Logs & Details</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <div class="mb-3">
        <a href="{{ route("admin.integrations.zatca.index") }}" class="btn btn-sm btn-primary">Back to ZATCA Overview</a>
        <a href="{{ route("admin.integrations.zatca.edit") }}" class="btn btn-sm btn-warning">Edit Configuration</a>
        {{-- <button class="btn btn-sm btn-info">Test ZATCA Connection</button> --}}
    </div>

    <div class="card mb-3">
        <div class="card-header">Current Configuration Summary</div>
        <div class="card-body">
            <p><strong>Phase:</strong> {{-- $settings->zatca_phase ?? "Phase 2: Integration" --}} Phase 2: Integration</p>
            <p><strong>Environment:</strong> {{-- ucfirst($settings->zatca_environment ?? "simulation") --}} Simulation</p>
            <p><strong>Onboarding Status:</strong> {{-- ucfirst($settings->zatca_onboarding_status ?? "onboarded") --}} Onboarded</p>
            <p><strong>Production CSID:</strong> {{-- $settings->csid_production ? "Set" : "Not Set" --}} Set</p>
            <p><strong>Simulation CSID:</strong> {{-- $settings->csid_simulation ? "Set" : "Not Set" --}} Set</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Invoice Submission Logs (Last 100 Entries)</div>
        <div class="card-body">
            <form method="GET" action="{{ route("admin.integrations.zatca.show") }}" class="mb-3">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" name="invoice_number" class="form-control form-control-sm" placeholder="Invoice Number" value="{{ request("invoice_number") }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">All Statuses</option>
                            <option value="cleared" {{ request("status") == "cleared" ? "selected" : "" }}>Cleared</option>
                            <option value="reported" {{ request("status") == "reported" ? "selected" : "" }}>Reported</option>
                            <option value="rejected" {{ request("status") == "rejected" ? "selected" : "" }}>Rejected</option>
                            <option value="pending" {{ request("status") == "pending" ? "selected" : "" }}>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="submission_date" class="form-control form-control-sm" value="{{ request("submission_date") }}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-info btn-sm">Filter Logs</button>
                        <a href="{{ route("admin.integrations.zatca.show") }}" class="btn btn-secondary btn-sm">Clear Filters</a>
                    </div>
                </div>
            </form>

            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Invoice No.</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>ZATCA Status</th>
                        <th>Clearance/Reporting ID</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- @forelse($logs as $log) --}}
                    {{-- Replace with actual log data --}}
                    <tr>
                        <td>2023-05-13 16:00:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->invoice->sale_id) --}}">INV-2023-005</a></td>
                        <td>Standard Tax Invoice</td>
                        <td>575.00 SAR</td>
                        <td><span class="badge bg-success">Cleared</span></td>
                        <td>ZATCA-CLR-001</td>
                        <td><a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 15:30:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->invoice->sale_id) --}}">SMI-2023-010</a></td>
                        <td>Simplified Tax Invoice</td>
                        <td>80.00 SAR</td>
                        <td><span class="badge bg-success">Reported</span></td>
                        <td>N/A</td>
                        <td><a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-13 14:00:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->invoice->sale_id) --}}">INV-2023-004</a></td>
                        <td>Standard Tax Invoice</td>
                        <td>1200.00 SAR</td>
                        <td><span class="badge bg-danger">Rejected</span></td>
                        <td>N/A</td>
                        <td><a href="#" class="btn btn-xs btn-outline-danger">View Errors</a> <a href="#" class="btn btn-xs btn-outline-info">View XML</a></td>
                    </tr>
                    {{-- @empty
                    <tr>
                        <td colspan="7" class="text-center">No ZATCA submission logs found matching your criteria.</td>
                    </tr>
                    @endforelse --}}
                </tbody>
            </table>
            {{-- Pagination for logs --}}
            {{-- {{ $logs->links() }} --}}
        </div>
    </div>

</div>
@endsection

