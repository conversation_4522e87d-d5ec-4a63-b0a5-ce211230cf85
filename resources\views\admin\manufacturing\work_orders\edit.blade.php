@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Edit Work Order: {{-- $workOrder->work_order_number --}}</h1>
        <form action="{{-- route("admin.manufacturing.work_orders.update", $workOrder->id) --}}" method="POST">
            @csrf
            @method("PUT")
            <div class="form-group">
                <label for="work_order_number">Work Order Number</label>
                <input type="text" name="work_order_number" id="work_order_number" class="form-control" value="{{-- $workOrder->work_order_number --}}" required>
            </div>
            <div class="form-group">
                <label for="item_id">Item to Produce</label>
                <select name="item_id" id="item_id" class="form-control" required>
                    {{-- @foreach($manufacturedItems as $item) --}}
                    {{-- <option value="{{ $item->id }}" {{ $workOrder->item_id == $item->id ? "selected" : "" }}>{{ $item->name }} ({{ $item->item_code }})</option> --}}
                    {{-- @endforeach --}}
                     <option value="">Select Item to Produce</option>
                </select>
            </div>
            <div class="form-group">
                <label for="bom_id">Bill of Materials (BOM)</label>
                <select name="bom_id" id="bom_id" class="form-control">
                    {{-- @foreach($boms as $bom) --}}
                    {{-- <option value="{{ $bom->id }}" {{ $workOrder->bom_id == $bom->id ? "selected" : "" }}>{{ $bom->name }} - v{{$bom->version}} (for {{ $bom->item->name }})</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select BOM (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="quantity_to_produce">Quantity to Produce</label>
                <input type="number" step="0.0001" name="quantity_to_produce" id="quantity_to_produce" class="form-control" value="{{-- $workOrder->quantity_to_produce --}}" required>
            </div>
             <div class="form-group">
                <label for="quantity_produced">Quantity Produced</label>
                <input type="number" step="0.0001" name="quantity_produced" id="quantity_produced" class="form-control" value="{{-- $workOrder->quantity_produced ?? 0.0000 --}}">
            </div>
            <div class="form-group">
                <label for="quantity_scrapped">Quantity Scrapped</label>
                <input type="number" step="0.0001" name="quantity_scrapped" id="quantity_scrapped" class="form-control" value="{{-- $workOrder->quantity_scrapped ?? 0.0000 --}}">
            </div>
            <div class="form-group">
                <label for="status">Status</label>
                <select name="status" id="status" class="form-control">
                    <option value="pending" {{-- $workOrder->status == "pending" ? "selected" : "" --}}>Pending</option>
                    <option value="in_progress" {{-- $workOrder->status == "in_progress" ? "selected" : "" --}}>In Progress</option>
                    <option value="completed" {{-- $workOrder->status == "completed" ? "selected" : "" --}}>Completed</option>
                    <option value="cancelled" {{-- $workOrder->status == "cancelled" ? "selected" : "" --}}>Cancelled</option>
                    <option value="on_hold" {{-- $workOrder->status == "on_hold" ? "selected" : "" --}}>On Hold</option>
                </select>
            </div>
            <div class="form-group">
                <label for="planned_start_date">Planned Start Date</label>
                <input type="datetime-local" name="planned_start_date" id="planned_start_date" class="form-control" value="{{-- $workOrder->planned_start_date ? \Carbon\Carbon::parse($workOrder->planned_start_date)->format("Y-m-d\TH:i") : "" --}}">
            </div>
            <div class="form-group">
                <label for="planned_end_date">Planned End Date</label>
                <input type="datetime-local" name="planned_end_date" id="planned_end_date" class="form-control" value="{{-- $workOrder->planned_end_date ? \Carbon\Carbon::parse($workOrder->planned_end_date)->format("Y-m-d\TH:i") : "" --}}">
            </div>
            <div class="form-group">
                <label for="actual_start_date">Actual Start Date</label>
                <input type="datetime-local" name="actual_start_date" id="actual_start_date" class="form-control" value="{{-- $workOrder->actual_start_date ? \Carbon\Carbon::parse($workOrder->actual_start_date)->format("Y-m-d\TH:i") : "" --}}">
            </div>
            <div class="form-group">
                <label for="actual_end_date">Actual End Date</label>
                <input type="datetime-local" name="actual_end_date" id="actual_end_date" class="form-control" value="{{-- $workOrder->actual_end_date ? \Carbon\Carbon::parse($workOrder->actual_end_date)->format("Y-m-d\TH:i") : "" --}}">
            </div>
            <div class="form-group">
                <label for="branch_id">Branch</label>
                <select name="branch_id" id="branch_id" class="form-control">
                    {{-- @foreach($branches as $branch) --}}
                    {{-- <option value="{{ $branch->id }}" {{ $workOrder->branch_id == $branch->id ? "selected" : "" }}>{{ $branch->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select Branch (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="assigned_to_id">Assigned To</label>
                <select name="assigned_to_id" id="assigned_to_id" class="form-control">
                    {{-- @foreach($users as $user) --}}
                    {{-- <option value="{{ $user->id }}" {{ $workOrder->assigned_to_id == $user->id ? "selected" : "" }}>{{ $user->name }}</option> --}}
                    {{-- @endforeach --}}
                    <option value="">Select User (Optional)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea name="notes" id="notes" class="form-control">{{-- $workOrder->notes --}}</textarea>
            </div>

            {{-- Section for Work Order Items (Consumed / Produced) - More complex, typically handled with dynamic rows or separate management --}}
            {{-- For simplicity, this example doesn\'t include direct editing of consumed/produced items here --}}

            <button type="submit" class="btn btn-success mt-3">Update Work Order</button>
        </form>
    </div>
@endsection
