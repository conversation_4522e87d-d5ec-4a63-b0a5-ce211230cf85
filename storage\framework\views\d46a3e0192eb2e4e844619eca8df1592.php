<?php $__env->startSection('title', 'سجلات النظام'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">سجلات النظام</h3>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- سجلات التطبيق -->
                        <div class="col-md-3 mb-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-file-text-fill text-primary" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">سجلات التطبيق</h5>
                                    <p class="card-text">سجلات عمليات التطبيق العامة</p>
                                    <a href="<?php echo e(route('admin.system.logs.application')); ?>" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> عرض السجلات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- سجلات الأخطاء -->
                        <div class="col-md-3 mb-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">سجلات الأخطاء</h5>
                                    <p class="card-text">سجلات الأخطاء والاستثناءات</p>
                                    <a href="<?php echo e(route('admin.system.logs.errors')); ?>" class="btn btn-danger">
                                        <i class="fas fa-eye"></i> عرض الأخطاء
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- سجلات الوصول -->
                        <div class="col-md-3 mb-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-check-fill text-success" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">سجلات الوصول</h5>
                                    <p class="card-text">سجلات دخول المستخدمين</p>
                                    <a href="<?php echo e(route('admin.system.logs.access')); ?>" class="btn btn-success">
                                        <i class="fas fa-eye"></i> عرض الوصول
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- سجلات المراجعة -->
                        <div class="col-md-3 mb-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-shield-check text-warning" style="font-size: 3rem;"></i>
                                    <h5 class="card-title mt-3">سجلات المراجعة</h5>
                                    <p class="card-text">سجلات العمليات الحساسة</p>
                                    <a href="<?php echo e(route('admin.system.logs.audit')); ?>" class="btn btn-warning">
                                        <i class="fas fa-eye"></i> عرض المراجعة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-file-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">سجلات اليوم</span>
                                    <span class="info-box-number">1,247</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">أخطاء اليوم</span>
                                    <span class="info-box-number">3</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">تسجيلات دخول</span>
                                    <span class="info-box-number">45</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-hdd"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">حجم السجلات</span>
                                    <span class="info-box-number">125 MB</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات إدارة السجلات -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>أدوات إدارة السجلات</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5>البحث في السجلات</h5>
                                            <form action="<?php echo e(route('admin.system.logs.search')); ?>" method="POST">
                                                <?php echo csrf_field(); ?>
                                                <div class="form-group">
                                                    <input type="text" class="form-control" name="search_term" placeholder="ابحث في السجلات...">
                                                </div>
                                                <div class="form-group">
                                                    <select class="form-control" name="log_type">
                                                        <option value="">جميع أنواع السجلات</option>
                                                        <option value="application">سجلات التطبيق</option>
                                                        <option value="errors">سجلات الأخطاء</option>
                                                        <option value="access">سجلات الوصول</option>
                                                        <option value="audit">سجلات المراجعة</option>
                                                    </select>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-search"></i> بحث
                                                </button>
                                            </form>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>إدارة السجلات</h5>
                                            <div class="btn-group-vertical w-100">
                                                <button type="button" class="btn btn-warning mb-2" onclick="clearLogs('application')">
                                                    <i class="fas fa-trash"></i> مسح سجلات التطبيق
                                                </button>
                                                <button type="button" class="btn btn-danger mb-2" onclick="clearLogs('errors')">
                                                    <i class="fas fa-trash"></i> مسح سجلات الأخطاء
                                                </button>
                                                <button type="button" class="btn btn-info mb-2" onclick="downloadLogs('all')">
                                                    <i class="fas fa-download"></i> تحميل جميع السجلات
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function clearLogs(type) {
        if (confirm('هل أنت متأكد من مسح هذه السجلات؟')) {
            fetch(`<?php echo e(route("admin.system.logs.clear", "")); ?>/${type}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم مسح السجلات بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء مسح السجلات');
                }
            });
        }
    }

    function downloadLogs(type) {
        window.location.href = `<?php echo e(route("admin.system.logs.download", "")); ?>/${type}`;
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/system/logs/index.blade.php ENDPATH**/ ?>