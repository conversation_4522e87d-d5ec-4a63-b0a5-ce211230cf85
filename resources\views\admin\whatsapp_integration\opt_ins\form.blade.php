@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($optIn) ? "Edit" : "Add New" }} WhatsApp Opt-In Status</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <strong>Whoops!</strong> There were some problems with your input.<br><br>
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($optIn) ? route(\'admin.whatsapp_integration.opt_ins.update\', $optIn->id) : route(\'admin.whatsapp_integration.opt_ins.store\') }}" method="POST">
        @csrf
        @if(isset($optIn))
            @method(\'PUT\')
        @endif

        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>User:</strong>
                    <select name="user_id" class="form-control" {{ isset($optIn) ? 'disabled' : '' }}>
                        @foreach(App\Models\User::all() as $user)
                            <option value="{{ $user->id }}" {{ (isset($optIn) && $optIn->user_id == $user->id) || old(\'user_id\') == $user->id ? \'selected\' : \'\'}}>
                                {{ $user->name }} ({{ $user->email }})
                            </option>
                        @endforeach
                    </select>
                     @if(isset($optIn))
                        <input type="hidden" name="user_id" value="{{ $optIn->user_id }}">
                    @endif
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Phone Number:</strong>
                    <input type="text" name="phone_number" value="{{ old(\'phone_number\', $optIn->phone_number ?? \'\') }}" class="form-control" placeholder="User phone number with country code" {{ isset($optIn) ? 'readonly' : '' }}>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Opt-In Status:</strong>
                    <select name="opt_in_status" class="form-control">
                        <option value="1" {{ (isset($optIn) && $optIn->opt_in_status) || old(\'opt_in_status\') == \'1\' ? \'selected\' : \'\'}}>Opted-In</option>
                        <option value="0" {{ (isset($optIn) && !$optIn->opt_in_status) || old(\'opt_in_status\') == \'0\' ? \'selected\' : \'\'}}>Opted-Out</option>
                    </select>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12">
                <div class="form-group">
                    <strong>Opt-In Source (Informational):</strong>
                    <input type="text" name="opt_in_source" value="{{ old(\'opt_in_source\', $optIn->opt_in_source ?? \'manual_admin_update\') }}" class="form-control" placeholder="e.g., manual_admin_update" {{ isset($optIn) ? 'readonly' : '' }}>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-12 text-center">
                <button type="submit" class="btn btn-primary">Submit</button>
                <a class="btn btn-secondary" href="{{ route(\'admin.whatsapp_integration.opt_ins.index\') }}"> Back</a>
            </div>
        </div>
    </form>
</div>
@endsection

