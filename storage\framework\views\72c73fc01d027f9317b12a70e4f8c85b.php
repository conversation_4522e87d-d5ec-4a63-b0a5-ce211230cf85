<?php $__env->startSection('title', 'تقارير المناديب'); ?>

<?php $__env->startSection('header'); ?>
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">
            <i class="bi bi-graph-up-arrow text-primary me-2"></i>
            تقارير المناديب
        </h2>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download me-1"></i>
                تصدير التقارير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-2"></i>CSV</a></li>
            </ul>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي المناديب</h6>
                            <h3 class="mb-0"><?php echo e(number_format($stats['total_representatives'])); ?></h3>
                            <small><?php echo e($stats['active_representatives']); ?> نشط</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people-fill fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">زيارات هذا الشهر</h6>
                            <h3 class="mb-0"><?php echo e(number_format($stats['monthly_visits'])); ?></h3>
                            <small><?php echo e($stats['completed_visits']); ?> مكتملة</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-calendar-check-fill fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">عمولات الشهر</h6>
                            <h3 class="mb-0"><?php echo e(number_format($stats['monthly_commissions'], 2)); ?></h3>
                            <small>ريال سعودي</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-cash-coin fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">مصروفات معلقة</h6>
                            <h3 class="mb-0"><?php echo e(number_format($stats['pending_expenses'], 2)); ?></h3>
                            <small>ريال سعودي</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-receipt-cutoff fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- أفضل المناديب -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-trophy-fill text-warning me-2"></i>
                        أفضل المناديب هذا الشهر
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($topRepresentatives->count() > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $topRepresentatives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $representative): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary rounded-pill me-3"><?php echo e($index + 1); ?></span>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($representative->name); ?></h6>
                                            <small class="text-muted"><?php echo e($representative->employee_code); ?></small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success"><?php echo e(number_format($representative->monthly_commission ?? 0, 2)); ?> ريال</strong>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-person-x display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات متاحة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات الزيارات اليومية -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart-line-fill text-info me-2"></i>
                        الزيارات اليومية (آخر 7 أيام)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyVisitsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط التقارير التفصيلية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-file-earmark-bar-graph-fill text-primary me-2"></i>
                        التقارير التفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.representatives_performance')); ?>" 
                               class="card text-decoration-none h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-check-fill text-primary fs-1 mb-3"></i>
                                    <h5 class="card-title">أداء المناديب</h5>
                                    <p class="card-text text-muted">تقرير مفصل عن أداء كل مندوب</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.areas_performance')); ?>" 
                               class="card text-decoration-none h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-geo-alt-fill text-success fs-1 mb-3"></i>
                                    <h5 class="card-title">أداء المناطق</h5>
                                    <p class="card-text text-muted">تحليل أداء المناطق الجغرافية</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.routes_efficiency')); ?>" 
                               class="card text-decoration-none h-100 border-info">
                                <div class="card-body text-center">
                                    <i class="bi bi-signpost-fill text-info fs-1 mb-3"></i>
                                    <h5 class="card-title">كفاءة خطوط السير</h5>
                                    <p class="card-text text-muted">تحليل كفاءة خطوط السير</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.visits_analysis')); ?>" 
                               class="card text-decoration-none h-100 border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-calendar-check text-warning fs-1 mb-3"></i>
                                    <h5 class="card-title">تحليل الزيارات</h5>
                                    <p class="card-text text-muted">إحصائيات مفصلة عن الزيارات</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.commissions_summary')); ?>" 
                               class="card text-decoration-none h-100 border-danger">
                                <div class="card-body text-center">
                                    <i class="bi bi-cash-stack text-danger fs-1 mb-3"></i>
                                    <h5 class="card-title">ملخص العمولات</h5>
                                    <p class="card-text text-muted">تقرير شامل عن العمولات</p>
                                </div>
                            </a>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <a href="<?php echo e(route('admin.sales-representatives.reports.targets_achievement')); ?>" 
                               class="card text-decoration-none h-100 border-secondary">
                                <div class="card-body text-center">
                                    <i class="bi bi-bullseye text-secondary fs-1 mb-3"></i>
                                    <h5 class="card-title">إنجاز الأهداف</h5>
                                    <p class="card-text text-muted">تتبع إنجاز الأهداف الشهرية</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="bi bi-geo-fill text-primary fs-1 mb-3"></i>
                    <h5><?php echo e($stats['total_areas']); ?></h5>
                    <p class="text-muted mb-0">منطقة مبيعات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="bi bi-signpost-2-fill text-success fs-1 mb-3"></i>
                    <h5><?php echo e($stats['total_routes']); ?></h5>
                    <p class="text-muted mb-0">خط سير</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="bi bi-percent text-info fs-1 mb-3"></i>
                    <h5><?php echo e($stats['monthly_visits'] > 0 ? round(($stats['completed_visits'] / $stats['monthly_visits']) * 100, 1) : 0); ?>%</h5>
                    <p class="text-muted mb-0">معدل إكمال الزيارات</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للزيارات اليومية
    const ctx = document.getElementById('dailyVisitsChart').getContext('2d');
    const dailyVisitsData = <?php echo json_encode($dailyVisits, 15, 512) ?>;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyVisitsData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('ar-SA', { weekday: 'short', month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'إجمالي الزيارات',
                data: dailyVisitsData.map(item => item.visits),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'الزيارات المكتملة',
                data: dailyVisitsData.map(item => item.completed),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/sales-representatives/reports/index.blade.php ENDPATH**/ ?>