<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Modules\Branches\Branch;
use App\Models\User;

class KitchenStation extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'color',
        'is_active',
        'sort_order',
        'branch_id',
        'tenant_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the branch that owns the kitchen station.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that owns the kitchen station.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the menu items for the kitchen station.
     */
    public function menuItems(): HasMany
    {
        return $this->hasMany(MenuItem::class, 'kitchen_station_id');
    }

    /**
     * Get the printers for the kitchen station.
     */
    public function printers(): HasMany
    {
        return $this->hasMany(KitchenPrinter::class, 'kitchen_station_id');
    }

    /**
     * Get active printers for the kitchen station.
     */
    public function activePrinters(): HasMany
    {
        return $this->hasMany(KitchenPrinter::class, 'kitchen_station_id')->where('is_active', true);
    }

    /**
     * Get the order items for the kitchen station.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(RestaurantOrderItem::class, 'kitchen_station_id');
    }

    /**
     * Scope a query to only include active stations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
