<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PaymentTerminal\PaymentTerminalServiceInterface;
use App\Services\PaymentTerminal\Providers\VerifoneProvider;
use App\Services\PaymentTerminal\Providers\IngenicoProvider;
use Illuminate\Support\Facades\Log;

class TestPaymentTerminalIntegration extends Command
{
    protected $signature = 'test:payment-terminal {provider}';
    protected $description = 'Test payment terminal integration for a given provider (verifone or ingenico)';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $providerName = strtolower($this->argument('provider'));
        $this->info("Testing Payment Terminal Integration for: " . ucfirst($providerName));

        $service = null;
        $config = [];

        if ($providerName === 'verifone') {
            $service = new VerifoneProvider();
            // Dummy sandbox config for Verifone - actual tests would need valid sandbox credentials
            $config = [
                'api_key' => 'test_verifone_api_key',
                'entity_id' => 'test_verifone_entity_id',
                'environment' => 'sandbox' // or 'production'
            ];
            $this->comment("Using VerifoneProvider with dummy sandbox configuration.");
        } elseif ($providerName === 'ingenico') {
            $service = new IngenicoProvider();
            // Dummy config for Ingenico
            $config = [
                'terminal_ip' => '*************',
                'terminal_port' => '12345',
                // other necessary config based on SDK/protocol
            ];
            $this->comment("Using IngenicoProvider with dummy configuration (placeholder implementation).");
        } else {
            $this->error("Invalid provider specified. Use 'verifone' or 'ingenico'.");
            return 1;
        }

        $service->setConfig($config);

        // Test Case 1: Test Connection
        $this->line("\n--- Test Case 1: Test Connection ---");
        $connectionResult = $service->testConnection();
        $this->logAndOutputResult($connectionResult, 'Test Connection');

        // Test Case 2: Initiate Payment
        $this->line("\n--- Test Case 2: Initiate Payment ---");
        $initiateResult = $service->initiatePayment(100.50, 'SAR', ['invoice_id' => 'INV-123']);
        $this->logAndOutputResult($initiateResult, 'Initiate Payment');
        $transactionId = $initiateResult['transaction_id'] ?? null;
        if ($providerName === 'verifone' && ($initiateResult['status'] ?? '') === 'error' && str_contains($initiateResult['message'] ?? '', '401')){
            $this->warn("Verifone initiatePayment failed with 401 (Unauthorized) as expected with dummy API key. This tests the API call structure.");
        }


        // Test Case 3: Check Payment Status (if transaction ID exists)
        $this->line("\n--- Test Case 3: Check Payment Status ---");
        if ($transactionId) {
            $statusResult = $service->checkPaymentStatus($transactionId);
            $this->logAndOutputResult($statusResult, 'Check Payment Status');
            if ($providerName === 'verifone' && ($statusResult['status'] ?? '') === 'error' && str_contains($statusResult['message'] ?? '', '401')){
                 $this->warn("Verifone checkPaymentStatus failed with 401 (Unauthorized) as expected with dummy API key. This tests the API call structure.");
            }
        } else {
            $this->warn("Skipping Check Payment Status because no transaction ID was obtained from initiatePayment.");
            Log::warning("TestPaymentTerminal: Skipping Check Payment Status for {$providerName} - no transaction ID.");
        }
        
        // Test Case 4: Refund Payment (if transaction ID exists)
        $this->line("\n--- Test Case 4: Refund Payment ---");
        if ($transactionId) {
            $refundResult = $service->refundPayment($transactionId, 50.00, 'SAR');
            $this->logAndOutputResult($refundResult, 'Refund Payment');
        } else {
            $this->warn("Skipping Refund Payment because no transaction ID was obtained from initiatePayment.");
            Log::warning("TestPaymentTerminal: Skipping Refund Payment for {$providerName} - no transaction ID.");
        }

        // Test Case 5: Initiate Payment with missing configuration (Verifone specific)
        if ($providerName === 'verifone') {
            $this->line("\n--- Test Case 5: Initiate Payment (Verifone - Missing Config) ---");
            $verifoneNoConfig = new VerifoneProvider();
            $verifoneNoConfig->setConfig([]); // Empty config
            $initiateNoConfigResult = $verifoneNoConfig->initiatePayment(10.00, 'SAR');
            $this->logAndOutputResult($initiateNoConfigResult, 'Initiate Payment (Missing Config)');
             if (($initiateNoConfigResult['status'] ?? '') === 'error' && str_contains($initiateNoConfigResult['message'] ?? '', 'not configured')){
                $this->info("Test for missing Verifone config passed as expected.");
            }
        }
        
        $this->info("\nTesting completed for " . ucfirst($providerName));
        Log::info("TestPaymentTerminal: Testing completed for " . ucfirst($providerName));
        return 0;
    }

    private function logAndOutputResult(array $result, string $testName)
    {
        $this->line("Result for {$testName}:");
        $this->line(json_encode($result, JSON_PRETTY_PRINT));
        if (($result['status'] ?? 'error') === 'error' || ($result['status'] ?? 'error') === 'pending_implementation') {
            Log::warning("TestPaymentTerminal [{$testName} - {$this->argument('provider')}]: ", $result);
        } else {
            Log::info("TestPaymentTerminal [{$testName} - {$this->argument('provider')}]: ", $result);
        }
    }
}

