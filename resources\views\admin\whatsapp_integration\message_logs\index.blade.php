@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>WhatsApp Message Logs</h1>
    {{-- Typically, logs are not created manually, so no 'Add New' button --}}

    @if ($message = Session::get('success'))
        <div class="alert alert-success">
            <p>{{ $message }}</p>
        </div>
    @endif

    <table class="table table-bordered">
        <tr>
            <th>No</th>
            <th>Message SID</th>
            <th>Recipient</th>
            <th>Type</th>
            <th>Status</th>
            <th>Direction</th>
            <th>Sent At</th>
            <th>Related To</th>
            <th width="180px">Action</th>
        </tr>
        @php $i = 0; @endphp
        @foreach ($messageLogs as $log)
        <tr>
            <td>{{ ++$i }}</td>
            <td>{{ $log->message_sid }}</td>
            <td>{{ $log->recipient_phone_number }}</td>
            <td>{{ $log->message_type }}</td>
            <td>{{ $log->status }}</td>
            <td>{{ $log->direction }}</td>
            <td>{{ $log->sent_at ? $log->sent_at->format("Y-m-d H:i:s") : "N/A" }}</td>
            <td>
                @if($log->related_model_type && $log->related_model_id)
                    {{ class_basename($log->related_model_type) }} #{{$log->related_model_id}}
                @else
                    N/A
                @endif
            </td>
            <td>
                <a class="btn btn-info btn-sm" href="{{ route("admin.whatsapp_integration.message_logs.show", $log->id) }}">Show</a>
                {{-- Logs are generally not editable or deletable by admin directly --}}
                {{-- <a class="btn btn-primary btn-sm" href="{{ route("admin.whatsapp_integration.message_logs.edit", $log->id) }}">Edit</a> --}}
                {{-- <form action="{{ route("admin.whatsapp_integration.message_logs.destroy", $log->id) }}" method="POST" style="display:inline;">
                    @csrf
                    @method("DELETE")
                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                </form> --}}
            </td>
        </tr>
        @endforeach
    </table>
    {{-- {!! $messageLogs->links() !!} --}}
</div>
@endsection

