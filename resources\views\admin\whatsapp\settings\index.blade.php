@extends('layouts.admin')

@section('title', 'إعدادات واتساب')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-whatsapp text-success me-2"></i>
                        إعدادات تكامل واتساب
                    </h4>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addConfigModal">
                        <i class="bi bi-plus-circle me-1"></i>
                        إضافة إعداد جديد
                    </button>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($configurations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المزود</th>
                                        <th>معرف رقم الهاتف</th>
                                        <th>معرف حساب الأعمال</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($configurations as $config)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>
                                                <span class="badge bg-primary">{{ $config->provider_name }}</span>
                                            </td>
                                            <td>
                                                <code>{{ $config->phone_number_id }}</code>
                                            </td>
                                            <td>
                                                <code>{{ $config->business_account_id ?? 'غير محدد' }}</code>
                                            </td>
                                            <td>
                                                @if($config->is_active)
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        مفعل
                                                    </span>
                                                @else
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-x-circle me-1"></i>
                                                        غير مفعل
                                                    </span>
                                                @endif
                                            </td>
                                            <td>{{ $config->created_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="editConfig({{ $config->id }})">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-success"
                                                            onclick="testConnection({{ $config->id }})">
                                                        <i class="bi bi-wifi"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteConfig({{ $config->id }})">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-whatsapp text-muted" style="font-size: 4rem;"></i>
                            <h5 class="text-muted mt-3">لا توجد إعدادات واتساب</h5>
                            <p class="text-muted">قم بإضافة إعداد جديد لبدء استخدام تكامل واتساب</p>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addConfigModal">
                                <i class="bi bi-plus-circle me-1"></i>
                                إضافة إعداد جديد
                            </button>
                        </div>
                    @endif
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                كيفية الحصول على بيانات واتساب
                            </h6>
                        </div>
                        <div class="card-body">
                            <ol class="mb-0">
                                <li>قم بزيارة <a href="https://developers.facebook.com" target="_blank">Facebook Developers</a></li>
                                <li>أنشئ تطبيق جديد واختر "Business"</li>
                                <li>أضف منتج "WhatsApp Business API"</li>
                                <li>احصل على Access Token و Phone Number ID</li>
                                <li>قم بتكوين Webhook للرسائل الواردة</li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                ملاحظات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="mb-0">
                                <li>تأكد من صحة Access Token</li>
                                <li>رقم الهاتف يجب أن يكون مسجل في WhatsApp Business</li>
                                <li>قم بتفعيل إعداد واحد فقط في كل مرة</li>
                                <li>اختبر الاتصال قبل الاستخدام الفعلي</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة إعداد جديد -->
<div class="modal fade" id="addConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة إعداد واتساب جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="configForm" method="POST" action="{{ route('admin.whatsapp.settings.store') }}">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="provider_name" class="form-label">اسم المزود</label>
                                <select class="form-select" id="provider_name" name="provider_name" required>
                                    <option value="">اختر المزود</option>
                                    <option value="Meta">Meta (Facebook)</option>
                                    <option value="Twilio">Twilio</option>
                                    <option value="360Dialog">360Dialog</option>
                                    <option value="Other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone_number_id" class="form-label">معرف رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone_number_id" name="phone_number_id" required>
                                <div class="form-text">معرف رقم الهاتف من WhatsApp Business API</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="access_token" class="form-label">Access Token</label>
                        <textarea class="form-control" id="access_token" name="access_token" rows="3" required></textarea>
                        <div class="form-text">رمز الوصول من Facebook Developers</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="business_account_id" class="form-label">معرف حساب الأعمال</label>
                                <input type="text" class="form-control" id="business_account_id" name="business_account_id">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="app_id" class="form-label">معرف التطبيق</label>
                                <input type="text" class="form-control" id="app_id" name="app_id">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1">
                            <label class="form-check-label" for="is_active">
                                تفعيل هذا الإعداد
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>
                        حفظ الإعداد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function editConfig(id) {
    // TODO: تنفيذ تعديل الإعداد
    alert('سيتم تنفيذ تعديل الإعداد قريباً');
}

function testConnection(id) {
    // TODO: تنفيذ اختبار الاتصال
    alert('سيتم تنفيذ اختبار الاتصال قريباً');
}

function deleteConfig(id) {
    if(confirm('هل أنت متأكد من حذف هذا الإعداد؟')) {
        // TODO: تنفيذ حذف الإعداد
        alert('سيتم تنفيذ حذف الإعداد قريباً');
    }
}
</script>
@endsection
