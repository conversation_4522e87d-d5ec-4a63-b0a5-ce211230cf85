<?php $__env->startSection('title', 'اختبار رمز العملة السعودي'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">اختبار رمز العملة السعودي ﷼</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>استخدام Helper Class:</h4>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::format(1000):</strong> <?php echo e(\App\Helpers\CurrencyHelper::format(1000)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::formatSAR(2500):</strong> <?php echo e(\App\Helpers\CurrencyHelper::formatSAR(2500)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::getSymbol():</strong> <?php echo e(\App\Helpers\CurrencyHelper::getSymbol()); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::getName('SAR'):</strong> <?php echo e(\App\Helpers\CurrencyHelper::getName('SAR')); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::formatForInput(1500):</strong> <?php echo e(\App\Helpers\CurrencyHelper::formatForInput(1500)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>رمز الريال مباشرة:</strong> ﷼
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>استخدام Helper Class:</h4>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>Currency::format(3000):</strong> <?php echo e(Currency::format(3000)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>Currency::formatSAR(4500):</strong> <?php echo e(Currency::formatSAR(4500)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>Currency::getSymbol():</strong> <?php echo e(Currency::getSymbol()); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>Currency::getName('SAR'):</strong> <?php echo e(Currency::getName('SAR')); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>Currency::getDefaultCurrency():</strong> <?php echo e(Currency::getDefaultCurrency()); ?>

                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>العملات المدعومة:</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>الكود</th>
                                            <th>الاسم</th>
                                            <th>الرمز</th>
                                            <th>مثال</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $supportedCurrencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($code); ?></td>
                                            <td><?php echo e($currency['name']); ?></td>
                                            <td style="font-size: 1.5em;"><?php echo e($currency['symbol']); ?></td>
                                            <td><?php echo e(Currency::format(1000, $code)); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>أمثلة على مبالغ مختلفة بالريال السعودي:</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ صغير</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(25.50)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ متوسط</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(1250)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ كبير</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(125000)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ ضخم</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(1250000)); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>رمز الريال السعودي بأحجام مختلفة:</h4>
                            <div class="text-center">
                                <p style="font-size: 1rem;">حجم عادي: ﷼</p>
                                <p style="font-size: 1.5rem;">حجم متوسط: ﷼</p>
                                <p style="font-size: 2rem;">حجم كبير: ﷼</p>
                                <p style="font-size: 3rem;">حجم ضخم: ﷼</p>
                                <p style="font-size: 4rem;">حجم عملاق: ﷼</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>اختبار التحويل من نص إلى رقم:</h4>
                            <div class="alert alert-info">
                                <p><strong>النص:</strong> "1,250.75 ﷼"</p>
                                <p><strong>الرقم:</strong> <?php echo e(\App\Helpers\CurrencyHelper::parseAmount('1,250.75 ﷼')); ?></p>
                                <p><strong>مُنسق مرة أخرى:</strong> <?php echo e(\App\Helpers\CurrencyHelper::format(\App\Helpers\CurrencyHelper::parseAmount('1,250.75 ﷼'))); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> تم تطبيق رمز الريال السعودي بنجاح!</h5>
                                <p>الآن يمكن استخدام رمز الريال السعودي (﷼) في جميع أنحاء النظام باستخدام:</p>
                                <ul>
                                    <li><code><?php echo e(currency_format(amount)); ?></code> - في ملفات Blade</li>
                                    <li><code>\App\Helpers\CurrencyHelper::format(amount)</code> - في PHP</li>
                                    <li><code><?php echo e(currency_symbol()); ?></code> - للحصول على الرمز فقط</li>
                                    <li><code>﷼</code> - رمز الريال السعودي مباشرة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/currency-test.blade.php ENDPATH**/ ?>