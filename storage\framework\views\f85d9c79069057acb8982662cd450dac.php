<?php $__env->startSection('title', 'اختبار رمز العملة السعودي'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-currency-exchange me-2"></i>
                        اختبار رمز العملة السعودي <?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => 'SAR','class' => 'currency-sar-highlight']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => 'SAR','class' => 'currency-sar-highlight']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="text-primary">
                                <i class="bi bi-code-slash me-2"></i>
                                استخدام Helper Class:
                            </h4>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>CurrencyHelper::format(1000):</strong>
                                    <span class="badge bg-success fs-6"><?php echo e(\App\Helpers\CurrencyHelper::format(1000)); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>CurrencyHelper::formatSAR(2500):</strong>
                                    <span class="badge bg-success fs-6"><?php echo e(\App\Helpers\CurrencyHelper::formatSAR(2500)); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>CurrencyHelper::getSymbol():</strong>
                                    <span class="badge bg-primary fs-5"><?php echo e(\App\Helpers\CurrencyHelper::getSymbol()); ?></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::getName('SAR'):</strong> <?php echo e(\App\Helpers\CurrencyHelper::getName('SAR')); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>\App\Helpers\CurrencyHelper::formatForInput(1500):</strong> <?php echo e(\App\Helpers\CurrencyHelper::formatForInput(1500)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong>رمز الريال مباشرة:</strong> ﷼
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>استخدام Blade Directives:</h4>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong><?php echo \App\Helpers\CurrencyHelper::format(3000); ?>:</strong> <?php echo \App\Helpers\CurrencyHelper::format(3000); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong><?php echo \App\Helpers\CurrencyHelper::formatSAR(4500); ?>:</strong> <?php echo \App\Helpers\CurrencyHelper::formatSAR(4500); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong><?php echo \App\Helpers\CurrencyHelper::getSymbol(); ?>:</strong> <?php echo \App\Helpers\CurrencyHelper::getSymbol(); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong><?php echo \App\Helpers\CurrencyHelper::getName('SAR'); ?>:</strong> <?php echo \App\Helpers\CurrencyHelper::getName('SAR'); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>currency_format(5000):</strong> <?php echo e(currency_format(5000)); ?>

                                </li>
                                <li class="list-group-item">
                                    <strong><?php echo \App\Helpers\CurrencyHelper::getNewSymbol(); ?>:</strong> <?php echo \App\Helpers\CurrencyHelper::getNewSymbol(); ?>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>العملات المدعومة:</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>الكود</th>
                                            <th>الاسم</th>
                                            <th>الرمز</th>
                                            <th>مثال</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $supportedCurrencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($code); ?></td>
                                            <td><?php echo e($currency['name']); ?></td>
                                            <td style="font-size: 1.5em;"><?php echo e($currency['symbol']); ?></td>
                                            <td><?php echo e(\App\Helpers\CurrencyHelper::format(1000, $code)); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>أمثلة على مبالغ مختلفة بالريال السعودي:</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ صغير</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(25.50)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ متوسط</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(1250)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ كبير</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(125000)); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title">مبلغ ضخم</h5>
                                            <p class="card-text display-6"><?php echo e(\App\Helpers\CurrencyHelper::format(1250000)); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>رمز الريال السعودي بأحجام مختلفة:</h4>
                            <div class="text-center">
                                <p style="font-size: 1rem;">حجم عادي: ﷼</p>
                                <p style="font-size: 1.5rem;">حجم متوسط: ﷼</p>
                                <p style="font-size: 2rem;">حجم كبير: ﷼</p>
                                <p style="font-size: 3rem;">حجم ضخم: ﷼</p>
                                <p style="font-size: 4rem;">حجم عملاق: ﷼</p>
                            </div>
                        </div>
                    </div>

                    <!-- قسم الرمز الجديد للريال السعودي -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-success">
                                <i class="bi bi-currency-exchange me-2"></i>
                                الرمز الجديد للريال السعودي - الرمز الرسمي الجديد المعتمد من ساما (2025):
                            </h4>
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">🆕 الرمز الجديد للريال السعودي (مثل $ و €)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>الرمز الجديد:</h6>
                                            <div class="text-center p-4 border rounded" style="background-color: #f8f9fa;">
                                                <div style="font-size: 2rem;" class="mb-3"><?php echo config('currency.supported_currencies.SAR.symbol_new'); ?> مثل $</div>
                                                <div style="font-size: 3rem;" class="mb-3"><?php echo config('currency.supported_currencies.SAR.symbol_new'); ?> 100</div>
                                                <div style="font-size: 4rem;"><?php echo config('currency.supported_currencies.SAR.symbol_new'); ?></div>
                                            </div>
                                            <div class="text-center mt-3">
                                                <h6>النسخة المبسطة:</h6>
                                                <div style="font-size: 2rem;" class="text-success"><?php echo config('currency.supported_currencies.SAR.symbol_simple_new'); ?> 500</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>معلومات الرمز الجديد:</h6>
                                            <ul class="list-group">
                                                <li class="list-group-item">
                                                    <strong>التصميم:</strong> رمز واحد مثل $ و € و £
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الأساس:</strong> مستوحى من الأحرف العربية لكلمة "ريال"
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الاعتماد:</strong> فبراير 2025 من الملك سلمان بن عبدالعزيز
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الهدف:</strong> تعزيز الهوية المالية السعودية عالمياً
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الاستخدام:</strong> في الأسعار والمعاملات المالية
                                                </li>
                                            </ul>
                                            <div class="alert alert-success mt-3">
                                                <small>
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    هذا هو الرمز الجديد الرسمي مثل $ و €
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم الرمز الرسمي من البنك المركزي السعودي -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-info">
                                <i class="bi bi-bank me-2"></i>
                                الرمز الرسمي للريال السعودي - معتمد من البنك المركزي السعودي (ساما):
                            </h4>
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">🏛️ الرمز الرسمي المعتمد من ساما</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>الرمز الرسمي:</h6>
                                            <div class="text-center p-4 border rounded" style="background-color: #f8f9fa;">
                                                <div style="font-size: 2rem; color: #28a745;" class="mb-3"><?php echo config('currency.supported_currencies.SAR.symbol_official'); ?></div>
                                                <div style="font-size: 3rem; color: #28a745;" class="mb-3"><?php echo config('currency.supported_currencies.SAR.symbol_sama'); ?></div>
                                                <div style="font-size: 4rem; color: #28a745;"><?php echo config('currency.supported_currencies.SAR.symbol_official'); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>معلومات الرمز الرسمي:</h6>
                                            <ul class="list-group">
                                                <li class="list-group-item">
                                                    <strong>المصدر:</strong> البنك المركزي السعودي (ساما)
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الموقع الرسمي:</strong>
                                                    <a href="https://www.sama.gov.sa/en-US/Currency/SRS/Pages/default.aspx" target="_blank">
                                                        sama.gov.sa
                                                    </a>
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>التصميم:</strong> رمز هندسي معاصر
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>الاستخدام:</strong> معتمد رسمياً للمعاملات المالية
                                                </li>
                                            </ul>
                                            <div class="alert alert-success mt-3">
                                                <small>
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    هذا هو الرمز الرسمي المعتمد من البنك المركزي السعودي
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم الرمز العربي الأصيل -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-primary">
                                <i class="bi bi-translate me-2"></i>
                                الرمز العربي الأصيل للريال السعودي:
                            </h4>
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">تصميم الرمز حسب الأحرف العربية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>الرمز المصمم بـ CSS:</h6>
                                            <div class="text-center p-4 border rounded" style="background-color: #f8f9fa;">
                                                <div style="font-size: 1rem;"><?php echo config('currency.supported_currencies.SAR.symbol_arabic'); ?></div>
                                                <div style="font-size: 1.5rem;" class="mt-2"><?php echo config('currency.supported_currencies.SAR.symbol_arabic'); ?></div>
                                                <div style="font-size: 2rem;" class="mt-2"><?php echo config('currency.supported_currencies.SAR.symbol_arabic'); ?></div>
                                                <div style="font-size: 3rem;" class="mt-2"><?php echo config('currency.supported_currencies.SAR.symbol_arabic'); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>شرح تكوين الرمز:</h6>
                                            <ul class="list-group">
                                                <li class="list-group-item">
                                                    <strong>حرف الراء (ر):</strong> خط عمودي مائل مع خط أفقي
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>حرف الياء (ي):</strong> خط عمودي مائل مع نقطتين
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>حرف الألف (ا):</strong> خط مستقيم عمودي
                                                </li>
                                                <li class="list-group-item">
                                                    <strong>حرف اللام (ل):</strong> خط عمودي مع خط أفقي مائل
                                                </li>
                                            </ul>
                                            <div class="alert alert-info mt-3">
                                                <small>
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    هذا التصميم مستوحى من الخط العربي الأصيل ويحاكي شكل كلمة "ريال"
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4>اختبار التحويل من نص إلى رقم:</h4>
                            <div class="alert alert-info">
                                <p><strong>النص:</strong> "1,250.75 ﷼"</p>
                                <p><strong>الرقم:</strong> <?php echo e(\App\Helpers\CurrencyHelper::parseAmount('1,250.75 ﷼')); ?></p>
                                <p><strong>مُنسق مرة أخرى:</strong> <?php echo e(\App\Helpers\CurrencyHelper::format(\App\Helpers\CurrencyHelper::parseAmount('1,250.75 ﷼'))); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> تم تطبيق رمز الريال السعودي بنجاح!</h5>
                                <p>الآن يمكن استخدام رمز الريال السعودي (﷼) في جميع أنحاء النظام باستخدام:</p>
                                <ul>
                                    <li><code>currency_format($amount)</code> - في ملفات Blade</li>
                                    <li><code>\App\Helpers\CurrencyHelper::format($amount)</code> - في PHP</li>
                                    <li><code>currency_symbol()</code> - للحصول على الرمز فقط</li>
                                    <li><code>﷼</code> - رمز الريال السعودي مباشرة</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- قسم اختبار المكونات الجديدة -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-warning">
                                <i class="bi bi-puzzle me-2"></i>
                                اختبار المكونات الجديدة (Components):
                            </h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0">مكون رمز العملة</h5>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>رمز الريال السعودي:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => 'SAR']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => 'SAR']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>رمز الدولار الأمريكي:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => 'USD']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => 'USD']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>رمز اليورو:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => 'EUR']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => 'EUR']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>رمز الدرهم الإماراتي:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => 'AED']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => 'AED']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="mb-0">مكون المبالغ</h5>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>مبلغ موجب:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '1500','type' => 'positive']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '1500','type' => 'positive']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>مبلغ سالب:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '-750','type' => 'negative']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '-750','type' => 'negative']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>مبلغ كبير:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '125000','size' => 'large']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '125000','size' => 'large']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <strong>مبلغ صغير:</strong>
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '25.50','size' => 'small']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '25.50','size' => 'small']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم اختبار الأنماط -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-danger">
                                <i class="bi bi-palette me-2"></i>
                                اختبار الأنماط المختلفة:
                            </h4>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <h6>عادي</h6>
                                            <div class="p-3 border rounded">
                                                <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '1000']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '1000']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6>مميز</h6>
                                            <div class="p-3 border rounded">
                                                <span class="currency-sar-highlight">
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '2000']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '2000']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6>متوهج</h6>
                                            <div class="p-3 border rounded bg-dark">
                                                <span class="currency-sar-glow">
                                                    <?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '3000']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '3000']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h6>في الجداول</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <td>المبلغ:</td>
                                                    <td><?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '4000']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '4000']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم معلومات العملات المدعومة -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h4 class="text-primary">
                                <i class="bi bi-globe me-2"></i>
                                العملات المدعومة:
                            </h4>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الكود</th>
                                            <th>الرمز</th>
                                            <th>الاسم بالعربية</th>
                                            <th>الاسم بالإنجليزية</th>
                                            <th>مثال</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = config('currency.supported_currencies'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><span class="badge bg-secondary"><?php echo e($code); ?></span></td>
                                            <td class="fs-5"><?php if (isset($component)) { $__componentOriginal6299f99d45ed451986aff81ca0a231c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6299f99d45ed451986aff81ca0a231c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-symbol','data' => ['currency' => ''.e($code).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-symbol'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currency' => ''.e($code).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $attributes = $__attributesOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__attributesOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6299f99d45ed451986aff81ca0a231c3)): ?>
<?php $component = $__componentOriginal6299f99d45ed451986aff81ca0a231c3; ?>
<?php unset($__componentOriginal6299f99d45ed451986aff81ca0a231c3); ?>
<?php endif; ?></td>
                                            <td><?php echo e($currency['name']); ?></td>
                                            <td><?php echo e($currency['name_en']); ?></td>
                                            <td><?php if (isset($component)) { $__componentOriginal38147dde4cf2a169c9acdca176b90247 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal38147dde4cf2a169c9acdca176b90247 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-amount','data' => ['amount' => '1000','currency' => ''.e($code).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('currency-amount'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['amount' => '1000','currency' => ''.e($code).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $attributes = $__attributesOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__attributesOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal38147dde4cf2a169c9acdca176b90247)): ?>
<?php $component = $__componentOriginal38147dde4cf2a169c9acdca176b90247; ?>
<?php unset($__componentOriginal38147dde4cf2a169c9acdca176b90247); ?>
<?php endif; ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/currency-test.blade.php ENDPATH**/ ?>