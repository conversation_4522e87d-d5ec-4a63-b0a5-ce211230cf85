@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('إدارة المستخدمين') }}</span>
                    @if ($subscription && count($users) < $maxUsers)
                        <a href="{{ route('tenant.users.create') }}" class="btn btn-primary btn-sm">{{ __('إضافة مستخدم جديد') }}</a>
                    @endif
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger" role="alert">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if ($subscription)
                        <div class="alert alert-info">
                            {{ __('عدد المستخدمين الحالي:') }} {{ count($users) }} / {{ $maxUsers }}
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('الاسم') }}</th>
                                        <th>{{ __('البريد الإلكتروني') }}</th>
                                        <th>{{ __('الأدوار') }}</th>
                                        <th>{{ __('الحالة') }}</th>
                                        <th>{{ __('تاريخ الإنشاء') }}</th>
                                        <th>{{ __('الإجراءات') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($users as $user)
                                        <tr>
                                            <td>{{ $user->name }}</td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                @foreach ($user->roles as $role)
                                                    <span class="badge bg-primary">{{ $role->display_name }}</span>
                                                @endforeach
                                            </td>
                                            <td>
                                                @if ($user->is_active)
                                                    <span class="badge bg-success">{{ __('نشط') }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ __('غير نشط') }}</span>
                                                @endif
                                            </td>
                                            <td>{{ $user->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('tenant.users.edit', $user->id) }}" class="btn btn-sm btn-primary">{{ __('تعديل') }}</a>
                                                    
                                                    @if ($user->id != auth()->id())
                                                        <form action="{{ route('tenant.users.toggle-status', $user->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-sm {{ $user->is_active ? 'btn-warning' : 'btn-success' }}">
                                                                {{ $user->is_active ? __('تعطيل') : __('تفعيل') }}
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">{{ __('لا يوجد مستخدمين') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            {{ __('لا يوجد اشتراك نشط حالياً. يرجى الاشتراك للتمكن من إدارة المستخدمين.') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
