<?php $__env->startSection("content"); ?>
    <div class="container">
        <h2>إدارة القيود المحاسبية</h2>
        <a href="<?php echo e(route("admin.journal_entries.create")); ?>" class="btn btn-primary mb-3">إنشاء قيد محاسبي جديد</a>

        <?php if(session("success")): ?>
            <div class="alert alert-success">
                <?php echo e(session("success")); ?>

            </div>
        <?php endif; ?>
        <?php if(session("error")): ?>
            <div class="alert alert-danger">
                <?php echo e(session("error")); ?>

            </div>
        <?php endif; ?>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>رقم القيد</th>
                    <th>تاريخ القيد</th>
                    <th>الوصف</th>
                    <th>الحالة</th>
                    <th>الفرع</th>
                    <th>إجمالي المدين</th>
                    <th>إجمالي الدائن</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $journalEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($entry->id); ?></td>
                        <td><?php echo e($entry->entry_number); ?></td>
                        <td><?php echo e($entry->entry_date->format("Y-m-d")); ?></td>
                        <td><?php echo e(Str::limit($entry->description, 50)); ?></td>
                        <td><span class="badge badge-<?php echo e($entry->status_color); ?>"><?php echo e(ucfirst($entry->status)); ?></span></td>
                        <td><?php echo e($entry->branch->name ?? "N/A"); ?></td>
                        <td><?php echo e(number_format($entry->total_debit, 2)); ?></td>
                        <td><?php echo e(number_format($entry->total_credit, 2)); ?></td>
                        <td>
                            <a href="<?php echo e(route("admin.journal_entries.show", $entry->id)); ?>" class="btn btn-info btn-sm">عرض</a>
                            <?php if($entry->status == "draft"): ?>
                                <a href="<?php echo e(route("admin.journal_entries.edit", $entry->id)); ?>" class="btn btn-warning btn-sm">تعديل</a>
                                <form action="<?php echo e(route("admin.journal_entries.destroy", $entry->id)); ?>" method="POST" style="display:inline-block;">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field("DELETE"); ?>
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا القيد؟')">حذف</button>
                                </form>
                                <form action="<?php echo e(route("admin.journal_entries.post", $entry->id)); ?>" method="POST" style="display:inline-block;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-success btn-sm">ترحيل</button>
                                </form>
                            <?php endif; ?>
                             <?php if($entry->status == "posted"): ?>
                                <form action="<?php echo e(route("admin.journal_entries.cancel", $entry->id)); ?>" method="POST" style="display:inline-block;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-secondary btn-sm">إلغاء</button>
                                </form>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="9">لا توجد قيود محاسبية</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php echo e($journalEntries->links()); ?>

    </div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/journal_entries/index.blade.php ENDPATH**/ ?>