<?php

namespace App\Models\Subscription;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle',
        'max_branches',
        'max_users',
        'max_customers',
        'max_products',
        'max_invoices_per_month',
        'max_sales_representatives',
        'features',
        'modules',
        'has_restaurant_module',
        'has_sales_reps_module',
        'has_inventory_module',
        'has_hr_module',
        'has_crm_module',
        'has_reports_module',
        'has_api_access',
        'has_custom_branding',
        'has_priority_support',
        'has_data_backup',
        'storage_limit_gb',
        'is_popular',
        'is_active',
        'sort_order',
        'restrictions',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'modules' => 'array',
        'has_restaurant_module' => 'boolean',
        'has_sales_reps_module' => 'boolean',
        'has_inventory_module' => 'boolean',
        'has_hr_module' => 'boolean',
        'has_crm_module' => 'boolean',
        'has_reports_module' => 'boolean',
        'has_api_access' => 'boolean',
        'has_custom_branding' => 'boolean',
        'has_priority_support' => 'boolean',
        'has_data_backup' => 'boolean',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
        'restrictions' => 'array',
    ];

    /**
     * Get the tenant subscriptions for this plan.
     */
    public function tenantSubscriptions(): HasMany
    {
        return $this->hasMany(TenantSubscription::class);
    }

    /**
     * Get active tenant subscriptions for this plan.
     */
    public function activeTenantSubscriptions(): HasMany
    {
        return $this->tenantSubscriptions()->where('status', 'active');
    }

    /**
     * Get billing cycle text in Arabic.
     */
    public function getBillingCycleTextAttribute(): string
    {
        return match($this->billing_cycle) {
            'monthly' => 'شهري',
            'quarterly' => 'ربع سنوي',
            'yearly' => 'سنوي',
            default => $this->billing_cycle
        };
    }

    /**
     * Get price per month for comparison.
     */
    public function getPricePerMonthAttribute(): float
    {
        return match($this->billing_cycle) {
            'monthly' => $this->price,
            'quarterly' => $this->price / 3,
            'yearly' => $this->price / 12,
            default => $this->price
        };
    }

    /**
     * Check if plan has specific module.
     */
    public function hasModule(string $module): bool
    {
        $moduleProperty = 'has_' . $module . '_module';
        return property_exists($this, $moduleProperty) ? $this->$moduleProperty : false;
    }

    /**
     * Check if plan has specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Get all available modules.
     */
    public function getAvailableModules(): array
    {
        $modules = [];
        
        if ($this->has_restaurant_module) $modules[] = 'restaurant';
        if ($this->has_sales_reps_module) $modules[] = 'sales_representatives';
        if ($this->has_inventory_module) $modules[] = 'inventory';
        if ($this->has_hr_module) $modules[] = 'hr';
        if ($this->has_crm_module) $modules[] = 'crm';
        if ($this->has_reports_module) $modules[] = 'reports';

        return $modules;
    }

    /**
     * Get plan limits as array.
     */
    public function getLimits(): array
    {
        return [
            'branches' => $this->max_branches,
            'users' => $this->max_users,
            'customers' => $this->max_customers,
            'products' => $this->max_products,
            'invoices_per_month' => $this->max_invoices_per_month,
            'sales_representatives' => $this->max_sales_representatives,
            'storage_gb' => $this->storage_limit_gb,
        ];
    }

    /**
     * Calculate discount for longer billing cycles.
     */
    public function getDiscountPercentage(): float
    {
        return match($this->billing_cycle) {
            'quarterly' => 10, // 10% خصم للربع سنوي
            'yearly' => 20, // 20% خصم للسنوي
            default => 0
        };
    }

    /**
     * Get savings amount compared to monthly.
     */
    public function getSavingsAmount(): float
    {
        if ($this->billing_cycle === 'monthly') {
            return 0;
        }

        $monthlyEquivalent = match($this->billing_cycle) {
            'quarterly' => $this->price_per_month * 3,
            'yearly' => $this->price_per_month * 12,
            default => 0
        };

        return $monthlyEquivalent - $this->price;
    }

    /**
     * Scope to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Scope to filter by billing cycle.
     */
    public function scopeBillingCycle($query, $cycle)
    {
        return $query->where('billing_cycle', $cycle);
    }

    /**
     * Scope to filter by module availability.
     */
    public function scopeWithModule($query, $module)
    {
        $column = 'has_' . $module . '_module';
        return $query->where($column, true);
    }
}
