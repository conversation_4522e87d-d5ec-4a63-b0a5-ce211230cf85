<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_types', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('slug')->unique(); // For programmatic access, e.g., assets, liabilities, equity, income, expenses
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->boolean('is_primary')->default(false); // Indicates if it's a main type like Assets, Liabilities etc.
            $table->foreignId('parent_id')->nullable()->constrained('account_types')->onDelete('cascade'); // For hierarchical types
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_types');
    }
};

