<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PermissionGroup;
use Illuminate\Http\Request;

class PermissionGroupController extends Controller
{
    /**
     * عرض قائمة مجموعات الصلاحيات
     */
    public function index()
    {
        $permissionGroups = PermissionGroup::orderBy('order')->get();
        return view('admin.permissions.groups.index', compact('permissionGroups'));
    }

    /**
     * عرض نموذج إنشاء مجموعة صلاحيات جديدة
     */
    public function create()
    {
        return view('admin.permissions.groups.form');
    }

    /**
     * حفظ مجموعة صلاحيات جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permission_groups',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        PermissionGroup::create($request->all());

        return redirect()->route('admin.permissions_system.groups.index')
            ->with('success', 'تم إنشاء مجموعة الصلاحيات بنجاح');
    }

    /**
     * عرض مجموعة صلاحيات محددة
     */
    public function show(PermissionGroup $group)
    {
        return view('admin.permissions.groups.show', compact('group'));
    }

    /**
     * عرض نموذج تعديل مجموعة صلاحيات
     */
    public function edit(PermissionGroup $group)
    {
        return view('admin.permissions.groups.form', compact('group'));
    }

    /**
     * تحديث مجموعة صلاحيات
     */
    public function update(Request $request, PermissionGroup $group)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permission_groups,name,' . $group->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        $group->update($request->all());

        return redirect()->route('admin.permissions_system.groups.index')
            ->with('success', 'تم تحديث مجموعة الصلاحيات بنجاح');
    }

    /**
     * حذف مجموعة صلاحيات
     */
    public function destroy(PermissionGroup $group)
    {
        // التحقق من عدم وجود صلاحيات مرتبطة بالمجموعة
        if ($group->permissions()->count() > 0) {
            return redirect()->route('admin.permissions_system.groups.index')
                ->with('error', 'لا يمكن حذف مجموعة الصلاحيات لأنها تحتوي على صلاحيات');
        }

        $group->delete();

        return redirect()->route('admin.permissions_system.groups.index')
            ->with('success', 'تم حذف مجموعة الصلاحيات بنجاح');
    }
}
