@extends('layouts.admin')

@section('title', 'تعديل السنة المالية')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل السنة المالية: {{ $fiscalYear->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.fiscal_years.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.fiscal_years.update', $fiscalYear) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">اسم السنة المالية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $fiscalYear->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" id="start_date" name="start_date" value="{{ old('start_date', $fiscalYear->formatted_start_date) }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="end_date">تاريخ النهاية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" id="end_date" name="end_date" value="{{ old('end_date', $fiscalYear->formatted_end_date) }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" {{ old('is_active', $fiscalYear->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">سنة مالية نشطة</label>
                                    </div>
                                    <small class="form-text text-muted">تحديد هذا الخيار يجعل هذه السنة المالية هي السنة النشطة الحالية</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_locked" name="is_locked" {{ old('is_locked', $fiscalYear->is_locked) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_locked">سنة مالية مقفلة</label>
                                    </div>
                                    <small class="form-text text-muted">تحديد هذا الخيار يمنع إضافة أو تعديل القيود المحاسبية لهذه السنة</small>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                <a href="{{ route('admin.fiscal_years.index') }}" class="btn btn-secondary">إلغاء</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // التحقق من تاريخ النهاية أكبر من تاريخ البداية
        $('#end_date').on('change', function() {
            var startDate = $('#start_date').val();
            var endDate = $(this).val();
            
            if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
                alert('يجب أن يكون تاريخ النهاية بعد تاريخ البداية');
                $(this).val('{{ $fiscalYear->formatted_end_date }}');
            }
        });
        
        // التحقق عند تغيير تاريخ البداية أيضًا
        $('#start_date').on('change', function() {
            var startDate = $(this).val();
            var endDate = $('#end_date').val();
            
            if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
                alert('يجب أن يكون تاريخ النهاية بعد تاريخ البداية');
                $(this).val('{{ $fiscalYear->formatted_start_date }}');
            }
        });
    });
</script>
@endpush
