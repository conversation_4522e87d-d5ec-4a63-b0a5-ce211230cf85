<?php

namespace App\Http\Controllers\Modules\Subscriptions;

use App\Http\Controllers\Controller;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Modules\Subscriptions\SubscriptionChange;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class SubscriptionController extends Controller
{
    /**
     * إنشاء مثيل جديد من وحدة التحكم.
     */
    public function __construct()
    {
        // التحقق من أن المستخدم هو سوبر أدمن إذا كان الإعداد يتطلب ذلك
        if (Config::get('subscriptions.super_admin_only', true)) {
            $this->middleware(function ($request, $next) {
                if (!Auth::user() || !Auth::user()->hasRole('super_admin')) {
                    abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
                }
                return $next($request);
            });
        }
    }
    /**
     * Display a listing of the subscriptions.
     */
    public function index()
    {
        $subscriptions = Subscription::with(['tenant', 'plan'])->latest()->paginate(10);
        return view('admin.subscriptions.index', compact('subscriptions'));
    }

    /**
     * Show the form for creating a new subscription.
     */
    public function create()
    {
        $plans = SubscriptionPlan::where('is_active', true)->get();
        $tenants = User::whereHas('roles', function ($query) {
            $query->where('name', 'tenant');
        })->get();

        return view('admin.subscriptions.form', compact('plans', 'tenants'));
    }

    /**
     * Store a newly created subscription in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,cancelled,pending',
            'notes' => 'nullable|string',
            'auto_renew' => 'boolean',
            'price_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        $validatedData['auto_renew'] = $request->has('auto_renew');

        // Get the plan
        $plan = SubscriptionPlan::findOrFail($request->subscription_plan_id);

        // Set initial counts
        $validatedData['current_users_count'] = 1; // Start with the tenant user
        $validatedData['current_branches_count'] = 0;

        DB::beginTransaction();

        try {
            // Create the subscription
            $subscription = Subscription::create($validatedData);

            // Create a payment record if price is greater than 0
            if ($validatedData['price_paid'] > 0) {
                $subscription->payments()->create([
                    'amount' => $validatedData['price_paid'],
                    'payment_date' => now(),
                    'payment_method' => $validatedData['payment_method'] ?? 'manual',
                    'transaction_id' => $validatedData['payment_reference'] ?? null,
                    'receipt_number' => 'RCPT-' . date('Ymd') . '-' . strtoupper(uniqid()),
                    'status' => 'paid',
                    'notes' => 'دفعة الاشتراك الأولية',
                ]);
            }

            // Create a subscription change record
            $subscription->changes()->create([
                'user_id' => Auth::id(),
                'change_type' => 'new',
                'change_details' => 'إنشاء اشتراك جديد',
                'new_plan_id' => $plan->id,
                'effective_date' => $validatedData['start_date'],
                'price_difference' => $validatedData['price_paid'],
            ]);

            DB::commit();

            return redirect()->route('admin.subscriptions.index')
                ->with('success', 'تم إنشاء الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء الاشتراك: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['tenant', 'plan', 'payments', 'changes.user', 'changes.oldPlan', 'changes.newPlan']);
        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Show the form for editing the specified subscription.
     */
    public function edit(Subscription $subscription)
    {
        $plans = SubscriptionPlan::where('is_active', true)->get();
        $tenants = User::whereHas('roles', function ($query) {
            $query->where('name', 'tenant');
        })->get();

        return view('admin.subscriptions.form', compact('subscription', 'plans', 'tenants'));
    }

    /**
     * Update the specified subscription in storage.
     */
    public function update(Request $request, Subscription $subscription)
    {
        $validatedData = $request->validate([
            'tenant_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,cancelled,pending',
            'notes' => 'nullable|string',
            'auto_renew' => 'boolean',
            'price_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        $validatedData['auto_renew'] = $request->has('auto_renew');

        // Check if plan has changed
        $planChanged = $subscription->subscription_plan_id != $request->subscription_plan_id;
        $oldPlanId = $subscription->subscription_plan_id;

        DB::beginTransaction();

        try {
            // Update the subscription
            $subscription->update($validatedData);

            // If plan changed, create a change record
            if ($planChanged) {
                $newPlan = SubscriptionPlan::findOrFail($request->subscription_plan_id);
                $oldPlan = SubscriptionPlan::findOrFail($oldPlanId);

                $changeType = $newPlan->price > $oldPlan->price ? 'upgrade' : 'downgrade';
                $priceDifference = $newPlan->price - $oldPlan->price;

                $subscription->changes()->create([
                    'user_id' => Auth::id(),
                    'change_type' => $changeType,
                    'change_details' => 'تغيير خطة الاشتراك من ' . $oldPlan->name . ' إلى ' . $newPlan->name,
                    'old_plan_id' => $oldPlanId,
                    'new_plan_id' => $newPlan->id,
                    'effective_date' => now(),
                    'price_difference' => $priceDifference,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.subscriptions.index')
                ->with('success', 'تم تحديث الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الاشتراك: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified subscription from storage.
     */
    public function destroy(Subscription $subscription)
    {
        $subscription->delete();

        return redirect()->route('admin.subscriptions.index')
            ->with('success', 'تم حذف الاشتراك بنجاح');
    }

    /**
     * Renew the specified subscription.
     */
    public function renew(Request $request, Subscription $subscription)
    {
        $validatedData = $request->validate([
            'end_date' => 'required|date|after:today',
            'price_paid' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        DB::beginTransaction();

        try {
            // Update the subscription
            $subscription->update([
                'end_date' => $validatedData['end_date'],
                'status' => 'active',
            ]);

            // Create a payment record
            $subscription->payments()->create([
                'amount' => $validatedData['price_paid'],
                'payment_date' => now(),
                'payment_method' => $validatedData['payment_method'] ?? 'manual',
                'transaction_id' => $validatedData['payment_reference'] ?? null,
                'receipt_number' => 'RCPT-' . date('Ymd') . '-' . strtoupper(uniqid()),
                'status' => 'paid',
                'notes' => 'تجديد الاشتراك',
            ]);

            // Create a change record
            $subscription->changes()->create([
                'user_id' => Auth::id(),
                'change_type' => 'renew',
                'change_details' => 'تجديد الاشتراك حتى ' . $validatedData['end_date'],
                'old_plan_id' => $subscription->subscription_plan_id,
                'new_plan_id' => $subscription->subscription_plan_id,
                'effective_date' => now(),
                'price_difference' => $validatedData['price_paid'],
            ]);

            DB::commit();

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('success', 'تم تجديد الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تجديد الاشتراك: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Cancel the specified subscription.
     */
    public function cancel(Subscription $subscription)
    {
        DB::beginTransaction();

        try {
            // Update the subscription
            $subscription->update([
                'status' => 'cancelled',
                'auto_renew' => false,
            ]);

            // Create a change record
            $subscription->changes()->create([
                'user_id' => Auth::id(),
                'change_type' => 'cancel',
                'change_details' => 'إلغاء الاشتراك',
                'old_plan_id' => $subscription->subscription_plan_id,
                'effective_date' => now(),
                'price_difference' => 0,
            ]);

            DB::commit();

            return redirect()->route('admin.subscriptions.show', $subscription)
                ->with('success', 'تم إلغاء الاشتراك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إلغاء الاشتراك: ' . $e->getMessage());
        }
    }
}
