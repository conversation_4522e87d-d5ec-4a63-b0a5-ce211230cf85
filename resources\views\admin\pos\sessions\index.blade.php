@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>POS Sessions</h1>
    <a href="{{ route("admin.pos.sessions.create") }}" class="btn btn-primary mb-3">Start New Session</a>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Device</th>
                <th>User</th>
                <th>Status</th>
                <th>Opened At</th>
                <th>Closed At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {{-- @foreach($sessions as $session) --}}
            {{-- Replace with actual data when available --}}
            <tr>
                <td>1</td>
                <td>Main Counter POS</td>
                <td>Cashier 1</td>
                <td>Open</td>
                <td>2023-05-13 09:00:00</td>
                <td>N/A</td>
                <td>
                    <a href="{{-- route("admin.pos.sessions.show", $session->id) --}}" class="btn btn-info btn-sm">View</a>
                    {{-- @if($session->status == "open") --}}
                    <a href="{{-- route("admin.pos.sessions.edit", $session->id) --}}" class="btn btn-warning btn-sm">Manage</a> {{-- Or link to POS interface for this session --}}
                    <form action="{{-- route("admin.pos.sessions.close", $session->id) --}}" method="POST" style="display:inline-block;">
                        @csrf
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure you want to close this session?")">Close Session</button>
                    </form>
                    {{-- @endif --}}
                </td>
            </tr>
            {{-- @endforeach --}}
        </tbody>
    </table>
</div>
@endsection

