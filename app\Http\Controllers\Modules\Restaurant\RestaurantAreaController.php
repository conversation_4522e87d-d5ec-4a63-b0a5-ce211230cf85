<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\RestaurantArea;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RestaurantAreaController extends Controller
{
    public function index()
    {
        $areas = RestaurantArea::with(['branch', 'tables'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.restaurant.areas.index', compact('areas'));
    }

    public function create()
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.areas.form', compact('branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');

        RestaurantArea::create($validatedData);

        return redirect()->route('admin.restaurant.areas.index')
            ->with('success', __('Restaurant area created successfully.'));
    }

    public function show(RestaurantArea $area)
    {
        $area->load(['branch', 'tables.currentOrder']);
        return view('admin.restaurant.areas.show', compact('area'));
    }

    public function edit(RestaurantArea $area)
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.areas.form', compact('area', 'branches'));
    }

    public function update(Request $request, RestaurantArea $area)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_active'] = $request->has('is_active');

        $area->update($validatedData);

        return redirect()->route('admin.restaurant.areas.index')
            ->with('success', __('Restaurant area updated successfully.'));
    }

    public function destroy(RestaurantArea $area)
    {
        // Check if area has tables
        if ($area->tables()->count() > 0) {
            return redirect()->route('admin.restaurant.areas.index')
                ->with('error', __('Cannot delete area that has tables assigned to it.'));
        }

        $area->delete();

        return redirect()->route('admin.restaurant.areas.index')
            ->with('success', __('Restaurant area deleted successfully.'));
    }
}
