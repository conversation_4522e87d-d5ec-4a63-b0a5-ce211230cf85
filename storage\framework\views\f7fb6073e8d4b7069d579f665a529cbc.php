<?php $__env->startSection('title', 'لوحة التحكم الرئيسية'); ?>

<?php $__env->startSection('header', 'لوحة التحكم الرئيسية'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-people-fill text-primary" style="font-size: 2.5rem;"></i>
            </div>
            <h5>المستخدمين</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\User::count()); ?></h3>
            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-sm btn-outline-primary mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-building text-success" style="font-size: 2.5rem;"></i>
            </div>
            <h5>الفروع</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\Modules\Branches\Branch::count() ?? 0); ?></h3>
            <a href="<?php echo e(route('admin.branches.index')); ?>" class="btn btn-sm btn-outline-success mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-journal-text text-info" style="font-size: 2.5rem;"></i>
            </div>
            <h5>الحسابات</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\Modules\GeneralLedger\Account::count() ?? 0); ?></h3>
            <a href="<?php echo e(route('admin.accounts.index')); ?>" class="btn btn-sm btn-outline-info mt-3">عرض الكل</a>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="admin-card text-center">
            <div class="mb-3">
                <i class="bi bi-journal-plus text-warning" style="font-size: 2.5rem;"></i>
            </div>
            <h5>القيود المحاسبية</h5>
            <h3 class="mb-0"><?php echo e(\App\Models\Modules\GeneralLedger\JournalEntry::count() ?? 0); ?></h3>
            <a href="<?php echo e(route('admin.journal_entries.index')); ?>" class="btn btn-sm btn-outline-warning mt-3">عرض الكل</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="admin-card">
            <h5 class="mb-3">آخر المستخدمين</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ التسجيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = \App\Models\User::latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($user->id); ?></td>
                            <td><?php echo e($user->name); ?></td>
                            <td><?php echo e($user->email); ?></td>
                            <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-sm btn-primary">عرض كل المستخدمين</a>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="admin-card">
            <h5 class="mb-3">آخر القيود المحاسبية</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الوصف</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = \App\Models\Modules\GeneralLedger\JournalEntry::latest()->take(5)->get() ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($entry->id); ?></td>
                            <td><?php echo e($entry->description); ?></td>
                            <td><?php echo e($entry->total_amount); ?></td>
                            <td><?php echo e($entry->entry_date); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <a href="<?php echo e(route('admin.journal_entries.index')); ?>" class="btn btn-sm btn-primary">عرض كل القيود</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="admin-card">
            <h5 class="mb-3">روابط سريعة</h5>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-outline-primary w-100 py-3">
                        <i class="bi bi-person-plus-fill mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة مستخدم جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.branches.create')); ?>" class="btn btn-outline-success w-100 py-3">
                        <i class="bi bi-building-add mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة فرع جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.accounts.create')); ?>" class="btn btn-outline-info w-100 py-3">
                        <i class="bi bi-journal-plus mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة حساب جديد</div>
                    </a>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="<?php echo e(route('admin.journal_entries.create')); ?>" class="btn btn-outline-warning w-100 py-3">
                        <i class="bi bi-file-earmark-plus mb-2" style="font-size: 1.5rem;"></i>
                        <div>إضافة قيد محاسبي</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>