<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_order_item_modifiers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_item_id')->constrained('restaurant_order_items')->onDelete('cascade');
            $table->foreignId('modifier_id')->constrained('menu_item_modifiers')->onDelete('cascade');
            $table->foreignId('modifier_option_id')->constrained('menu_item_modifier_options')->onDelete('cascade');
            $table->decimal('price_adjustment', 8, 2)->default(0); // تعديل السعر
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_order_item_modifiers');
    }
};
