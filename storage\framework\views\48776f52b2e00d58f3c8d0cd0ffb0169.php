<?php $__env->startSection('title', 'الإعدادات العامة لخدمات التوصيل'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>
                        الإعدادات العامة لخدمات التوصيل
                    </h4>
                    <a href="<?php echo e(route('admin.integrations.delivery.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        العودة
                    </a>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo e(route('admin.integrations.delivery.settings.update')); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- إعدادات عامة -->
                        <div class="card border-primary mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">الإعدادات العامة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_provider" class="form-label">مزود الخدمة الافتراضي</label>
                                            <select class="form-select" id="default_provider" name="default_provider" required>
                                                <option value="aramex">أرامكس (Aramex)</option>
                                                <option value="smsa">SMSA Express</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="auto_assign" class="form-label">التعيين التلقائي</label>
                                            <select class="form-select" id="auto_assign" name="auto_assign">
                                                <option value="1">مفعل</option>
                                                <option value="0">غير مفعل</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_weight" class="form-label">الحد الأقصى للوزن (كجم)</label>
                                            <input type="number" class="form-control" id="max_weight" name="max_weight" 
                                                   value="30" min="1" max="100">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="delivery_timeout" class="form-label">مهلة التوصيل (أيام)</label>
                                            <input type="number" class="form-control" id="delivery_timeout" name="delivery_timeout" 
                                                   value="7" min="1" max="30">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الإشعارات -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">إعدادات الإشعارات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" value="1" checked>
                                                <label class="form-check-label" for="email_notifications">
                                                    إشعارات البريد الإلكتروني
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" value="1">
                                                <label class="form-check-label" for="sms_notifications">
                                                    إشعارات الرسائل النصية
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="admin_notifications" name="admin_notifications" value="1" checked>
                                                <label class="form-check-label" for="admin_notifications">
                                                    إشعارات المدير
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="customer_notifications" name="customer_notifications" value="1" checked>
                                                <label class="form-check-label" for="customer_notifications">
                                                    إشعارات العملاء
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التتبع -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">إعدادات التتبع</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tracking_frequency" class="form-label">تكرار التحديث (دقائق)</label>
                                            <select class="form-select" id="tracking_frequency" name="tracking_frequency">
                                                <option value="15">كل 15 دقيقة</option>
                                                <option value="30" selected>كل 30 دقيقة</option>
                                                <option value="60">كل ساعة</option>
                                                <option value="120">كل ساعتين</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="auto_tracking" name="auto_tracking" value="1" checked>
                                                <label class="form-check-label" for="auto_tracking">
                                                    التتبع التلقائي
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.integrations.delivery.index')); ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-1"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/integrations/delivery/settings.blade.php ENDPATH**/ ?>