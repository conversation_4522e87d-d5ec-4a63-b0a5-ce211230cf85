@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.work_centers_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.work_centers_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-industry me-1"></i>
            {{ __(\'manufacturing.work_centers_list\') }}
            <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_work_center\') }}</a> {{-- Link to create page --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for work centers table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.wc_code\') }}</th>
                        <th>{{ __(\'manufacturing.wc_name\') }}</th>
                        <th>{{ __(\'manufacturing.wc_type\') }}</th>
                        <th>{{ __(\'manufacturing.wc_capacity_hours_per_day\') }}</th>
                        <th>{{ __(\'manufacturing.wc_cost_rate_per_hour\') }}</th> {{-- This might be a combined or representative cost --}}
                        <th>{{ __(\'manufacturing.is_active\') }}</th>
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>WC-ASSEMBLY-01</td>
                        <td>Main Assembly Line</td>
                        <td>Assembly Line</td>
                        <td>16.00</td>
                        <td>75.00</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a> {{-- Link to show/edit page --}}
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a> {{-- Link to edit page --}}
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button> {{-- Form for delete --}}
                        </td>
                    </tr>
                     <tr>
                        <td>WC-PAINTING-01</td>
                        <td>Painting Booth A</td>
                        <td>Machine</td>
                        <td>8.00</td>
                        <td>50.00</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a>
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button>
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

