@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ isset($item) ? __('admin_views.edit_item') : __('admin_views.add_new_item') }}</h1>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ isset($item) ? route('admin.items.update', $item->id) : route('admin.items.store') }}" method="POST">
        @csrf
        @if(isset($item))
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="name_ar">{{ __('admin_views.name_ar') }} <span class="text-danger">*</span></label>
                    <input type="text" name="name_ar" id="name_ar" class="form-control" value="{{ old('name_ar', $item->name_ar ?? '') }}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="name_en">{{ __('admin_views.name_en') }} <span class="text-danger">*</span></label>
                    <input type="text" name="name_en" id="name_en" class="form-control" value="{{ old('name_en', $item->name_en ?? '') }}" required>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="code">{{ __('admin_views.code') }}</label>
                    <input type="text" name="code" id="code" class="form-control" value="{{ old('code', $item->code ?? '') }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type">{{ __('admin_views.type') }} <span class="text-danger">*</span></label>
                    <select name="type" id="type" class="form-control" required>
                        <option value="raw_material" {{ old('type', $item->type ?? '') == 'raw_material' ? 'selected' : '' }}>{{ __('admin_views.raw_material') }}</option>
                        <option value="finished_good" {{ old('type', $item->type ?? '') == 'finished_good' ? 'selected' : '' }}>{{ __('admin_views.finished_good') }}</option>
                        <option value="semi_finished_good" {{ old('type', $item->type ?? '') == 'semi_finished_good' ? 'selected' : '' }}>{{ __('admin_views.semi_finished_good') }}</option>
                        <option value="service" {{ old('type', $item->type ?? '') == 'service' ? 'selected' : '' }}>{{ __('admin_views.service') }}</option>
                        <option value="other" {{ old('type', $item->type ?? '') == 'other' ? 'selected' : '' }}>{{ __('admin_views.other') }}</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="unit_of_measure_ar">{{ __('admin_views.unit_of_measure_ar') }}</label>
                    <input type="text" name="unit_of_measure_ar" id="unit_of_measure_ar" class="form-control" value="{{ old('unit_of_measure_ar', $item->unit_of_measure_ar ?? '') }}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="unit_of_measure_en">{{ __('admin_views.unit_of_measure_en') }}</label>
                    <input type="text" name="unit_of_measure_en" id="unit_of_measure_en" class="form-control" value="{{ old('unit_of_measure_en', $item->unit_of_measure_en ?? '') }}">
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description_ar">{{ __('admin_views.description_ar') }}</label>
            <textarea name="description_ar" id="description_ar" class="form-control">{{ old('description_ar', $item->description_ar ?? '') }}</textarea>
        </div>

        <div class="form-group">
            <label for="description_en">{{ __('admin_views.description_en') }}</label>
            <textarea name="description_en" id="description_en" class="form-control">{{ old('description_en', $item->description_en ?? '') }}</textarea>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="standard_cost">{{ __('admin_views.standard_cost') }}</label>
                    <input type="number" step="0.0001" name="standard_cost" id="standard_cost" class="form-control" value="{{ old('standard_cost', $item->standard_cost ?? '0.0000') }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="last_purchase_price">{{ __('admin_views.last_purchase_price') }}</label>
                    <input type="number" step="0.0001" name="last_purchase_price" id="last_purchase_price" class="form-control" value="{{ old('last_purchase_price', $item->last_purchase_price ?? '') }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="selling_price">{{ __('admin_views.selling_price') }}</label>
                    <input type="number" step="0.0001" name="selling_price" id="selling_price" class="form-control" value="{{ old('selling_price', $item->selling_price ?? '') }}">
                </div>
            </div>
        </div>
        
        <div class="row">
             <div class="col-md-6">
                <div class="form-group">
                    <label for="branch_id">{{ __('admin_views.branch') }}</label>
                    <select name="branch_id" id="branch_id" class="form-control">
                        <option value="">{{ __('admin_views.select_branch') }}</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ old('branch_id', $item->branch_id ?? '') == $branch->id ? 'selected' : '' }}>{{ $branch->name_ar }} ({{ $branch->name_en }})</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="inventory_account_id">{{ __('admin_views.inventory_account') }}</label>
                    <select name="inventory_account_id" id="inventory_account_id" class="form-control">
                        <option value="">{{ __('admin_views.select_account') }}</option>
                        @foreach($accounts as $account)
                            <option value="{{ $account->id }}" {{ old('inventory_account_id', $item->inventory_account_id ?? '') == $account->id ? 'selected' : '' }}>{{ $account->name_ar }} ({{ $account->account_number }})</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
         <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cogs_account_id">{{ __('admin_views.cogs_account') }}</label>
                    <select name="cogs_account_id" id="cogs_account_id" class="form-control">
                        <option value="">{{ __('admin_views.select_account') }}</option>
                        @foreach($accounts as $account)
                            <option value="{{ $account->id }}" {{ old('cogs_account_id', $item->cogs_account_id ?? '') == $account->id ? 'selected' : '' }}>{{ $account->name_ar }} ({{ $account->account_number }})</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="sales_revenue_account_id">{{ __('admin_views.sales_revenue_account') }}</label>
                    <select name="sales_revenue_account_id" id="sales_revenue_account_id" class="form-control">
                        <option value="">{{ __('admin_views.select_account') }}</option>
                        @foreach($accounts as $account)
                            <option value="{{ $account->id }}" {{ old('sales_revenue_account_id', $item->sales_revenue_account_id ?? '') == $account->id ? 'selected' : '' }}>{{ $account->name_ar }} ({{ $account->account_number }})</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $item->is_active ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                    {{ __('admin_views.is_active') }}
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_manufactured" id="is_manufactured" value="1" {{ old('is_manufactured', $item->is_manufactured ?? false) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_manufactured">
                    {{ __('admin_views.is_manufactured') }}
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_purchased" id="is_purchased" value="1" {{ old('is_purchased', $item->is_purchased ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_purchased">
                    {{ __('admin_views.is_purchased') }}
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="is_sold" id="is_sold" value="1" {{ old('is_sold', $item->is_sold ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_sold">
                    {{ __('admin_views.is_sold') }}
                </label>
            </div>
        </div>

        <button type="submit" class="btn btn-success">{{ isset($item) ? __('admin_views.update_item') : __('admin_views.save_item') }}</button>
        <a href="{{ route('admin.items.index') }}" class="btn btn-secondary">{{ __('admin_views.cancel') }}</a>
    </form>
</div>
@endsection

