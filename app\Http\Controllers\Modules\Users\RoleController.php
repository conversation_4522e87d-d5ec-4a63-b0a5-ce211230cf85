<?php

namespace App\Http\Controllers\Modules\Users;

use App\Http\Controllers\Controller;
use App\Models\Modules\Users\Role;
use App\Models\Modules\Permissions\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $roles = Role::latest()->paginate(10);
        return view("admin.roles.index", compact("roles"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissions = Permission::all();
        $permissionGroups = $permissions->groupBy("group_name");
        return view("admin.roles.create", compact("permissionGroups"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => "required|string|max:255|unique:roles,name",
            "slug" => "required|string|max:255|unique:roles,slug",
            "description" => "nullable|string",
            "permissions" => "nullable|array",
            "permissions.*" => "exists:permissions,id",
        ]);

        $role = Role::create([
            "name" => $request->name,
            "slug" => Str::slug($request->slug),
            "description" => $request->description,
        ]);

        if ($request->has("permissions")) {
            $role->permissions()->sync($request->permissions);
        }

        return redirect()->route("admin.roles.index")
                         ->with("success", "Role created successfully.");
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        $role->load("permissions");
        return view("admin.roles.show", compact("role"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all();
        $permissionGroups = $permissions->groupBy("group_name");
        $role->load("permissions");
        return view("admin.roles.edit", compact("role", "permissionGroups"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            "name" => "required|string|max:255|unique:roles,name," . $role->id,
            "slug" => "required|string|max:255|unique:roles,slug," . $role->id,
            "description" => "nullable|string",
            "permissions" => "nullable|array",
            "permissions.*" => "exists:permissions,id",
        ]);

        $role->update([
            "name" => $request->name,
            "slug" => Str::slug($request->slug),
            "description" => $request->description,
        ]);

        $role->permissions()->sync($request->permissions ?? []);

        return redirect()->route("admin.roles.index")
                         ->with("success", "Role updated successfully.");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        // Add logic here to check if role can be deleted (e.g., no associated users)
        // For now, a simple delete
        try {
            $role->delete();
            return redirect()->route("admin.roles.index")
                             ->with("success", "Role deleted successfully.");
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.roles.index")
                             ->with("error", "Could not delete role. It might be associated with other records.");
        }
    }
}

