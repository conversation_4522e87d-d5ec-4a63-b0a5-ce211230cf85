<?php $__env->startSection('title', 'تقارير المبيعات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        تقارير المبيعات
                    </h4>
                    <a href="<?php echo e(route('admin.sales.invoices.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        العودة للفواتير
                    </a>
                </div>
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">1,234</h4>
                                    <p class="card-text">إجمالي الفواتير</p>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">156,750 ريال</h4>
                                    <p class="card-text">إجمالي المبيعات</p>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">89</h4>
                                    <p class="card-text">فواتير معلقة</p>
                                    <small>تحتاج متابعة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 class="card-title">23,450 ريال</h4>
                                    <p class="card-text">متوسط الفاتورة</p>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر التقارير -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                فلاتر التقارير
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo e(request('date_from', date('Y-m-01'))); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo e(request('date_to', date('Y-m-d'))); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="customer_id" class="form-label">العميل</label>
                                    <select class="form-select" id="customer_id" name="customer_id">
                                        <option value="">جميع العملاء</option>
                                        <option value="1">أحمد محمد</option>
                                        <option value="2">شركة النور</option>
                                        <option value="3">فاطمة علي</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">حالة الفاتورة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="draft">مسودة</option>
                                        <option value="sent">مرسلة</option>
                                        <option value="paid">مدفوعة</option>
                                        <option value="partially_paid">مدفوعة جزئياً</option>
                                        <option value="overdue">متأخرة</option>
                                        <option value="cancelled">ملغية</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>
                                        تطبيق الفلاتر
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- أنواع التقارير -->
                    <div class="row">
                        <!-- تقرير المبيعات الشهرية -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-calendar-month me-2"></i>
                                        تقرير المبيعات الشهرية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">عرض تفصيلي للمبيعات حسب الشهر مع المقارنات والاتجاهات</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewReport('monthly')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-primary" onclick="exportReport('monthly')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المبيعات حسب العميل -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-people me-2"></i>
                                        تقرير المبيعات حسب العميل
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تحليل مفصل للمبيعات لكل عميل مع ترتيب أفضل العملاء</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-success" onclick="viewReport('by_customer')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="exportReport('by_customer')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المبيعات حسب المنتج -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="bi bi-box me-2"></i>
                                        تقرير المبيعات حسب المنتج
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تحليل أداء المنتجات وأكثرها مبيعاً مع الكميات والأرباح</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-warning" onclick="viewReport('by_product')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="exportReport('by_product')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الفواتير المتأخرة -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        تقرير الفواتير المتأخرة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">قائمة بالفواتير المتأخرة السداد مع تفاصيل العملاء والمبالغ</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-danger" onclick="viewReport('overdue')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="exportReport('overdue')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الضرائب -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-receipt me-2"></i>
                                        تقرير الضرائب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">ملخص الضرائب المحصلة والمستحقة مع التفاصيل الضريبية</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewReport('tax')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="exportReport('tax')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الأرباح والخسائر -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-graph-up-arrow me-2"></i>
                                        تقرير الأرباح والخسائر
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تحليل الأرباح والخسائر من المبيعات مع هوامش الربح</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">آخر تحديث: اليوم</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewReport('profit_loss')">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="exportReport('profit_loss')">
                                                <i class="bi bi-download me-1"></i>
                                                تصدير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات العامة -->
                    <div class="d-flex justify-content-center mt-4">
                        <button class="btn btn-success me-2" onclick="exportAllReports()">
                            <i class="bi bi-download me-1"></i>
                            تصدير جميع التقارير
                        </button>
                        <button class="btn btn-primary me-2" onclick="scheduleReports()">
                            <i class="bi bi-calendar-event me-1"></i>
                            جدولة التقارير
                        </button>
                        <button class="btn btn-info" onclick="emailReports()">
                            <i class="bi bi-envelope me-1"></i>
                            إرسال بالبريد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function viewReport(type) {
    alert(`سيتم عرض تقرير: ${getReportName(type)}`);
}

function exportReport(type) {
    alert(`سيتم تصدير تقرير: ${getReportName(type)} إلى Excel`);
}

function exportAllReports() {
    alert('سيتم تصدير جميع التقارير إلى ملف مضغوط');
}

function scheduleReports() {
    alert('سيتم فتح نافذة جدولة التقارير');
}

function emailReports() {
    alert('سيتم فتح نافذة إرسال التقارير بالبريد الإلكتروني');
}

function resetFilters() {
    document.getElementById('date_from').value = '<?php echo e(date('Y-m-01')); ?>';
    document.getElementById('date_to').value = '<?php echo e(date('Y-m-d')); ?>';
    document.getElementById('customer_id').value = '';
    document.getElementById('status').value = '';
}

function getReportName(type) {
    const names = {
        'monthly': 'المبيعات الشهرية',
        'by_customer': 'المبيعات حسب العميل',
        'by_product': 'المبيعات حسب المنتج',
        'overdue': 'الفواتير المتأخرة',
        'tax': 'الضرائب',
        'profit_loss': 'الأرباح والخسائر'
    };
    return names[type] || type;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/sales/reports/index.blade.php ENDPATH**/ ?>