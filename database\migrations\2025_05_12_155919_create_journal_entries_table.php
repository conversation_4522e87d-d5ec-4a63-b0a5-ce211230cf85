<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("journal_entries", function (Blueprint $table) {
            $table->id();
            $table->string("entry_number")->unique(); // Can be auto-generated or manually entered based on settings
            $table->date("entry_date");
            $table->text("description_ar")->nullable();
            $table->text("description_en")->nullable();
            $table->string("status")->default("draft"); // e.g., draft, posted, cancelled
            $table->string("reference_type")->nullable(); // e.g., Invoice, Receipt, Payment, Manual
            $table->unsignedBigInteger("reference_id")->nullable(); // ID of the related document (e.g., invoice_id)
            $table->foreignId("branch_id")->nullable()->constrained("branches")->onDelete("set null");
            $table->foreignId("created_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->foreignId("posted_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamp("posted_at")->nullable();
            $table->decimal("total_debit", 15, 2)->default(0.00);
            $table->decimal("total_credit", 15, 2)->default(0.00);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("journal_entries");
    }
};

