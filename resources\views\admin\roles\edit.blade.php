@extends("layouts.admin")

@section("main-content")
    <div class="container">
        <h2>Edit Role: {{ $role->name }}</h2>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route("admin.roles.update", $role->id) }}" method="POST">
            @csrf
            @method("PUT")
            <div class="form-group">
                <label for="name">Role Name:</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old("name", $role->name) }}" required>
            </div>
            <div class="form-group">
                <label for="slug">Role Slug:</label>
                <input type="text" class="form-control" id="slug" name="slug" value="{{ old("slug", $role->slug) }}" required>
            </div>
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea class="form-control" id="description" name="description">{{ old("description", $role->description) }}</textarea>
            </div>

            <div class="form-group">
                <h5>Assign Permissions</h5>
                @foreach($permissionGroups as $group => $permissionsInGroup)
                    <h6>{{ $group }}</h6>
                    @foreach($permissionsInGroup as $permission)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" value="{{ $permission->id }}" id="permission_{{ $permission->id }}" {{ $role->permissions->contains($permission->id) ? "checked" : "" }}>
                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                {{ $permission->name }}
                            </label>
                        </div>
                    @endforeach
                    <hr>
                @endforeach
            </div>

            <button type="submit" class="btn btn-success">Update Role</button>
            <a href="{{ route("admin.roles.index") }}" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
@endsection

