@extends('layouts.admin')

@section('title', 'تفاصيل الفاتورة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل الفاتورة #{{ $invoice->invoice_number }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.sales.invoices.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                        <a href="{{ route('admin.sales.invoices.edit', $invoice) }}" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <button class="btn btn-sm btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات الفاتورة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">رقم الفاتورة</th>
                                            <td>{{ $invoice->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>المرجع</th>
                                            <td>{{ $invoice->reference ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الفاتورة</th>
                                            <td>{{ $invoice->invoice_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الاستحقاق</th>
                                            <td>{{ $invoice->due_date->format('Y-m-d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                <span class="badge {{ $invoice->status_badge_class }}">
                                                    {{ $invoice->status_text }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات العميل</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">اسم العميل</th>
                                            <td>{{ $invoice->customer->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>البريد الإلكتروني</th>
                                            <td>{{ $invoice->customer->email ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>رقم الهاتف</th>
                                            <td>{{ $invoice->customer->phone ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>العنوان</th>
                                            <td>{{ $invoice->customer->address ?: 'غير متوفر' }}</td>
                                        </tr>
                                        <tr>
                                            <th>الرقم الضريبي</th>
                                            <td>{{ $invoice->customer->tax_number ?: 'غير متوفر' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">تفاصيل الفاتورة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>الخصم</th>
                                                    <th>الضريبة (%)</th>
                                                    <th>الإجمالي</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($invoice->items as $index => $item)
                                                    <tr>
                                                        <td>{{ $index + 1 }}</td>
                                                        <td>{{ $item->product->name }}</td>
                                                        <td>{{ $item->quantity }}</td>
                                                        <td>{{ $item->formatted_price }}</td>
                                                        <td>{{ number_format($item->discount, 2) }}</td>
                                                        <td>{{ $item->tax_rate }}%</td>
                                                        <td>{{ $item->formatted_line_total }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="6" class="text-right">المجموع الفرعي</th>
                                                    <td>{{ number_format($invoice->subtotal, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <th colspan="6" class="text-right">الخصم</th>
                                                    <td>{{ number_format($invoice->discount_total, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <th colspan="6" class="text-right">الضريبة</th>
                                                    <td>{{ number_format($invoice->tax_total, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <th colspan="6" class="text-right">الإجمالي</th>
                                                    <td>{{ $invoice->formatted_total }}</td>
                                                </tr>
                                                <tr>
                                                    <th colspan="6" class="text-right">المبلغ المستحق</th>
                                                    <td>{{ $invoice->formatted_due }}</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if ($invoice->notes || $invoice->terms)
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">ملاحظات</h5>
                                    </div>
                                    <div class="card-body">
                                        {{ $invoice->notes ?: 'لا توجد ملاحظات' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">الشروط والأحكام</h5>
                                    </div>
                                    <div class="card-body">
                                        {{ $invoice->terms ?: 'لا توجد شروط وأحكام' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">الإجراءات</h5>
                                </div>
                                <div class="card-body">
                                    <a href="{{ route('admin.sales.invoices.edit', $invoice) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <form action="{{ route('admin.sales.invoices.destroy', $invoice) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </form>
                                    <button class="btn btn-primary" onclick="window.print()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
