@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Buy Now, Pay Later (BNPL) Integrations</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <p>Manage and configure integrations with BNPL providers like Tabby and <PERSON>.</p>

    <div class="card mb-3">
        <div class="card-header">Tabby Integration</div>
        <div class="card-body">
            <p>Status: {{-- $settings->tabby_enabled ? "Active" : "Inactive" --}} Active</p>
            <p>Merchant ID: {{-- $settings->tabby_merchant_id ?? "Not Set" --}} TB12345</p>
            <a href="{{ route("admin.integrations.bnpl.edit", ["provider" => "tabby"]) }}" class="btn btn-warning btn-sm">Configure Tabby</a>
            <a href="{{ route("admin.integrations.bnpl.show", ["provider" => "tabby"]) }}" class="btn btn-info btn-sm">View Details/Logs</a>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">Tamara Integration</div>
        <div class="card-body">
            <p>Status: {{-- $settings->tamara_enabled ? "Active" : "Inactive" --}} Inactive</p>
            <p>API Token: {{-- $settings->tamara_api_token ? substr($settings->tamara_api_token, 0, 5) . "..." : "Not Set" --}} Not Set</p>
            <a href="{{ route("admin.integrations.bnpl.edit", ["provider" => "tamara"]) }}" class="btn btn-warning btn-sm">Configure Tamara</a>
            <a href="{{ route("admin.integrations.bnpl.show", ["provider" => "tamara"]) }}" class="btn btn-info btn-sm">View Details/Logs</a>
        </div>
    </div>

    {{-- Add more BNPL providers as needed --}}

</div>
@endsection

