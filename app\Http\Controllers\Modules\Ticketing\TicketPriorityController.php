<?php

namespace App\Http\Controllers\Modules\Ticketing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Ticketing\TicketPriority;
use Illuminate\Http\Request;

class TicketPriorityController extends Controller
{
    public function index()
    {
        $priorities = TicketPriority::latest()->paginate(15);
        return view("admin.ticketing.priorities.index", compact("priorities"));
    }

    public function create()
    {
        return view("admin.ticketing.priorities.form");
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_priorities,name",
            "level" => "required|integer|unique:ticket_priorities,level",
            "color" => "nullable|string|max:7", // Assuming hex color like #RRGGBB
            "is_active" => "boolean",
        ]);
        $validatedData["is_active"] = $request->has("is_active");

        TicketPriority::create($validatedData);
        return redirect()->route("admin.ticketing.priorities.index")->with("success", __("Ticket priority created successfully."));
    }

    public function show(TicketPriority $priority)
    {
        return view("admin.ticketing.priorities.show", compact("priority"));
    }

    public function edit(TicketPriority $priority)
    {
        return view("admin.ticketing.priorities.form", compact("priority"));
    }

    public function update(Request $request, TicketPriority $priority)
    {
        $validatedData = $request->validate([
            "name" => "required|string|max:255|unique:ticket_priorities,name," . $priority->id,
            "level" => "required|integer|unique:ticket_priorities,level," . $priority->id,
            "color" => "nullable|string|max:7",
            "is_active" => "boolean",
        ]);
        $validatedData["is_active"] = $request->has("is_active");

        $priority->update($validatedData);
        return redirect()->route("admin.ticketing.priorities.index")->with("success", __("Ticket priority updated successfully."));
    }

    public function destroy(TicketPriority $priority)
    {
        if ($priority->tickets()->count() > 0) {
            return redirect()->route("admin.ticketing.priorities.index")->with("error", __("Cannot delete priority with associated tickets."));
        }
        $priority->delete();
        return redirect()->route("admin.ticketing.priorities.index")->with("success", __("Ticket priority deleted successfully."));
    }
}

