<?php $__env->startSection('title', 'إدارة النسخ الاحتياطية'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة النسخ الاحتياطية</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="createBackup()">
                            <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
                        </button>
                        <a href="<?php echo e(route('admin.system.backup.settings')); ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-database"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي النسخ</span>
                                    <span class="info-box-number">12</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">آخر نسخة</span>
                                    <span class="info-box-number">اليوم</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-hdd"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">المساحة المستخدمة</span>
                                    <span class="info-box-number">2.5 GB</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">النسخ التلقائية</span>
                                    <span class="info-box-number">مفعلة</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة النسخ الاحتياطية -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الملف</th>
                                    <th>النوع</th>
                                    <th>الحجم</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>backup_2024_03_15_14_30.sql</td>
                                    <td><span class="badge badge-primary">قاعدة البيانات</span></td>
                                    <td>245 MB</td>
                                    <td>2024-03-15 14:30:00</td>
                                    <td><span class="badge badge-success">مكتملة</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.system.backup.download', 'backup_2024_03_15_14_30.sql')); ?>" class="btn btn-sm btn-success">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-info" onclick="restoreBackup('backup_2024_03_15_14_30.sql')">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteBackup('backup_2024_03_15_14_30.sql')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>backup_2024_03_14_14_30.sql</td>
                                    <td><span class="badge badge-primary">قاعدة البيانات</span></td>
                                    <td>238 MB</td>
                                    <td>2024-03-14 14:30:00</td>
                                    <td><span class="badge badge-success">مكتملة</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.system.backup.download', 'backup_2024_03_14_14_30.sql')); ?>" class="btn btn-sm btn-success">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-info" onclick="restoreBackup('backup_2024_03_14_14_30.sql')">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteBackup('backup_2024_03_14_14_30.sql')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة النسخ الاحتياطية
        console.log('تم تعطيل DataTables في صفحة النسخ الاحتياطية');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة النسخ الاحتياطية');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة النسخ الاحتياطية');
                return this;
            };
        }
    });

    function createBackup() {
        if (confirm('هل تريد إنشاء نسخة احتياطية جديدة؟')) {
            // إرسال طلب إنشاء النسخة الاحتياطية
            fetch('<?php echo e(route("admin.system.backup.create")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إنشاء النسخة الاحتياطية بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
            });
        }
    }

    function deleteBackup(filename) {
        if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
            // إرسال طلب حذف النسخة الاحتياطية
            fetch(`<?php echo e(route("admin.system.backup.delete", "")); ?>/${filename}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حذف النسخة الاحتياطية بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
                }
            });
        }
    }

    function restoreBackup(filename) {
        if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
            // إرسال طلب استعادة النسخة الاحتياطية
            fetch(`<?php echo e(route("admin.system.backup.restore", "")); ?>/${filename}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم استعادة النسخة الاحتياطية بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء استعادة النسخة الاحتياطية');
                }
            });
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/system/backup/index.blade.php ENDPATH**/ ?>