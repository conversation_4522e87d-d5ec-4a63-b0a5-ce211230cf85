<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_visits', function (Blueprint $table) {
            $table->id();
            $table->string('visit_code')->unique(); // كود الزيارة
            $table->foreignId('sales_representative_id')->constrained('sales_representatives')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->foreignId('sales_route_id')->nullable()->constrained('sales_routes')->onDelete('set null');
            $table->date('visit_date'); // تاريخ الزيارة
            $table->time('planned_start_time')->nullable(); // الوقت المخطط للبداية
            $table->time('planned_end_time')->nullable(); // الوقت المخطط للنهاية
            $table->time('actual_start_time')->nullable(); // الوقت الفعلي للبداية
            $table->time('actual_end_time')->nullable(); // الوقت الفعلي للنهاية
            $table->enum('visit_type', ['scheduled', 'unscheduled', 'follow_up', 'collection', 'delivery'])->default('scheduled');
            $table->enum('visit_purpose', ['sales', 'collection', 'delivery', 'survey', 'complaint', 'maintenance'])->default('sales');
            $table->enum('visit_status', ['planned', 'in_progress', 'completed', 'cancelled', 'postponed'])->default('planned');
            $table->enum('visit_result', ['successful', 'unsuccessful', 'partial', 'rescheduled'])->nullable();
            $table->text('visit_notes')->nullable(); // ملاحظات الزيارة
            $table->text('customer_feedback')->nullable(); // تعليقات العميل
            $table->json('location_coordinates')->nullable(); // إحداثيات الموقع
            $table->decimal('distance_traveled', 8, 2)->default(0); // المسافة المقطوعة
            $table->integer('duration_minutes')->default(0); // مدة الزيارة بالدقائق
            $table->decimal('order_amount', 12, 2)->default(0); // مبلغ الطلب
            $table->decimal('collection_amount', 12, 2)->default(0); // مبلغ التحصيل
            $table->json('visit_photos')->nullable(); // صور الزيارة
            $table->json('visit_documents')->nullable(); // مستندات الزيارة
            $table->boolean('requires_follow_up')->default(false); // يتطلب متابعة
            $table->date('next_visit_date')->nullable(); // تاريخ الزيارة القادمة
            $table->text('follow_up_notes')->nullable(); // ملاحظات المتابعة
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_visits');
    }
};
