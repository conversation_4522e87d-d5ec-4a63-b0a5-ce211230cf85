<li class="modern-tree-node">
    <div class="modern-tree-node-content <?php if($account->is_control_account): ?> parent-node <?php endif; ?>">
        <?php if($account->children && $account->children->count() > 0): ?>
            <span class="modern-tree-node-toggle tree-toggle" data-bs-toggle="collapse" data-bs-target="#account-<?php echo e($account->id); ?>">
                <i class="bi bi-chevron-down"></i>
            </span>
        <?php else: ?>
            <span class="modern-tree-node-toggle" style="visibility: hidden;">
                <i class="bi bi-chevron-down"></i>
            </span>
        <?php endif; ?>

        <span class="modern-tree-node-icon">
            <?php if($account->is_control_account): ?>
                <i class="bi bi-folder2" style="color: #f6c23e;"></i>
            <?php else: ?>
                <i class="bi bi-file-earmark-text" style="color: #4e73df;"></i>
            <?php endif; ?>
        </span>

        <div class="modern-tree-node-details">
            <div class="modern-tree-node-text">
                <span class="modern-tree-node-code"><?php echo e($account->code); ?></span>
                <span class="modern-tree-node-name">
                    <?php echo e($account->name_ar); ?>

                </span>
            </div>

            <?php
                $balance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                $balanceClass = $balance < 0 ? 'negative' : '';
            ?>

            <span class="modern-tree-node-balance <?php echo e($balanceClass); ?>">
                <?php echo e(number_format(abs($balance), 2)); ?>

                <small><?php echo e($balance < 0 ? 'دائن' : 'مدين'); ?></small>
            </span>
        </div>

        <div class="modern-tree-node-actions">
            <a href="<?php echo e(route('admin.accounts.show', $account->id)); ?>" class="btn btn-sm btn-light" title="عرض">
                <i class="bi bi-eye"></i>
            </a>
            <a href="<?php echo e(route('admin.accounts.edit', $account->id)); ?>" class="btn btn-sm btn-light" title="تعديل">
                <i class="bi bi-pencil"></i>
            </a>
        </div>
    </div>

    <?php if($account->children && $account->children->count() > 0): ?>
        <div class="collapse show modern-tree-children" id="account-<?php echo e($account->id); ?>">
            <ul class="modern-tree">
                <?php $__currentLoopData = $account->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo $__env->make('admin.accounts._modern_tree_node', ['account' => $child], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>
</li>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/accounts/_modern_tree_node.blade.php ENDPATH**/ ?>