<?php

namespace App\Events\Bnpl;

use App\Models\Order;
use App\Models\BnplTransaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BnplPaymentFailedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Order $order;
    public ?BnplTransaction $bnplTransaction;
    public string $reason;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param BnplTransaction|null $bnplTransaction
     * @param string $reason
     */
    public function __construct(Order $order, ?BnplTransaction $bnplTransaction, string $reason = 'Payment failed')
    {
        $this->order = $order;
        $this->bnplTransaction = $bnplTransaction;
        $this->reason = $reason;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel(\'channel-name\'),
        ];
    }
}

