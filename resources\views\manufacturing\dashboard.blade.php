@extends('layouts.app') {{-- Assuming a main layout file exists --}}

@section('content')
<div class="container-fluid">
    <h1 class="mt-4">{{ __('manufacturing.dashboard_title') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">{{ __('manufacturing.dashboard_title') }}</li>
    </ol>

    <div class="row">
        {{-- KPIs and Summaries will go here --}}
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">{{ __('manufacturing.open_work_orders') }}</div>
                            <div class="text-lg fw-bold">0</div> {{-- Placeholder --}}
                        </div>
                        <i class="fas fa-industry fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">{{ __('manufacturing.view_details') }}</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">{{ __('manufacturing.in_progress_work_orders') }}</div>
                            <div class="text-lg fw-bold">0</div> {{-- Placeholder --}}
                        </div>
                        <i class="fas fa-cogs fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">{{ __('manufacturing.view_details') }}</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">{{ __('manufacturing.completed_today') }}</div>
                            <div class="text-lg fw-bold">0</div> {{-- Placeholder --}}
                        </div>
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">{{ __('manufacturing.view_details') }}</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">{{ __('manufacturing.delayed_work_orders') }}</div>
                            <div class="text-lg fw-bold">0</div> {{-- Placeholder --}}
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">{{ __('manufacturing.view_details') }}</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    {{ __('manufacturing.production_vs_plan') }}
                </div>
                <div class="card-body"><canvas id="productionPlanChart" width="100%" height="50"></canvas></div> {{-- Placeholder for chart --}}
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-bell me-1"></i>
                    {{ __('manufacturing.alerts_and_notifications') }}
                </div>
                <div class="card-body">
                    {{-- Placeholder for alerts --}}
                    <p>{{ __('manufacturing.no_alerts') }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tasks me-1"></i>
            {{ __('manufacturing.quick_actions') }}
        </div>
        <div class="card-body">
            <a href="#" class="btn btn-primary">{{ __('manufacturing.create_work_order') }}</a>
            <a href="#" class="btn btn-secondary">{{ __('manufacturing.view_all_work_orders') }}</a>
            {{-- More quick actions can be added here --}}
        </div>
    </div>

</div>
@endsection

@push('scripts')
{{-- Add any specific scripts for this page, e.g., for charts --}}
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script> --}}
{{-- <script>
    // Placeholder for chart initialization
    // var ctx = document.getElementById("productionPlanChart");
    // var myLineChart = new Chart(ctx, {
    //   type: 'line',
    //   data: {
    //     labels: ["Mar 1", "Mar 2", "Mar 3"], // Example labels
    //     datasets: [{
    //       label: "Production",
    //       lineTension: 0.3,
    //       backgroundColor: "rgba(2,117,216,0.2)",
    //       borderColor: "rgba(2,117,216,1)",
    //       pointRadius: 5,
    //       pointBackgroundColor: "rgba(2,117,216,1)",
    //       pointBorderColor: "rgba(255,255,255,0.8)",
    //       pointHoverRadius: 5,
    //       pointHoverBackgroundColor: "rgba(2,117,216,1)",
    //       pointHitRadius: 50,
    //       pointBorderWidth: 2,
    //       data: [10, 20, 15], // Example data
    //     }],
    //   },
    //   options: {
    //     scales: {
    //       xAxes: [{
    //         time: {
    //           unit: 'date'
    //         },
    //         gridLines: {
    //           display: false
    //         },
    //         ticks: {
    //           maxTicksLimit: 7
    //         }
    //       }],
    //       yAxes: [{
    //         ticks: {
    //           min: 0,
    //           max: 30, // Adjust max based on data
    //           maxTicksLimit: 5
    //         },
    //         gridLines: {
    //           color: "rgba(0, 0, 0, .125)",
    //         }
    //       }],
    //     },
    //     legend: {
    //       display: false
    //     }
    //   }
    // });
</script> --}}
@endpush

