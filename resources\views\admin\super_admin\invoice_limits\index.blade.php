@extends('layouts.admin')

@section('title', 'إدارة حدود الفواتير')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة حدود الفواتير للباقات</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('admin.super_admin.invoice_limits.update') }}" method="POST">
                        @csrf
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>الباقة</th>
                                        <th>السعر</th>
                                        <th>دورة الفوترة</th>
                                        <th>حد الفواتير الشهري</th>
                                        <th>حد الفواتير الإجمالي</th>
                                        <th>عدد المشتركين</th>
                                        <th>نشط</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($plans as $plan)
                                        <tr>
                                            <td>{{ $plan->name }}</td>
                                            <td>{{ $plan->price }} ريال</td>
                                            <td>
                                                @switch($plan->billing_cycle)
                                                    @case('monthly')
                                                        شهري
                                                        @break
                                                    @case('quarterly')
                                                        ربع سنوي
                                                        @break
                                                    @case('semi_annually')
                                                        نصف سنوي
                                                        @break
                                                    @case('annually')
                                                        سنوي
                                                        @break
                                                    @default
                                                        {{ $plan->billing_cycle }}
                                                @endswitch
                                            </td>
                                            <td>
                                                <input type="hidden" name="limits[{{ $loop->index }}][subscription_plan_id]" value="{{ $plan->id }}">
                                                <input type="number" class="form-control" name="limits[{{ $loop->index }}][monthly_invoice_limit]" value="{{ $invoiceLimits->where('subscription_plan_id', $plan->id)->first()->monthly_invoice_limit ?? 100 }}" min="1" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" name="limits[{{ $loop->index }}][total_invoice_limit]" value="{{ $invoiceLimits->where('subscription_plan_id', $plan->id)->first()->total_invoice_limit ?? '' }}" min="0">
                                            </td>
                                            <td>{{ $plan->subscriptions->count() }}</td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" name="limits[{{ $loop->index }}][is_active]" value="1" {{ ($invoiceLimits->where('subscription_plan_id', $plan->id)->first()->is_active ?? true) ? 'checked' : '' }}>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center">لا توجد باقات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">معلومات حول حدود الفواتير</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle"></i> كيفية عمل حدود الفواتير</h5>
                        <ul>
                            <li><strong>حد الفواتير الشهري:</strong> هو الحد الأقصى لعدد الفواتير التي يمكن للعميل إنشاؤها في الشهر الواحد.</li>
                            <li><strong>حد الفواتير الإجمالي:</strong> هو الحد الأقصى لعدد الفواتير التي يمكن للعميل إنشاؤها طوال فترة الاشتراك. اتركه فارغاً لعدم وجود حد.</li>
                            <li><strong>نشط:</strong> إذا كان غير نشط، فلن يتم تطبيق حدود الفواتير على هذه الباقة.</li>
                        </ul>
                        <p>يتم إعادة تعيين حد الفواتير الشهري في بداية كل شهر تلقائياً.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
