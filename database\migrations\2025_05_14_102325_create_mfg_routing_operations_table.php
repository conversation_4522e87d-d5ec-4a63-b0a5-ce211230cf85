<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_routing_operations", function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("mfg_routing_id");
            $table->foreign("mfg_routing_id")->references("id")->on("mfg_routings")->onDelete("cascade");
            $table->unsignedBigInteger("mfg_operation_id");
            $table->foreign("mfg_operation_id")->references("id")->on("mfg_operations")->onDelete("cascade");
            $table->integer("sequence"); // Order of operation in the routing
            $table->unsignedBigInteger("work_center_id")->nullable(); // Specific work center for this step, overrides operation default
            $table->foreign("work_center_id")->references("id")->on("mfg_work_centers")->onDelete("set null");
            $table->decimal("setup_time_hours", 8, 2)->nullable(); // Time for setting up this operation in this routing
            $table->decimal("run_time_per_unit_hours", 10, 4)->nullable(); // Time to process one unit for this operation in this routing
            $table->integer("concurrent_operations")->default(1); // How many units can be processed at once
            $table->decimal("cost_per_hour_override", 15, 4)->nullable(); // Override standard cost for this operation in this routing
            $table->text("instructions")->nullable(); // Specific instructions for this step in the routing
            $table->timestamps();

            $table->unique(["mfg_routing_id", "mfg_operation_id", "sequence"], "mfg_routing_operation_sequence_unique");
            $table->unique(["mfg_routing_id", "sequence"], "mfg_routing_sequence_unique");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_routing_operations");
    }
};

