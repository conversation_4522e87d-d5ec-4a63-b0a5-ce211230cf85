@if(empty($rootAccounts))
    <div class="empty-state">
        <i class="fas fa-folder-open"></i>
        <h5>لا توجد حسابات</h5>
        <p>لم يتم إضافة أي حسابات بعد. يمكنك إضافة حسابات جديدة من خلال النقر على زر "إضافة حساب جديد".</p>
        <a href="{{ route('admin.accounts.create') }}" class="btn btn-primary mt-3">
            <i class="fas fa-plus-circle"></i> إضافة حساب جديد
        </a>
    </div>
@else
    <div class="table-responsive">
        <table class="table accounts-table">
            <thead>
                <tr>
                    <th width="10%">الكود</th>
                    <th width="10%">النوع</th>
                    <th width="30%">اسم الحساب</th>
                    <th width="15%">المستوى</th>
                    <th width="15%">الرصيد</th>
                    <th width="20%">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $allAccounts = [];
                    
                    // Collect all accounts in a flat structure
                    foreach($accountTypes as $type) {
                        if(isset($rootAccounts[$type->id])) {
                            foreach($rootAccounts[$type->id]['accounts'] as $account) {
                                $allAccounts[] = [
                                    'account' => $account,
                                    'level' => 0,
                                    'parent_name' => null
                                ];
                                
                                // Add children
                                if($account->children && $account->children->count() > 0) {
                                    foreach($account->children as $child) {
                                        $allAccounts[] = [
                                            'account' => $child,
                                            'level' => 1,
                                            'parent_name' => $account->name_ar
                                        ];
                                        
                                        // Add grandchildren
                                        if($child->children && $child->children->count() > 0) {
                                            foreach($child->children as $grandchild) {
                                                $allAccounts[] = [
                                                    'account' => $grandchild,
                                                    'level' => 2,
                                                    'parent_name' => $child->name_ar
                                                ];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                @endphp
                
                @foreach($allAccounts as $item)
                    @php
                        $account = $item['account'];
                        $level = $item['level'];
                        $parent_name = $item['parent_name'];
                        
                        $balance = $account->opening_balance_debit - $account->opening_balance_credit;
                        $balanceClass = $balance < 0 ? 'negative' : '';
                        
                        $levelText = 'رئيسي';
                        if($level == 1) $levelText = 'فرعي';
                        if($level == 2) $levelText = 'فرعي ثانوي';
                    @endphp
                    
                    <tr>
                        <td>{{ $account->code }}</td>
                        <td>
                            <div class="account-type-cell">
                                <span class="type-indicator type-{{ $account->accountType->slug }}"></span>
                                {{ $account->accountType->name_ar }}
                            </div>
                        </td>
                        <td>
                            @if($level > 0)
                                <span class="text-muted" style="margin-right: {{ $level * 20 }}px;">
                                    <i class="fas fa-long-arrow-alt-left mr-1"></i>
                                </span>
                            @endif
                            
                            @if($account->is_control_account)
                                <i class="fas fa-folder mr-1" style="color: #f6c23e;"></i>
                            @else
                                <i class="fas fa-file-alt mr-1" style="color: #4e73df;"></i>
                            @endif
                            
                            <span class="@if($account->is_control_account) font-weight-bold @endif">
                                {{ $account->name_ar }}
                            </span>
                            
                            @if($parent_name && $level > 0)
                                <small class="text-muted d-block" style="margin-right: {{ $level * 20 + 20 }}px;">
                                    تابع لـ: {{ $parent_name }}
                                </small>
                            @endif
                        </td>
                        <td>{{ $levelText }}</td>
                        <td class="{{ $balanceClass }}">
                            {{ number_format(abs($balance), 2) }}
                            <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                        </td>
                        <td>
                            <div class="account-actions">
                                <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if(!$account->children || $account->children->count() == 0)
                                    <form action="{{ route('admin.accounts.destroy', $account->id) }}" method="POST" style="display:inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endif
