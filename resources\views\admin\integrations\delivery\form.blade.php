@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Configure Delivery Service: {{-- ucfirst($provider) --}} Aramex</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{-- route("admin.integrations.delivery.update", ["provider" => $provider]) --}}" method="POST">
        @csrf
        @method("PUT") {{-- Or POST if creating for the first time --}}

        <div class="form-group">
            <label for="enabled">Enable {{-- ucfirst($provider) --}} Integration</label>
            <select name="enabled" id="enabled" class="form-control">
                <option value="1" {{-- old("enabled", $settings->{$provider."_enabled"} ?? false) ? "selected" : "" --}}>Enabled</option>
                <option value="0" {{-- !old("enabled", $settings->{$provider."_enabled"} ?? false) ? "selected" : "" --}}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="api_key">API Key</label>
            <input type="text" name="api_key" id="api_key" class="form-control" value="{{-- old("api_key", $settings->{$provider."_api_key"} ?? "") --}}">
        </div>

        <div class="form-group">
            <label for="account_number">Account Number</label>
            <input type="text" name="account_number" id="account_number" class="form-control" value="{{-- old("account_number", $settings->{$provider."_account_number"} ?? "") --}}">
        </div>

        <div class="form-group">
            <label for="service_url">Service URL (API Endpoint)</label>
            <input type="url" name="service_url" id="service_url" class="form-control" value="{{-- old("service_url", $settings->{$provider."_service_url"} ?? "") --}}">
        </div>
        
        <div class="form-group">
            <label for="pickup_address">Default Pickup Address</label>
            <textarea name="pickup_address" id="pickup_address" class="form-control" rows="3">{{-- old("pickup_address", $settings->{$provider."_pickup_address"} ?? "") --}}</textarea>
        </div>

        {{-- Add other provider-specific settings here --}}

        <button type="submit" class="btn btn-success mt-3">Save Configuration</button>
        <a href="{{ route("admin.integrations.delivery.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

