@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Configure Payment Terminal: {{-- ucfirst($terminal_id) --}} Default Terminal</h1>

    {{-- @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif --}}

    <form action="{{-- route("admin.integrations.payment_terminals.update", ["terminal_id" => $terminal_id]) --}}" method="POST">
        @csrf
        @method("PUT") {{-- Or POST --}}

        <div class="form-group">
            <label for="enabled">Enable Terminal Integration</label>
            <select name="enabled" id="enabled" class="form-control">
                <option value="1" {{-- old("enabled", $settings->{$terminal_id."_enabled"} ?? false) ? "selected" : "" --}}>Enabled</option>
                <option value="0" {{-- !old("enabled", $settings->{$terminal_id."_enabled"} ?? false) ? "selected" : "" --}}>Disabled</option>
            </select>
        </div>

        <div class="form-group">
            <label for="provider_name">Terminal Provider Name</label>
            <input type="text" name="provider_name" id="provider_name" class="form-control" value="{{-- old("provider_name", $settings->{$terminal_id."_provider_name"} ?? "Local Bank POS") --}}">
        </div>

        <div class="form-group">
            <label for="terminal_model">Terminal Model</label>
            <input type="text" name="terminal_model" id="terminal_model" class="form-control" value="{{-- old("terminal_model", $settings->{$terminal_id."_terminal_model"} ?? "Ingenico ICT250") --}}">
        </div>

        <div class="form-group">
            <label for="connection_type">Connection Type</label>
            <select name="connection_type" id="connection_type" class="form-control">
                <option value="ip" {{-- old("connection_type", $settings->{$terminal_id."_connection_type"} ?? "ip") == "ip" ? "selected" : "" --}}>IP/Network</option>
                <option value="serial" {{-- old("connection_type", $settings->{$terminal_id."_connection_type"} ?? "") == "serial" ? "selected" : "" --}}>Serial Port</option>
                <option value="bluetooth" {{-- old("connection_type", $settings->{$terminal_id."_connection_type"} ?? "") == "bluetooth" ? "selected" : "" --}}>Bluetooth</option>
                <option value="usb" {{-- old("connection_type", $settings->{$terminal_id."_connection_type"} ?? "") == "usb" ? "selected" : "" --}}>USB</option>
            </select>
        </div>

        <div class="form-group">
            <label for="ip_address">IP Address (if applicable)</label>
            <input type="text" name="ip_address" id="ip_address" class="form-control" value="{{-- old("ip_address", $settings->{$terminal_id."_ip_address"} ?? "") --}}">
        </div>

        <div class="form-group">
            <label for="port">Port (if applicable)</label>
            <input type="number" name="port" id="port" class="form-control" value="{{-- old("port", $settings->{$terminal_id."_port"} ?? "") --}}">
        </div>
        
        {{-- Add other terminal-specific settings like API keys, merchant IDs if integration is via cloud API --}}

        <button type="submit" class="btn btn-success mt-3">Save Configuration</button>
        <a href="{{ route("admin.integrations.payment_terminals.index") }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection

