@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.routings_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.routings_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-route me-1"></i>
            {{ __(\'manufacturing.routings_list\') }}
            <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_routing\') }}</a> {{-- Link to create page --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for routings table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.routing_code\') }}</th>
                        <th>{{ __(\'manufacturing.routing_name\') }}</th>
                        <th>{{ __(\'manufacturing.product_name\') }}</th> {{-- Associated product, if any --}}
                        <th>{{ __(\'manufacturing.version\') }}</th>
                        <th>{{ __(\'manufacturing.is_active\') }}</th>
                        <th>{{ __(\'manufacturing.is_default\') }}</th>
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>RTG-TBL-STD-01</td>
                        <td>Standard Routing for Wooden Table</td>
                        <td>Wooden Table (PROD-001)</td>
                        <td>1</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\') }}</span></td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\
                        </td>
                    </tr>
                     <tr>
                        <td>RTG-CHR-OPT-02</td>
                        <td>Optional Routing for Deluxe Chair</td>
                        <td>Deluxe Chair (PROD-003)</td>
                        <td>2</td>
                        <td><span class="badge bg-success">{{ __(\'general.yes\
                        <td><span class="badge bg-secondary">{{ __(\'general.no\
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

