<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("work_order_items", function (Blueprint $table) {
            $table->id();
            $table->foreignId("work_order_id")->constrained("work_orders")->onDelete("cascade");
            $table->foreignId("item_id")->constrained("items")->comment("Component item (raw material or semi-finished good) to be consumed");
            // This could also be a finished good if it's a sub-assembly produced by another WO

            $table->decimal("quantity_required", 15, 4)->comment("Based on BOM or manual entry");
            $table->decimal("quantity_issued", 15, 4)->default(0.0000)->comment("Actual quantity issued from inventory");
            $table->decimal("quantity_consumed", 15, 4)->default(0.0000)->comment("Actual quantity consumed in production");
            $table->decimal("quantity_returned", 15, 4)->default(0.0000)->comment("Quantity returned to inventory if not used");
            $table->decimal("quantity_scrapped", 15, 4)->default(0.0000)->comment("Quantity of this component scrapped during production");
            
            $table->string("unit_of_measure_ar")->nullable();
            $table->string("unit_of_measure_en")->nullable();

            // Costing information for this component at the time of consumption
            $table->decimal("unit_cost", 15, 4)->nullable()->comment("Cost of the component at the time of consumption/issuance");
            $table->decimal("total_cost", 15, 4)->nullable()->comment("Total cost for this component (quantity_consumed * unit_cost)");

            // WIP (Work In Progress) and Finished Goods accounts for posting transactions
            // These might be derived from the item or work order type in a more complex system
            $table->foreignId("wip_account_id")->nullable()->constrained("accounts")->onDelete("set null");
            $table->foreignId("inventory_consumption_account_id")->nullable()->constrained("accounts")->onDelete("set null"); // Usually the item's inventory account

            $table->text("notes_ar")->nullable();
            $table->text("notes_en")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("work_order_items");
    }
};

