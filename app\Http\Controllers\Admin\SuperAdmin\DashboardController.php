<?php

namespace App\Http\Controllers\Admin\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\InvoiceLimit;
use App\Models\Modules\Subscriptions\Subscription;
use App\Models\Modules\Subscriptions\SubscriptionPlan;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * عرض لوحة تحكم السوبر أدمن
     */
    public function index()
    {
        // إحصائيات الباقات والاشتراكات
        $totalPlans = SubscriptionPlan::count();
        $activePlans = SubscriptionPlan::where('is_active', true)->count();
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $totalTenants = User::whereHas('roles', function ($query) {
            $query->where('name', 'tenant');
        })->count();
        
        // الباقات النشطة
        $plans = SubscriptionPlan::where('is_active', true)->withCount('subscriptions')->get();
        
        // آخر الاشتراكات
        $latestSubscriptions = Subscription::with(['tenant', 'plan'])
            ->latest()
            ->take(5)
            ->get();
            
        return view('admin.super_admin.dashboard', compact(
            'totalPlans', 
            'activePlans', 
            'totalSubscriptions', 
            'activeSubscriptions',
            'totalTenants',
            'plans',
            'latestSubscriptions'
        ));
    }
}
