<?php

namespace App\Models\Modules\WhatsAppIntegration;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsAppMessageTemplate extends Model
{
    use HasFactory;

    protected $table = "whatsapp_message_templates";

    protected $fillable = [
        "name",
        "namespace",
        "language_code",
        "category",
        "components_json",
        "status",
        "provider_template_id",
    ];

    protected $casts = [
        "components_json" => "array", // Automatically cast JSON to array and vice versa
    ];

    /**
     * Get the message logs that used this template.
     */
    public function messageLogs()
    {
        return $this->hasMany(WhatsAppMessageLog::class, "template_id");
    }
}

