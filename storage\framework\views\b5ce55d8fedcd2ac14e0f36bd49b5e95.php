

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'currency' => 'SAR',
    'class' => '',
    'size' => 'normal' // normal, large, small
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'currency' => 'SAR',
    'class' => '',
    'size' => 'normal' // normal, large, small
]); ?>
<?php foreach (array_filter(([
    'currency' => 'SAR',
    'class' => '',
    'size' => 'normal' // normal, large, small
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $currencyConfig = config("currency.supported_currencies.{$currency}");
    $symbol = $currencyConfig['symbol'] ?? '﷼';
    
    // تحديد الكلاسات حسب العملة والحجم
    $classes = ['currency-symbol'];
    
    // إضافة كلاس خاص بالعملة
    $classes[] = 'currency-' . strtolower($currency);
    
    // إضافة كلاس الحجم
    switch($size) {
        case 'large':
            $classes[] = 'currency-large';
            break;
        case 'small':
            $classes[] = 'currency-small';
            break;
        default:
            $classes[] = 'currency-normal';
    }
    
    // إضافة الكلاسات المخصصة
    if($class) {
        $classes[] = $class;
    }
    
    $classString = implode(' ', $classes);
?>

<span class="<?php echo e($classString); ?>" title="<?php echo e($currencyConfig['name'] ?? 'ريال سعودي'); ?>"><?php echo e($symbol); ?></span>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/components/currency-symbol.blade.php ENDPATH**/ ?>