

<?php
    // الحصول على المعاملات مع القيم الافتراضية
    $currency = $attributes->get('currency', 'SAR');
    $class = $attributes->get('class', '');
    $size = $attributes->get('size', 'normal');

    $currencyConfig = config("currency.supported_currencies.{$currency}");
    $symbol = $currencyConfig['symbol'] ?? '﷼';

    // تحديد الكلاسات حسب العملة والحجم
    $classes = ['currency-symbol'];

    // إضافة كلاس خاص بالعملة
    $classes[] = 'currency-' . strtolower($currency);

    // إضافة كلاس الحجم
    switch($size) {
        case 'large':
            $classes[] = 'currency-large';
            break;
        case 'small':
            $classes[] = 'currency-small';
            break;
        default:
            $classes[] = 'currency-normal';
    }

    // إضافة الكلاسات المخصصة
    if($class) {
        $classes[] = $class;
    }

    $classString = implode(' ', $classes);
?>

<span class="<?php echo e($classString); ?>" title="<?php echo e($currencyConfig['name'] ?? 'ريال سعودي'); ?>"><?php echo e($symbol); ?></span>
<?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/components/currency-symbol.blade.php ENDPATH**/ ?>