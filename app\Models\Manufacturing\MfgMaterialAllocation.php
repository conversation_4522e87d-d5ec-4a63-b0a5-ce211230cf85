<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
// use App\Models\Inventory\Warehouse; // Assuming Warehouse model exists

class MfgMaterialAllocation extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mfg_material_allocations';

    protected $fillable = [
        'work_order_id',
        'product_id', // This is an mfg_product_id for the component
        'quantity_required',
        'quantity_allocated',
        'quantity_issued',
        'warehouse_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'quantity_required' => 'decimal:4',
        'quantity_allocated' => 'decimal:4',
        'quantity_issued' => 'decimal:4',
    ];

    /**
     * Get the work order this allocation belongs to.
     */
    public function mfgWorkOrder()
    {
        return $this->belongsTo(MfgWorkOrder::class, 'work_order_id');
    }

    /**
     * Get the manufacturing product (component) being allocated.
     */
    public function mfgProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'product_id');
    }

    // /**
    //  * Get the warehouse from which the material is allocated/issued.
    //  */
    // public function warehouse()
    // {
    //     return $this->belongsTo(Warehouse::class, 'warehouse_id');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedByUser()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}

