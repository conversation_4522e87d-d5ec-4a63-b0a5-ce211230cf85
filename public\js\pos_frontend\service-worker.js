// Service Worker for POS Frontend Offline Capabilities

const CACHE_NAME = "pos-cache-v1";
const urlsToCache = [
    "/pos", // Main POS route
    "/css/app.css", // Assuming a compiled app.css, adjust as needed
    "/js/app.js",   // Assuming a compiled app.js, adjust as needed
    "/js/pos_frontend/main.js", // Placeholder for POS specific JS if separated
    // Add paths to Bootstrap CSS/JS if served locally and not via CDN in app.blade.php
    // Add paths to any other critical static assets (images, fonts)
    "/pos/offline" // A dedicated offline fallback page
];

self.addEventListener("install", event => {
    console.log("[ServiceWorker] Install");
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log("[ServiceWorker] Caching app shell");
                return cache.addAll(urlsToCache);
            })
            .then(() => self.skipWaiting()) // Activate worker immediately
    );
});

self.addEventListener("activate", event => {
    console.log("[ServiceWorker] Activate");
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log("[ServiceWorker] Removing old cache", cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    return self.clients.claim(); // Take control of all open clients
});

self.addEventListener("fetch", event => {
    // We only want to intercept navigation requests for the offline page strategy
    // For other assets, try cache first, then network.
    if (event.request.mode === "navigate") {
        event.respondWith(
            fetch(event.request)
                .catch(() => {
                    return caches.match("/pos/offline"); // Return offline page if network fails
                })
        );
    } else {
        event.respondWith(
            caches.match(event.request)
                .then(response => {
                    return response || fetch(event.request); // Cache first, then network
                })
        );
    }
});

