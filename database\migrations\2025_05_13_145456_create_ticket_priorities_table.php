<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("ticket_priorities")) {
            Schema::create("ticket_priorities", function (Blueprint $table) {
                $table->id();
                // Add any other columns for ticket_priorities here if they were intended
                // For example:
                // $table->string("name")->unique();
                // $table->integer("level")->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("ticket_priorities");
    }
};

