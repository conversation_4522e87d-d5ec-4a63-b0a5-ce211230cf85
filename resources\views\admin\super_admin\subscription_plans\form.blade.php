@extends('layouts.admin')

@section('title', isset($subscriptionPlan) ? 'تعديل باقة اشتراك' : 'إضافة باقة اشتراك جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($subscriptionPlan) ? 'تعديل باقة اشتراك' : 'إضافة باقة اشتراك جديدة' }}</h3>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ isset($subscriptionPlan) ? route('admin.super_admin.subscription_plans.update', $subscriptionPlan) : route('admin.super_admin.subscription_plans.store') }}" method="POST">
                        @csrf
                        @if(isset($subscriptionPlan))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="mb-3">معلومات الباقة الأساسية</h4>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الباقة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $subscriptionPlan->name ?? '') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="code" class="form-label">كود الباقة</label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror" id="code" name="code" value="{{ old('code', $subscriptionPlan->code ?? '') }}">
                                    <small class="form-text text-muted">سيتم إنشاء كود تلقائي إذا تركت هذا الحقل فارغاً</small>
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف الباقة</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $subscriptionPlan->description ?? '') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="price" class="form-label">سعر الباقة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $subscriptionPlan->price ?? '') }}" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="billing_cycle" class="form-label">دورة الفوترة <span class="text-danger">*</span></label>
                                    <select class="form-select @error('billing_cycle') is-invalid @enderror" id="billing_cycle" name="billing_cycle" required>
                                        <option value="monthly" {{ (old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'monthly') ? 'selected' : '' }}>شهري</option>
                                        <option value="quarterly" {{ (old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'quarterly') ? 'selected' : '' }}>ربع سنوي</option>
                                        <option value="semi_annually" {{ (old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'semi_annually') ? 'selected' : '' }}>نصف سنوي</option>
                                        <option value="annually" {{ (old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'annually') ? 'selected' : '' }}>سنوي</option>
                                    </select>
                                    @error('billing_cycle')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="is_active" class="form-check-label">حالة الباقة</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ (old('is_active', $subscriptionPlan->is_active ?? true)) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">نشط</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h4 class="mb-3">حدود وميزات الباقة</h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_users" class="form-label">الحد الأقصى للمستخدمين <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('max_users') is-invalid @enderror" id="max_users" name="max_users" value="{{ old('max_users', $subscriptionPlan->max_users ?? 1) }}" min="1" required>
                                            @error('max_users')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_branches" class="form-label">الحد الأقصى للفروع <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('max_branches') is-invalid @enderror" id="max_branches" name="max_branches" value="{{ old('max_branches', $subscriptionPlan->max_branches ?? 1) }}" min="1" required>
                                            @error('max_branches')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="monthly_invoice_limit" class="form-label">حد الفواتير الشهري <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('monthly_invoice_limit') is-invalid @enderror" id="monthly_invoice_limit" name="monthly_invoice_limit" value="{{ old('monthly_invoice_limit', $invoiceLimit->monthly_invoice_limit ?? 100) }}" min="1" required>
                                            @error('monthly_invoice_limit')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="total_invoice_limit" class="form-label">حد الفواتير الإجمالي</label>
                                            <input type="number" class="form-control @error('total_invoice_limit') is-invalid @enderror" id="total_invoice_limit" name="total_invoice_limit" value="{{ old('total_invoice_limit', $invoiceLimit->total_invoice_limit ?? '') }}" min="0">
                                            <small class="form-text text-muted">اتركه فارغاً لعدم وجود حد</small>
                                            @error('total_invoice_limit')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="storage_space_gb" class="form-label">مساحة التخزين (جيجابايت) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('storage_space_gb') is-invalid @enderror" id="storage_space_gb" name="storage_space_gb" value="{{ old('storage_space_gb', $subscriptionPlan->storage_space_gb ?? 1) }}" min="1" required>
                                    @error('storage_space_gb')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <h5 class="mt-4 mb-3">الوحدات المتاحة</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_pos" name="has_pos" value="1" {{ (old('has_pos', $subscriptionPlan->has_pos ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_pos">نقاط البيع</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_inventory" name="has_inventory" value="1" {{ (old('has_inventory', $subscriptionPlan->has_inventory ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_inventory">إدارة المخزون</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_accounting" name="has_accounting" value="1" {{ (old('has_accounting', $subscriptionPlan->has_accounting ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_accounting">المحاسبة</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_manufacturing" name="has_manufacturing" value="1" {{ (old('has_manufacturing', $subscriptionPlan->has_manufacturing ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_manufacturing">التصنيع</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_hr" name="has_hr" value="1" {{ (old('has_hr', $subscriptionPlan->has_hr ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_hr">الموارد البشرية</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_crm" name="has_crm" value="1" {{ (old('has_crm', $subscriptionPlan->has_crm ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_crm">إدارة علاقات العملاء</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_purchases" name="has_purchases" value="1" {{ (old('has_purchases', $subscriptionPlan->has_purchases ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_purchases">المشتريات</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_sales" name="has_sales" value="1" {{ (old('has_sales', $subscriptionPlan->has_sales ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_sales">المبيعات</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_reports" name="has_reports" value="1" {{ (old('has_reports', $subscriptionPlan->has_reports ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_reports">التقارير</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_api_access" name="has_api_access" value="1" {{ (old('has_api_access', $subscriptionPlan->has_api_access ?? false)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_api_access">الوصول إلى API</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.super_admin.subscription_plans.index') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">{{ isset($subscriptionPlan) ? 'تحديث الباقة' : 'إنشاء الباقة' }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
