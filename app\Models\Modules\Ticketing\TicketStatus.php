<?php

namespace App\Models\Modules\Ticketing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketStatus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'color',
        'is_default_new',
        'is_default_closed',
        'is_active',
    ];

    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }
}
