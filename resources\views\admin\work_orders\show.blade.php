@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>{{ __("admin_views.view_work_order") }}: {{ $workOrder->work_order_code }}</h1>

    <div class="card">
        <div class="card-header">
            <h4>{{ __("admin_views.work_order_details") }}</h4>
        </div>
        <div class="card-body">
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.id") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->id }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.work_order_code") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->work_order_code }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.item_to_produce") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->item ? $workOrder->item->name_ar . " (" . $workOrder->item->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.bom") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->bom ? $workOrder->bom->name_ar . " (" . $workOrder->bom->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.quantity_to_produce") }}:</strong></div>
                <div class="col-md-9">{{ number_format($workOrder->quantity_to_produce, 4) }} {{ $workOrder->item && $workOrder->item->unit_of_measure_ar ? $workOrder->item->unit_of_measure_ar : "" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.actual_quantity_produced") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->actual_quantity_produced ? number_format($workOrder->actual_quantity_produced, 4) : "-" }} {{ $workOrder->item && $workOrder->item->unit_of_measure_ar ? $workOrder->item->unit_of_measure_ar : "" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.status") }}:</strong></div>
                <div class="col-md-9"><span class="badge badge-{{ $workOrder->status_badge_class }}">{{ __("admin_views.work_order_status_" . $workOrder->status) }}</span></div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.branch") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->branch ? $workOrder->branch->name_ar . " (" . $workOrder->branch->name_en . ")" : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.start_date") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->start_date ? $workOrder->start_date->format("Y-m-d") : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.end_date") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->end_date ? $workOrder->end_date->format("Y-m-d") : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.notes") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->notes ?? "-" }}</div>
            </div>
             <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.created_by") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->createdBy ? $workOrder->createdBy->name : "-" }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.created_at") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->created_at }}</div>
            </div>
            <div class="row mb-2">
                <div class="col-md-3"><strong>{{ __("admin_views.updated_at") }}:</strong></div>
                <div class="col-md-9">{{ $workOrder->updated_at }}</div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h4>{{ __("admin_views.component_items_consumed") }}</h4>
        </div>
        <div class="card-body">
            @if($workOrder->workOrderItems && $workOrder->workOrderItems->count() > 0)
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>{{ __("admin_views.component_item") }}</th>
                            <th>{{ __("admin_views.required_quantity") }}</th>
                            <th>{{ __("admin_views.actual_consumed_quantity") }}</th>
                            <th>{{ __("admin_views.unit_of_measure_ar") }}</th>
                            <th>{{ __("admin_views.cost") }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($workOrder->workOrderItems as $woItem)
                            <tr>
                                <td>{{ $woItem->item ? $woItem->item->name_ar : "-" }}</td>
                                <td>{{ number_format($woItem->quantity_required, 4) }}</td>
                                <td>{{ number_format($woItem->quantity_consumed, 4) }}</td>
                                <td>{{ $woItem->unit_of_measure_ar ?? ($woItem->item ? $woItem->item->unit_of_measure_ar : "-") }}</td>
                                <td>{{ $woItem->cost ? number_format($woItem->cost, 4) : "-" }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <p>{{ __("admin_views.no_component_items_found") }}</p>
            @endif
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h4>{{ __("admin_views.production_costs") }}</h4>
        </div>
        <div class="card-body">
            @if($workOrder->productionCosts && $workOrder->productionCosts->count() > 0)
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>{{ __("admin_views.cost_type") }}</th>
                            <th>{{ __("admin_views.description") }}</th>
                            <th>{{ __("admin_views.amount") }}</th>
                            <th>{{ __("admin_views.cost_date") }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($workOrder->productionCosts as $cost)
                            <tr>
                                <td>{{ __("admin_views.cost_type_" . $cost->cost_type) }}</td>
                                <td>{{ $cost->description_ar ?? "-" }}</td>
                                <td>{{ number_format($cost->amount, 4) }}</td>
                                <td>{{ $cost->cost_date ? $cost->cost_date->format("Y-m-d") : "-" }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <p>{{ __("admin_views.no_production_costs_found") }}</p>
            @endif
        </div>
        <div class="card-footer">
            @if($workOrder->status == "planned" || $workOrder->status == "in_progress")
                <a href="{{ route("admin.work_orders.edit", $workOrder->id) }}" class="btn btn-warning">{{ __("admin_views.edit") }}</a>
            @endif
            <a href="{{ route("admin.work_orders.index") }}" class="btn btn-secondary">{{ __("admin_views.back_to_list") }}</a>
        </div>
    </div>

</div>
@endsection

