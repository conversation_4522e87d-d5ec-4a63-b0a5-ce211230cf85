<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("mfg_bom_items", function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("mfg_bom_id");
            $table->foreign("mfg_bom_id")->references("id")->on("mfg_boms")->onDelete("cascade");
            $table->unsignedBigInteger("component_product_id"); // This is an mfg_product_id
            $table->foreign("component_product_id")->references("id")->on("mfg_products")->onDelete("cascade");
            $table->decimal("quantity_per_parent", 15, 4);
            $table->string("unit_of_measure");
            $table->decimal("scrap_percentage", 5, 2)->default(0.00); // Percentage of scrap for this component
            $table->text("notes")->nullable();
            $table->integer("sequence")->nullable(); // Optional sequence for assembly
            $table->unsignedBigInteger("operation_id")->nullable(); // Optional: link to a specific operation in a routing where this item is consumed
            // $table->foreign('operation_id')->references('id')->on('mfg_operations')->onDelete('set null'); // Uncomment when mfg_operations is created
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("mfg_bom_items");
    }
};

