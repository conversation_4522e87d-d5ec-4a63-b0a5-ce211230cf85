<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SalesRepresentativeController extends Controller
{
    public function index()
    {
        $representatives = SalesRepresentative::with(['manager', 'branch', 'activeSalesAreas'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->paginate(15);

        return view('admin.sales-representatives.representatives.index', compact('representatives'));
    }

    public function create()
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $managers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['manager', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        return view('admin.sales-representatives.representatives.form', compact('branches', 'managers'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'employee_code' => 'required|string|max:50|unique:sales_representatives,employee_code',
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'national_id' => 'required|string|max:20|unique:sales_representatives,national_id',
            'birth_date' => 'nullable|date',
            'address' => 'nullable|string',
            'hire_date' => 'required|date',
            'base_salary' => 'nullable|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'target_amount' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,suspended',
            'commission_type' => 'required|in:percentage,fixed,tiered',
            'commission_tiers' => 'nullable|json',
            'vehicle_type' => 'nullable|string|max:100',
            'vehicle_number' => 'nullable|string|max:50',
            'license_number' => 'nullable|string|max:50',
            'license_expiry' => 'nullable|date',
            'notes' => 'nullable|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'manager_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('profile_image')) {
            $validatedData['profile_image'] = $request->file('profile_image')->store('representatives', 'public');
        }

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');

        // Handle commission tiers
        if ($request->commission_type === 'tiered' && $request->has('commission_tiers')) {
            $validatedData['commission_tiers'] = json_decode($request->commission_tiers, true);
        }

        SalesRepresentative::create($validatedData);

        return redirect()->route('admin.sales-representatives.representatives.index')
            ->with('success', __('Sales representative created successfully.'));
    }

    public function show(SalesRepresentative $representative)
    {
        $representative->load([
            'manager', 
            'branch', 
            'activeSalesAreas', 
            'salesRoutes', 
            'customers',
            'salesTargets' => function($query) {
                $query->current()->latest();
            },
            'salesCommissions' => function($query) {
                $query->latest()->take(10);
            },
            'salesVisits' => function($query) {
                $query->latest()->take(10);
            }
        ]);

        // Calculate current month statistics
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $stats = [
            'total_sales' => $representative->calculateSales($currentMonth, $endOfMonth),
            'total_commission' => $representative->calculateCommission($currentMonth, $endOfMonth),
            'total_visits' => $representative->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->count(),
            'completed_visits' => $representative->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->where('visit_status', 'completed')
                ->count(),
            'achievement' => $representative->getCurrentMonthAchievement(),
        ];

        return view('admin.sales-representatives.representatives.show', compact('representative', 'stats'));
    }

    public function edit(SalesRepresentative $representative)
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $managers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['manager', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        return view('admin.sales-representatives.representatives.form', compact('representative', 'branches', 'managers'));
    }

    public function update(Request $request, SalesRepresentative $representative)
    {
        $validatedData = $request->validate([
            'employee_code' => 'required|string|max:50|unique:sales_representatives,employee_code,' . $representative->id,
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'national_id' => 'required|string|max:20|unique:sales_representatives,national_id,' . $representative->id,
            'birth_date' => 'nullable|date',
            'address' => 'nullable|string',
            'hire_date' => 'required|date',
            'base_salary' => 'nullable|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'target_amount' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,suspended',
            'commission_type' => 'required|in:percentage,fixed,tiered',
            'commission_tiers' => 'nullable|json',
            'vehicle_type' => 'nullable|string|max:100',
            'vehicle_number' => 'nullable|string|max:50',
            'license_number' => 'nullable|string|max:50',
            'license_expiry' => 'nullable|date',
            'notes' => 'nullable|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'manager_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        if ($request->hasFile('profile_image')) {
            if ($representative->profile_image) {
                Storage::disk('public')->delete($representative->profile_image);
            }
            $validatedData['profile_image'] = $request->file('profile_image')->store('representatives', 'public');
        }

        $validatedData['is_active'] = $request->has('is_active');

        // Handle commission tiers
        if ($request->commission_type === 'tiered' && $request->has('commission_tiers')) {
            $validatedData['commission_tiers'] = json_decode($request->commission_tiers, true);
        }

        $representative->update($validatedData);

        return redirect()->route('admin.sales-representatives.representatives.index')
            ->with('success', __('Sales representative updated successfully.'));
    }

    public function destroy(SalesRepresentative $representative)
    {
        // Check if representative has active assignments
        if ($representative->activeSalesAreas()->count() > 0) {
            return redirect()->route('admin.sales-representatives.representatives.index')
                ->with('error', __('Cannot delete representative with active area assignments.'));
        }

        // Check if representative has customers
        if ($representative->customers()->count() > 0) {
            return redirect()->route('admin.sales-representatives.representatives.index')
                ->with('error', __('Cannot delete representative with assigned customers.'));
        }

        if ($representative->profile_image) {
            Storage::disk('public')->delete($representative->profile_image);
        }

        $representative->delete();

        return redirect()->route('admin.sales-representatives.representatives.index')
            ->with('success', __('Sales representative deleted successfully.'));
    }

    public function updateStatus(Request $request, SalesRepresentative $representative)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,suspended'
        ]);

        $representative->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => __('Representative status updated successfully.'),
            'status' => $representative->status,
            'status_color' => $representative->status_color
        ]);
    }

    public function dashboard(SalesRepresentative $representative)
    {
        // Load representative with relationships
        $representative->load(['activeSalesAreas', 'salesRoutes', 'customers']);

        // Get current month data
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Calculate statistics
        $stats = [
            'total_customers' => $representative->customers()->count(),
            'active_routes' => $representative->salesRoutes()->active()->count(),
            'assigned_areas' => $representative->activeSalesAreas()->count(),
            'monthly_sales' => $representative->calculateSales($currentMonth, $endOfMonth),
            'monthly_commission' => $representative->calculateCommission($currentMonth, $endOfMonth),
            'monthly_visits' => $representative->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->count(),
            'completed_visits' => $representative->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->where('visit_status', 'completed')
                ->count(),
            'pending_visits' => $representative->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->whereIn('visit_status', ['planned', 'in_progress'])
                ->count(),
        ];

        // Get current target
        $currentTarget = $representative->getCurrentMonthTarget();
        $achievement = $representative->getCurrentMonthAchievement();

        // Get recent activities
        $recentVisits = $representative->salesVisits()
            ->with(['customer', 'salesRoute'])
            ->latest()
            ->take(5)
            ->get();

        $recentCommissions = $representative->salesCommissions()
            ->with(['customer', 'order'])
            ->latest()
            ->take(5)
            ->get();

        return view('admin.sales-representatives.representatives.dashboard', compact(
            'representative', 
            'stats', 
            'currentTarget', 
            'achievement', 
            'recentVisits', 
            'recentCommissions'
        ));
    }
}
