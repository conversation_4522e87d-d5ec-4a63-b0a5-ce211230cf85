@extends("layouts.admin")

@section("content")
    <div class="container">
        <h1>Work Order Details: {{-- $workOrder->work_order_number --}}</h1>
        <table class="table table-bordered mt-3">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{-- $workOrder->id --}}</td>
                </tr>
                <tr>
                    <th>Work Order Number</th>
                    <td>{{-- $workOrder->work_order_number --}}</td>
                </tr>
                <tr>
                    <th>Item to Produce</th>
                    <td>{{-- $workOrder->item ? $workOrder->item->name . " (" . $workOrder->item->item_code . ")" : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>BOM Used</th>
                    <td>{{-- $workOrder->bom ? $workOrder->bom->name . " - v" . $workOrder->bom->version : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Quantity to Produce</th>
                    <td>{{-- number_format($workOrder->quantity_to_produce, 4) --}}</td>
                </tr>
                <tr>
                    <th>Quantity Produced</th>
                    <td>{{-- number_format($workOrder->quantity_produced ?? 0, 4) --}}</td>
                </tr>
                <tr>
                    <th>Quantity Scrapped</th>
                    <td>{{-- number_format($workOrder->quantity_scrapped ?? 0, 4) --}}</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>{{-- ucfirst(str_replace("_", " ", $workOrder->status)) --}}</td>
                </tr>
                <tr>
                    <th>Planned Start Date</th>
                    <td>{{-- $workOrder->planned_start_date ? \Carbon\Carbon::parse($workOrder->planned_start_date)->format("Y-m-d H:i") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Planned End Date</th>
                    <td>{{-- $workOrder->planned_end_date ? \Carbon\Carbon::parse($workOrder->planned_end_date)->format("Y-m-d H:i") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Actual Start Date</th>
                    <td>{{-- $workOrder->actual_start_date ? \Carbon\Carbon::parse($workOrder->actual_start_date)->format("Y-m-d H:i") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Actual End Date</th>
                    <td>{{-- $workOrder->actual_end_date ? \Carbon\Carbon::parse($workOrder->actual_end_date)->format("Y-m-d H:i") : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Branch</th>
                    <td>{{-- $workOrder->branch ? $workOrder->branch->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Assigned To</th>
                    <td>{{-- $workOrder->assignedTo ? $workOrder->assignedTo->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Created By</th>
                    <td>{{-- $workOrder->createdBy ? $workOrder->createdBy->name : "N/A" --}}</td>
                </tr>
                <tr>
                    <th>Notes</th>
                    <td>{{-- $workOrder->notes --}}</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{-- $workOrder->created_at --}}</td>
                </tr>
                <tr>
                    <th>Updated At</th>
                    <td>{{-- $workOrder->updated_at --}}</td>
                </tr>
            </tbody>
        </table>

        {{-- Section for Consumed Items and Produced Items --}}
        <h3 class="mt-4">Work Order Items (Consumed/Produced)</h3>
        {{-- This would typically involve listing items from manufacturing_work_order_items table --}}
        {{-- For simplicity, this is a placeholder --}}
        <p>Details of consumed materials and produced sub-assemblies/finished goods would be listed here.</p>

        <a href="{{-- route("admin.manufacturing.work_orders.index") --}}" class="btn btn-secondary mt-3">Back to List</a>
        <a href="{{-- route("admin.manufacturing.work_orders.edit", $workOrder->id) --}}" class="btn btn-warning mt-3">Edit</a>
    </div>
@endsection
