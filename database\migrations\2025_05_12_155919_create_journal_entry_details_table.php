<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("journal_entry_details", function (Blueprint $table) {
            $table->id();
            $table->foreignId("journal_entry_id")->constrained("journal_entries")->onDelete("cascade");
            $table->foreignId("account_id")->constrained("accounts");
            $table->decimal("debit", 15, 2)->default(0.00);
            $table->decimal("credit", 15, 2)->default(0.00);
            $table->text("description_ar")->nullable();
            $table->text("description_en")->nullable();
            // Optional: Link to a specific entity like customer, vendor, employee, product, project etc.
            $table->string("related_entity_type")->nullable(); // e.g., App\Models\Customer, App\Models\Vendor
            $table->unsignedBigInteger("related_entity_id")->nullable();
            $table->timestamps();

            $table->index(["related_entity_type", "related_entity_id"]); // Index for polymorphic relation
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("journal_entry_details");
    }
};

