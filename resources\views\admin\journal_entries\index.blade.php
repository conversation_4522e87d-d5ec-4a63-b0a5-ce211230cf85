@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>إدارة القيود المحاسبية</h2>
        <a href="{{ route("admin.journal_entries.create") }}" class="btn btn-primary mb-3">إنشاء قيد محاسبي جديد</a>

        @if (session("success"))
            <div class="alert alert-success">
                {{ session("success") }}
            </div>
        @endif
        @if (session("error"))
            <div class="alert alert-danger">
                {{ session("error") }}
            </div>
        @endif

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>رقم القيد</th>
                    <th>تاريخ القيد</th>
                    <th>الوصف</th>
                    <th>الحالة</th>
                    <th>الفرع</th>
                    <th>إجمالي المدين</th>
                    <th>إجمالي الدائن</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($journalEntries as $entry)
                    <tr>
                        <td>{{ $entry->id }}</td>
                        <td>{{ $entry->entry_number }}</td>
                        <td>{{ $entry->entry_date->format("Y-m-d") }}</td>
                        <td>{{ Str::limit($entry->description, 50) }}</td>
                        <td><span class="badge badge-{{ $entry->status_color }}">{{ ucfirst($entry->status) }}</span></td>
                        <td>{{ $entry->branch->name ?? "N/A" }}</td>
                        <td>{{ number_format($entry->total_debit, 2) }}</td>
                        <td>{{ number_format($entry->total_credit, 2) }}</td>
                        <td>
                            <a href="{{ route("admin.journal_entries.show", $entry->id) }}" class="btn btn-info btn-sm">عرض</a>
                            @if($entry->status == "draft")
                                <a href="{{ route("admin.journal_entries.edit", $entry->id) }}" class="btn btn-warning btn-sm">تعديل</a>
                                <form action="{{ route("admin.journal_entries.destroy", $entry->id) }}" method="POST" style="display:inline-block;">
                                    @csrf
                                    @method("DELETE")
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا القيد؟')">حذف</button>
                                </form>
                                <form action="{{ route("admin.journal_entries.post", $entry->id) }}" method="POST" style="display:inline-block;">
                                    @csrf
                                    <button type="submit" class="btn btn-success btn-sm">ترحيل</button>
                                </form>
                            @endif
                             @if($entry->status == "posted")
                                <form action="{{ route("admin.journal_entries.cancel", $entry->id) }}" method="POST" style="display:inline-block;">
                                    @csrf
                                    <button type="submit" class="btn btn-secondary btn-sm">إلغاء</button>
                                </form>
                            @endif
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="9">لا توجد قيود محاسبية</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        {{ $journalEntries->links() }}
    </div>
@endsection

