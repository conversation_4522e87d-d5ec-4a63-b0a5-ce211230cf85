<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ __("Restaurant POS") }} - {{ config('app.name') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .pos-container {
            height: 100vh;
            overflow: hidden;
        }
        
        .menu-section {
            height: 100vh;
            overflow-y: auto;
            background: white;
            border-right: 1px solid #dee2e6;
        }
        
        .cart-section {
            height: 100vh;
            background: white;
            display: flex;
            flex-direction: column;
        }
        
        .category-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 10px;
        }
        
        .category-tab {
            border: none;
            background: white;
            margin: 2px;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .category-tab.active {
            background: #007bff;
            color: white;
        }
        
        .category-tab:hover {
            background: #e9ecef;
        }
        
        .category-tab.active:hover {
            background: #0056b3;
        }
        
        .menu-items {
            padding: 15px;
        }
        
        .menu-item-card {
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }
        
        .menu-item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        
        .menu-item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .cart-header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .cart-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
        }
        
        .cart-footer {
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 20px;
        }
        
        .table-selector {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .table-btn {
            width: 60px;
            height: 60px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }
        
        .table-btn.available {
            border-color: #28a745;
            color: #28a745;
        }
        
        .table-btn.occupied {
            border-color: #dc3545;
            color: #dc3545;
            background: #fff5f5;
        }
        
        .table-btn.selected {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .quantity-btn:hover {
            background: #f8f9fa;
        }
        
        .total-section {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .checkout-btn {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <div class="row g-0 h-100">
            <!-- Menu Section -->
            <div class="col-8 menu-section">
                <!-- Category Tabs -->
                <div class="category-tabs">
                    <div class="d-flex flex-wrap" id="categoryTabs">
                        <!-- Categories will be loaded here -->
                    </div>
                </div>
                
                <!-- Menu Items -->
                <div class="menu-items" id="menuItems">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ __("Loading...") }}</span>
                        </div>
                        <p class="mt-2">{{ __("Loading menu...") }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Cart Section -->
            <div class="col-4 cart-section">
                <!-- Cart Header -->
                <div class="cart-header">
                    <h4 class="mb-0">{{ __("Current Order") }}</h4>
                    <small id="orderInfo">{{ __("Select table and add items") }}</small>
                </div>
                
                <!-- Table Selector -->
                <div class="p-3">
                    <div class="table-selector">
                        <h6 class="mb-3">{{ __("Select Table") }}</h6>
                        <div id="tableSelector">
                            <!-- Tables will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{{ __("Cart is empty") }}</p>
                        <small>{{ __("Add items from the menu") }}</small>
                    </div>
                </div>
                
                <!-- Cart Footer -->
                <div class="cart-footer">
                    <div class="total-section">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __("Subtotal") }}:</span>
                            <span id="subtotal">0.00 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ __("Tax") }} (15%):</span>
                            <span id="tax">0.00 ريال</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>{{ __("Total") }}:</span>
                            <span id="total">0.00 ريال</span>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-success checkout-btn" onclick="processOrder()" disabled id="checkoutBtn">
                            <i class="fas fa-check"></i> {{ __("Process Order") }}
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearCart()">
                            <i class="fas fa-trash"></i> {{ __("Clear Cart") }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentCategory = '';
        let selectedTable = null;
        let cart = [];
        let menuData = [];
        let tablesData = [];

        document.addEventListener('DOMContentLoaded', function() {
            loadMenu();
            loadTables();
        });

        function loadMenu() {
            fetch('/admin/restaurant/pos/menu')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        menuData = data.data;
                        displayCategories();
                        if (menuData.length > 0) {
                            selectCategory(menuData[0].id);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading menu:', error);
                    document.getElementById('menuItems').innerHTML = `
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5>{{ __("Error loading menu") }}</h5>
                            <button class="btn btn-primary" onclick="loadMenu()">{{ __("Try Again") }}</button>
                        </div>
                    `;
                });
        }

        function loadTables() {
            fetch('/admin/restaurant/pos/tables')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        tablesData = data.data;
                        displayTables();
                    }
                })
                .catch(error => {
                    console.error('Error loading tables:', error);
                });
        }

        function displayCategories() {
            const container = document.getElementById('categoryTabs');
            const categoriesHtml = menuData.map(category => `
                <button class="category-tab" onclick="selectCategory(${category.id})" 
                        style="border-color: ${category.color};" id="cat-${category.id}">
                    ${category.name}
                </button>
            `).join('');
            container.innerHTML = categoriesHtml;
        }

        function selectCategory(categoryId) {
            currentCategory = categoryId;
            
            // Update active tab
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(`cat-${categoryId}`).classList.add('active');
            
            // Display items
            const category = menuData.find(cat => cat.id === categoryId);
            displayMenuItems(category ? category.items : []);
        }

        function displayMenuItems(items) {
            const container = document.getElementById('menuItems');
            
            if (items.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{{ __("No items in this category") }}</h5>
                    </div>
                `;
                return;
            }

            const itemsHtml = items.map(item => `
                <div class="menu-item-card" onclick="addToCart(${item.id})">
                    <div class="d-flex">
                        ${item.image ? 
                            `<img src="${item.image}" class="menu-item-image me-3" alt="${item.name}">` :
                            `<div class="menu-item-image me-3 bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-utensils text-muted"></i>
                            </div>`
                        }
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.name}</h6>
                            <p class="text-muted small mb-2">${item.description || ''}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <strong class="text-primary">${item.price} ريال</strong>
                                <div class="item-badges">
                                    ${item.is_spicy ? '<span class="badge bg-danger">🌶️</span>' : ''}
                                    ${item.is_vegetarian ? '<span class="badge bg-success">🥬</span>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = itemsHtml;
        }

        function displayTables() {
            const container = document.getElementById('tableSelector');
            const tablesHtml = tablesData.map(table => `
                <div class="table-btn ${table.status}" onclick="selectTable(${table.id})" 
                     id="table-${table.id}" title="${table.name} - ${table.status}">
                    <div class="text-center">
                        <small>${table.number}</small>
                    </div>
                </div>
            `).join('');
            container.innerHTML = tablesHtml;
        }

        function selectTable(tableId) {
            selectedTable = tableId;
            
            // Update visual selection
            document.querySelectorAll('.table-btn').forEach(btn => btn.classList.remove('selected'));
            document.getElementById(`table-${tableId}`).classList.add('selected');
            
            const table = tablesData.find(t => t.id === tableId);
            document.getElementById('orderInfo').textContent = `${table.name} - ${table.capacity} {{ __("seats") }}`;
            
            updateCheckoutButton();
        }

        function addToCart(itemId) {
            const category = menuData.find(cat => cat.items.some(item => item.id === itemId));
            const item = category.items.find(item => item.id === itemId);
            
            const existingItem = cart.find(cartItem => cartItem.id === itemId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: item.id,
                    name: item.name,
                    price: parseFloat(item.price),
                    quantity: 1,
                    modifiers: []
                });
            }
            
            displayCart();
            updateTotals();
            updateCheckoutButton();
        }

        function displayCart() {
            const container = document.getElementById('cartItems');
            
            if (cart.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{{ __("Cart is empty") }}</p>
                        <small>{{ __("Add items from the menu") }}</small>
                    </div>
                `;
                return;
            }

            const cartHtml = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${item.name}</h6>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity(${index}, -1)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="mx-2">${item.quantity}</span>
                            <button class="quantity-btn" onclick="updateQuantity(${index}, 1)">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <strong>${(item.price * item.quantity).toFixed(2)} ريال</strong>
                    </div>
                </div>
            `).join('');

            container.innerHTML = cartHtml;
        }

        function updateQuantity(index, change) {
            cart[index].quantity += change;
            
            if (cart[index].quantity <= 0) {
                cart.splice(index, 1);
            }
            
            displayCart();
            updateTotals();
            updateCheckoutButton();
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            displayCart();
            updateTotals();
            updateCheckoutButton();
        }

        function updateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ريال';
            document.getElementById('tax').textContent = tax.toFixed(2) + ' ريال';
            document.getElementById('total').textContent = total.toFixed(2) + ' ريال';
        }

        function updateCheckoutButton() {
            const btn = document.getElementById('checkoutBtn');
            btn.disabled = cart.length === 0 || !selectedTable;
        }

        function clearCart() {
            if (confirm('{{ __("Are you sure you want to clear the cart?") }}')) {
                cart = [];
                displayCart();
                updateTotals();
                updateCheckoutButton();
            }
        }

        function processOrder() {
            if (cart.length === 0 || !selectedTable) {
                alert('{{ __("Please select a table and add items to cart") }}');
                return;
            }

            const orderData = {
                type: 'dine_in',
                table_id: selectedTable,
                items: cart.map(item => ({
                    menu_item_id: item.id,
                    quantity: item.quantity,
                    notes: '',
                    modifiers: item.modifiers
                }))
            };

            fetch('/admin/restaurant/pos/create-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`{{ __("Order created successfully!") }}\n{{ __("Order Number") }}: ${data.data.order_number}`);
                    clearCart();
                    selectedTable = null;
                    document.querySelectorAll('.table-btn').forEach(btn => btn.classList.remove('selected'));
                    document.getElementById('orderInfo').textContent = '{{ __("Select table and add items") }}';
                    loadTables(); // Refresh table statuses
                } else {
                    alert('{{ __("Error creating order") }}: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error creating order:', error);
                alert('{{ __("Error creating order") }}');
            });
        }
    </script>
</body>
</html>
