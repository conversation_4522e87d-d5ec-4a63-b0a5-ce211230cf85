@if(empty($rootAccounts))
    <div class="empty-state">
        <i class="fas fa-folder-open"></i>
        <h5>لا توجد حسابات</h5>
        <p>لم يتم إضافة أي حسابات بعد. يمكنك إضافة حسابات جديدة من خلال النقر على زر "إضافة حساب جديد".</p>
        <a href="{{ route('admin.accounts.create') }}" class="btn btn-primary mt-3">
            <i class="fas fa-plus-circle"></i> إضافة حساب جديد
        </a>
    </div>
@else
    <div class="accounts-tree">
        @foreach($accountTypes as $type)
            @if(isset($rootAccounts[$type->id]))
                <div class="account-type-section">
                    <div class="account-type-header" data-toggle="collapse" data-target="#type-{{ $type->id }}" aria-expanded="true">
                        <i class="fas fa-chevron-down toggle-icon"></i>
                        
                        @php
                            $typeIcon = 'folder';
                            if($type->slug == 'assets') $typeIcon = 'landmark';
                            elseif($type->slug == 'liabilities') $typeIcon = 'hand-holding-usd';
                            elseif($type->slug == 'equity') $typeIcon = 'balance-scale';
                            elseif($type->slug == 'revenue') $typeIcon = 'chart-line';
                            elseif($type->slug == 'expenses') $typeIcon = 'shopping-cart';
                            elseif($type->slug == 'closing') $typeIcon = 'file-invoice-dollar';
                            
                            $totalBalance = 0;
                            foreach($rootAccounts[$type->id]['accounts'] as $account) {
                                $totalBalance += ($account->opening_balance_debit - $account->opening_balance_credit);
                            }
                            $balanceClass = $totalBalance < 0 ? 'negative' : '';
                        @endphp
                        
                        <i class="fas fa-{{ $typeIcon }} type-icon"></i>
                        <span class="type-name">{{ $type->name_ar }}</span>
                        <span class="badge badge-pill badge-light ml-2">{{ $rootAccounts[$type->id]['accounts']->count() }}</span>
                        
                        <div class="type-balance {{ $balanceClass }}">
                            {{ number_format(abs($totalBalance), 2) }}
                            <small>{{ $totalBalance < 0 ? 'دائن' : 'مدين' }}</small>
                        </div>
                    </div>
                    
                    <div class="collapse show account-type-content" id="type-{{ $type->id }}">
                        <div class="tree-container">
                            <ul class="tree">
                                @foreach($rootAccounts[$type->id]['accounts'] as $account)
                                    <li class="tree-node">
                                        <div class="tree-node-content @if($account->is_control_account) parent-node @endif">
                                            @if($account->children && $account->children->count() > 0)
                                                <span class="tree-node-toggle" data-toggle="collapse" data-target="#account-{{ $account->id }}">
                                                    <i class="fas fa-chevron-down"></i>
                                                </span>
                                            @else
                                                <span class="tree-node-toggle" style="visibility: hidden;">
                                                    <i class="fas fa-chevron-down"></i>
                                                </span>
                                            @endif
                                            
                                            <span class="tree-node-icon">
                                                @if($account->is_control_account)
                                                    <i class="fas fa-folder" style="color: #f6c23e;"></i>
                                                @else
                                                    <i class="fas fa-file-alt" style="color: #4e73df;"></i>
                                                @endif
                                            </span>
                                            
                                            <div class="tree-node-details">
                                                <span class="tree-node-code">{{ $account->code }}</span>
                                                <span class="tree-node-name">
                                                    <span class="type-indicator type-{{ $account->accountType->slug }}"></span>
                                                    {{ $account->name_ar }}
                                                </span>
                                                
                                                @php
                                                    $balance = $account->opening_balance_debit - $account->opening_balance_credit;
                                                    $balanceClass = $balance < 0 ? 'negative' : '';
                                                @endphp
                                                
                                                <span class="tree-node-balance {{ $balanceClass }}">
                                                    {{ number_format(abs($balance), 2) }}
                                                    <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                                                </span>
                                                
                                                <div class="tree-node-actions">
                                                    <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if(!$account->children || $account->children->count() == 0)
                                                        <form action="{{ route('admin.accounts.destroy', $account->id) }}" method="POST" style="display:inline-block;">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        
                                        @if($account->children && $account->children->count() > 0)
                                            <div class="collapse show tree-children" id="account-{{ $account->id }}">
                                                <ul class="tree">
                                                    @foreach($account->children as $child)
                                                        @include('admin.accounts._tree_node', ['account' => $child])
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
@endif
