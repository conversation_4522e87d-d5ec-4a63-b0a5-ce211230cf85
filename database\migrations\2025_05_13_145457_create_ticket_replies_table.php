<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("ticket_replies")) {
            Schema::create("ticket_replies", function (Blueprint $table) {
                $table->id();
                // Add any other columns for ticket_replies here if they were intended
                // For example:
                // $table->foreignId("ticket_id")->constrained("tickets");
                // $table->foreignId("user_id")->constrained("users"); // or admin_id
                // $table->text("reply_content");
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("ticket_replies");
    }
};

