<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\RestaurantOrder;
use App\Models\Modules\Restaurant\RestaurantTable;
use App\Models\Customer;
use App\Models\User;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RestaurantOrderController extends Controller
{
    public function index()
    {
        $orders = RestaurantOrder::with(['table', 'customer', 'waiter', 'cashier', 'orderItems'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->latest()
            ->paginate(20);

        return view('admin.restaurant.orders.index', compact('orders'));
    }

    public function create()
    {
        $tables = RestaurantTable::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->available()
            ->orderBy('number')
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $waiters = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['waiter', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.orders.form', compact('tables', 'customers', 'waiters', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'type' => 'required|in:dine_in,takeaway,delivery',
            'table_id' => 'nullable|exists:restaurant_tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'delivery_address' => 'nullable|string',
            'guests_count' => 'nullable|integer|min:1|max:50',
            'notes' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'waiter_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['order_number'] = RestaurantOrder::generateOrderNumber();
        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['cashier_id'] = Auth::id();
        $validatedData['order_time'] = now();
        $validatedData['status'] = 'pending';

        $order = RestaurantOrder::create($validatedData);

        // Update table status if dine-in
        if ($order->type === 'dine_in' && $order->table_id) {
            $order->table->update(['status' => 'occupied']);
        }

        return redirect()->route('admin.restaurant.orders.show', $order)
            ->with('success', __('Order created successfully.'));
    }

    public function show(RestaurantOrder $order)
    {
        $order->load(['table', 'customer', 'waiter', 'cashier', 'orderItems.menuItem', 'orderItems.modifiers']);
        return view('admin.restaurant.orders.show', compact('order'));
    }

    public function edit(RestaurantOrder $order)
    {
        $tables = RestaurantTable::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where(function($query) use ($order) {
                $query->where('status', 'available')
                      ->orWhere('id', $order->table_id);
            })
            ->orderBy('number')
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $waiters = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['waiter', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.orders.form', compact('order', 'tables', 'customers', 'waiters', 'branches'));
    }

    public function update(Request $request, RestaurantOrder $order)
    {
        $validatedData = $request->validate([
            'type' => 'required|in:dine_in,takeaway,delivery',
            'table_id' => 'nullable|exists:restaurant_tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'delivery_address' => 'nullable|string',
            'guests_count' => 'nullable|integer|min:1|max:50',
            'notes' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'waiter_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $oldTableId = $order->table_id;
        $order->update($validatedData);

        // Update table statuses
        if ($oldTableId !== $order->table_id) {
            // Free old table
            if ($oldTableId) {
                RestaurantTable::find($oldTableId)->update(['status' => 'available']);
            }
            
            // Occupy new table
            if ($order->table_id && $order->type === 'dine_in') {
                $order->table->update(['status' => 'occupied']);
            }
        }

        return redirect()->route('admin.restaurant.orders.show', $order)
            ->with('success', __('Order updated successfully.'));
    }

    public function destroy(RestaurantOrder $order)
    {
        // Free table if occupied
        if ($order->table_id) {
            $order->table->update(['status' => 'available']);
        }

        $order->delete();

        return redirect()->route('admin.restaurant.orders.index')
            ->with('success', __('Order deleted successfully.'));
    }

    public function updateStatus(Request $request, RestaurantOrder $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,served,completed,cancelled'
        ]);

        $order->update(['status' => $request->status]);

        // Update timestamps based on status
        switch ($request->status) {
            case 'ready':
                $order->update(['ready_time' => now()]);
                break;
            case 'served':
                $order->update(['served_time' => now()]);
                break;
            case 'completed':
                if ($order->table_id) {
                    $order->table->update(['status' => 'available']);
                }
                break;
        }

        return response()->json([
            'success' => true,
            'message' => __('Order status updated successfully.'),
            'status' => $order->status,
            'status_color' => $order->status_color
        ]);
    }

    public function printKitchen(RestaurantOrder $order)
    {
        // Implementation for printing to kitchen printers
        return response()->json([
            'success' => true,
            'message' => __('Order sent to kitchen successfully.')
        ]);
    }

    public function printReceipt(RestaurantOrder $order)
    {
        // Implementation for printing receipt
        return response()->json([
            'success' => true,
            'message' => __('Receipt printed successfully.')
        ]);
    }
}
