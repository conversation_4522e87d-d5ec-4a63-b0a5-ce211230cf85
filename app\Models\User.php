<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens; // If using Sanctum for API authentication
use App\Models\Modules\Branches\Branch;
use App\Models\Role;
use App\Models\Permission;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        "name",
        "email",
        "password",
        "branch_id",
        "language",
        "is_active",
        "tenant_id", // إضافة حقل معرف المستأجر
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        "password",
        "remember_token",
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        "email_verified_at" => "datetime",
        "password" => "hashed",
        "is_active" => "boolean",
    ];

    /**
     * Get the branch that the user belongs to.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the tenant that the user belongs to.
     * This is used for users who are employees of a tenant.
     */
    public function tenant()
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the users that belong to this tenant.
     * This is used for users who are tenants themselves.
     */
    public function tenantUsers()
    {
        return $this->hasMany(User::class, 'tenant_id');
    }

    /**
     * The roles that belong to the user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, "role_user");
    }

    /**
     * Check if the user has a specific role.
     * @param string|array $role
     * @return bool
     */
    public function hasRole($role): bool
    {
        if (is_string($role)) {
            return $this->roles->contains("name", $role) || $this->roles->contains("slug", $role);
        }
        if (is_array($role)) {
            foreach ($role as $r) {
                if ($this->hasRole($r)) {
                    return true;
                }
            }
            return false;
        }
        return $this->roles->intersect(Role::whereIn("name", (array)$role)->orWhereIn("slug", (array)$role)->get())->isNotEmpty();
    }

    /**
     * العلاقة مع الصلاحيات المباشرة
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class);
    }

    /**
     * التحقق من وجود صلاحية معينة
     * @param string|array $permission
     * @return bool
     */
    public function hasPermissionTo($permission): bool
    {
        // التحقق من الصلاحيات المباشرة
        if (is_string($permission)) {
            if ($this->permissions->contains('name', $permission)) {
                return true;
            }
        } else {
            if ($this->permissions->whereIn('name', (array) $permission)->count()) {
                return true;
            }
        }

        // التحقق من صلاحيات الأدوار
        foreach ($this->roles as $role) {
            if (is_string($permission)) {
                if ($role->permissions->contains('name', $permission)) {
                    return true;
                }
            } else {
                if ($role->permissions->whereIn('name', (array) $permission)->count()) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * إضافة صلاحيات للمستخدم
     */
    public function givePermissionTo(...$permissions)
    {
        $permissions = collect($permissions)
            ->flatten()
            ->map(function ($permission) {
                if (is_string($permission)) {
                    return Permission::whereName($permission)->firstOrFail();
                }

                return $permission;
            });

        $this->permissions()->syncWithoutDetaching($permissions->pluck('id')->toArray());

        return $this;
    }

    /**
     * سحب صلاحيات من المستخدم
     */
    public function revokePermissionTo(...$permissions)
    {
        $permissions = collect($permissions)
            ->flatten()
            ->map(function ($permission) {
                if (is_string($permission)) {
                    return Permission::whereName($permission)->firstOrFail();
                }

                return $permission;
            });

        $this->permissions()->detach($permissions->pluck('id')->toArray());

        return $this;
    }

    /**
     * Check if the user is an admin.
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(["admin", "superadmin"]);
    }
}

