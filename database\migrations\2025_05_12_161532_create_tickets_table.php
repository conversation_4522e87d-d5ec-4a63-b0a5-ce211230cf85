<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create("tickets", function (Blueprint $table) {
            $table->id();
            $table->string("ticket_code")->unique()->comment("Unique identifier for the ticket, e.g., TKT-YYYYMMDD-XXXX");
            $table->string("subject");
            $table->longText("description");

            $table->foreignId("ticket_category_id")->constrained("ticket_categories")->onDelete("cascade");
            $table->foreignId("ticket_priority_id")->constrained("ticket_priorities")->onDelete("cascade");
            $table->foreignId("ticket_status_id")->constrained("ticket_statuses")->onDelete("cascade");
            
            $table->foreignId("branch_id")->nullable()->constrained("branches")->onDelete("set null"); // Branch associated with the ticket
            $table->foreignId("created_by_user_id")->comment("User who created the ticket (can be customer or internal staff)")->constrained("users")->onDelete("cascade");
            $table->foreignId("assigned_to_user_id")->nullable()->comment("Staff user assigned to handle the ticket")->constrained("users")->onDelete("set null");
            // If you have a separate customers table, you might add a customer_id foreign key here.
            // For now, assuming customers are also users or tickets are raised by internal users.

            $table->timestamp("resolved_at")->nullable();
            $table->timestamp("closed_at")->nullable();
            $table->foreignId("closed_by_user_id")->nullable()->constrained("users")->onDelete("set null");

            $table->string("source")->nullable()->comment("e.g., email, web_form, phone_call, chat");
            $table->ipAddress("submitter_ip")->nullable();
            $table->text("internal_notes")->nullable()->comment("Notes visible only to staff");

            $table->foreignId("updated_by_user_id")->nullable()->constrained("users")->onDelete("set null");
            $table->timestamps();
            $table->softDeletes(); // If you want to soft delete tickets
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("tickets");
    }
};

