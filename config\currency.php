<?php

return [
    /*
    |--------------------------------------------------------------------------
    | إعدادات العملة
    |--------------------------------------------------------------------------
    |
    | هذا الملف يحتوي على إعدادات العملة المستخدمة في النظام
    |
    */

    // العملة الافتراضية
    'default' => env('DEFAULT_CURRENCY', 'SAR'),

    // رمز العملة الافتراضي
    'default_symbol' => env('DEFAULT_CURRENCY_SYMBOL', '﷼'),

    // موضع رمز العملة (before/after)
    'symbol_position' => env('CURRENCY_SYMBOL_POSITION', 'after'),

    // فاصل الآلاف
    'thousands_separator' => env('CURRENCY_THOUSANDS_SEPARATOR', ','),

    // فاصل العشرية
    'decimal_separator' => env('CURRENCY_DECIMAL_SEPARATOR', '.'),

    // عدد الخانات العشرية
    'decimal_places' => env('CURRENCY_DECIMAL_PLACES', 2),

    // العملات المدعومة
    'supported_currencies' => [
        'SAR' => [
            'name' => 'ريال سعودي',
            'name_en' => 'Saudi Riyal',
            'symbol' => '<span class="saudi-riyal-new"><div class="horizontal-lines"><div class="line1"></div><div class="line2"></div><div class="line3"></div></div></span>', // الرمز الجديد للريال السعودي المعتمد من ساما
            'symbol_old' => 'ر.س',
            'symbol_graphic' => '<span class="riyal-css"><div class="horizontal-lines"><div class="line1"></div><div class="line2"></div><div class="line3"></div><div class="line4"></div><div class="line5"></div><div class="line6"></div></div></span>',
            'symbol_arabic' => '<span class="riyal-arabic"><div class="letter-raa"></div><div class="letter-yaa"></div><div class="letter-alif"></div><div class="letter-lam"></div></span>',
            'symbol_official' => '<span class="riyal-official"></span>',
            'symbol_sama' => '<span class="riyal-sama"><div class="horizontal-lines"><div class="line1"></div><div class="line2"></div><div class="line3"></div></div></span>',
            'symbol_new' => '<span class="saudi-riyal-new"><div class="horizontal-lines"><div class="line1"></div><div class="line2"></div><div class="line3"></div></div></span>', // الرمز الجديد الرسمي الصحيح
            'symbol_simple_new' => '<span class="riyal-simple-new"><div class="simple-lines"><div class="simple-line1"></div><div class="simple-line2"></div></div></span>', // النسخة المبسطة
            'symbol_svg' => '<span class="riyal-symbol"><svg viewBox="0 0 100 120"><rect x="15" y="5" width="12" height="75" fill="currentColor" transform="skew(-8, 0)"/><rect x="65" y="5" width="12" height="55" fill="currentColor" transform="skew(-8, 0)"/><rect x="10" y="35" width="75" height="10" fill="currentColor" transform="rotate(-3 47.5 40)"/><rect x="15" y="50" width="60" height="10" fill="currentColor" transform="rotate(-3 45 55)"/><rect x="20" y="65" width="45" height="10" fill="currentColor" transform="rotate(-3 42.5 70)"/><rect x="10" y="80" width="35" height="10" fill="currentColor" transform="rotate(-3 27.5 85)"/><rect x="55" y="80" width="30" height="10" fill="currentColor" transform="rotate(-3 70 85)"/><rect x="60" y="95" width="25" height="10" fill="currentColor" transform="rotate(-3 72.5 100)"/></svg></span>',
            'symbol_simple' => 'ر.س',
            'code' => 'SAR',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after', // before or after
        ],
        'USD' => [
            'name' => 'دولار أمريكي',
            'name_en' => 'US Dollar',
            'symbol' => '$',
            'code' => 'USD',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'before',
        ],
        'EUR' => [
            'name' => 'يورو',
            'name_en' => 'Euro',
            'symbol' => '€',
            'code' => 'EUR',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'before',
        ],
        'AED' => [
            'name' => 'درهم إماراتي',
            'name_en' => 'UAE Dirham',
            'symbol' => 'د.إ',
            'code' => 'AED',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'KWD' => [
            'name' => 'دينار كويتي',
            'name_en' => 'Kuwaiti Dinar',
            'symbol' => 'د.ك',
            'code' => 'KWD',
            'decimal_places' => 3,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'QAR' => [
            'name' => 'ريال قطري',
            'name_en' => 'Qatari Riyal',
            'symbol' => 'ر.ق',
            'code' => 'QAR',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'BHD' => [
            'name' => 'دينار بحريني',
            'name_en' => 'Bahraini Dinar',
            'symbol' => 'د.ب',
            'code' => 'BHD',
            'decimal_places' => 3,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'OMR' => [
            'name' => 'ريال عماني',
            'name_en' => 'Omani Rial',
            'symbol' => 'ر.ع',
            'code' => 'OMR',
            'decimal_places' => 3,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'JOD' => [
            'name' => 'دينار أردني',
            'name_en' => 'Jordanian Dinar',
            'symbol' => 'د.أ',
            'code' => 'JOD',
            'decimal_places' => 3,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'EGP' => [
            'name' => 'جنيه مصري',
            'name_en' => 'Egyptian Pound',
            'symbol' => 'ج.م',
            'code' => 'EGP',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
    ],

    // إعدادات التحويل (إذا كان هناك نظام تحويل عملات)
    'conversion' => [
        'enabled' => env('CURRENCY_CONVERSION_ENABLED', false),
        'api_key' => env('CURRENCY_CONVERSION_API_KEY', ''),
        'provider' => env('CURRENCY_CONVERSION_PROVIDER', 'fixer'), // fixer, exchangerate-api, etc.
        'cache_duration' => env('CURRENCY_CONVERSION_CACHE_DURATION', 3600), // seconds
    ],
];
