@if(empty($rootAccounts))
    <div class="empty-state text-center p-5">
        <i class="bi bi-folder2-open display-4 text-muted mb-3"></i>
        <h5>لا توجد حسابات</h5>
        <p>لم يتم إضافة أي حسابات بعد. يمكنك إضافة حسابات جديدة من خلال النقر على زر "إضافة حساب جديد".</p>
        <a href="{{ route('admin.accounts.create') }}" class="btn btn-primary mt-3">
            <i class="bi bi-plus-circle me-1"></i> إضافة حساب جديد
        </a>
    </div>
@else
    <div class="modern-accounts-tree">
        @foreach($accountTypes as $type)
            @if(isset($rootAccounts[$type->id]))
                <div class="account-type-card mb-4">
                    <div class="account-type-header" data-bs-toggle="collapse" data-bs-target="#type-{{ $type->id }}" aria-expanded="true">
                        <div class="d-flex align-items-center">
                            <div class="account-type-icon type-bg-{{ $type->slug }}">
                                @if($type->slug == 'assets')
                                    <i class="bi bi-cash-coin"></i>
                                @elseif($type->slug == 'liabilities')
                                    <i class="bi bi-credit-card"></i>
                                @elseif($type->slug == 'equity')
                                    <i class="bi bi-pie-chart"></i>
                                @elseif($type->slug == 'revenue')
                                    <i class="bi bi-graph-up-arrow"></i>
                                @elseif($type->slug == 'expenses')
                                    <i class="bi bi-graph-down-arrow"></i>
                                @else
                                    <i class="bi bi-journal-bookmark"></i>
                                @endif
                            </div>
                            <div class="account-type-info">
                                <h6 class="account-type-name mb-0">{{ $type->name_ar }}</h6>
                                <small class="text-muted">{{ $rootAccounts[$type->id]['accounts']->count() }} حساب</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            @php
                                // حساب إجمالي الرصيد لهذا النوع من الحسابات
                                $typeBalance = 0;
                                foreach($rootAccounts[$type->id]['accounts'] as $account) {
                                    $accountBalance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                                    $typeBalance += $accountBalance;

                                    // إضافة أرصدة الحسابات الفرعية
                                    if($account->children && $account->children->count() > 0) {
                                        foreach($account->children as $child) {
                                            $childBalance = ($child->opening_balance_debit ?? 0) - ($child->opening_balance_credit ?? 0);
                                            $typeBalance += $childBalance;
                                        }
                                    }
                                }
                                $balanceClass = $typeBalance < 0 ? 'text-danger' : 'text-success';
                            @endphp
                            <div class="account-type-balance {{ $balanceClass }} ms-3 text-start">
                                <span class="balance-amount">{{ number_format(abs($typeBalance), 2) }}</span>
                                <small>{{ $typeBalance < 0 ? 'دائن' : 'مدين' }}</small>
                            </div>
                            <i class="bi bi-chevron-down toggle-icon"></i>
                        </div>
                    </div>

                    <div class="collapse show account-type-content" id="type-{{ $type->id }}">
                        <div class="modern-tree-container">
                            <ul class="modern-tree">
                                @foreach($rootAccounts[$type->id]['accounts'] as $account)
                                    <li class="modern-tree-node">
                                        <div class="modern-tree-node-content @if($account->is_control_account) parent-node @endif">
                                            @if($account->children && $account->children->count() > 0)
                                                <span class="modern-tree-node-toggle tree-toggle" data-bs-toggle="collapse" data-bs-target="#account-{{ $account->id }}">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                            @else
                                                <span class="modern-tree-node-toggle" style="visibility: hidden;">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                            @endif

                                            <span class="modern-tree-node-icon">
                                                @if($account->is_control_account)
                                                    <i class="bi bi-folder2" style="color: #f6c23e;"></i>
                                                @else
                                                    <i class="bi bi-file-earmark-text" style="color: #4e73df;"></i>
                                                @endif
                                            </span>

                                            <div class="modern-tree-node-details">
                                                <div class="modern-tree-node-text">
                                                    <span class="modern-tree-node-code">{{ $account->code }}</span>
                                                    <span class="modern-tree-node-name">
                                                        {{ $account->name_ar }}
                                                    </span>
                                                </div>

                                                @php
                                                    $balance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                                                    $balanceClass = $balance < 0 ? 'negative' : '';
                                                @endphp

                                                <span class="modern-tree-node-balance {{ $balanceClass }}">
                                                    {{ number_format(abs($balance), 2) }}
                                                    <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
                                                </span>
                                            </div>

                                            <div class="modern-tree-node-actions">
                                                <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-light" title="عرض">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-light" title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </div>

                                        @if($account->children && $account->children->count() > 0)
                                            <div class="collapse show modern-tree-children" id="account-{{ $account->id }}">
                                                <ul class="modern-tree">
                                                    @foreach($account->children as $child)
                                                        @include('admin.accounts._modern_tree_node', ['account' => $child])
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
@endif
