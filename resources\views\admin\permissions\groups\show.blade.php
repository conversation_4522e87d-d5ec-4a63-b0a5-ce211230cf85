@extends('layouts.admin')

@section('title', 'تفاصيل مجموعة الصلاحيات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل مجموعة الصلاحيات: {{ $group->display_name }}</h3>
                    <div>
                        <a href="{{ route('admin.permissions_system.groups.edit', $group) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.permissions_system.groups.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات المجموعة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $group->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الاسم المعروض</th>
                                            <td>{{ $group->display_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الوصف</th>
                                            <td>{{ $group->description ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>الترتيب</th>
                                            <td>{{ $group->order }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $group->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $group->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>الصلاحيات في هذه المجموعة</h5>
                                    <a href="{{ route('admin.permissions_system.permissions.create') }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-plus-circle"></i> إضافة صلاحية
                                    </a>
                                </div>
                                <div class="card-body">
                                    @if($group->permissions->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>الاسم</th>
                                                        <th>الاسم المعروض</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($group->permissions as $permission)
                                                        <tr>
                                                            <td>{{ $permission->id }}</td>
                                                            <td>{{ $permission->name }}</td>
                                                            <td>{{ $permission->display_name }}</td>
                                                            <td>
                                                                <div class="btn-group" role="group">
                                                                    <a href="{{ route('admin.permissions_system.permissions.show', $permission) }}" class="btn btn-sm btn-info">
                                                                        <i class="bi bi-eye"></i>
                                                                    </a>
                                                                    <a href="{{ route('admin.permissions_system.permissions.edit', $permission) }}" class="btn btn-sm btn-warning">
                                                                        <i class="bi bi-pencil"></i>
                                                                    </a>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا توجد صلاحيات في هذه المجموعة</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
