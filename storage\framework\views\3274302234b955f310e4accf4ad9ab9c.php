<?php $__env->startSection('styles'); ?>
<style>
    .login-container {
        max-width: 450px;
        margin: 50px auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }
    .login-logo {
        text-align: center;
        margin-bottom: 30px;
    }
    .login-logo i {
        font-size: 54px;
        color: #0d6efd;
        margin-bottom: 15px;
    }
    .login-logo h3 {
        font-weight: 600;
        margin-bottom: 5px;
    }
    .btn-login {
        background-color: #0d6efd;
        color: white;
        font-weight: 600;
        padding: 10px;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    .btn-login:hover {
        background-color: #0b5ed7;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .input-group-text {
        background-color: #f8f9fa;
        border-left: none;
    }
    .form-control {
        border-right: none;
    }
    .form-control:focus {
        box-shadow: none;
        border-color: #ced4da;
    }
    .invalid-feedback {
        font-size: 80%;
        color: #dc3545;
        margin-top: 5px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="login-container">
        <div class="login-logo">
            <i class="fas fa-building"></i>
            <h3>نظام المحاسبة</h3>
            <p class="text-muted">لوحة تحكم الإدارة</p>
        </div>

        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if(session('success')): ?>
            <div class="alert alert-success">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

            <form action="<?php echo e(route('login')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-3">
                    <label for="login" class="form-label">البريد الإلكتروني أو اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="login" name="login" value="<?php echo e(old('login')); ?>" required autofocus>
                    </div>
                    <?php $__errorArgs = ['login'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback d-block" role="alert">
                            <strong><?php echo e($message); ?></strong>
                        </span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback d-block" role="alert">
                            <strong><?php echo e($message); ?></strong>
                        </span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">تذكرني</label>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-login">تسجيل الدخول</button>
                </div>

                <div class="mt-4 text-center">
                    <p>ليس لديك حساب؟ <a href="<?php echo e(route('tenant.register')); ?>" class="text-primary fw-bold">سجل الآن</a></p>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/auth/login.blade.php ENDPATH**/ ?>