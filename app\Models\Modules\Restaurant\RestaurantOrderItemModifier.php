<?php

namespace App\Models\Modules\Restaurant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantOrderItemModifier extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_item_id',
        'modifier_id',
        'modifier_option_id',
        'price_adjustment',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
    ];

    /**
     * Get the order item that owns the modifier.
     */
    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(RestaurantOrderItem::class, 'order_item_id');
    }

    /**
     * Get the modifier.
     */
    public function modifier(): BelongsTo
    {
        return $this->belongsTo(MenuItemModifier::class, 'modifier_id');
    }

    /**
     * Get the modifier option.
     */
    public function modifierOption(): BelongsTo
    {
        return $this->belongsTo(MenuItemModifierOption::class, 'modifier_option_id');
    }
}
