<li class="modern-tree-node">
    <div class="modern-tree-node-content @if($account->is_control_account) parent-node @endif">
        @if($account->children && $account->children->count() > 0)
            <span class="modern-tree-node-toggle tree-toggle" data-bs-toggle="collapse" data-bs-target="#account-{{ $account->id }}">
                <i class="bi bi-chevron-down"></i>
            </span>
        @else
            <span class="modern-tree-node-toggle" style="visibility: hidden;">
                <i class="bi bi-chevron-down"></i>
            </span>
        @endif

        <span class="modern-tree-node-icon">
            @if($account->is_control_account)
                <i class="bi bi-folder2" style="color: #f6c23e;"></i>
            @else
                <i class="bi bi-file-earmark-text" style="color: #4e73df;"></i>
            @endif
        </span>

        <div class="modern-tree-node-details">
            <div class="modern-tree-node-text">
                <span class="modern-tree-node-code">{{ $account->code }}</span>
                <span class="modern-tree-node-name">
                    {{ $account->name_ar }}
                </span>
            </div>

            @php
                $balance = ($account->opening_balance_debit ?? 0) - ($account->opening_balance_credit ?? 0);
                $balanceClass = $balance < 0 ? 'negative' : '';
            @endphp

            <span class="modern-tree-node-balance {{ $balanceClass }}">
                {{ number_format(abs($balance), 2) }}
                <small>{{ $balance < 0 ? 'دائن' : 'مدين' }}</small>
            </span>
        </div>

        <div class="modern-tree-node-actions">
            <a href="{{ route('admin.accounts.show', $account->id) }}" class="btn btn-sm btn-light" title="عرض">
                <i class="bi bi-eye"></i>
            </a>
            <a href="{{ route('admin.accounts.edit', $account->id) }}" class="btn btn-sm btn-light" title="تعديل">
                <i class="bi bi-pencil"></i>
            </a>
        </div>
    </div>

    @if($account->children && $account->children->count() > 0)
        <div class="collapse show modern-tree-children" id="account-{{ $account->id }}">
            <ul class="modern-tree">
                @foreach($account->children as $child)
                    @include('admin.accounts._modern_tree_node', ['account' => $child])
                @endforeach
            </ul>
        </div>
    @endif
</li>
