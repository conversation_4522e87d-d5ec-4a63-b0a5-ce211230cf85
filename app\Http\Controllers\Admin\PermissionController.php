<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use App\Models\PermissionGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $permissions = Permission::with('group')->latest()->paginate(10);
        return view("admin.permissions.index", compact("permissions"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissionGroups = PermissionGroup::orderBy('order')->get();
        return view("admin.permissions.form", compact('permissionGroups'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "permission_group_id" => "required|exists:permission_groups,id",
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:permissions,slug",
            "module" => "required|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
        ]);

        Permission::create($validatedData);

        return redirect()->route("admin.permissions.index")->with("success", __("Permission created successfully."));
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        $permission->load("roles"); // Eager load roles that have this permission
        return view("admin.permissions.show", compact("permission"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permission $permission)
    {
        $permissionGroups = PermissionGroup::orderBy('order')->get();
        return view("admin.permissions.form", compact("permission", "permissionGroups"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $validatedData = $request->validate([
            "permission_group_id" => "required|exists:permission_groups,id",
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:permissions,slug," . $permission->id,
            "module" => "required|string|max:255",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
        ]);

        $permission->update($validatedData);

        return redirect()->route("admin.permissions.index")->with("success", __("Permission updated successfully."));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission)
    {
        // Check if the permission is assigned to any roles before deleting
        if ($permission->roles()->count() > 0) {
            return redirect()->route("admin.permissions.index")->with("error", __("Cannot delete this permission as it is assigned to one or more roles."));
        }

        try {
            $permission->delete();
            return redirect()->route("admin.permissions.index")->with("success", __("Permission deleted successfully."));
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.permissions.index")->with("error", __("Could not delete permission. A database error occurred."));
        }
    }
}

