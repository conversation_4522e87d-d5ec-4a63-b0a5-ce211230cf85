@extends('layouts.admin')

@section('title', 'تعديل عملية التصنيع')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل عملية التصنيع</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.manufacturing.operations.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <a href="{{ route('admin.manufacturing.operations.show', 1) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.manufacturing.operations.update', 1) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_number">رقم العملية</label>
                                    <input type="text" class="form-control" id="operation_number" name="operation_number" value="MFG-2024-001" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_name">اسم العملية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="operation_name" name="operation_name" value="تجميع أجهزة لابتوب" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operation_type">نوع العملية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="operation_type" name="operation_type" required>
                                        <option value="">اختر نوع العملية</option>
                                        <option value="assembly" selected>تجميع</option>
                                        <option value="production">إنتاج</option>
                                        <option value="packaging">تعبئة وتغليف</option>
                                        <option value="quality_check">فحص جودة</option>
                                        <option value="maintenance">صيانة</option>
                                        <option value="testing">اختبار</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">المنتج <span class="text-danger">*</span></label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">اختر المنتج</option>
                                        <option value="1" selected>لابتوب ديل XPS 13</option>
                                        <option value="2">ماوس لاسلكي لوجيتك</option>
                                        <option value="3">كيبورد ميكانيكي</option>
                                        <option value="4">شاشة سامسونج 24 بوصة</option>
                                        <option value="5">سماعات بلوتوث</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="required_quantity">الكمية المطلوبة <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="required_quantity" name="required_quantity" value="50" required min="1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="produced_quantity">الكمية المنتجة</label>
                                    <input type="number" class="form-control" id="produced_quantity" name="produced_quantity" value="35" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="unit_cost">تكلفة الوحدة</label>
                                    <input type="number" class="form-control" id="unit_cost" name="unit_cost" value="3500" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="2024-03-10" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">تاريخ الانتهاء المتوقع <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="2024-03-20" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="responsible_person">المسؤول عن العملية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="responsible_person" name="responsible_person" required>
                                        <option value="">اختر المسؤول</option>
                                        <option value="1" selected>أحمد محمد علي</option>
                                        <option value="2">فاطمة أحمد خالد</option>
                                        <option value="3">محمد علي حسن</option>
                                        <option value="4">سارة خالد محمد</option>
                                        <option value="5">عبدالله أحمد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">حالة العملية</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="planned">مخطط</option>
                                        <option value="in_progress" selected>قيد التنفيذ</option>
                                        <option value="completed">مكتمل</option>
                                        <option value="on_hold">معلق</option>
                                        <option value="cancelled">ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">وصف العملية</label>
                                    <textarea class="form-control" id="description" name="description" rows="4">عملية تجميع شاملة لأجهزة لابتوب ديل XPS 13 تتضمن تركيب جميع المكونات الأساسية مثل اللوحة الأم، المعالج، الذاكرة، وحدة التخزين، والشاشة.</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- المواد المطلوبة -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5>المواد المطلوبة للعملية</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="materialsTable">
                                        <thead>
                                            <tr>
                                                <th>المادة</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>الوحدة</th>
                                                <th>التكلفة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="materials[0][material_id]">
                                                        <option value="">اختر المادة</option>
                                                        <option value="1" selected>لوحة أم Intel</option>
                                                        <option value="2">معالج Intel Core i7</option>
                                                        <option value="3">ذاكرة RAM 16GB</option>
                                                        <option value="4">قرص SSD 512GB</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[0][quantity]" value="50" min="0" step="0.01">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="materials[0][unit]">
                                                        <option value="piece" selected>قطعة</option>
                                                        <option value="kg">كيلوجرام</option>
                                                        <option value="meter">متر</option>
                                                        <option value="liter">لتر</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[0][cost]" value="75000" step="0.01" min="0">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <select class="form-control" name="materials[1][material_id]">
                                                        <option value="">اختر المادة</option>
                                                        <option value="1">لوحة أم Intel</option>
                                                        <option value="2" selected>معالج Intel Core i7</option>
                                                        <option value="3">ذاكرة RAM 16GB</option>
                                                        <option value="4">قرص SSD 512GB</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[1][quantity]" value="50" min="0" step="0.01">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="materials[1][unit]">
                                                        <option value="piece" selected>قطعة</option>
                                                        <option value="kg">كيلوجرام</option>
                                                        <option value="meter">متر</option>
                                                        <option value="liter">لتر</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" name="materials[1][cost]" value="50000" step="0.01" min="0">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addMaterialRow()">
                                    <i class="fas fa-plus"></i> إضافة مادة
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_cost">التكلفة الإجمالية</label>
                                    <input type="number" class="form-control" id="total_cost" name="total_cost" value="175000" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">الأولوية</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low">منخفضة</option>
                                        <option value="medium">متوسطة</option>
                                        <option value="high" selected>عالية</option>
                                        <option value="urgent">عاجلة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('admin.manufacturing.operations.show', 1) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let materialRowIndex = 2;

function addMaterialRow() {
    const tbody = document.querySelector('#materialsTable tbody');
    const newRow = `
        <tr>
            <td>
                <select class="form-control" name="materials[${materialRowIndex}][material_id]">
                    <option value="">اختر المادة</option>
                    <option value="1">لوحة أم Intel</option>
                    <option value="2">معالج Intel Core i7</option>
                    <option value="3">ذاكرة RAM 16GB</option>
                    <option value="4">قرص SSD 512GB</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="materials[${materialRowIndex}][quantity]" min="0" step="0.01">
            </td>
            <td>
                <select class="form-control" name="materials[${materialRowIndex}][unit]">
                    <option value="piece">قطعة</option>
                    <option value="kg">كيلوجرام</option>
                    <option value="meter">متر</option>
                    <option value="liter">لتر</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control" name="materials[${materialRowIndex}][cost]" step="0.01" min="0">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', newRow);
    materialRowIndex++;
}

function removeMaterialRow(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotalCost();
}

function calculateTotalCost() {
    let total = 0;
    const costInputs = document.querySelectorAll('input[name*="[cost]"]');
    const unitCost = parseFloat(document.getElementById('unit_cost').value) || 0;
    const requiredQuantity = parseFloat(document.getElementById('required_quantity').value) || 0;
    
    // حساب تكلفة المواد
    costInputs.forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    
    // إضافة تكلفة الإنتاج
    total += (unitCost * requiredQuantity);
    
    document.getElementById('total_cost').value = total.toFixed(2);
}

// حساب التكلفة عند تغيير القيم
document.addEventListener('input', function(e) {
    if (e.target.name && (e.target.name.includes('[cost]') || e.target.id === 'unit_cost' || e.target.id === 'required_quantity')) {
        calculateTotalCost();
    }
});

// حساب التكلفة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotalCost();
});
</script>
@endpush
