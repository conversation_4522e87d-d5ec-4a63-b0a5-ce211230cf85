<?php

namespace App\Models\Modules\GeneralLedger;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FiscalYear extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'start_date',
        'end_date',
        'is_active',
        'is_locked',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'is_locked' => 'boolean',
    ];

    /**
     * Get the journal entries for this fiscal year.
     */
    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    /**
     * Check if the fiscal year is currently active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->is_active;
    }

    /**
     * Check if the fiscal year is locked for editing.
     *
     * @return bool
     */
    public function isLocked()
    {
        return $this->is_locked;
    }

    /**
     * Get the formatted start date.
     *
     * @return string
     */
    public function getFormattedStartDateAttribute()
    {
        return $this->start_date->format('Y-m-d');
    }

    /**
     * Get the formatted end date.
     *
     * @return string
     */
    public function getFormattedEndDateAttribute()
    {
        return $this->end_date->format('Y-m-d');
    }

    /**
     * Get the duration of the fiscal year in months.
     *
     * @return int
     */
    public function getDurationInMonthsAttribute()
    {
        return $this->start_date->diffInMonths($this->end_date) + 1;
    }
}
