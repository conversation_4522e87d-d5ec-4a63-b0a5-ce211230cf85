@extends('layouts.admin')

@section('title', 'تفاصيل الدور')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل الدور: {{ $role->display_name ?? $role->name }}</h3>
                    <div>
                        <a href="{{ route('admin.permissions_system.roles.edit', $role) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.permissions_system.roles.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات الدور</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $role->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الاسم المعروض</th>
                                            <td>{{ $role->display_name ?? $role->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الوصف</th>
                                            <td>{{ $role->description ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>نظامي</th>
                                            <td>
                                                @if($role->is_system)
                                                    <span class="badge bg-success">نعم</span>
                                                @else
                                                    <span class="badge bg-secondary">لا</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>عدد الصلاحيات</th>
                                            <td>{{ $role->permissions->count() }}</td>
                                        </tr>
                                        <tr>
                                            <th>عدد المستخدمين</th>
                                            <td>{{ $role->users->count() }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $role->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $role->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>المستخدمون بهذا الدور</h5>
                                </div>
                                <div class="card-body">
                                    @if($role->users->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>الاسم</th>
                                                        <th>البريد الإلكتروني</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($role->users as $user)
                                                        <tr>
                                                            <td>{{ $user->id }}</td>
                                                            <td>{{ $user->name }}</td>
                                                            <td>{{ $user->email }}</td>
                                                            <td>
                                                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-info">
                                                                    <i class="bi bi-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-center">لا يوجد مستخدمون بهذا الدور</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>الصلاحيات</h5>
                                </div>
                                <div class="card-body">
                                    @if($role->permissions->count() > 0)
                                        <div class="row">
                                            @foreach($role->permissions as $permission)
                                                <div class="col-md-3 mb-2">
                                                    <span class="badge bg-primary">{{ $permission->display_name ?? $permission->name }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <p class="text-center">لا توجد صلاحيات لهذا الدور</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

