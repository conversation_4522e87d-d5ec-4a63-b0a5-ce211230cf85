<?php

if (!function_exists('getAccountTypeIcon')) {
    /**
     * Get the icon for an account type
     *
     * @param string $slug
     * @return string
     */
    function getAccountTypeIcon($slug)
    {
        $icons = [
            'assets' => 'landmark',
            'liabilities' => 'hand-holding-usd',
            'equity' => 'balance-scale',
            'revenue' => 'chart-line',
            'expenses' => 'shopping-cart',
            'closing' => 'file-invoice-dollar'
        ];

        return $icons[$slug] ?? 'folder';
    }
}

if (!function_exists('currency_format')) {
    /**
     * تنسيق المبلغ مع رمز العملة
     *
     * @param float $amount المبلغ
     * @param string|null $currency كود العملة
     * @param bool $showSymbol إظهار رمز العملة
     * @return string
     */
    function currency_format($amount, $currency = null, $showSymbol = true)
    {
        return \App\Helpers\CurrencyHelper::format($amount, $currency, $showSymbol);
    }
}

if (!function_exists('currency_sar')) {
    /**
     * تنسيق المبلغ بالريال السعودي
     *
     * @param float $amount المبلغ
     * @param bool $showSymbol إظهار رمز العملة
     * @return string
     */
    function currency_sar($amount, $showSymbol = true)
    {
        return \App\Helpers\CurrencyHelper::formatSAR($amount, $showSymbol);
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * الحصول على رمز العملة
     *
     * @param string|null $currency كود العملة
     * @return string
     */
    function currency_symbol($currency = null)
    {
        return \App\Helpers\CurrencyHelper::getSymbol($currency);
    }
}

if (!function_exists('currency_name')) {
    /**
     * الحصول على اسم العملة
     *
     * @param string|null $currency كود العملة
     * @param string $locale اللغة
     * @return string
     */
    function currency_name($currency = null, $locale = 'ar')
    {
        return \App\Helpers\CurrencyHelper::getName($currency, $locale);
    }
}
