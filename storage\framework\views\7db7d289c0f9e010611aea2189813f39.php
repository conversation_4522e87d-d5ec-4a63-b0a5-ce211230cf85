<?php $__env->startSection('title', isset($subscriptionPlan) ? 'تعديل باقة اشتراك' : 'إضافة باقة اشتراك جديدة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo e(isset($subscriptionPlan) ? 'تعديل باقة اشتراك' : 'إضافة باقة اشتراك جديدة'); ?></h3>
                </div>
                <div class="card-body">
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(isset($subscriptionPlan) ? route('admin.super_admin.subscription_plans.update', $subscriptionPlan) : route('admin.super_admin.subscription_plans.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if(isset($subscriptionPlan)): ?>
                            <?php echo method_field('PUT'); ?>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="mb-3">معلومات الباقة الأساسية</h4>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الباقة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name', $subscriptionPlan->name ?? '')); ?>" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="code" class="form-label">كود الباقة</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="code" name="code" value="<?php echo e(old('code', $subscriptionPlan->code ?? '')); ?>">
                                    <small class="form-text text-muted">سيتم إنشاء كود تلقائي إذا تركت هذا الحقل فارغاً</small>
                                    <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف الباقة</label>
                                    <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description" rows="3"><?php echo e(old('description', $subscriptionPlan->description ?? '')); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="price" class="form-label">سعر الباقة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="price" name="price" value="<?php echo e(old('price', $subscriptionPlan->price ?? '')); ?>" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="billing_cycle" class="form-label">دورة الفوترة <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['billing_cycle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="billing_cycle" name="billing_cycle" required>
                                        <option value="monthly" <?php echo e((old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'monthly') ? 'selected' : ''); ?>>شهري</option>
                                        <option value="quarterly" <?php echo e((old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'quarterly') ? 'selected' : ''); ?>>ربع سنوي</option>
                                        <option value="semi_annually" <?php echo e((old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'semi_annually') ? 'selected' : ''); ?>>نصف سنوي</option>
                                        <option value="annually" <?php echo e((old('billing_cycle', $subscriptionPlan->billing_cycle ?? '') == 'annually') ? 'selected' : ''); ?>>سنوي</option>
                                    </select>
                                    <?php $__errorArgs = ['billing_cycle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="is_active" class="form-check-label">حالة الباقة</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e((old('is_active', $subscriptionPlan->is_active ?? true)) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">نشط</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h4 class="mb-3">حدود وميزات الباقة</h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_users" class="form-label">الحد الأقصى للمستخدمين <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['max_users'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="max_users" name="max_users" value="<?php echo e(old('max_users', $subscriptionPlan->max_users ?? 1)); ?>" min="1" required>
                                            <?php $__errorArgs = ['max_users'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max_branches" class="form-label">الحد الأقصى للفروع <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['max_branches'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="max_branches" name="max_branches" value="<?php echo e(old('max_branches', $subscriptionPlan->max_branches ?? 1)); ?>" min="1" required>
                                            <?php $__errorArgs = ['max_branches'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="monthly_invoice_limit" class="form-label">حد الفواتير الشهري <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['monthly_invoice_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="monthly_invoice_limit" name="monthly_invoice_limit" value="<?php echo e(old('monthly_invoice_limit', $invoiceLimit->monthly_invoice_limit ?? 100)); ?>" min="1" required>
                                            <?php $__errorArgs = ['monthly_invoice_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="total_invoice_limit" class="form-label">حد الفواتير الإجمالي</label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['total_invoice_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="total_invoice_limit" name="total_invoice_limit" value="<?php echo e(old('total_invoice_limit', $invoiceLimit->total_invoice_limit ?? '')); ?>" min="0">
                                            <small class="form-text text-muted">اتركه فارغاً لعدم وجود حد</small>
                                            <?php $__errorArgs = ['total_invoice_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="storage_space_gb" class="form-label">مساحة التخزين (جيجابايت) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['storage_space_gb'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="storage_space_gb" name="storage_space_gb" value="<?php echo e(old('storage_space_gb', $subscriptionPlan->storage_space_gb ?? 1)); ?>" min="1" required>
                                    <?php $__errorArgs = ['storage_space_gb'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <h5 class="mt-4 mb-3">الوحدات المتاحة</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_pos" name="has_pos" value="1" <?php echo e((old('has_pos', $subscriptionPlan->has_pos ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_pos">نقاط البيع</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_inventory" name="has_inventory" value="1" <?php echo e((old('has_inventory', $subscriptionPlan->has_inventory ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_inventory">إدارة المخزون</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_accounting" name="has_accounting" value="1" <?php echo e((old('has_accounting', $subscriptionPlan->has_accounting ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_accounting">المحاسبة</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_manufacturing" name="has_manufacturing" value="1" <?php echo e((old('has_manufacturing', $subscriptionPlan->has_manufacturing ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_manufacturing">التصنيع</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_hr" name="has_hr" value="1" <?php echo e((old('has_hr', $subscriptionPlan->has_hr ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_hr">الموارد البشرية</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_crm" name="has_crm" value="1" <?php echo e((old('has_crm', $subscriptionPlan->has_crm ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_crm">إدارة علاقات العملاء</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_purchases" name="has_purchases" value="1" <?php echo e((old('has_purchases', $subscriptionPlan->has_purchases ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_purchases">المشتريات</label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_sales" name="has_sales" value="1" <?php echo e((old('has_sales', $subscriptionPlan->has_sales ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_sales">المبيعات</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_reports" name="has_reports" value="1" <?php echo e((old('has_reports', $subscriptionPlan->has_reports ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_reports">التقارير</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="has_api_access" name="has_api_access" value="1" <?php echo e((old('has_api_access', $subscriptionPlan->has_api_access ?? false)) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="has_api_access">الوصول إلى API</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="<?php echo e(route('admin.super_admin.subscription_plans.index')); ?>" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary"><?php echo e(isset($subscriptionPlan) ? 'تحديث الباقة' : 'إنشاء الباقة'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/super_admin/subscription_plans/form.blade.php ENDPATH**/ ?>