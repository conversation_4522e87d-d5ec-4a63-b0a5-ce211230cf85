@extends('layouts.admin')

@section('title', 'إدارة الصلاحيات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">الصلاحيات</h3>
                    <a href="{{ route('admin.permissions_system.permissions.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة صلاحية جديدة
                    </a>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table id="permissions-table" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>الاسم</th>
                                    <th>المعرف</th>
                                    <th>المجموعة</th>
                                    <th>الوصف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($permissions as $permission)
                                    <tr>
                                        <td>{{ $permission->id }}</td>
                                        <td>{{ $permission->display_name ?? $permission->name }}</td>
                                        <td>{{ $permission->slug }}</td>
                                        <td>{{ $permission->group_name ?? '-' }}</td>
                                        <td>{{ $permission->description ?? '-' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.permissions_system.permissions.show', $permission->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.permissions_system.permissions.edit', $permission->id) }}" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route('admin.permissions_system.permissions.destroy', $permission->id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الصلاحية؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد صلاحيات مسجلة</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // انتظار تحميل الصفحة بالكامل
        setTimeout(function() {
            // التحقق من وجود الجدول
            var table = $('#permissions-table');
            if (table.length) {
                // التحقق من وجود صفوف في الجدول
                var rows = table.find('tbody tr');

                // إذا كان هناك صف واحد فقط ويحتوي على "لا توجد صلاحيات"، لا نطبق DataTables
                if (rows.length === 1 && rows.first().find('td').attr('colspan')) {
                    console.log('لا توجد بيانات لعرضها في جدول الصلاحيات');
                    return;
                }

                // إذا كان هناك بيانات، طبق DataTables
                if (rows.length > 0) {
                    try {
                        table.DataTable({
                            "language": {
                                "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
                            },
                            "paging": true,
                            "searching": true,
                            "ordering": true,
                            "info": true,
                            "autoWidth": false,
                            "responsive": true,
                            "columnDefs": [
                                { "orderable": false, "targets": [5] } // تعطيل الترتيب للعمود الإجراءات (العمود الأخير)
                            ],
                            "order": [[ 0, "desc" ]], // ترتيب حسب ID تنازلي
                            "destroy": true // السماح بإعادة التهيئة
                        });
                        console.log('تم تحميل DataTables للصلاحيات بنجاح');
                    } catch (error) {
                        console.error('خطأ في تحميل DataTables للصلاحيات:', error);
                    }
                }
            }
        }, 200);
    });
</script>
@endpush
