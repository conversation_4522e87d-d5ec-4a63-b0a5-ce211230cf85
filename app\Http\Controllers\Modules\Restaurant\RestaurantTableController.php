<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\RestaurantTable;
use App\Models\Modules\Restaurant\RestaurantArea;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RestaurantTableController extends Controller
{
    public function index()
    {
        $tables = RestaurantTable::with(['area', 'branch', 'currentOrder'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->orderBy('number')
            ->paginate(20);

        return view('admin.restaurant.tables.index', compact('tables'));
    }

    public function create()
    {
        $areas = RestaurantArea::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.tables.form', compact('areas', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'required|string|max:50|unique:restaurant_tables,number',
            'area_id' => 'nullable|exists:restaurant_areas,id',
            'capacity' => 'required|integer|min:1|max:50',
            'status' => 'required|in:available,occupied,reserved,maintenance',
            'x_position' => 'nullable|numeric',
            'y_position' => 'nullable|numeric',
            'shape' => 'nullable|string|max:20',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');

        RestaurantTable::create($validatedData);

        return redirect()->route('admin.restaurant.tables.index')
            ->with('success', __('Restaurant table created successfully.'));
    }

    public function show(RestaurantTable $table)
    {
        $table->load(['area', 'branch', 'orders' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.restaurant.tables.show', compact('table'));
    }

    public function edit(RestaurantTable $table)
    {
        $areas = RestaurantArea::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.restaurant.tables.form', compact('table', 'areas', 'branches'));
    }

    public function update(Request $request, RestaurantTable $table)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'required|string|max:50|unique:restaurant_tables,number,' . $table->id,
            'area_id' => 'nullable|exists:restaurant_areas,id',
            'capacity' => 'required|integer|min:1|max:50',
            'status' => 'required|in:available,occupied,reserved,maintenance',
            'x_position' => 'nullable|numeric',
            'y_position' => 'nullable|numeric',
            'shape' => 'nullable|string|max:20',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_active'] = $request->has('is_active');

        $table->update($validatedData);

        return redirect()->route('admin.restaurant.tables.index')
            ->with('success', __('Restaurant table updated successfully.'));
    }

    public function destroy(RestaurantTable $table)
    {
        // Check if table has active orders
        if ($table->orders()->active()->count() > 0) {
            return redirect()->route('admin.restaurant.tables.index')
                ->with('error', __('Cannot delete table that has active orders.'));
        }

        $table->delete();

        return redirect()->route('admin.restaurant.tables.index')
            ->with('success', __('Restaurant table deleted successfully.'));
    }

    public function updateStatus(Request $request, RestaurantTable $table)
    {
        $request->validate([
            'status' => 'required|in:available,occupied,reserved,maintenance'
        ]);

        $table->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => __('Table status updated successfully.'),
            'status' => $table->status,
            'status_color' => $table->status_color
        ]);
    }

    public function floorPlan()
    {
        $areas = RestaurantArea::with(['activeTables'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->active()
            ->ordered()
            ->get();

        return view('admin.restaurant.tables.floor-plan', compact('areas'));
    }
}
