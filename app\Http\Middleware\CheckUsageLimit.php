<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Subscription\TenantSubscription;

class CheckUsageLimit
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $resource)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $tenantId = $user->tenant_id ?? $user->id;

        // Get active subscription
        $subscription = TenantSubscription::where('tenant_id', $tenantId)
            ->where('status', 'active')
            ->orWhere('status', 'trial')
            ->with('subscriptionPlan')
            ->first();

        if (!$subscription) {
            return redirect()->route('subscription.plans')
                ->with('error', 'يجب عليك اختيار باقة اشتراك للمتابعة.');
        }

        // Check if limit is exceeded for creation operations
        if ($request->isMethod('POST') && $subscription->isLimitExceeded($resource)) {
            $limit = $subscription->getLimit($resource);
            $resourceName = $this->getResourceName($resource);
            
            return redirect()->back()
                ->with('error', "لقد وصلت إلى الحد الأقصى المسموح به من {$resourceName} ({$limit}). يرجى ترقية باقتك للمتابعة.");
        }

        return $next($request);
    }

    /**
     * Get resource name in Arabic.
     */
    private function getResourceName(string $resource): string
    {
        return match($resource) {
            'branches' => 'الفروع',
            'users' => 'المستخدمين',
            'customers' => 'العملاء',
            'products' => 'المنتجات',
            'invoices_per_month' => 'الفواتير الشهرية',
            'sales_representatives' => 'المناديب',
            default => $resource
        };
    }
}
