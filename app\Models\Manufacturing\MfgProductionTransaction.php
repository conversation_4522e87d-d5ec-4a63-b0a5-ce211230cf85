<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
// use App\Models\Inventory\Location as InventoryLocation; // If InventoryLocation model exists

class MfgProductionTransaction extends Model
{
    use HasFactory;

    protected $table = 'mfg_production_transactions';

    protected $fillable = [
        'mfg_work_order_id',
        'mfg_product_id',
        'mfg_operation_id',
        'mfg_work_center_id',
        'transaction_type',
        'quantity',
        'unit_of_measure',
        'transaction_date',
        'employee_id',
        'cost',
        'inventory_location_id_from',
        'inventory_location_id_to',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
        'transaction_date' => 'datetime',
        'cost' => 'decimal:4',
    ];

    /**
     * Get the work order this transaction belongs to.
     */
    public function mfgWorkOrder()
    {
        return $this->belongsTo(MfgWorkOrder::class, 'mfg_work_order_id');
    }

    /**
     * Get the product involved in this transaction.
     */
    public function mfgProduct()
    {
        return $this->belongsTo(MfgProduct::class, 'mfg_product_id');
    }

    /**
     * Get the operation related to this transaction (optional).
     */
    public function mfgOperation()
    {
        return $this->belongsTo(MfgOperation::class, 'mfg_operation_id');
    }

    /**
     * Get the work center where this transaction occurred (optional).
     */
    public function mfgWorkCenter()
    {
        return $this->belongsTo(MfgWorkCenter::class, 'mfg_work_center_id');
    }

    // /**
    //  * Get the employee who performed the transaction (if applicable).
    //  */
    // public function employee()
    // {
    //     return $this->belongsTo(Employee::class, 'employee_id'); // Assuming Employee model exists
    // }

    // /**
    //  * Get the source inventory location (if applicable).
    //  */
    // public function inventoryLocationFrom()
    // {
    //     return $this->belongsTo(InventoryLocation::class, 'inventory_location_id_from');
    // }

    // /**
    //  * Get the target inventory location (if applicable).
    //  */
    // public function inventoryLocationTo()
    // {
    //     return $this->belongsTo(InventoryLocation::class, 'inventory_location_id_to');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}

