<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تم نقل إنشاء جدول الأدوار إلى ملف هجرة آخر
        // تم نقل إنشاء جدول علاقة الأدوار بالصلاحيات إلى ملف هجرة آخر

        // إضافة حقل branch_id إلى جدول المستخدمين إذا لم يكن موجوداً
        if (!Schema::hasColumn('users', 'branch_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إزالة حقل branch_id من جدول المستخدمين إذا كان موجوداً
        if (Schema::hasColumn('users', 'branch_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            });
        }
    }
};
