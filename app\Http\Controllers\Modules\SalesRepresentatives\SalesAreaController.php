<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesArea;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SalesAreaController extends Controller
{
    public function index()
    {
        $salesAreas = SalesArea::with(['regionManager', 'branch', 'activeSalesRepresentatives', 'customers'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.sales-representatives.areas.index', compact('salesAreas'));
    }

    public function create()
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $managers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['manager', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        return view('admin.sales-representatives.areas.form', compact('branches', 'managers'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:sales_areas,code',
            'description' => 'nullable|string',
            'city' => 'required|string|max:100',
            'district' => 'nullable|string|max:100',
            'boundaries' => 'nullable|string',
            'coordinates' => 'nullable|json',
            'postal_code' => 'nullable|string|max:20',
            'area_type' => 'required|in:urban,suburban,rural',
            'priority_level' => 'required|integer|min:1|max:5',
            'travel_allowance' => 'nullable|numeric|min:0',
            'estimated_visit_time' => 'nullable|integer|min:1',
            'special_instructions' => 'nullable|string',
            'requires_approval' => 'boolean',
            'region_manager_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['requires_approval'] = $request->has('requires_approval');
        $validatedData['is_active'] = $request->has('is_active');

        // Handle coordinates
        if ($request->has('coordinates') && $request->coordinates) {
            $validatedData['coordinates'] = json_decode($request->coordinates, true);
        }

        SalesArea::create($validatedData);

        return redirect()->route('admin.sales-representatives.areas.index')
            ->with('success', __('Sales area created successfully.'));
    }

    public function show(SalesArea $area)
    {
        $area->load([
            'regionManager', 
            'branch', 
            'activeSalesRepresentatives', 
            'salesRoutes', 
            'customers'
        ]);

        // Calculate area statistics
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $stats = [
            'total_customers' => $area->customers()->count(),
            'active_representatives' => $area->activeSalesRepresentatives()->count(),
            'total_routes' => $area->salesRoutes()->count(),
            'monthly_sales' => $area->calculateSales($currentMonth, $endOfMonth),
            'monthly_visits' => $area->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->count(),
            'completed_visits' => $area->salesVisits()
                ->whereBetween('visit_date', [$currentMonth, $endOfMonth])
                ->where('visit_status', 'completed')
                ->count(),
        ];

        return view('admin.sales-representatives.areas.show', compact('area', 'stats'));
    }

    public function edit(SalesArea $area)
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $managers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['manager', 'admin', 'superadmin']);
        })->orderBy('name')->get();

        return view('admin.sales-representatives.areas.form', compact('area', 'branches', 'managers'));
    }

    public function update(Request $request, SalesArea $area)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:sales_areas,code,' . $area->id,
            'description' => 'nullable|string',
            'city' => 'required|string|max:100',
            'district' => 'nullable|string|max:100',
            'boundaries' => 'nullable|string',
            'coordinates' => 'nullable|json',
            'postal_code' => 'nullable|string|max:20',
            'area_type' => 'required|in:urban,suburban,rural',
            'priority_level' => 'required|integer|min:1|max:5',
            'travel_allowance' => 'nullable|numeric|min:0',
            'estimated_visit_time' => 'nullable|integer|min:1',
            'special_instructions' => 'nullable|string',
            'requires_approval' => 'boolean',
            'region_manager_id' => 'nullable|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['requires_approval'] = $request->has('requires_approval');
        $validatedData['is_active'] = $request->has('is_active');

        // Handle coordinates
        if ($request->has('coordinates') && $request->coordinates) {
            $validatedData['coordinates'] = json_decode($request->coordinates, true);
        }

        $area->update($validatedData);

        return redirect()->route('admin.sales-representatives.areas.index')
            ->with('success', __('Sales area updated successfully.'));
    }

    public function destroy(SalesArea $area)
    {
        // Check if area has active representatives
        if ($area->activeSalesRepresentatives()->count() > 0) {
            return redirect()->route('admin.sales-representatives.areas.index')
                ->with('error', __('Cannot delete area with active representative assignments.'));
        }

        // Check if area has customers
        if ($area->customers()->count() > 0) {
            return redirect()->route('admin.sales-representatives.areas.index')
                ->with('error', __('Cannot delete area with assigned customers.'));
        }

        $area->delete();

        return redirect()->route('admin.sales-representatives.areas.index')
            ->with('success', __('Sales area deleted successfully.'));
    }

    public function assignRepresentative(Request $request, SalesArea $area)
    {
        $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'assignment_type' => 'required|in:primary,secondary,temporary',
            'effective_from' => 'required|date',
            'effective_to' => 'nullable|date|after:effective_from',
            'commission_override' => 'nullable|numeric|min:0|max:100',
            'assignment_notes' => 'nullable|string',
        ]);

        // Check if representative is already assigned to this area
        $existingAssignment = $area->salesRepresentatives()
            ->wherePivot('sales_representative_id', $request->sales_representative_id)
            ->wherePivot('is_active', true)
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => __('Representative is already assigned to this area.')
            ], 422);
        }

        // If this is a primary assignment, deactivate other primary assignments
        if ($request->assignment_type === 'primary') {
            $area->salesRepresentatives()
                ->wherePivot('assignment_type', 'primary')
                ->wherePivot('is_active', true)
                ->updateExistingPivot($area->salesRepresentatives()->pluck('sales_representatives.id'), [
                    'is_active' => false,
                    'effective_to' => now()->subDay(),
                ]);
        }

        $area->salesRepresentatives()->attach($request->sales_representative_id, [
            'assigned_date' => now(),
            'effective_from' => $request->effective_from,
            'effective_to' => $request->effective_to,
            'assignment_type' => $request->assignment_type,
            'commission_override' => $request->commission_override,
            'assignment_notes' => $request->assignment_notes,
            'is_active' => true,
            'assigned_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Representative assigned successfully.')
        ]);
    }

    public function unassignRepresentative(Request $request, SalesArea $area, SalesRepresentative $representative)
    {
        $area->salesRepresentatives()->updateExistingPivot($representative->id, [
            'is_active' => false,
            'effective_to' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Representative unassigned successfully.')
        ]);
    }

    public function getAreasByCity(Request $request)
    {
        $city = $request->get('city');
        
        $areas = SalesArea::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('city', $city)
            ->where('is_active', true)
            ->ordered()
            ->get(['id', 'name', 'code']);

        return response()->json([
            'success' => true,
            'data' => $areas
        ]);
    }

    public function map()
    {
        $salesAreas = SalesArea::with(['activeSalesRepresentatives', 'customers'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->whereNotNull('coordinates')
            ->get();

        return view('admin.sales-representatives.areas.map', compact('salesAreas'));
    }
}
