@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>BNPL Provider Details & Logs: {{ ucfirst($provider) }}</h1>

    @if(session("success"))
        <div class="alert alert-success">
            {{ session("success") }}
        </div>
    @endif

    <div class="card mb-3">
        <div class="card-header">Configuration Summary for {{ ucfirst($provider) }}</div>
        <div class="card-body">
            <p><strong>Status:</strong> {{-- ($settings->{$provider."_enabled"} ?? false) ? "Enabled" : "Disabled" --}} Enabled</p>
            <p><strong>Environment:</strong> {{-- ucfirst($settings->{$provider."_environment"} ?? "sandbox") --}} Sandbox</p>
            @if($provider == "tabby")
                <p><strong>Public Key:</strong> {{-- $settings->tabby_public_key ? substr($settings->tabby_public_key, 0, 10) . "..." : "Not Set" --}} pk_test_xxxx</p>
                <p><strong>Merchant ID:</strong> {{-- $settings->tabby_merchant_id ?? "Not Set" --}} TB12345</p>
            @elseif($provider == "tamara")
                <p><strong>API Token:</strong> {{-- $settings->tamara_api_token ? substr($settings->tamara_api_token, 0, 10) . "..." : "Not Set" --}} tamara_test_xxxx</p>
            @endif
            <a href="{{ route("admin.integrations.bnpl.edit", ["provider" => $provider]) }}" class="btn btn-sm btn-warning">Edit Configuration</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Transaction Logs (Last 50 Entries for {{ ucfirst($provider) }})</div>
        <div class="card-body">
            <table class="table table-striped table-sm">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Sale ID</th>
                        <th>Order ID ({{ ucfirst($provider) }})</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- @forelse($logs as $log) --}}
                    {{-- Replace with actual log data --}}
                    <tr>
                        <td>2023-05-13 14:00:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->sale_id) --}}">S1003</a></td>
                        <td>{{-- $log->provider_order_id --}}TABBY-ORDER-XYZ</td>
                        <td>250.00</td>
                        <td><span class="badge bg-success">Captured</span></td>
                        <td><a href="#" class="btn btn-xs btn-info">View Details</a></td>
                    </tr>
                    <tr>
                        <td>2023-05-12 16:30:00</td>
                        <td><a href="#{{-- route("admin.pos.sales.show", $log->sale_id) --}}">S1000</a></td>
                        <td>{{-- $log->provider_order_id --}}TAMARA-ORDER-ABC</td>
                        <td>180.75</td>
                        <td><span class="badge bg-warning">Authorized</span></td>
                        <td><a href="#" class="btn btn-xs btn-info">View Details</a></td>
                    </tr>
                    {{-- @empty
                    <tr>
                        <td colspan="6" class="text-center">No transaction logs found for {{ ucfirst($provider) }}.</td>
                    </tr>
                    @endforelse --}}
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-3">
        <a href="{{ route("admin.integrations.bnpl.index") }}" class="btn btn-secondary">Back to BNPL Integrations</a>
    </div>
</div>
@endsection

