<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_terminals', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الجهاز
            $table->string('terminal_id')->unique(); // معرف الجهاز
            $table->string('location')->nullable(); // موقع الجهاز
            $table->enum('type', ['restaurant', 'retail', 'takeaway'])->default('restaurant'); // نوع الجهاز
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable(); // إعدادات الجهاز
            $table->string('receipt_printer_ip')->nullable(); // طابعة الفواتير
            $table->integer('receipt_printer_port')->default(9100);
            $table->foreignId('default_table_id')->nullable()->constrained('restaurant_tables')->onDelete('set null');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('tenant_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_terminals');
    }
};
