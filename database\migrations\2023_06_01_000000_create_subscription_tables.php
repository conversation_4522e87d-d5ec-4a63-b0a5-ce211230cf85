<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // خطط الاشتراك
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'semi_annually', 'annually'])->default('monthly');
            $table->integer('max_users')->default(1);
            $table->integer('max_branches')->default(1);
            $table->boolean('has_pos')->default(false);
            $table->boolean('has_inventory')->default(false);
            $table->boolean('has_accounting')->default(false);
            $table->boolean('has_manufacturing')->default(false);
            $table->boolean('has_hr')->default(false);
            $table->boolean('has_restaurant')->default(false);
            $table->boolean('has_crm')->default(false);
            $table->boolean('has_purchases')->default(false);
            $table->boolean('has_sales')->default(false);
            $table->boolean('has_reports')->default(false);
            $table->boolean('has_api_access')->default(false);
            $table->integer('storage_space_gb')->default(1);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        // اشتراكات العملاء
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('users')->comment('المستخدم الرئيسي للعميل');
            $table->foreignId('subscription_plan_id')->constrained('subscription_plans');
            $table->string('subscription_number')->unique();
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['active', 'expired', 'cancelled', 'pending'])->default('pending');
            $table->text('notes')->nullable();
            $table->boolean('auto_renew')->default(false);
            $table->integer('current_users_count')->default(0);
            $table->integer('current_branches_count')->default(0);
            $table->decimal('price_paid', 10, 2);
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // سجل المدفوعات
        Schema::create('subscription_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained('subscriptions');
            $table->decimal('amount', 10, 2);
            $table->date('payment_date');
            $table->string('payment_method');
            $table->string('transaction_id')->nullable();
            $table->string('receipt_number')->nullable();
            $table->enum('status', ['paid', 'pending', 'failed', 'refunded'])->default('pending');
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        // سجل تغييرات الاشتراك
        Schema::create('subscription_changes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained('subscriptions');
            $table->foreignId('user_id')->constrained('users')->comment('المستخدم الذي قام بالتغيير');
            $table->string('change_type'); // upgrade, downgrade, renew, cancel, etc.
            $table->text('change_details')->nullable();
            $table->foreignId('old_plan_id')->nullable()->constrained('subscription_plans');
            $table->foreignId('new_plan_id')->nullable()->constrained('subscription_plans');
            $table->date('effective_date');
            $table->decimal('price_difference', 10, 2)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_changes');
        Schema::dropIfExists('subscription_payments');
        Schema::dropIfExists('subscriptions');
        Schema::dropIfExists('subscription_plans');
    }
};
