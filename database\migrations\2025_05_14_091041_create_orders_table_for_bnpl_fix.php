<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("orders")) {
            Schema::create("orders", function (Blueprint $table) {
                $table->id();
                $table->foreignId("user_id")->nullable()->constrained("users")->onDelete("set null"); // Assuming a users table for customers
                $table->string("order_number")->unique();
                $table->dateTime("order_date");
                $table->decimal("total_amount", 15, 2);
                $table->string("status")->default("pending"); // e.g., pending, processing, completed, cancelled
                $table->string("payment_method")->nullable();
                // Add other essential order fields here
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("orders");
    }
};

