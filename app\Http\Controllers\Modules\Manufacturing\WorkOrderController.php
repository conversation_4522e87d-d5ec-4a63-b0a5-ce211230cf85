<?php

namespace App\Http\Controllers\Modules\Manufacturing;

use App\Http\Controllers\Controller;
use App\Models\Modules\Manufacturing\ManufacturingWorkOrder;
use App\Models\Modules\Manufacturing\ManufacturingItem;
use App\Models\Modules\Manufacturing\ManufacturingBom;
use App\Models\Modules\Branches\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WorkOrderController extends Controller
{
    public function index()
    {
        $workOrders = ManufacturingWorkOrder::with(["item", "bom", "branch", "assignedTo"])->latest()->paginate(10);
        return view("admin.manufacturing.work_orders.index", compact("workOrders"));
    }

    public function create()
    {
        $manufacturedItems = ManufacturingItem::where("is_manufactured", true)->orWhere("item_type", "finished_good")->orWhere("item_type", "semi_finished_good")->get();
        $boms = ManufacturingBom::where("is_active", true)->with("item")->get();
        $branches = Branch::all();
        $users = User::all(); // For assigning work orders
        return view("admin.manufacturing.work_orders.create", compact("manufacturedItems", "boms", "branches", "users"));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "work_order_number" => "required|string|max:255|unique:manufacturing_work_orders,work_order_number",
            "item_id" => "required|exists:manufacturing_items,id",
            "bom_id" => "nullable|exists:manufacturing_boms,id",
            "quantity_to_produce" => "required|numeric|min:0.0001",
            "status" => "required|string|in:pending,in_progress,completed,cancelled,on_hold",
            "planned_start_date" => "nullable|date",
            "planned_end_date" => "nullable|date|after_or_equal:planned_start_date",
            "branch_id" => "nullable|exists:branches,id",
            "assigned_to_id" => "nullable|exists:users,id",
            "notes" => "nullable|string",
        ]);

        DB::beginTransaction();
        try {
            $workOrder = ManufacturingWorkOrder::create([
                "work_order_number" => $validatedData["work_order_number"],
                "item_id" => $validatedData["item_id"],
                "bom_id" => $validatedData["bom_id"],
                "quantity_to_produce" => $validatedData["quantity_to_produce"],
                "status" => $validatedData["status"],
                "planned_start_date" => $validatedData["planned_start_date"],
                "planned_end_date" => $validatedData["planned_end_date"],
                "branch_id" => $validatedData["branch_id"],
                "assigned_to_id" => $validatedData["assigned_to_id"],
                "notes" => $validatedData["notes"],
                "created_by_id" => auth()->id(), // Assuming authenticated user
            ]);

            // TODO: Add logic for creating WorkOrderItems based on BOM if selected, or allow manual addition.
            // TODO: Add initial inventory reservations and GL postings if applicable (e.g. WIP account).

            DB::commit();
            return redirect()->route("admin.manufacturing.work_orders.index")->with("success", "Work Order created successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with("error", "Error creating Work Order: " . $e->getMessage());
        }
    }

    public function show(ManufacturingWorkOrder $workOrder)
    {
        $workOrder->load(["item", "bom.item", "branch", "assignedTo", "createdBy", "workOrderItems.item", "consumedItems.item", "productionCosts.account"]);
        return view("admin.manufacturing.work_orders.show", compact("workOrder"));
    }

    public function edit(ManufacturingWorkOrder $workOrder)
    {
        $manufacturedItems = ManufacturingItem::where("is_manufactured", true)->orWhere("item_type", "finished_good")->orWhere("item_type", "semi_finished_good")->get();
        $boms = ManufacturingBom::where("is_active", true)->with("item")->get();
        $branches = Branch::all();
        $users = User::all();
        return view("admin.manufacturing.work_orders.edit", compact("workOrder", "manufacturedItems", "boms", "branches", "users"));
    }

    public function update(Request $request, ManufacturingWorkOrder $workOrder)
    {
        $validatedData = $request->validate([
            "work_order_number" => "required|string|max:255|unique:manufacturing_work_orders,work_order_number," . $workOrder->id,
            "item_id" => "required|exists:manufacturing_items,id",
            "bom_id" => "nullable|exists:manufacturing_boms,id",
            "quantity_to_produce" => "required|numeric|min:0.0001",
            "quantity_produced" => "nullable|numeric|min:0|lte:quantity_to_produce",
            "quantity_scrapped" => "nullable|numeric|min:0",
            "status" => "required|string|in:pending,in_progress,completed,cancelled,on_hold",
            "planned_start_date" => "nullable|date",
            "planned_end_date" => "nullable|date|after_or_equal:planned_start_date",
            "actual_start_date" => "nullable|date",
            "actual_end_date" => "nullable|date|after_or_equal:actual_start_date",
            "branch_id" => "nullable|exists:branches,id",
            "assigned_to_id" => "nullable|exists:users,id",
            "notes" => "nullable|string",
        ]);

        DB::beginTransaction();
        try {
            $workOrder->update([
                "work_order_number" => $validatedData["work_order_number"],
                "item_id" => $validatedData["item_id"],
                "bom_id" => $validatedData["bom_id"],
                "quantity_to_produce" => $validatedData["quantity_to_produce"],
                "quantity_produced" => $validatedData["quantity_produced"] ?? $workOrder->quantity_produced,
                "quantity_scrapped" => $validatedData["quantity_scrapped"] ?? $workOrder->quantity_scrapped,
                "status" => $validatedData["status"],
                "planned_start_date" => $validatedData["planned_start_date"],
                "planned_end_date" => $validatedData["planned_end_date"],
                "actual_start_date" => $validatedData["actual_start_date"],
                "actual_end_date" => $validatedData["actual_end_date"],
                "branch_id" => $validatedData["branch_id"],
                "assigned_to_id" => $validatedData["assigned_to_id"],
                "notes" => $validatedData["notes"],
                "updated_by_id" => auth()->id(),
            ]);

            // TODO: Add logic for updating WorkOrderItems, consumed materials, produced quantities.
            // TODO: Add GL postings for changes in WIP, material consumption, finished goods production.
            // Example: If status changes to 'completed', trigger finished goods inventory update and WIP clearing.

            DB::commit();
            return redirect()->route("admin.manufacturing.work_orders.index")->with("success", "Work Order updated successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with("error", "Error updating Work Order: " . $e->getMessage());
        }
    }

    public function destroy(ManufacturingWorkOrder $workOrder)
    {
        // Add checks: Cannot delete if status is 'in_progress' or 'completed' without proper reversal.
        if (in_array($workOrder->status, ["in_progress", "completed"])) {
            return redirect()->route("admin.manufacturing.work_orders.index")->with("error", "Cannot delete a Work Order that is in progress or completed. Please cancel or reverse first.");
        }

        DB::beginTransaction();
        try {
            // Delete related items first if necessary (e.g., work_order_items, consumed_items, production_costs)
            // For now, assuming cascading deletes or manual cleanup is handled elsewhere / or no strict FKs for these yet.
            $workOrder->delete();
            DB::commit();
            return redirect()->route("admin.manufacturing.work_orders.index")->with("success", "Work Order deleted successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route("admin.manufacturing.work_orders.index")->with("error", "Error deleting Work Order: " . $e->getMessage());
        }
    }

    /**
     * Display manufacturing reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function reports()
    {
        return view('admin.manufacturing.reports.index');
    }

    /**
     * Generate production summary report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function productionSummary(Request $request)
    {
        return view('admin.manufacturing.reports.production_summary');
    }

    /**
     * Generate material consumption report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function materialConsumption(Request $request)
    {
        return view('admin.manufacturing.reports.material_consumption');
    }

    // TODO: Add methods for:
    // - Starting a work order (changes status, reserves materials)
    // - Recording material consumption
    // - Recording production output (finished goods / semi-finished goods)
    // - Recording labor and overhead costs
    // - Completing a work order (updates inventory, finalizes costs, closes WIP)
    // - Cancelling a work order
}

