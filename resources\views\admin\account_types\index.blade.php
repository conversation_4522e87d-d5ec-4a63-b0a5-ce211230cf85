@extends("layouts.admin")

@section("main-content")
    <div class="container">
        <h2>Account Types Management</h2>
        <a href="{{ route("admin.account_types.create") }}" class="btn btn-primary mb-3">Create New Account Type</a>

        @if (session("success"))
            <div class="alert alert-success">
                {{ session("success") }}
            </div>
        @endif

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Code</th>
                    <th>Is Debit Balance?</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($accountTypes as $accountType)
                    <tr>
                        <td>{{ $accountType->id }}</td>
                        <td>{{ $accountType->name }}</td>
                        <td>{{ $accountType->code ?? "N/A" }}</td>
                        <td>{{ $accountType->is_debit_balance ? "Yes" : "No" }}</td>
                        <td>
                            <a href="{{ route("admin.account_types.show", $accountType->id) }}" class="btn btn-info btn-sm">View</a>
                            <a href="{{ route("admin.account_types.edit", $accountType->id) }}" class="btn btn-warning btn-sm">Edit</a>
                            <form action="{{ route("admin.account_types.destroy", $accountType->id) }}" method="POST" style="display:inline-block;">
                                @csrf
                                @method("DELETE")
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure?")">Delete</button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5">No account types found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        {{ $accountTypes->links() }}
    </div>
@endsection

