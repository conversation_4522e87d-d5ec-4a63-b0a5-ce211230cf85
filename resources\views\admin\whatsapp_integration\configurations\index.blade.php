@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>WhatsApp Configurations</h1>
    <a href="{{ route('admin.whatsapp_integration.configurations.create') }}" class="btn btn-primary mb-3">Add New Configuration</a>

    @if ($message = Session::get('success'))
        <div class="alert alert-success">
            <p>{{ $message }}</p>
        </div>
    @endif

    <table class="table table-bordered">
        <tr>
            <th>No</th>
            <th>Provider Name</th>
            <th>Phone Number ID</th>
            <th>Is Active</th>
            <th width="280px">Action</th>
        </tr>
        @foreach ($configurations as $configuration)
        <tr>
            <td>{{ ++$i }}</td>
            <td>{{ $configuration->provider_name }}</td>
            <td>{{ $configuration->phone_number_id }}</td>
            <td>{{ $configuration->is_active ? 'Yes' : 'No' }}</td>
            <td>
                <form action="{{ route('admin.whatsapp_integration.configurations.destroy',$configuration->id) }}" method="POST">
                    <a class="btn btn-info" href="{{ route('admin.whatsapp_integration.configurations.show',$configuration->id) }}">Show</a>
                    <a class="btn btn-primary" href="{{ route('admin.whatsapp_integration.configurations.edit',$configuration->id) }}">Edit</a>
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </td>
        </tr>
        @endforeach
    </table>
    {!! $configurations->links() !!}
</div>
@endsection

