<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'slug',
        'description_ar',
        'description_en',
        'is_primary',
        'parent_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the accounts for this account type.
     */
    public function accounts()
    {
        return $this->hasMany(Account::class);
    }

    /**
     * Scope to get active account types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the name attribute based on current locale
     */
    public function getNameAttribute()
    {
        return $this->name_ar ?? $this->name_en ?? 'غير محدد';
    }

    /**
     * Get the description attribute based on current locale
     */
    public function getDescriptionAttribute()
    {
        return $this->description_ar ?? $this->description_en ?? '';
    }

    /**
     * Get the parent account type.
     */
    public function parent()
    {
        return $this->belongsTo(AccountType::class, 'parent_id');
    }

    /**
     * Get the children account types.
     */
    public function children()
    {
        return $this->hasMany(AccountType::class, 'parent_id');
    }
}
