<?php

namespace App\Http\Controllers\Modules\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SalesReportController extends Controller
{
    /**
     * Display sales reports dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.reports.sales.index');
    }

    /**
     * Generate sales summary report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function salesSummary(Request $request)
    {
        return view('admin.reports.sales.summary');
    }

    /**
     * Generate sales by customer report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function salesByCustomer(Request $request)
    {
        return view('admin.reports.sales.by_customer');
    }

    /**
     * Generate sales by product report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function salesByProduct(Request $request)
    {
        return view('admin.reports.sales.by_product');
    }

    /**
     * Generate sales by period report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function salesByPeriod(Request $request)
    {
        return view('admin.reports.sales.by_period');
    }

    /**
     * Generate top customers report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function topCustomers(Request $request)
    {
        return view('admin.reports.sales.top_customers');
    }

    /**
     * Generate top products report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function topProducts(Request $request)
    {
        return view('admin.reports.sales.top_products');
    }

    /**
     * Generate sales returns report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function salesReturns(Request $request)
    {
        return view('admin.reports.sales.returns');
    }
}
