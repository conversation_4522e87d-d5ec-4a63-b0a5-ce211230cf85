@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.reports_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.reports_title\') }}</li>
    </ol>

    <div class="row">
        {{-- Report: Work Order Status --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-list-alt me-1"></i>
                    {{ __(\'manufacturing.report_work_order_status_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_work_order_status_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a> {{-- Link to specific report page --}}
                </div>
            </div>
        </div>

        {{-- Report: Work Order Cost Analysis --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-dollar-sign me-1"></i>
                    {{ __(\'manufacturing.report_work_order_cost_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_work_order_cost_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a>
                </div>
            </div>
        </div>

        {{-- Report: Material Consumption --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-pallet me-1"></i>
                    {{ __(\'manufacturing.report_material_consumption_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_material_consumption_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a>
                </div>
            </div>
        </div>

        {{-- Report: Work Center Efficiency --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    {{ __(\'manufacturing.report_wc_efficiency_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_wc_efficiency_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a>
                </div>
            </div>
        </div>

        {{-- Report: Finished Goods & Scrap --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-box-open me-1"></i>
                    {{ __(\'manufacturing.report_finished_goods_scrap_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_finished_goods_scrap_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a>
                </div>
            </div>
        </div>

        {{-- Report: Material Traceability --}}
        <div class="col-xl-4 col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-search-location me-1"></i>
                    {{ __(\'manufacturing.report_material_traceability_title\') }}
                </div>
                <div class="card-body">
                    <p>{{ __(\'manufacturing.report_material_traceability_desc\') }}</p>
                    <a href="#" class="btn btn-primary">{{ __(\'manufacturing.view_report\') }}</a>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page --}}
@endpush

