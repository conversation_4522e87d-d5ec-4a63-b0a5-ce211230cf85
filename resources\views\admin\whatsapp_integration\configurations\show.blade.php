@extends("layouts.admin")

@section("content")
<div class="container">
    <h1>Show WhatsApp Configuration</h1>

    <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Provider Name:</strong>
                {{ $configuration->provider_name }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Phone Number ID:</strong>
                {{ $configuration->phone_number_id }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Business Account ID:</strong>
                {{ $configuration->business_account_id ?? "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>App ID:</strong>
                {{ $configuration->app_id ?? "N/A" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Is Active:</strong>
                {{ $configuration->is_active ? "Yes" : "No" }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Created At:</strong>
                {{ $configuration->created_at }}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="form-group">
                <strong>Updated At:</strong>
                {{ $configuration->updated_at }}
            </div>
        </div>
    </div>
    <div class="pull-right">
        <a class="btn btn-primary" href="{{ route("admin.whatsapp_integration.configurations.index") }}"> Back</a>
    </div>
</div>
@endsection

