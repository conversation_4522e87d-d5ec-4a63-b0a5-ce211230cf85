<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
// Potentially use Guzzle or similar for HTTP-based terminals, or direct socket functions for TCP/IP

class PaymentTerminalService
{
    protected $terminalIp;
    protected $terminalPort;
    protected $timeout = 30; // seconds

    public function __construct()
    {
        // Load terminal configuration (e.g., from .env or a config file)
        // For now, we can use placeholders or make them configurable via a settings page later
        $this->terminalIp = config('services.payment_terminal.ip', '*************'); // Example IP
        $this->terminalPort = config('services.payment_terminal.port', 9100); // Example Port for some terminals
    }

    /**
     * Initializes a payment transaction with the terminal.
     *
     * @param float $amount The amount to be charged.
     * @param string $transactionId A unique ID for this transaction (e.g., order ID).
     * @return array An array containing the status and data from the terminal.
     */
    public function initiatePayment(float $amount, string $transactionId): array
    {
        Log::info("PTIS: Initiating payment for {$transactionId} - Amount: {$amount}");

        // This is a placeholder for actual terminal communication logic.
        // The actual implementation will depend on the specific terminal's protocol.
        // For TCP/IP JSON/XML based protocol as designed:

        $payload = [
            'command' => 'INITIATE_PAYMENT',
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'currency' => 'SAR'
        ];

        try {
            // Simulate sending data and receiving a response
            // $response = $this->sendAndReceiveViaSocket(json_encode($payload));
            // $parsedResponse = json_decode($response, true);

            // SIMULATED RESPONSE FOR NOW
            // In a real scenario, this block would involve actual socket communication
            // and error handling for connection issues, timeouts, etc.
            sleep(2); // Simulate network delay

            // Simulate different outcomes for testing
            $simulatedOutcome = rand(1, 10);
            if ($simulatedOutcome <= 7) { // 70% success
                $parsedResponse = [
                    'status' => 'APPROVED',
                    'transaction_id' => $transactionId,
                    'terminal_transaction_id' => 'TERM_TRX_' . strtoupper(uniqid()),
                    'approval_code' => 'AUTH' . rand(100000, 999999),
                    'masked_pan' => '************' . rand(1000, 9999),
                    'card_type' => (rand(0,1) ? 'MADA' : 'VISA'),
                    'timestamp' => now()->toIso8601String()
                ];
                Log::info("PTIS: Payment successful for {$transactionId}", $parsedResponse);
                return ['success' => true, 'data' => $parsedResponse, 'message' => 'Payment approved by terminal.'];
            } elseif ($simulatedOutcome <= 9) { // 20% decline
                $parsedResponse = [
                    'status' => 'DECLINED',
                    'transaction_id' => $transactionId,
                    'terminal_transaction_id' => 'TERM_TRX_' . strtoupper(uniqid()),
                    'error_code' => '51',
                    'error_message' => 'Insufficient Funds',
                    'timestamp' => now()->toIso8601String()
                ];
                Log::warning("PTIS: Payment declined for {$transactionId}", $parsedResponse);
                return ['success' => false, 'data' => $parsedResponse, 'message' => 'Payment declined: Insufficient Funds.'];
            } else { // 10% error
                Log::error("PTIS: Terminal communication error for {$transactionId}");
                return ['success' => false, 'message' => 'Terminal communication error. Please check device.'];
            }

        } catch (\Exception $e) {
            Log::error("PTIS: Exception during payment initiation for {$transactionId}: " . $e->getMessage());
            return ['success' => false, 'message' => 'Error communicating with payment terminal: ' . $e->getMessage()];
        }
    }

    /**
     * Sends data to the terminal via a TCP/IP socket and waits for a response.
     * This is a basic example and needs robust error handling and protocol specifics.
     */
    protected function sendAndReceiveViaSocket(string $data): ?string
    {
        Log::debug("PTIS: Attempting to connect to terminal at {$this->terminalIp}:{$this->terminalPort}");
        $socket = @fsockopen($this->terminalIp, $this->terminalPort, $errno, $errstr, $this->timeout);

        if (!$socket) {
            Log::error("PTIS: Socket connection failed: {$errno} - {$errstr}");
            throw new \Exception("Cannot connect to payment terminal: {$errstr}");
        }

        Log::debug("PTIS: Connected. Sending data: {$data}");
        fwrite($socket, $data . "\n"); // Assuming newline terminated messages

        $response = '';
        stream_set_timeout($socket, $this->timeout);
        // Reading response logic needs to be protocol-specific (e.g., read until newline, or fixed length, or based on a header)
        // This is a simplified example
        while (!feof($socket)) {
            $line = fgets($socket, 4096); // Read up to 4KB or until newline
            if ($line === false) break; // Error or timeout
            $response .= $line;
            // Add logic here to break if end-of-message marker is received based on protocol
            if (strpos($line, "}") !== false) { // Simple check for JSON end, highly protocol dependent
                break;
            }
        }
        fclose($socket);
        Log::debug("PTIS: Received response: {$response}");

        if (empty($response)) {
            Log::warning("PTIS: No response received from terminal or connection timed out.");
            throw new \Exception("No response from payment terminal.");
        }
        return $response;
    }

    /**
     * Sends a command to cancel an ongoing transaction (if supported by terminal/protocol).
     *
     * @param string $transactionId The ID of the transaction to cancel.
     * @return array Status of the cancellation attempt.
     */
    public function cancelPayment(string $transactionId): array
    {
        Log::info("PTIS: Initiating cancel for {$transactionId}");
        // Placeholder for actual cancellation logic
        // $payload = ['command' => 'CANCEL_PAYMENT', 'transaction_id' => $transactionId];
        // $response = $this->sendAndReceiveViaSocket(json_encode($payload));
        // $parsedResponse = json_decode($response, true);
        // return ['success' => $parsedResponse['status'] === 'CANCELLED', 'data' => $parsedResponse];
        sleep(1);
        return ['success' => true, 'message' => 'Cancellation request sent (simulated).'];
    }

     /**
     * Checks the status of a transaction (if supported by terminal/protocol).
     *
     * @param string $transactionId The ID of the transaction to check.
     * @return array Status of the transaction.
     */
    public function checkTransactionStatus(string $transactionId): array
    {
        Log::info("PTIS: Checking status for {$transactionId}");
        // Placeholder for actual status check logic
        // $payload = ['command' => 'GET_STATUS', 'transaction_id' => $transactionId];
        // $response = $this->sendAndReceiveViaSocket(json_encode($payload));
        // $parsedResponse = json_decode($response, true);
        // return ['success' => true, 'data' => $parsedResponse];
        sleep(1);
        // Simulate a response
        $statuses = ['APPROVED', 'PENDING', 'DECLINED', 'ERROR'];
        $randomStatus = $statuses[array_rand($statuses)];
        return ['success' => true, 'data' => ['transaction_id' => $transactionId, 'status' => $randomStatus], 'message' => 'Status check (simulated).'];
    }
}

