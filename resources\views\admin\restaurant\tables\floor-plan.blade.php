@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ __("Restaurant Floor Plan") }}</h1>
                <div>
                    <button class="btn btn-info" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> {{ __("Fullscreen") }}
                    </button>
                    <a href="{{ route('admin.restaurant.tables.index') }}" class="btn btn-secondary">
                        <i class="fas fa-list"></i> {{ __("Table List") }}
                    </a>
                    <a href="{{ route('admin.restaurant.tables.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {{ __("Add Table") }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-wrap align-items-center gap-4">
                        <div class="legend-item">
                            <div class="legend-color bg-success"></div>
                            <span>{{ __("Available") }}</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color bg-danger"></div>
                            <span>{{ __("Occupied") }}</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color bg-warning"></div>
                            <span>{{ __("Reserved") }}</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color bg-secondary"></div>
                            <span>{{ __("Maintenance") }}</span>
                        </div>
                        <div class="ms-auto">
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshFloorPlan()">
                                <i class="fas fa-sync-alt"></i> {{ __("Refresh") }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floor Plan -->
    <div class="row">
        <div class="col-12">
            <div class="card floor-plan-card">
                <div class="card-body p-0">
                    <div class="floor-plan-container" id="floorPlanContainer">
                        @if($areas->count() > 0)
                            @foreach($areas as $area)
                                <div class="area-section" style="border-color: {{ $area->color }};">
                                    <div class="area-header" style="background-color: {{ $area->color }}20; border-bottom-color: {{ $area->color }};">
                                        <h6 class="mb-0" style="color: {{ $area->color }};">
                                            <i class="fas fa-map-marker-alt me-2"></i>{{ $area->name }}
                                        </h6>
                                        <small class="text-muted">{{ $area->activeTables->count() }} {{ __("tables") }}</small>
                                    </div>
                                    
                                    <div class="area-tables">
                                        @if($area->activeTables->count() > 0)
                                            @foreach($area->activeTables as $table)
                                                <div class="table-item {{ $table->status }}" 
                                                     data-table-id="{{ $table->id }}"
                                                     onclick="showTableDetails({{ $table->id }})"
                                                     style="left: {{ $table->x_position * 10 }}px; top: {{ $table->y_position * 10 }}px;">
                                                    <div class="table-visual {{ $table->shape }}">
                                                        <div class="table-number">{{ $table->number }}</div>
                                                        <div class="table-capacity">
                                                            <i class="fas fa-user"></i> {{ $table->capacity }}
                                                        </div>
                                                    </div>
                                                    <div class="table-info">
                                                        <strong>{{ $table->name }}</strong>
                                                        @if($table->currentOrder)
                                                            <small class="d-block text-primary">
                                                                #{{ $table->currentOrder->order_number }}
                                                            </small>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="text-center py-4 text-muted">
                                                <i class="fas fa-table fa-2x mb-2"></i>
                                                <p>{{ __("No tables in this area") }}</p>
                                                <a href="{{ route('admin.restaurant.tables.create', ['area_id' => $area->id]) }}" 
                                                   class="btn btn-sm btn-primary">
                                                    {{ __("Add Table") }}
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-map fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">{{ __("No areas found") }}</h5>
                                <p class="text-muted">{{ __("Create areas and tables to see the floor plan") }}</p>
                                <a href="{{ route('admin.restaurant.areas.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> {{ __("Create Area") }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table Details Modal -->
<div class="modal fade" id="tableDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __("Table Details") }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tableDetailsContent">
                <!-- Table details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __("Close") }}</button>
                <button type="button" class="btn btn-primary" id="editTableBtn">{{ __("Edit Table") }}</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.floor-plan-card {
    min-height: 600px;
}

.floor-plan-container {
    position: relative;
    min-height: 600px;
    background: 
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    padding: 20px;
}

.area-section {
    border: 2px solid #dee2e6;
    border-radius: 12px;
    margin-bottom: 30px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.area-header {
    padding: 15px 20px;
    border-bottom: 2px solid #dee2e6;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.area-tables {
    position: relative;
    min-height: 200px;
    padding: 20px;
}

.table-item {
    position: absolute;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.table-item:hover {
    transform: scale(1.1);
    z-index: 20;
}

.table-visual {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.table-visual.square {
    border-radius: 12px;
}

.table-visual.rectangle {
    width: 100px;
    height: 60px;
    border-radius: 8px;
}

.table-item.available .table-visual {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.table-item.occupied .table-visual {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.table-item.reserved .table-visual {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.table-item.maintenance .table-visual {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.table-number {
    font-size: 18px;
    font-weight: bold;
}

.table-capacity {
    font-size: 10px;
    opacity: 0.9;
}

.table-info {
    position: absolute;
    top: 85px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    background: white;
    padding: 5px 10px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    white-space: nowrap;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.table-item:hover .table-info {
    opacity: 1;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .table-visual {
        width: 60px;
        height: 60px;
    }
    
    .table-visual.rectangle {
        width: 80px;
        height: 50px;
    }
    
    .table-number {
        font-size: 14px;
    }
    
    .table-capacity {
        font-size: 8px;
    }
}
</style>
@endpush

@push('scripts')
<script>
let isFullscreen = false;

function toggleFullscreen() {
    const container = document.querySelector('.container-fluid');
    
    if (!isFullscreen) {
        container.classList.add('fullscreen-mode');
        document.querySelector('[onclick="toggleFullscreen()"] i').className = 'fas fa-compress';
        document.querySelector('[onclick="toggleFullscreen()"]').innerHTML = '<i class="fas fa-compress"></i> {{ __("Exit Fullscreen") }}';
        isFullscreen = true;
    } else {
        container.classList.remove('fullscreen-mode');
        document.querySelector('[onclick="toggleFullscreen()"] i').className = 'fas fa-expand';
        document.querySelector('[onclick="toggleFullscreen()"]').innerHTML = '<i class="fas fa-expand"></i> {{ __("Fullscreen") }}';
        isFullscreen = false;
    }
}

function showTableDetails(tableId) {
    // Fetch table details and show in modal
    fetch(`/admin/restaurant/tables/${tableId}`)
        .then(response => response.text())
        .then(html => {
            // Extract table data from response or make separate API call
            const modal = new bootstrap.Modal(document.getElementById('tableDetailsModal'));
            document.getElementById('tableDetailsContent').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">{{ __("Loading...") }}</span>
                    </div>
                </div>
            `;
            modal.show();
            
            // Simulate loading table details
            setTimeout(() => {
                document.getElementById('tableDetailsContent').innerHTML = `
                    <div class="table-details">
                        <h6>{{ __("Table Information") }}</h6>
                        <p><strong>{{ __("Table ID") }}:</strong> ${tableId}</p>
                        <p><strong>{{ __("Status") }}:</strong> <span class="badge bg-success">{{ __("Available") }}</span></p>
                        <p><strong>{{ __("Capacity") }}:</strong> 4 {{ __("seats") }}</p>
                        <p><strong>{{ __("Current Order") }}:</strong> {{ __("None") }}</p>
                    </div>
                `;
                
                document.getElementById('editTableBtn').onclick = function() {
                    window.location.href = `/admin/restaurant/tables/${tableId}/edit`;
                };
            }, 500);
        })
        .catch(error => {
            console.error('Error loading table details:', error);
        });
}

function refreshFloorPlan() {
    location.reload();
}

// Auto-refresh every 30 seconds
setInterval(refreshFloorPlan, 30000);

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
    } else if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
        e.preventDefault();
        refreshFloorPlan();
    }
});
</script>
@endpush
