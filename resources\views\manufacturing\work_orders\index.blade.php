@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.work_orders_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.work_orders_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-cogs me-1"></i>
            {{ __(\'manufacturing.work_orders_list\') }}
            <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_work_order\') }}</a> {{-- Link to create page --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for work orders table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.wo_number\') }}</th>
                        <th>{{ __(\'manufacturing.product_name\') }}</th>
                        <th>{{ __(\'manufacturing.quantity_to_produce\') }}</th>
                        <th>{{ __(\'manufacturing.quantity_produced\') }}</th>
                        <th>{{ __(\'manufacturing.wo_status\') }}</th>
                        <th>{{ __(\'manufacturing.wo_scheduled_start_date\') }}</th>
                        <th>{{ __(\'manufacturing.wo_scheduled_end_date\') }}</th>
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>WO-2025-001</td>
                        <td>Wooden Table (PROD-001)</td>
                        <td>10</td>
                        <td>5</td>
                        <td><span class="badge bg-warning text-dark">{{ __(\'manufacturing.status_in_progress\') }}</span></td>
                        <td>2025-05-15</td>
                        <td>2025-05-20</td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a> {{-- Link to show/edit page --}}
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a> {{-- Link to edit page --}}
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button> {{-- Form for delete --}}
                        </td>
                    </tr>
                     <tr>
                        <td>WO-2025-002</td>
                        <td>Wooden Chair (PROD-002)</td>
                        <td>50</td>
                        <td>0</td>
                        <td><span class="badge bg-primary">{{ __(\'manufacturing.status_planned\') }}</span></td>
                        <td>2025-05-18</td>
                        <td>2025-05-25</td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                            <a href="#" class="btn btn-warning btn-sm">{{ __(\'general.edit\') }}</a>
                            <button class="btn btn-danger btn-sm">{{ __(\'general.delete\') }}</button>
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

