@extends('layouts.admin')

@section('title', 'تفاصيل الضريبة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل الضريبة: {{ $tax->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.taxes.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات الضريبة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">الاسم</th>
                                            <td>{{ $tax->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الرمز</th>
                                            <td>{{ $tax->code }}</td>
                                        </tr>
                                        <tr>
                                            <th>النسبة</th>
                                            <td>{{ $tax->formatted_rate }}</td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if ($tax->is_active)
                                                    <span class="badge badge-success">نشط</span>
                                                @else
                                                    <span class="badge badge-secondary">غير نشط</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>افتراضي</th>
                                            <td>
                                                @if ($tax->is_default)
                                                    <span class="badge badge-primary">افتراضي</span>
                                                @else
                                                    <span class="badge badge-light">غير افتراضي</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>الوصف</th>
                                            <td>{{ $tax->description ?: 'لا يوجد وصف' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $tax->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $tax->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">استخدام الضريبة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> يتم تطبيق هذه الضريبة على المعاملات المالية وفقًا للإعدادات المحددة.
                                    </div>
                                    
                                    <div class="mt-4">
                                        <h6>مثال على حساب الضريبة:</h6>
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <p>المبلغ قبل الضريبة: 1000 ريال</p>
                                                <p>نسبة الضريبة: {{ $tax->formatted_rate }}</p>
                                                <p>قيمة الضريبة: {{ number_format(1000 * $tax->rate / 100, 2) }} ريال</p>
                                                <p>المبلغ بعد الضريبة: {{ number_format(1000 + (1000 * $tax->rate / 100), 2) }} ريال</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">الإجراءات</h5>
                                </div>
                                <div class="card-body">
                                    <a href="{{ route('admin.taxes.edit', $tax) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <form action="{{ route('admin.taxes.destroy', $tax) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الضريبة؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
