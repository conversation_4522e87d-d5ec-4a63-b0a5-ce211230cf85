<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('order_id')->nullable()->comment('Corresponds to the POS order or invoice ID');
            $table->string('payment_method'); // e.g., cash, card, network_pos, tabby, tamara
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('SAR');
            $table->string('status'); // e.g., PENDING, APPROVED, DECLINED, ERROR, CANCELLED
            $table->string('terminal_transaction_id')->nullable()->comment('Transaction ID from the payment terminal');
            $table->string('approval_code')->nullable()->comment('Approval code from the payment network');
            $table->string('masked_pan')->nullable()->comment('Masked PAN (card number)');
            $table->string('card_type')->nullable()->comment('e.g., MADA, VISA, MASTERCARD');
            $table->string('terminal_id')->nullable()->comment('Identifier of the physical terminal used');
            $table->text('response_data')->nullable()->comment('Full response data from the terminal or gateway (JSON)');
            $table->text('error_message')->nullable()->comment('Error message if the transaction failed');
            $table->foreignId('user_id')->nullable()->constrained('users')->comment('User who processed the transaction (cashier)');
            $table->foreignId('customer_id')->nullable()->constrained('customers')->comment('Customer associated with the transaction');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};

