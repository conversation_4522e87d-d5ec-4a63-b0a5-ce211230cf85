@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>Users Management</h2>
        <a href="{{ route("admin.users.create") }}" class="btn btn-primary mb-3">Create New User</a>

        @if (session("success"))
            <div class="alert alert-success">
                {{ session("success") }}
            </div>
        @endif

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Branch</th>
                    <th>Roles</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($users as $user)
                    <tr>
                        <td>{{ $user->id }}</td>
                        <td>{{ $user->name }}</td>
                        <td>{{ $user->email }}</td>
                        <td>{{ $user->branch->name ?? "N/A" }}</td>
                        <td>
                            @forelse ($user->roles as $role)
                                <span class="badge badge-info">{{ $role->name }}</span>
                            @empty
                                No roles assigned
                            @endforelse
                        </td>
                        <td>
                            <a href="{{ route("admin.users.show", $user->id) }}" class="btn btn-info btn-sm">View</a>
                            <a href="{{ route("admin.users.edit", $user->id) }}" class="btn btn-warning btn-sm">Edit</a>
                            <form action="{{ route("admin.users.destroy", $user->id) }}" method="POST" style="display:inline-block;">
                                @csrf
                                @method("DELETE")
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm("Are you sure?")">Delete</button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6">No users found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
@endsection

