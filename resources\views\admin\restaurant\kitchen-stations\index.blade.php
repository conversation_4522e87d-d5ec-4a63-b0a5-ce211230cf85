@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ __("Kitchen Stations") }}</h1>
                <a href="{{ route('admin.restaurant.kitchen-stations.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __("Add New Station") }}
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __("Kitchen Stations List") }}</h5>
                </div>
                <div class="card-body">
                    @if($kitchenStations->count() > 0)
                        <div class="row">
                            @foreach($kitchenStations as $station)
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card station-card h-100">
                                        <div class="card-header" style="background-color: {{ $station->color ?? '#007bff' }}20; border-bottom: 3px solid {{ $station->color ?? '#007bff' }};">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0" style="color: {{ $station->color ?? '#007bff' }}">
                                                    <i class="fas fa-gear me-2"></i>{{ $station->name }}
                                                </h6>
                                                @if($station->is_active)
                                                    <span class="badge bg-success">{{ __("Active") }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ __("Inactive") }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        <div class="card-body">
                                            @if($station->description)
                                                <p class="card-text text-muted">{{ $station->description }}</p>
                                            @endif
                                            
                                            <div class="row text-center mb-3">
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-primary mb-0">{{ $station->menuItems->count() }}</h5>
                                                        <small class="text-muted">{{ __("Menu Items") }}</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-success mb-0">{{ $station->printers->count() }}</h5>
                                                        <small class="text-muted">{{ __("Printers") }}</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="stat-item">
                                                        <h5 class="text-info mb-0">{{ $station->sort_order }}</h5>
                                                        <small class="text-muted">{{ __("Order") }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <strong class="text-muted">{{ __("Branch") }}:</strong>
                                                <span class="badge bg-secondary">{{ $station->branch->name ?? '-' }}</span>
                                            </div>
                                            
                                            @if($station->printers->count() > 0)
                                                <div class="mb-3">
                                                    <strong class="text-muted">{{ __("Printers") }}:</strong>
                                                    <div class="mt-1">
                                                        @foreach($station->printers as $printer)
                                                            <span class="badge {{ $printer->is_active ? 'bg-success' : 'bg-danger' }} me-1">
                                                                {{ $printer->name }}
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('admin.restaurant.kitchen-stations.show', $station) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> {{ __("View") }}
                                                </a>
                                                <a href="{{ route('admin.restaurant.kitchen-stations.edit', $station) }}" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i> {{ __("Edit") }}
                                                </a>
                                                <form action="{{ route('admin.restaurant.kitchen-stations.destroy', $station) }}" 
                                                      method="POST" style="display: inline-block;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            title="{{ __('Delete') }}"
                                                            onclick="return confirm('{{ __('Are you sure you want to delete this kitchen station?') }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $kitchenStations->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-gear fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __("No kitchen stations found") }}</h5>
                            <p class="text-muted">{{ __("Start by creating your first kitchen station.") }}</p>
                            <a href="{{ route('admin.restaurant.kitchen-stations.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> {{ __("Add First Station") }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.station-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
}

.station-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.btn-group .btn {
    flex: 1;
}

.card-header {
    transition: all 0.3s;
}

.station-card:hover .card-header {
    transform: scale(1.02);
}
</style>
@endpush
