{{-- Payment Modal --}}
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">إتمام عملية الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3 text-center">
                    <h4 class="mb-1">الإجمالي المطلوب</h4>
                    <h2 id="payment-modal-total" class="fw-bold display-5 text-success">0.00 ريال</h2>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment-method" class="form-label fw-bold">طريقة الدفع</label>
                        <select class="form-select form-select-lg" id="payment-method">
                            <option value="cash" selected>نقداً <i class="bi bi-cash-coin"></i></option>
                            <option value="card">بطاقة (محاكاة) <i class="bi bi-credit-card"></i></option>
                            <option value="network_pos">شبكة (جهاز نقاط البيع) <i class="bi bi-wifi"></i></option> {{-- New Option --}}
                            <option value="tabby">تابي <i class="bi bi-clock-history"></i></option>
                            <option value="tamara">تمارا <i class="bi bi-wallet2"></i></option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3" id="cash-payment-details">
                        <label for="amount-tendered" class="form-label fw-bold">المبلغ المستلم</label>
                        <input type="number" class="form-control form-control-lg" id="amount-tendered" placeholder="0.00" inputmode="decimal">
                    </div>
                </div>
                
                <div id="cash-change-details" class="mb-3 text-center">
                    <h5 class="mb-1">الباقي للعميل</h5>
                    <h3 id="change-due" class="fw-bold text-info">0.00 ريال</h3>
                </div>

                <div id="card-payment-details" style="display: none;" class="text-center p-3 border rounded bg-light">
                    <p class="fs-5"><i class="bi bi-credit-card-2-front-fill fs-1 text-primary"></i></p>
                    <p class="text-muted">يرجى استخدام جهاز الدفع لإتمام العملية بالبطاقة (هذه محاكاة).</p>
                    <div class="spinner-border text-primary my-2" role="status" id="card-payment-spinner" style="display: none;">
                        <span class="visually-hidden">جاري المعالجة...</span>
                    </div>
                    <p id="card-payment-status" class="mt-2"></p>
                </div>

                {{-- New section for Network POS payment details --}}
                <div id="network-pos-payment-details" style="display: none;" class="text-center p-3 border rounded bg-light">
                    <p class="fs-5"><i class="bi bi-wifi fs-1 text-primary"></i></p>
                    <p class="text-muted" id="network-pos-message">يرجى استخدام جهاز نقاط البيع لإتمام العملية.</p>
                    <div class="spinner-border text-primary my-2" role="status" id="network-pos-spinner" style="display: none;">
                        <span class="visually-hidden">جاري الاتصال بالجهاز...</span>
                    </div>
                    <p id="network-pos-status" class="mt-2"></p>
                </div>

                <div id="bnpl-payment-details" style="display: none;" class="text-center p-3 border rounded bg-light">
                    <p class="fs-5"><i class="bi bi-phone-fill fs-1 text-success"></i></p>
                    <p class="text-muted">سيتم توجيه العميل لإكمال الدفع عبر <span id="bnpl-provider-name"></span>.</p>
                </div>
                
                <hr class="mt-4">
                <div class="mb-2">
                    <p class="fw-bold mb-1">خيارات الإيصال:</p>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="receipt-option-print" value="print" checked>
                        <label class="form-check-label" for="receipt-option-print">طباعة</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="receipt-option-email" value="email">
                        <label class="form-check-label" for="receipt-option-email">إرسال بريد إلكتروني</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="receipt-option-whatsapp" value="whatsapp">
                        <label class="form-check-label" for="receipt-option-whatsapp">إرسال واتساب</label>
                    </div>
                </div>
                <div id="receipt-email-input-group" class="mb-3" style="display:none;">
                    <label for="receipt-customer-email" class="form-label">بريد العميل الإلكتروني</label>
                    <input type="email" class="form-control" id="receipt-customer-email" placeholder="<EMAIL>">
                </div>
                 <div id="receipt-whatsapp-input-group" class="mb-3" style="display:none;">
                    <label for="receipt-customer-whatsapp" class="form-label">رقم واتساب العميل</label>
                    <input type="tel" class="form-control" id="receipt-customer-whatsapp" placeholder="+9665XXXXXXXX">
                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"><i class="bi bi-x-lg me-1"></i> إلغاء</button>
                <button type="button" class="btn btn-success btn-lg px-5" onclick="processPayment()" id="confirm-payment-btn"><i class="bi bi-check-circle-fill me-2"></i>تأكيد الدفع</button>
            </div>
        </div>
    </div>
</div>

@push("scripts")
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const paymentMethodSelect = document.getElementById("payment-method");
        const cashPaymentDetails = document.getElementById("cash-payment-details");
        const cashChangeDetails = document.getElementById("cash-change-details");
        const cardPaymentDetails = document.getElementById("card-payment-details");
        const networkPosPaymentDetails = document.getElementById("network-pos-payment-details"); // New
        const bnplPaymentDetails = document.getElementById("bnpl-payment-details");
        const bnplProviderName = document.getElementById("bnpl-provider-name");
        const amountTenderedInput = document.getElementById("amount-tendered");
        const changeDueElement = document.getElementById("change-due");
        const paymentModalTotalElement = document.getElementById("payment-modal-total");

        const receiptOptionEmail = document.getElementById("receipt-option-email");
        const receiptEmailInputGroup = document.getElementById("receipt-email-input-group");
        const receiptCustomerEmail = document.getElementById("receipt-customer-email");
        const receiptOptionWhatsapp = document.getElementById("receipt-option-whatsapp");
        const receiptWhatsappInputGroup = document.getElementById("receipt-whatsapp-input-group");
        const receiptCustomerWhatsapp = document.getElementById("receipt-customer-whatsapp");

        function updateTotalOnModalShow() {
            if (typeof cart !== "undefined" && typeof deliveryCost !== "undefined") {
                const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                const taxRate = 0.15; 
                const tax = subtotal * taxRate;
                const total = subtotal + tax + deliveryCost;
                if (paymentModalTotalElement) paymentModalTotalElement.textContent = `${total.toFixed(2)} ريال`;
                if (amountTenderedInput) amountTenderedInput.value = "";
                if (changeDueElement) changeDueElement.textContent = "0.00 ريال";
            }
        }

        var paymentModalEl = document.getElementById("paymentModal");
        if (paymentModalEl) {
            paymentModalEl.addEventListener("show.bs.modal", updateTotalOnModalShow);
        }

        if (paymentMethodSelect) {
            paymentMethodSelect.addEventListener("change", function() {
                cashPaymentDetails.style.display = "none";
                cashChangeDetails.style.display = "none";
                cardPaymentDetails.style.display = "none";
                networkPosPaymentDetails.style.display = "none"; // New
                bnplPaymentDetails.style.display = "none";
                document.getElementById("card-payment-status").textContent = "";
                document.getElementById("network-pos-status").textContent = ""; // New
                document.getElementById("network-pos-message").textContent = "يرجى استخدام جهاز نقاط البيع لإتمام العملية."; // Reset message

                if (this.value === "cash") {
                    cashPaymentDetails.style.display = "block";
                    cashChangeDetails.style.display = "block";
                } else if (this.value === "card") {
                    cardPaymentDetails.style.display = "block";
                } else if (this.value === "network_pos") { // New
                    networkPosPaymentDetails.style.display = "block";
                } else if (this.value === "tabby" || this.value === "tamara") {
                    bnplPaymentDetails.style.display = "block";
                    bnplProviderName.textContent = this.value === "tabby" ? "تابي" : "تمارا";
                }
                if (amountTenderedInput) amountTenderedInput.value = "";
                if (changeDueElement) changeDueElement.textContent = "0.00 ريال";
            });
        }

        if (amountTenderedInput && paymentModalTotalElement && changeDueElement) {
            amountTenderedInput.addEventListener("input", function() {
                const totalText = paymentModalTotalElement.textContent.replace(" ريال", "");
                const total = parseFloat(totalText) || 0;
                const tendered = parseFloat(this.value) || 0;
                const change = tendered - total;
                changeDueElement.textContent = `${Math.max(0, change).toFixed(2)} ريال`;
            });
        }

        if(receiptOptionEmail && receiptEmailInputGroup) {
            receiptOptionEmail.addEventListener("change", function() {
                receiptEmailInputGroup.style.display = this.checked ? "block" : "none";
                if(this.checked && typeof selectedCustomer !== "undefined" && selectedCustomer && selectedCustomer.email) {
                    receiptCustomerEmail.value = selectedCustomer.email;
                }
            });
        }
        if(receiptOptionWhatsapp && receiptWhatsappInputGroup) {
            receiptOptionWhatsapp.addEventListener("change", function() {
                receiptWhatsappInputGroup.style.display = this.checked ? "block" : "none";
                if(this.checked && typeof selectedCustomer !== "undefined" && selectedCustomer && selectedCustomer.phone) {
                    receiptCustomerWhatsapp.value = selectedCustomer.phone;
                }
            });
        }
    });

    async function processPayment() {
        const paymentMethod = document.getElementById("payment-method").value;
        const totalAmountText = document.getElementById("payment-modal-total").textContent.replace(" ريال", "");
        const totalAmount = parseFloat(totalAmountText) || 0;
        const amountTendered = document.getElementById("amount-tendered").value;
        const changeDue = document.getElementById("change-due").textContent.replace(" ريال", "");
        const confirmButton = document.getElementById("confirm-payment-btn");
        const orderId = "ORDER-" + Date.now(); // Simple unique ID for the transaction

        confirmButton.disabled = true;
        confirmButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري المعالجة...`;

        let paymentSuccessful = false;
        let paymentMessage = "";
        let terminalData = null;

        if (paymentMethod === "network_pos") {
            const networkPosSpinner = document.getElementById("network-pos-spinner");
            const networkPosStatus = document.getElementById("network-pos-status");
            const networkPosMessage = document.getElementById("network-pos-message");
            
            networkPosSpinner.style.display = "inline-block";
            networkPosStatus.textContent = "جاري إرسال الطلب إلى جهاز الدفع...";
            networkPosMessage.textContent = "يرجى الانتظار...";

            try {
                // Replace with actual API endpoint for initiating network payment
                const response = await fetch("/api/pos/initiate-network-payment", { 
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector("meta[name=\"csrf-token\"]")?.getAttribute("content") // If CSRF is used
                    },
                    body: JSON.stringify({ 
                        amount: totalAmount,
                        transaction_id: orderId 
                    })
                });

                const result = await response.json();
                networkPosSpinner.style.display = "none";

                if (result.success && result.data && result.data.status === "APPROVED") {
                    paymentSuccessful = true;
                    paymentMessage = `تمت الموافقة على عملية الدفع الشبكي. معرف العملية: ${result.data.terminal_transaction_id}`;
                    networkPosStatus.textContent = paymentMessage;
                    networkPosMessage.textContent = "الدفع ناجح!";
                    terminalData = result.data; // Store terminal data for receipt
                } else {
                    paymentSuccessful = false;
                    paymentMessage = result.message || "فشلت عملية الدفع عبر الشبكة.";
                    if(result.data && result.data.error_message) paymentMessage += ` السبب: ${result.data.error_message}`;
                    networkPosStatus.textContent = paymentMessage;
                    networkPosMessage.textContent = "فشل الدفع. يرجى المحاولة مرة أخرى أو اختيار طريقة أخرى.";
                }
            } catch (error) {
                console.error("Network POS payment error:", error);
                networkPosSpinner.style.display = "none";
                paymentSuccessful = false;
                paymentMessage = "خطأ في الاتصال بخدمة الدفع الشبكي. يرجى التحقق من الاتصال والمحاولة مرة أخرى.";
                networkPosStatus.textContent = paymentMessage;
                networkPosMessage.textContent = "خطأ في الاتصال.";
            }
            finalizePaymentProcessing(paymentSuccessful, paymentMessage, terminalData);
            return; 
        }

        // Simulate API call / processing delay for other methods
        setTimeout(() => {
            console.log("Processing payment:", { paymentMethod, totalAmount, amountTendered, changeDue, cart, selectedCustomer });
            
            paymentSuccessful = true; // Simulate success for other methods by default
            paymentMessage = `تمت معالجة الدفع بمبلغ ${totalAmount.toFixed(2)} ريال بطريقة "${paymentMethod}".`;

            if (paymentMethod === "card") {
                document.getElementById("card-payment-spinner").style.display = "inline-block";
                document.getElementById("card-payment-status").textContent = "يرجى تمرير البطاقة (محاكاة)...";
                setTimeout(() => {
                    document.getElementById("card-payment-spinner").style.display = "none";
                    document.getElementById("card-payment-status").textContent = "تمت الموافقة على عملية البطاقة (محاكاة).";
                    finalizePaymentProcessing(paymentSuccessful, paymentMessage);
                }, 2000); 
                return; 
            } else if (paymentMethod === "tabby" || paymentMethod === "tamara"){
                paymentMessage = `تم توجيه العميل لإكمال الدفع عبر ${paymentMethod}. يرجى تأكيد الإتمام من طرف العميل.`
            }

            finalizePaymentProcessing(paymentSuccessful, paymentMessage);

        }, 500); 
    }

    function finalizePaymentProcessing(isSuccess, message, terminalPaymentData = null) {
        const confirmButton = document.getElementById("confirm-payment-btn");
        alert(message); 

        if (isSuccess) {
            const saleDataForReceipt = {
                cart: JSON.parse(JSON.stringify(cart)),
                customer: selectedCustomer ? JSON.parse(JSON.stringify(selectedCustomer)) : null,
                paymentMethod: document.getElementById("payment-method").value,
                amountTendered: document.getElementById("amount-tendered").value,
                changeDue: document.getElementById("change-due").textContent.replace(" ريال", ""),
                invoiceId: "INV-" + Date.now(), // Should be generated backend ideally
                timestamp: Date.now(),
                cashierName: "مندوب مبيعات", // Get from auth if available
                totalAmount: parseFloat(document.getElementById("payment-modal-total").textContent.replace(" ريال", "")) || 0,
                subTotal: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                taxAmount: (cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 0.15),
                deliveryCharge: typeof deliveryCost !== "undefined" ? deliveryCost : 0,
                receiptOptions: {
                    print: document.getElementById("receipt-option-print").checked,
                    email: document.getElementById("receipt-option-email").checked ? document.getElementById("receipt-customer-email").value : null,
                    whatsapp: document.getElementById("receipt-option-whatsapp").checked ? document.getElementById("receipt-customer-whatsapp").value : null,
                },
                terminalPaymentData: terminalPaymentData // Add terminal data here
            };

            var paymentModalEl = document.getElementById("paymentModal");
            if (paymentModalEl) {
                var paymentModalInstance = bootstrap.Modal.getInstance(paymentModalEl);
                if (paymentModalInstance) paymentModalInstance.hide();
            }

            if (typeof showReceiptModal === "function") {
                showReceiptModal(saleDataForReceipt);
            } else {
                console.error("showReceiptModal function is not defined.");
            }

            if (typeof clearCart === "function") {
                clearCart(); 
            }
        }
        confirmButton.disabled = false;
        confirmButton.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>تأكيد الدفع`;
    }

</script>
@endpush

