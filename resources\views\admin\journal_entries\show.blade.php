@extends("layouts.admin")

@section("content")
    <div class="container">
        <h2>تفاصيل القيد المحاسبي: {{ $journalEntry->entry_number }}</h2>

        <div class="card mb-3">
            <div class="card-header">
                معلومات القيد
            </div>
            <div class="card-body">
                <p><strong>رقم القيد:</strong> {{ $journalEntry->entry_number }}</p>
                <p><strong>تاريخ القيد:</strong> {{ $journalEntry->entry_date->format("Y-m-d") }}</p>
                <p><strong>الحالة:</strong> <span class="badge badge-{{ $journalEntry->status_color }}">
                    @if($journalEntry->status == 'draft')
                        مسودة
                    @elseif($journalEntry->status == 'posted')
                        مرحل
                    @elseif($journalEntry->status == 'cancelled')
                        ملغي
                    @else
                        {{ ucfirst($journalEntry->status) }}
                    @endif
                </span></p>
                <p><strong>الفرع:</strong> {{ $journalEntry->branch->name ?? "غير محدد" }}</p>
                <p><strong>الوصف:</strong> {{ $journalEntry->description ?? "غير محدد" }}</p>
                <p><strong>تم الإنشاء بواسطة:</strong> {{ $journalEntry->createdBy->name ?? "غير محدد" }} في {{ $journalEntry->created_at->format("Y-m-d H:i:s") }}</p>
                @if($journalEntry->postedBy)
                <p><strong>تم الترحيل بواسطة:</strong> {{ $journalEntry->postedBy->name ?? "غير محدد" }} في {{ $journalEntry->posted_at ? $journalEntry->posted_at->format("Y-m-d H:i:s") : "غير محدد" }}</p>
                @endif
            </div>
        </div>

        <h4>بنود القيد المحاسبي</h4>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الحساب</th>
                    <th>المبلغ المدين</th>
                    <th>المبلغ الدائن</th>
                    <th>الوصف</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($journalEntry->items as $item)
                    <tr>
                        <td>{{ $item->account->name }} ({{ $item->account->code }})</td>
                        <td>{{ number_format($item->debit_amount, 2) }}</td>
                        <td>{{ number_format($item->credit_amount, 2) }}</td>
                        <td>{{ $item->description ?? "غير محدد" }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4">لا توجد بنود لهذا القيد المحاسبي.</td>
                    </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <td class="text-right"><strong>الإجمالي:</strong></td>
                    <td><strong>{{ number_format($journalEntry->items->sum("debit_amount"), 2) }}</strong></td>
                    <td><strong>{{ number_format($journalEntry->items->sum("credit_amount"), 2) }}</strong></td>
                    <td></td>
                </tr>
            </tfoot>
        </table>

        @if($journalEntry->status == "draft")
            <a href="{{ route("admin.journal_entries.edit", $journalEntry->id) }}" class="btn btn-warning">تعديل القيد</a>
            <form action="{{ route("admin.journal_entries.post", $journalEntry->id) }}" method="POST" style="display:inline-block;">
                @csrf
                <button type="submit" class="btn btn-success">ترحيل القيد</button>
            </form>
        @endif
        @if($journalEntry->status == "posted")
            <form action="{{ route("admin.journal_entries.cancel", $journalEntry->id) }}" method="POST" style="display:inline-block;">
                @csrf
                <button type="submit" class="btn btn-secondary">إلغاء القيد</button>
            </form>
        @endif
        <a href="{{ route("admin.journal_entries.index") }}" class="btn btn-secondary">العودة للقائمة</a>
    </div>
@endsection

