@extends("layouts.admin")

@section("content")
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ __("Restaurant Areas") }}</h1>
                <a href="{{ route('admin.restaurant.areas.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __("Add New Area") }}
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ __("Areas List") }}</h5>
                </div>
                <div class="card-body">
                    @if($areas->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ __("Name") }}</th>
                                        <th>{{ __("Description") }}</th>
                                        <th>{{ __("Color") }}</th>
                                        <th>{{ __("Tables Count") }}</th>
                                        <th>{{ __("Branch") }}</th>
                                        <th>{{ __("Sort Order") }}</th>
                                        <th>{{ __("Status") }}</th>
                                        <th>{{ __("Actions") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($areas as $area)
                                        <tr>
                                            <td>
                                                <strong>{{ $area->name }}</strong>
                                            </td>
                                            <td>{{ $area->description ?? '-' }}</td>
                                            <td>
                                                <span class="badge" style="background-color: {{ $area->color }}; color: white;">
                                                    {{ $area->color }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ $area->tables->count() }} {{ __("Tables") }}
                                                </span>
                                            </td>
                                            <td>{{ $area->branch->name ?? '-' }}</td>
                                            <td>{{ $area->sort_order }}</td>
                                            <td>
                                                @if($area->is_active)
                                                    <span class="badge bg-success">{{ __("Active") }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ __("Inactive") }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.restaurant.areas.show', $area) }}" 
                                                       class="btn btn-sm btn-outline-info" title="{{ __('View') }}">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.restaurant.areas.edit', $area) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="{{ __('Edit') }}">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.restaurant.areas.destroy', $area) }}" 
                                                          method="POST" style="display: inline-block;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                title="{{ __('Delete') }}"
                                                                onclick="return confirm('{{ __('Are you sure you want to delete this area?') }}')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $areas->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{{ __("No restaurant areas found") }}</h5>
                            <p class="text-muted">{{ __("Start by creating your first restaurant area.") }}</p>
                            <a href="{{ route('admin.restaurant.areas.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> {{ __("Add First Area") }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
@endpush
