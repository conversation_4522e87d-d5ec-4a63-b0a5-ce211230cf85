{{-- القائمة الجانبية المُعاد ترتيبها --}}
<div class="sidebar-menu">
    <!-- لوحة التحكم -->
    <a href="{{ route('admin.dashboard') }}" class="{{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
        <i class="bi bi-speedometer2"></i>
        <span>لوحة التحكم</span>
    </a>

    @if(Auth::check() && Auth::user()->hasRole('super_admin'))
    <!-- قسم السوبر أدمن -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">السوبر أدمن</small>
    </div>

    <a href="{{ route('admin.super_admin.dashboard') }}" class="{{ request()->routeIs('admin.super_admin.dashboard') ? 'active' : '' }}">
        <i class="bi bi-speedometer"></i>
        <span>لوحة تحكم السوبر أدمن</span>
    </a>

    <a href="{{ route('admin.super_admin.subscription_plans.index') }}" class="{{ request()->routeIs('admin.super_admin.subscription_plans.*') ? 'active' : '' }}">
        <i class="bi bi-list-stars"></i>
        <span>إدارة الباقات</span>
    </a>

    <a href="{{ route('admin.super_admin.invoice_limits.index') }}" class="{{ request()->routeIs('admin.super_admin.invoice_limits.*') ? 'active' : '' }}">
        <i class="bi bi-file-earmark-ruled"></i>
        <span>حدود الفواتير</span>
    </a>
    @endif

    <!-- قسم المحاسبة والمالية -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المحاسبة والمالية</small>
    </div>

    <!-- دليل الحسابات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.accounts.*') || request()->routeIs('admin.account_types.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-journal-bookmark-fill"></i>
            <span>دليل الحسابات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.accounts.new') }}">
                <i class="bi bi-journal-text me-2"></i> الحسابات
            </a>
            <a class="dropdown-item" href="{{ route('admin.account_types.index') }}">
                <i class="bi bi-tags me-2"></i> أنواع الحسابات
            </a>
        </div>
    </div>

    <a href="{{ route('admin.journal_entries.index') }}" class="{{ request()->routeIs('admin.journal_entries.*') ? 'active' : '' }}">
        <i class="bi bi-journal-plus"></i>
        <span>القيود المحاسبية</span>
    </a>

    <a href="{{ route('admin.fiscal_years.index') }}" class="{{ request()->routeIs('admin.fiscal_years.*') ? 'active' : '' }}">
        <i class="bi bi-calendar-range-fill"></i>
        <span>السنوات المالية</span>
    </a>

    <a href="{{ route('admin.taxes.index') }}" class="{{ request()->routeIs('admin.taxes.*') ? 'active' : '' }}">
        <i class="bi bi-percent"></i>
        <span>الضرائب والرسوم</span>
    </a>

    <!-- قسم المبيعات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المبيعات</small>
    </div>

    <a href="{{ route('admin.sales.customers.index') }}" class="{{ request()->routeIs('admin.sales.customers.*') ? 'active' : '' }}">
        <i class="bi bi-person-badge-fill"></i>
        <span>العملاء</span>
    </a>

    <a href="{{ route('admin.sales.invoices.index') }}" class="{{ request()->routeIs('admin.sales.invoices.*') ? 'active' : '' }}">
        <i class="bi bi-receipt"></i>
        <span>فواتير المبيعات</span>
    </a>

    <a href="{{ route('admin.sales.quotations.index') }}" class="{{ request()->routeIs('admin.sales.quotations.*') ? 'active' : '' }}">
        <i class="bi bi-file-earmark-text-fill"></i>
        <span>عروض الأسعار</span>
    </a>

    <a href="{{ route('admin.sales.returns.index') }}" class="{{ request()->routeIs('admin.sales.returns.*') ? 'active' : '' }}">
        <i class="bi bi-arrow-return-left"></i>
        <span>مرتجعات المبيعات</span>
    </a>

    <!-- قسم المشتريات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المشتريات</small>
    </div>

    <a href="{{ route('admin.purchases.suppliers.index') }}" class="{{ request()->routeIs('admin.purchases.suppliers.*') ? 'active' : '' }}">
        <i class="bi bi-truck"></i>
        <span>الموردين</span>
    </a>

    <a href="{{ route('admin.purchases.invoices.index') }}" class="{{ request()->routeIs('admin.purchases.invoices.*') ? 'active' : '' }}">
        <i class="bi bi-bag-fill"></i>
        <span>فواتير المشتريات</span>
    </a>

    <a href="{{ route('admin.purchases.orders.index') }}" class="{{ request()->routeIs('admin.purchases.orders.*') ? 'active' : '' }}">
        <i class="bi bi-cart-plus-fill"></i>
        <span>طلبات الشراء</span>
    </a>

    <a href="{{ route('admin.purchases.returns.index') }}" class="{{ request()->routeIs('admin.purchases.returns.*') ? 'active' : '' }}">
        <i class="bi bi-arrow-return-right"></i>
        <span>مرتجعات المشتريات</span>
    </a>

    <!-- قسم المخزون والمنتجات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">المخزون والمنتجات</small>
    </div>

    <!-- إدارة الأصناف -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.items.*') || request()->routeIs('admin.inventory.categories.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-box-seam-fill"></i>
            <span>إدارة الأصناف</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.items.index') }}">
                <i class="bi bi-box me-2"></i> الأصناف
            </a>
            <a class="dropdown-item" href="{{ route('admin.inventory.categories.index') }}">
                <i class="bi bi-tags me-2"></i> التصنيفات
            </a>
        </div>
    </div>

    <!-- إدارة المخزون -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.inventory.warehouses.*') || request()->routeIs('admin.inventory.adjustments.*') || request()->routeIs('admin.inventory.transfers.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-building-fill-check"></i>
            <span>إدارة المخزون</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.inventory.warehouses.index') }}">
                <i class="bi bi-building me-2"></i> المستودعات
            </a>
            <a class="dropdown-item" href="{{ route('admin.inventory.adjustments.index') }}">
                <i class="bi bi-arrow-up-down me-2"></i> تسويات المخزون
            </a>
            <a class="dropdown-item" href="{{ route('admin.inventory.transfers.index') }}">
                <i class="bi bi-arrow-left-right me-2"></i> نقل المخزون
            </a>
        </div>
    </div>

    <!-- قسم التصنيع -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التصنيع</small>
    </div>

    <!-- إدارة الإنتاج -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.manufacturing.boms.*') || request()->routeIs('admin.manufacturing.work_orders.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-gear-wide-connected"></i>
            <span>إدارة الإنتاج</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.manufacturing.boms.index') }}">
                <i class="bi bi-diagram-3 me-2"></i> قوائم المواد
            </a>
            <a class="dropdown-item" href="{{ route('admin.manufacturing.work_orders.index') }}">
                <i class="bi bi-clipboard-check me-2"></i> أوامر العمل
            </a>
        </div>
    </div>

    <a href="{{ route('admin.manufacturing.operations.index') }}" class="{{ request()->routeIs('admin.manufacturing.operations.*') ? 'active' : '' }}">
        <i class="bi bi-gear-fill"></i>
        <span>العمليات</span>
    </a>

    <!-- قسم نقاط البيع -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نقاط البيع</small>
    </div>

    <a href="{{ route('pos.main') }}" target="_blank" class="text-success">
        <i class="bi bi-display"></i>
        <span>فتح نقطة البيع</span>
    </a>

    <!-- إدارة نقاط البيع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.pos.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-pc-display"></i>
            <span>إدارة نقاط البيع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.pos.terminals.index') }}">
                <i class="bi bi-pc me-2"></i> أجهزة نقاط البيع
            </a>
            <a class="dropdown-item" href="{{ route('admin.pos.sessions.index') }}">
                <i class="bi bi-clock-history me-2"></i> جلسات البيع
            </a>
            <a class="dropdown-item" href="{{ route('admin.pos.settings.index') }}">
                <i class="bi bi-sliders me-2"></i> إعدادات نقاط البيع
            </a>
        </div>
    </div>

    <!-- قسم الموارد البشرية -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الموارد البشرية</small>
    </div>

    <a href="{{ route('admin.hr.employees.index') }}" class="{{ request()->routeIs('admin.hr.employees.*') ? 'active' : '' }}">
        <i class="bi bi-person-vcard-fill"></i>
        <span>الموظفين</span>
    </a>

    <a href="{{ route('admin.hr.departments.index') }}" class="{{ request()->routeIs('admin.hr.departments.*') ? 'active' : '' }}">
        <i class="bi bi-diagram-2-fill"></i>
        <span>الأقسام</span>
    </a>

    <a href="{{ route('admin.hr.payroll.index') }}" class="{{ request()->routeIs('admin.hr.payroll.*') ? 'active' : '' }}">
        <i class="bi bi-cash-stack"></i>
        <span>الرواتب</span>
    </a>

    <!-- قسم التكاملات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التكاملات</small>
    </div>

    <!-- تكامل ZATCA -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.integrations.zatca.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-shield-check text-success"></i>
            <span>هيئة الزكاة والضريبة</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.integrations.zatca.index') }}">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.zatca.edit') }}">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.zatca.show') }}">
                <i class="bi bi-file-text me-2"></i> سجلات الفواتير
            </a>
        </div>
    </div>

    <!-- تكامل واتساب -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.whatsapp.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-whatsapp text-success"></i>
            <span>تكامل واتساب</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.whatsapp.settings.index') }}">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="{{ route('admin.whatsapp.messages.index') }}">
                <i class="bi bi-chat-dots me-2"></i> الرسائل
            </a>
            <a class="dropdown-item" href="{{ route('admin.whatsapp.templates.index') }}">
                <i class="bi bi-file-text me-2"></i> القوالب
            </a>
            <a class="dropdown-item" href="{{ route('admin.whatsapp.opt_ins.index') }}">
                <i class="bi bi-person-check me-2"></i> الموافقات
            </a>
        </div>
    </div>

    <!-- تكامل BNPL -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.integrations.bnpl.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-credit-card-2-front text-primary"></i>
            <span>اشتري الآن وادفع لاحقاً</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.integrations.bnpl.index') }}">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.bnpl.settings') }}">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.bnpl.transactions.index') }}">
                <i class="bi bi-list-ul me-2"></i> المعاملات
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.bnpl.providers.index') }}">
                <i class="bi bi-building me-2"></i> مقدمي الخدمة
            </a>
        </div>
    </div>

    <!-- تكامل أجهزة الدفع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.integrations.payment_terminals.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-credit-card text-info"></i>
            <span>أجهزة الدفع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.integrations.payment_terminals.index') }}">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.payment_terminals.settings') }}">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.payment_terminals.transactions.index') }}">
                <i class="bi bi-list-ul me-2"></i> المعاملات
            </a>
        </div>
    </div>

    <!-- تكامل التوصيل -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.integrations.delivery.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-truck text-warning"></i>
            <span>خدمات التوصيل</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.integrations.delivery.index') }}">
                <i class="bi bi-speedometer me-2"></i> لوحة التحكم
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.delivery.settings') }}">
                <i class="bi bi-gear me-2"></i> الإعدادات
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.delivery.orders.index') }}">
                <i class="bi bi-box me-2"></i> طلبات التوصيل
            </a>
            <a class="dropdown-item" href="{{ route('admin.integrations.delivery.tracking.index') }}">
                <i class="bi bi-geo-alt me-2"></i> تتبع الطلبات
            </a>
        </div>
    </div>

    <!-- قسم التقارير -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">التقارير</small>
    </div>

    <!-- التقارير المالية -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.reports.financial.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-file-earmark-bar-graph-fill"></i>
            <span>التقارير المالية</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.reports.financial.index') }}">
                <i class="bi bi-graph-up me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="{{ route('admin.financial_reports.index') }}">
                <i class="bi bi-file-earmark-spreadsheet me-2"></i> التقارير المحاسبية
            </a>
        </div>
    </div>

    <!-- تقارير المبيعات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.sales.reports.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-cart-check-fill"></i>
            <span>تقارير المبيعات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.sales.reports.index') }}">
                <i class="bi bi-graph-up me-2"></i> تقارير عامة
            </a>
            <a class="dropdown-item" href="{{ route('admin.reports.sales.index') }}">
                <i class="bi bi-bar-chart me-2"></i> تحليل المبيعات
            </a>
        </div>
    </div>

    <!-- تقارير المشتريات -->
    <a href="{{ route('admin.reports.purchases.index') }}" class="{{ request()->routeIs('admin.reports.purchases.*') ? 'active' : '' }}">
        <i class="bi bi-bag-check-fill"></i>
        <span>تقارير المشتريات</span>
    </a>

    <!-- تقارير المخزون -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.reports.inventory.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-boxes"></i>
            <span>تقارير المخزون</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.reports.inventory.index') }}">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="{{ route('admin.reports.inventory.stock_levels') }}">
                <i class="bi bi-bar-chart me-2"></i> مستويات المخزون
            </a>
            <a class="dropdown-item" href="{{ route('admin.reports.inventory.low_stock') }}">
                <i class="bi bi-exclamation-triangle me-2"></i> المخزون المنخفض
            </a>
        </div>
    </div>

    <!-- تقارير التصنيع -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.manufacturing.reports.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-gear-wide"></i>
            <span>تقارير التصنيع</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.manufacturing.reports.index') }}">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="{{ route('admin.manufacturing.reports.production_summary') }}">
                <i class="bi bi-graph-up me-2"></i> ملخص الإنتاج
            </a>
            <a class="dropdown-item" href="{{ route('admin.manufacturing.reports.material_consumption') }}">
                <i class="bi bi-box me-2"></i> استهلاك المواد
            </a>
        </div>
    </div>

    <!-- قسم نظام التذاكر -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">نظام التذاكر</small>
    </div>

    <!-- إدارة التذاكر -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.ticketing.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-ticket-perforated-fill"></i>
            <span>إدارة التذاكر</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.ticketing.tickets.index') }}">
                <i class="bi bi-ticket me-2"></i> التذاكر
            </a>
            <a class="dropdown-item" href="{{ route('admin.ticketing.ticket_categories.index') }}">
                <i class="bi bi-tags me-2"></i> التصنيفات
            </a>
            <a class="dropdown-item" href="{{ route('admin.ticketing.ticket_priorities.index') }}">
                <i class="bi bi-exclamation-circle me-2"></i> الأولويات
            </a>
            <a class="dropdown-item" href="{{ route('admin.ticketing.ticket_statuses.index') }}">
                <i class="bi bi-check-circle me-2"></i> الحالات
            </a>
        </div>
    </div>

    <!-- تقارير التذاكر -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.ticketing.reports.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-graph-up"></i>
            <span>تقارير التذاكر</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.ticketing.reports.index') }}">
                <i class="bi bi-list-ul me-2"></i> التقارير العامة
            </a>
            <a class="dropdown-item" href="{{ route('admin.ticketing.reports.performance') }}">
                <i class="bi bi-speedometer me-2"></i> تقرير الأداء
            </a>
        </div>
    </div>

    <!-- قسم الإدارة والإعدادات -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الإدارة والإعدادات</small>
    </div>

    <a href="{{ route('admin.users.index') }}" class="{{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
        <i class="bi bi-people-fill"></i>
        <span>المستخدمين</span>
    </a>

    <a href="{{ route('admin.branches.index') }}" class="{{ request()->routeIs('admin.branches.*') ? 'active' : '' }}">
        <i class="bi bi-building-fill"></i>
        <span>الفروع</span>
    </a>

    <!-- نظام الصلاحيات -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.permissions_system.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-shield-lock-fill"></i>
            <span>نظام الصلاحيات</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.permissions_system.roles.index') }}">
                <i class="bi bi-person-badge me-2"></i> الأدوار
            </a>
            <a class="dropdown-item" href="{{ route('admin.permissions_system.permissions.index') }}">
                <i class="bi bi-key me-2"></i> الصلاحيات
            </a>
            <a class="dropdown-item" href="{{ route('admin.permissions_system.groups.index') }}">
                <i class="bi bi-folder me-2"></i> مجموعات الصلاحيات
            </a>
        </div>
    </div>

    <a href="{{ route('admin.settings.index') }}" class="{{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
        <i class="bi bi-gear-fill"></i>
        <span>إعدادات النظام</span>
    </a>

    <!-- قسم النظام -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">النظام</small>
    </div>

    <!-- النسخ الاحتياطية -->
    <div class="dropdown">
        <a href="#" class="dropdown-toggle {{ request()->routeIs('admin.system.backup.*') ? 'active' : '' }}" data-bs-toggle="dropdown">
            <i class="bi bi-cloud-arrow-up-fill"></i>
            <span>النسخ الاحتياطية</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="{{ route('admin.system.backup.index') }}">
                <i class="bi bi-list-ul me-2"></i> إدارة النسخ
            </a>
            <a class="dropdown-item" href="{{ route('admin.system.backup.settings') }}">
                <i class="bi bi-gear me-2"></i> إعدادات النسخ
            </a>
        </div>
    </div>

    <!-- سجلات النظام -->
    <a href="{{ route('admin.system.logs.index') }}" class="{{ request()->routeIs('admin.system.logs.*') ? 'active' : '' }}">
        <i class="bi bi-file-text-fill"></i>
        <span>سجلات النظام</span>
    </a>

    <!-- اختبار العملة -->
    <a href="{{ route('admin.currency.test') }}" class="{{ request()->routeIs('admin.currency.test') ? 'active' : '' }}">
        <i class="bi bi-currency-exchange"></i>
        <span>اختبار العملة</span>
    </a>

    <!-- تسجيل الخروج -->
    <div class="menu-category mt-4 mb-2">
        <small class="text-uppercase px-3 text-muted fw-bold">الجلسة</small>
    </div>

    <a href="{{ route('logout.confirm') }}" class="text-danger">
        <i class="bi bi-box-arrow-right"></i>
        <span>تسجيل الخروج</span>
    </a>
</div>
