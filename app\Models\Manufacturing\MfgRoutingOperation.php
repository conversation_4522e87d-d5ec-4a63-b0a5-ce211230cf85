<?php

namespace App\Models\Manufacturing;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MfgRoutingOperation extends Model
{
    use HasFactory;

    protected $table = 'mfg_routing_operations';

    protected $fillable = [
        'mfg_routing_id',
        'mfg_operation_id',
        'sequence',
        'work_center_id',
        'setup_time_hours',
        'run_time_per_unit_hours',
        'concurrent_operations',
        'cost_per_hour_override',
        'instructions',
    ];

    protected $casts = [
        'setup_time_hours' => 'decimal:2',
        'run_time_per_unit_hours' => 'decimal:4',
        'cost_per_hour_override' => 'decimal:4',
    ];

    /**
     * Get the routing this operation belongs to.
     */
    public function mfgRouting()
    {
        return $this->belongsTo(MfgRouting::class, 'mfg_routing_id');
    }

    /**
     * Get the operation details.
     */
    public function mfgOperation()
    {
        return $this->belongsTo(MfgOperation::class, 'mfg_operation_id');
    }

    /**
     * Get the work center for this specific routing step (overrides operation default).
     */
    public function workCenter()
    {
        return $this->belongsTo(MfgWorkCenter::class, 'work_center_id');
    }
}

