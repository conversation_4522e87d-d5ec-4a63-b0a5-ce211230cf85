<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesVisit;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\SalesRepresentatives\SalesRoute;
use App\Models\Modules\Sales\Customer;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SalesVisitController extends Controller
{
    public function index()
    {
        $visits = SalesVisit::with(['salesRepresentative', 'customer', 'salesRoute', 'branch'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.sales-representatives.visits.index', compact('visits'));
    }

    public function create()
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $routes = SalesRoute::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.visits.form', compact('representatives', 'customers', 'routes', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'customer_id' => 'required|exists:customers,id',
            'sales_route_id' => 'nullable|exists:sales_routes,id',
            'visit_date' => 'required|date',
            'planned_start_time' => 'nullable|date_format:H:i',
            'planned_end_time' => 'nullable|date_format:H:i|after:planned_start_time',
            'visit_type' => 'required|in:scheduled,unscheduled,follow_up,collection,delivery',
            'visit_purpose' => 'required|in:sales,collection,delivery,survey,complaint,maintenance',
            'visit_notes' => 'nullable|string',
            'requires_follow_up' => 'boolean',
            'next_visit_date' => 'nullable|date|after:visit_date',
            'follow_up_notes' => 'nullable|string',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['visit_code'] = SalesVisit::generateVisitCode();
        $validatedData['visit_status'] = 'planned';
        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['requires_follow_up'] = $request->has('requires_follow_up');

        SalesVisit::create($validatedData);

        return redirect()->route('admin.sales-representatives.visits.index')
            ->with('success', __('Sales visit created successfully.'));
    }

    public function show(SalesVisit $visit)
    {
        $visit->load(['salesRepresentative', 'customer', 'salesRoute', 'branch']);

        return view('admin.sales-representatives.visits.show', compact('visit'));
    }

    public function edit(SalesVisit $visit)
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $routes = SalesRoute::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->ordered()
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.visits.form', compact('visit', 'representatives', 'customers', 'routes', 'branches'));
    }

    public function update(Request $request, SalesVisit $visit)
    {
        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'customer_id' => 'required|exists:customers,id',
            'sales_route_id' => 'nullable|exists:sales_routes,id',
            'visit_date' => 'required|date',
            'planned_start_time' => 'nullable|date_format:H:i',
            'planned_end_time' => 'nullable|date_format:H:i|after:planned_start_time',
            'visit_type' => 'required|in:scheduled,unscheduled,follow_up,collection,delivery',
            'visit_purpose' => 'required|in:sales,collection,delivery,survey,complaint,maintenance',
            'visit_notes' => 'nullable|string',
            'customer_feedback' => 'nullable|string',
            'order_amount' => 'nullable|numeric|min:0',
            'collection_amount' => 'nullable|numeric|min:0',
            'requires_follow_up' => 'boolean',
            'next_visit_date' => 'nullable|date|after:visit_date',
            'follow_up_notes' => 'nullable|string',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['requires_follow_up'] = $request->has('requires_follow_up');

        $visit->update($validatedData);

        return redirect()->route('admin.sales-representatives.visits.index')
            ->with('success', __('Sales visit updated successfully.'));
    }

    public function destroy(SalesVisit $visit)
    {
        // Only allow deletion of planned visits
        if ($visit->visit_status !== 'planned') {
            return redirect()->route('admin.sales-representatives.visits.index')
                ->with('error', __('Cannot delete visit that has been started or completed.'));
        }

        $visit->delete();

        return redirect()->route('admin.sales-representatives.visits.index')
            ->with('success', __('Sales visit deleted successfully.'));
    }

    public function startVisit(Request $request, SalesVisit $visit)
    {
        if ($visit->visit_status !== 'planned') {
            return response()->json([
                'success' => false,
                'message' => __('Visit has already been started or completed.')
            ], 422);
        }

        $visit->startVisit();

        return response()->json([
            'success' => true,
            'message' => __('Visit started successfully.'),
            'status' => $visit->visit_status,
            'actual_start_time' => $visit->actual_start_time?->format('H:i')
        ]);
    }

    public function completeVisit(Request $request, SalesVisit $visit)
    {
        $validatedData = $request->validate([
            'visit_result' => 'required|in:successful,unsuccessful,partial,rescheduled',
            'visit_notes' => 'nullable|string',
            'customer_feedback' => 'nullable|string',
            'order_amount' => 'nullable|numeric|min:0',
            'collection_amount' => 'nullable|numeric|min:0',
            'requires_follow_up' => 'boolean',
            'next_visit_date' => 'nullable|date|after:today',
            'follow_up_notes' => 'nullable|string',
        ]);

        $validatedData['requires_follow_up'] = $request->has('requires_follow_up');

        $visit->completeVisit($validatedData);

        return response()->json([
            'success' => true,
            'message' => __('Visit completed successfully.'),
            'status' => $visit->visit_status,
            'result' => $visit->visit_result
        ]);
    }

    public function cancelVisit(Request $request, SalesVisit $visit)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $visit->cancelVisit($request->reason);

        return response()->json([
            'success' => true,
            'message' => __('Visit cancelled successfully.'),
            'status' => $visit->visit_status
        ]);
    }

    public function calendar()
    {
        $visits = SalesVisit::with(['salesRepresentative', 'customer'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereBetween('visit_date', [now()->startOfMonth(), now()->endOfMonth()])
            ->get();

        $calendarEvents = $visits->map(function ($visit) {
            return [
                'id' => $visit->id,
                'title' => $visit->customer->name . ' - ' . $visit->salesRepresentative->name,
                'start' => $visit->visit_date->format('Y-m-d') . 'T' . ($visit->planned_start_time ?? '09:00'),
                'end' => $visit->visit_date->format('Y-m-d') . 'T' . ($visit->planned_end_time ?? '10:00'),
                'backgroundColor' => $visit->visit_status_color,
                'borderColor' => $visit->visit_status_color,
                'url' => route('admin.sales-representatives.visits.show', $visit),
            ];
        });

        return view('admin.sales-representatives.visits.calendar', compact('calendarEvents'));
    }

    public function map()
    {
        $visits = SalesVisit::with(['salesRepresentative', 'customer'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->whereDate('visit_date', now())
            ->whereNotNull('location_coordinates')
            ->get();

        return view('admin.sales-representatives.visits.map', compact('visits'));
    }

    public function getVisitsByRepresentative(Request $request)
    {
        $representativeId = $request->get('representative_id');
        $date = $request->get('date', now()->format('Y-m-d'));
        
        $visits = SalesVisit::with(['customer', 'salesRoute'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('sales_representative_id', $representativeId)
            ->whereDate('visit_date', $date)
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $visits
        ]);
    }

    public function getTodayVisits()
    {
        $visits = SalesVisit::with(['salesRepresentative', 'customer', 'salesRoute'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->today()
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $visits
        ]);
    }
}
