<?php

namespace App\Http\Controllers\Modules\Branches;

use App\Http\Controllers\Controller;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;

class BranchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $branches = Branch::with("parent")->latest()->paginate(10);
        return view("admin.branches.index", compact("branches"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::where("is_active", true)->get();
        return view("admin.branches.create", compact("branches"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => "required|string|max:255",
            "code" => "nullable|string|max:50|unique:branches,code",
            "address" => "nullable|string",
            "phone" => "nullable|string|max:20",
            "email" => "nullable|email|max:255",
            "parent_id" => "nullable|exists:branches,id",
            "is_active" => "required|boolean",
        ]);

        Branch::create($request->all());

        return redirect()->route("admin.branches.index")
                         ->with("success", "Branch created successfully.");
    }

    /**
     * Display the specified resource.
     */
    public function show(Branch $branch)
    {
        return view("admin.branches.show", compact("branch"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Branch $branch)
    {
        $allBranches = Branch::where("is_active", true)->where("id", "!=", $branch->id)->get(); // Exclude self from parent list
        return view("admin.branches.edit", compact("branch", "allBranches"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Branch $branch)
    {
        $request->validate([
            "name" => "required|string|max:255",
            "code" => "nullable|string|max:50|unique:branches,code," . $branch->id,
            "address" => "nullable|string",
            "phone" => "nullable|string|max:20",
            "email" => "nullable|email|max:255",
            "parent_id" => "nullable|exists:branches,id",
            "is_active" => "required|boolean",
        ]);

        $branch->update($request->all());

        return redirect()->route("admin.branches.index")
                         ->with("success", "Branch updated successfully.");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Branch $branch)
    {
        // Add logic here to check if branch can be deleted (e.g., no child branches, no associated users/transactions)
        // For now, a simple delete
        try {
            $branch->delete();
            return redirect()->route("admin.branches.index")
                             ->with("success", "Branch deleted successfully.");
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle foreign key constraint violation or other DB errors
            return redirect()->route("admin.branches.index")
                             ->with("error", "Could not delete branch. It might be associated with other records.");
        }
    }
}

