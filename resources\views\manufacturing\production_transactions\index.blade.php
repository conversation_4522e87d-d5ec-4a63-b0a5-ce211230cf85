@extends(\'layouts.app\')

@section(\'content\')
<div class="container-fluid">
    <h1 class="mt-4">{{ __(\'manufacturing.production_transactions_title\') }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route(\'manufacturing.dashboard\') }}">{{ __(\'manufacturing.dashboard_title\') }}</a></li>
        <li class="breadcrumb-item active">{{ __(\'manufacturing.production_transactions_title\') }}</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-exchange-alt me-1"></i>
            {{ __(\'manufacturing.production_transactions_list\') }}
            {{-- Typically, transactions are created from Work Orders, not directly from a list page --}}
            {{-- <a href="#" class="btn btn-primary btn-sm float-end">{{ __(\'manufacturing.add_new_transaction\') }}</a> --}}
        </div>
        <div class="card-body">
            {{-- Placeholder for production transactions table --}}
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{{ __(\'manufacturing.transaction_id\') }}</th>
                        <th>{{ __(\'manufacturing.wo_number\') }}</th>
                        <th>{{ __(\'manufacturing.product_name\') }}</th>
                        <th>{{ __(\'manufacturing.transaction_type\') }}</th>
                        <th>{{ __(\'manufacturing.quantity\') }}</th>
                        <th>{{ __(\'manufacturing.transaction_date\') }}</th>
                        <th>{{ __(\'manufacturing.employee_name\') }}</th> {{-- Assuming employee relationship --}}
                        <th>{{ __(\'manufacturing.actions\') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Example Row - Loop through actual data here --}}
                    <tr>
                        <td>TRANS-001</td>
                        <td>WO-2025-001</td>
                        <td>Wood Plank (RAW-001)</td>
                        <td>{{ __(\'manufacturing.transaction_type_issue\') }}</td>
                        <td>20.00</td>
                        <td>2025-05-15 10:00</td>
                        <td>John Doe</td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                        </td>
                    </tr>
                     <tr>
                        <td>TRANS-002</td>
                        <td>WO-2025-001</td>
                        <td>Wooden Table (PROD-001)</td>
                        <td>{{ __(\'manufacturing.transaction_type_receipt\') }}</td>
                        <td>5.00</td>
                        <td>2025-05-15 16:00</td>
                        <td>Jane Smith</td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm">{{ __(\'general.view\') }}</a>
                        </td>
                    </tr>
                    {{-- End Example Row --}}
                </tbody>
            </table>
            {{-- Placeholder for pagination --}}
        </div>
    </div>
</div>
@endsection

@push(\'scripts\')
{{-- Add any specific scripts for this page, e.g., for DataTable --}}
@endpush

