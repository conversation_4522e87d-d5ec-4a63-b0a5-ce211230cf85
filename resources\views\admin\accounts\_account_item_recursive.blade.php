@foreach ($accounts as $account)
    <li style="margin-left: {{ $level * 20 }}px;">
        <strong>{{ $account->code }} - {{ $account->name_ar }}</strong> ({{ $account->accountType->name_ar }}) - الرصيد: {{ number_format($account->opening_balance_debit - $account->opening_balance_credit, 2) }}
        <a href="{{ route("admin.accounts.show", $account->id) }}" class="btn btn-sm btn-info">عرض</a>
        <a href="{{ route("admin.accounts.edit", $account->id) }}" class="btn btn-sm btn-warning">تعديل</a>
        @if(!$account->childAccountsRecursive->count())
            <form action="{{ route("admin.accounts.destroy", $account->id) }}" method="POST" style="display:inline-block;">
                @csrf
                @method("DELETE")
                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">حذف</button>
            </form>
        @endif
    </li>
    @if ($account->childAccountsRecursive && $account->childAccountsRecursive->count() > 0)
        <ul>
            @include("admin.accounts._account_item_recursive", ["accounts" => $account->childAccountsRecursive, "level" => $level + 1])
        </ul>
    @endif
@endforeach

