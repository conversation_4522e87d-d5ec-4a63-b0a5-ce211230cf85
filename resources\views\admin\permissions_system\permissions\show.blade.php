@extends('layouts.admin')

@section('title', 'تفاصيل الصلاحية: {{ $permission->name }}')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">تفاصيل الصلاحية: {{ $permission->name }}</h3>
                    <div>
                        <a href="{{ route('admin.permissions_system.permissions.edit', $permission->id) }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> تعديل
                        </a>
                        <a href="{{ route('admin.permissions_system.permissions.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>معلومات الصلاحية</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">المعرف</th>
                                            <td>{{ $permission->id }}</td>
                                        </tr>
                                        <tr>
                                            <th>الاسم</th>
                                            <td>{{ $permission->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>المعرف (Slug)</th>
                                            <td>{{ $permission->slug }}</td>
                                        </tr>
                                        <tr>
                                            <th>المجموعة</th>
                                            <td>{{ $permission->group_name ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>الوحدة</th>
                                            <td>{{ $permission->module ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>الوصف</th>
                                            <td>{{ $permission->description ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $permission->created_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ التحديث</th>
                                            <td>{{ $permission->updated_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>الأدوار التي تملك هذه الصلاحية</h5>
                                </div>
                                <div class="card-body">
                                    @if($permission->roles->count() > 0)
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم الدور</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($permission->roles as $role)
                                                    <tr>
                                                        <td>{{ $role->id }}</td>
                                                        <td>{{ $role->display_name ?? $role->name }}</td>
                                                        <td>
                                                            <a href="{{ route('admin.permissions_system.roles.show', $role->id) }}" class="btn btn-sm btn-info">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    @else
                                        <div class="alert alert-info">
                                            لا توجد أدوار تملك هذه الصلاحية حاليًا.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
