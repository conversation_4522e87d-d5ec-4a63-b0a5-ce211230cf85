@extends('layouts.admin')

@section('title', 'إدارة أجهزة نقاط البيع')

@push('styles')
<style>
/* تعطيل DataTables في هذه الصفحة */
.dataTables_wrapper,
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    display: none !important;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">أجهزة نقاط البيع</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.pos.terminals.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة جهاز جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- حقل البحث البسيط -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في الأجهزة...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الجهاز</th>
                                    <th>الرمز</th>
                                    <th>الفرع</th>
                                    <th>عنوان IP</th>
                                    <th>نوع الجهاز</th>
                                    <th>الحالة</th>
                                    <th>آخر نشاط</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>جهاز نقطة البيع الرئيسي</td>
                                    <td>POS-001</td>
                                    <td>الفرع الرئيسي</td>
                                    <td>*************</td>
                                    <td>جهاز لوحي</td>
                                    <td><span class="badge badge-success">نشط</span></td>
                                    <td>منذ 5 دقائق</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.pos.terminals.show', 1) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.pos.terminals.edit', 1) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.pos.terminals.destroy', 1) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>جهاز نقطة البيع الثانوي</td>
                                    <td>POS-002</td>
                                    <td>الفرع الثاني</td>
                                    <td>192.168.1.101</td>
                                    <td>حاسوب مكتبي</td>
                                    <td><span class="badge badge-warning">غير متصل</span></td>
                                    <td>منذ ساعة</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.pos.terminals.show', 2) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.pos.terminals.edit', 2) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.pos.terminals.destroy', 2) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(function () {
        // تعطيل DataTables تماماً في صفحة أجهزة نقاط البيع
        console.log('تم تعطيل DataTables في صفحة أجهزة نقاط البيع');
        
        // منع تطبيق DataTables على أي جدول في هذه الصفحة
        if (window.jQuery && window.jQuery.fn) {
            // إعادة تعريف DataTables لتعطيلها
            window.jQuery.fn.DataTable = function() {
                console.log('DataTables تم منعه في صفحة أجهزة نقاط البيع');
                return this;
            };
            
            // منع dataTable أيضاً
            window.jQuery.fn.dataTable = function() {
                console.log('dataTable تم منعه في صفحة أجهزة نقاط البيع');
                return this;
            };
        }
        
        // إضافة وظائف بحث وترتيب بسيطة
        $('#searchInput').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.table tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
    });
</script>
@endpush
