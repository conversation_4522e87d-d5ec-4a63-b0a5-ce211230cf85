<?php

namespace App\Http\Controllers\Modules\Restaurant;

use App\Http\Controllers\Controller;
use App\Models\Modules\Restaurant\PosTerminal;
use App\Models\Modules\Restaurant\RestaurantTable;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PosTerminalController extends Controller
{
    public function index()
    {
        $posTerminals = PosTerminal::with(['branch', 'defaultTable'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->orderBy('name')
            ->paginate(15);

        return view('admin.restaurant.pos-terminals.index', compact('posTerminals'));
    }

    public function create()
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $tables = RestaurantTable::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('number')
            ->get();

        return view('admin.restaurant.pos-terminals.form', compact('branches', 'tables'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'terminal_id' => 'required|string|max:100|unique:pos_terminals,terminal_id',
            'location' => 'nullable|string|max:255',
            'type' => 'required|in:restaurant,retail,takeaway',
            'receipt_printer_ip' => 'nullable|ip',
            'receipt_printer_port' => 'nullable|integer|min:1|max:65535',
            'default_table_id' => 'nullable|exists:restaurant_tables,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();
        $validatedData['is_active'] = $request->has('is_active');

        PosTerminal::create($validatedData);

        return redirect()->route('admin.restaurant.pos-terminals.index')
            ->with('success', __('POS terminal created successfully.'));
    }

    public function show(PosTerminal $posTerminal)
    {
        $posTerminal->load(['branch', 'defaultTable']);
        return view('admin.restaurant.pos-terminals.show', compact('posTerminal'));
    }

    public function edit(PosTerminal $posTerminal)
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $tables = RestaurantTable::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('number')
            ->get();

        return view('admin.restaurant.pos-terminals.form', compact('posTerminal', 'branches', 'tables'));
    }

    public function update(Request $request, PosTerminal $posTerminal)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'terminal_id' => 'required|string|max:100|unique:pos_terminals,terminal_id,' . $posTerminal->id,
            'location' => 'nullable|string|max:255',
            'type' => 'required|in:restaurant,retail,takeaway',
            'receipt_printer_ip' => 'nullable|ip',
            'receipt_printer_port' => 'nullable|integer|min:1|max:65535',
            'default_table_id' => 'nullable|exists:restaurant_tables,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validatedData['is_active'] = $request->has('is_active');

        $posTerminal->update($validatedData);

        return redirect()->route('admin.restaurant.pos-terminals.index')
            ->with('success', __('POS terminal updated successfully.'));
    }

    public function destroy(PosTerminal $posTerminal)
    {
        $posTerminal->delete();

        return redirect()->route('admin.restaurant.pos-terminals.index')
            ->with('success', __('POS terminal deleted successfully.'));
    }

    public function testPrinter(PosTerminal $posTerminal)
    {
        $isConnected = $posTerminal->testReceiptPrinter();

        return response()->json([
            'success' => $isConnected,
            'message' => $isConnected 
                ? __('Receipt printer connection successful.') 
                : __('Receipt printer connection failed.'),
            'status' => $isConnected ? 'connected' : 'disconnected'
        ]);
    }
}
