<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

trait TenantScoped
{
    /**
     * Boot the trait.
     */
    public static function bootTenantScoped()
    {
        // تطبيق نطاق المستأجر على جميع الاستعلامات
        static::addGlobalScope('tenant', function (Builder $builder) {
            // تخطي النطاق إذا كان المستخدم مدير نظام
            if (Auth::check() && Auth::user()->hasRole('admin')) {
                return;
            }

            // الحصول على معرف المستأجر من الجلسة
            $tenantId = Session::get('tenant_id');
            
            if ($tenantId) {
                // تحديد العمود المستخدم للتصفية حسب المستأجر
                $tenantColumn = self::getTenantColumn();
                
                // تطبيق التصفية
                $builder->where($tenantColumn, $tenantId);
            }
        });

        // تعيين معرف المستأجر تلقائياً عند إنشاء سجل جديد
        static::creating(function ($model) {
            // تخطي التعيين إذا كان المستخدم مدير نظام
            if (Auth::check() && Auth::user()->hasRole('admin')) {
                return;
            }

            // الحصول على معرف المستأجر من الجلسة
            $tenantId = Session::get('tenant_id');
            
            if ($tenantId) {
                // تحديد العمود المستخدم لتخزين معرف المستأجر
                $tenantColumn = self::getTenantColumn();
                
                // تعيين معرف المستأجر إذا لم يكن معيناً بالفعل
                if (!$model->{$tenantColumn}) {
                    $model->{$tenantColumn} = $tenantId;
                }
            }
        });
    }

    /**
     * تحديد العمود المستخدم لتخزين معرف المستأجر.
     * يمكن تجاوز هذه الدالة في النماذج الفردية إذا كان اسم العمود مختلفاً.
     */
    public static function getTenantColumn()
    {
        return 'tenant_id';
    }
}
