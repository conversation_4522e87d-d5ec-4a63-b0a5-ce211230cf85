<?php $__env->startSection("content"); ?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><?php echo e(__("Ticket Statuses")); ?></h1>
        <a href="<?php echo e(route("admin.ticketing.ticket_statuses.create")); ?>" class="btn btn-primary"><?php echo e(__("Create New Status")); ?></a>
    </div>

    <?php if(session("success")): ?>
        <div class="alert alert-success">
            <?php echo e(session("success")); ?>

        </div>
    <?php endif; ?>

    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th><?php echo e(__("ID")); ?></th>
                <th><?php echo e(__("Name")); ?></th>
                <th><?php echo e(__("Type")); ?></th>
                <th><?php echo e(__("Color")); ?></th>
                <th><?php echo e(__("Default New")); ?></th>
                <th><?php echo e(__("Default Closed")); ?></th>
                <th><?php echo e(__("Active")); ?></th>
                <th><?php echo e(__("Actions")); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($status->id); ?></td>
                    <td><?php echo e($status->name); ?></td>
                    <td><?php echo e(ucfirst($status->type)); ?></td>
                    <td>
                        <span style="display:inline-block; width: 20px; height: 20px; background-color: <?php echo e($status->color ?? '#FFFFFF'); ?>; border: 1px solid #ccc;"></span>
                        <?php echo e($status->color); ?>

                    </td>
                    <td>
                        <?php if($status->is_default_new): ?>
                            <span class="badge badge-info"><?php echo e(__("Yes")); ?></span>
                        <?php else: ?>
                            <?php echo e(__("No")); ?>

                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if($status->is_default_closed): ?>
                            <span class="badge badge-secondary"><?php echo e(__("Yes")); ?></span>
                        <?php else: ?>
                            <?php echo e(__("No")); ?>

                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if($status->is_active): ?>
                            <span class="badge badge-success"><?php echo e(__("Yes")); ?></span>
                        <?php else: ?>
                            <span class="badge badge-danger"><?php echo e(__("No")); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a href="<?php echo e(route("admin.ticketing.ticket_statuses.show", $status->id)); ?>" class="btn btn-sm btn-info"><?php echo e(__("View")); ?></a>
                        <a href="<?php echo e(route("admin.ticketing.ticket_statuses.edit", $status->id)); ?>" class="btn btn-sm btn-warning"><?php echo e(__("Edit")); ?></a>
                        <form action="<?php echo e(route("admin.ticketing.ticket_statuses.destroy", $status->id)); ?>" method="POST" style="display: inline-block;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field("DELETE"); ?>
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm("<?php echo e(__("Are you sure you want to delete this status?")); ?>")"><?php echo e(__("Delete")); ?></button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="8" class="text-center"><?php echo e(__("No ticket statuses found.")); ?></td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
    <?php echo e($statuses->links()); ?>

</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make("layouts.admin", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\accounting_project_ERP\resources\views/admin/ticketing/statuses/index.blade.php ENDPATH**/ ?>