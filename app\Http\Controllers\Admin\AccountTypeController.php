<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AccountType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class AccountTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $accountTypes = AccountType::with("parent")->latest()->paginate(10);
        return view("admin.account_types.index", compact("accountTypes"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $parentAccountTypes = AccountType::where("is_active", true)->orderBy("name_" . app()->getLocale())->get();
        return view("admin.account_types.form", compact("parentAccountTypes"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:account_types,slug",
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "parent_id" => "nullable|exists:account_types,id",
            "is_primary" => "boolean",
            "is_active" => "boolean",
        ]);

        $validatedData["is_primary"] = $request->has("is_primary");
        $validatedData["is_active"] = $request->has("is_active");

        AccountType::create($validatedData);

        return redirect()->route("admin.account_types.index")->with("success", __("Account type created successfully."));
    }

    /**
     * Display the specified resource.
     */
    public function show(AccountType $accountType)
    {
        $accountType->load("parent");
        return view("admin.account_types.show", compact("accountType"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AccountType $accountType)
    {
        $parentAccountTypes = AccountType::where("is_active", true)
                                        ->where("id", "!=", $accountType->id) // Prevent selecting itself as parent
                                        ->orderBy("name_" . app()->getLocale())->get();
        return view("admin.account_types.form", compact("accountType", "parentAccountTypes"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AccountType $accountType)
    {
        $validatedData = $request->validate([
            "name_ar" => "required|string|max:255",
            "name_en" => "required|string|max:255",
            "slug" => "required|string|max:255|unique:account_types,slug," . $accountType->id,
            "description_ar" => "nullable|string",
            "description_en" => "nullable|string",
            "parent_id" => "nullable|exists:account_types,id",
            "is_primary" => "boolean",
            "is_active" => "boolean",
        ]);

        $validatedData["is_primary"] = $request->has("is_primary");
        $validatedData["is_active"] = $request->has("is_active");
        
        // Prevent setting parent_id to itself or a child to prevent infinite loops
        if ($request->parent_id == $accountType->id) {
            return redirect()->back()->withErrors(["parent_id" => __("Cannot set an account type as its own parent.")])->withInput();
        }
        // Add more complex cycle detection if needed

        $accountType->update($validatedData);

        return redirect()->route("admin.account_types.index")->with("success", __("Account type updated successfully."));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AccountType $accountType)
    {
        // Check if the account type has child account types
        if ($accountType->children()->count() > 0) {
            return redirect()->route("admin.account_types.index")->with("error", __("Cannot delete this account type as it has child account types."));
        }
        // Check if the account type is assigned to any accounts
        if ($accountType->accounts()->count() > 0) {
            return redirect()->route("admin.account_types.index")->with("error", __("Cannot delete this account type as it is assigned to one or more accounts."));
        }

        try {
            $accountType->delete();
            return redirect()->route("admin.account_types.index")->with("success", __("Account type deleted successfully."));
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.account_types.index")->with("error", __("Could not delete account type. A database error occurred."));
        }
    }
}

