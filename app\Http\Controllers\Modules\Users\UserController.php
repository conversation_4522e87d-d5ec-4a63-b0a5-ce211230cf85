<?php

namespace App\Http\Controllers\Modules\Users;

use App\Http\Controllers\Controller;
use App\Models\User; // Corrected namespace for User model
use App\Models\Modules\Branches\Branch;
use App\Models\Modules\Users\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with(["branch", "roles"])->latest()->paginate(10);
        return view("admin.users.index", compact("users"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::where("is_active", true)->get();
        $roles = Role::all();
        return view("admin.users.create", compact("branches", "roles"));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            "name" => ["required", "string", "max:255"],
            "email" => ["required", "string", "email", "max:255", "unique:".User::class],
            "password" => ["required", "confirmed", Rules\Password::defaults()],
            "branch_id" => ["nullable", "exists:branches,id"],
            "roles" => ["nullable", "array"],
            "roles.*" => ["exists:roles,id"],
        ]);

        $user = User::create([
            "name" => $request->name,
            "email" => $request->email,
            "password" => Hash::make($request->password),
            "branch_id" => $request->branch_id,
        ]);

        if ($request->has("roles")) {
            $user->roles()->sync($request->roles);
        }

        return redirect()->route("admin.users.index")
                         ->with("success", "User created successfully.");
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(["branch", "roles"]);
        return view("admin.users.show", compact("user"));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $branches = Branch::where("is_active", true)->get();
        $roles = Role::all();
        $user->load("roles");
        return view("admin.users.edit", compact("user", "branches", "roles"));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            "name" => ["required", "string", "max:255"],
            "email" => ["required", "string", "email", "max:255", "unique:".User::class.",email,".$user->id],
            "password" => ["nullable", "confirmed", Rules\Password::defaults()],
            "branch_id" => ["nullable", "exists:branches,id"],
            "roles" => ["nullable", "array"],
            "roles.*" => ["exists:roles,id"],
        ]);

        $userData = [
            "name" => $request->name,
            "email" => $request->email,
            "branch_id" => $request->branch_id,
        ];

        if ($request->filled("password")) {
            $userData["password"] = Hash::make($request->password);
        }

        $user->update($userData);

        $user->roles()->sync($request->roles ?? []);

        return redirect()->route("admin.users.index")
                         ->with("success", "User updated successfully.");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Add logic here to check if user can be deleted (e.g., not the last admin, no critical associated data)
        // For now, a simple delete
        try {
            $user->delete();
            return redirect()->route("admin.users.index")
                             ->with("success", "User deleted successfully.");
        } catch (\Illuminate\Database\QueryException $e) {
            return redirect()->route("admin.users.index")
                             ->with("error", "Could not delete user. It might be associated with other records.");
        }
    }
}

