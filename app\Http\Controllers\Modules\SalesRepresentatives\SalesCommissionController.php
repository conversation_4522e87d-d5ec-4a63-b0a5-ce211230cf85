<?php

namespace App\Http\Controllers\Modules\SalesRepresentatives;

use App\Http\Controllers\Controller;
use App\Models\Modules\SalesRepresentatives\SalesCommission;
use App\Models\Modules\SalesRepresentatives\SalesRepresentative;
use App\Models\Modules\Sales\Customer;
use App\Models\Modules\Branches\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SalesCommissionController extends Controller
{
    public function index()
    {
        $commissions = SalesCommission::with(['salesRepresentative', 'customer', 'approvedBy'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->ordered()
            ->paginate(15);

        return view('admin.sales-representatives.commissions.index', compact('commissions'));
    }

    public function create()
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.commissions.form', compact('representatives', 'customers', 'branches'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'customer_id' => 'required|exists:customers,id',
            'commission_type' => 'required|in:sales,collection,target_bonus,special_bonus',
            'transaction_date' => 'required|date',
            'base_amount' => 'required|numeric|min:0',
            'commission_rate' => 'required|numeric|min:0',
            'commission_amount' => 'required|numeric|min:0',
            'additional_bonus' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'calculation_method' => 'required|in:percentage,fixed,tiered,custom',
            'commission_notes' => 'nullable|string',
            'period_month' => 'nullable|integer|min:1|max:12',
            'period_year' => 'nullable|integer|min:2020',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['commission_code'] = SalesCommission::generateCommissionCode();
        $validatedData['net_commission'] = $validatedData['commission_amount'] + ($validatedData['additional_bonus'] ?? 0) - ($validatedData['deductions'] ?? 0);
        $validatedData['payment_status'] = 'pending';
        $validatedData['tenant_id'] = Auth::user()->tenant_id ?? Auth::id();

        SalesCommission::create($validatedData);

        return redirect()->route('admin.sales-representatives.commissions.index')
            ->with('success', __('Sales commission created successfully.'));
    }

    public function show(SalesCommission $commission)
    {
        $commission->load(['salesRepresentative', 'customer', 'order', 'invoice', 'approvedBy', 'branch']);

        return view('admin.sales-representatives.commissions.show', compact('commission'));
    }

    public function edit(SalesCommission $commission)
    {
        if ($commission->payment_status === 'paid') {
            return redirect()->route('admin.sales-representatives.commissions.index')
                ->with('error', __('Cannot edit paid commission.'));
        }

        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->ordered()
            ->get();

        $customers = Customer::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $branches = Branch::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.sales-representatives.commissions.form', compact('commission', 'representatives', 'customers', 'branches'));
    }

    public function update(Request $request, SalesCommission $commission)
    {
        if ($commission->payment_status === 'paid') {
            return redirect()->route('admin.sales-representatives.commissions.index')
                ->with('error', __('Cannot update paid commission.'));
        }

        $validatedData = $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'customer_id' => 'required|exists:customers,id',
            'commission_type' => 'required|in:sales,collection,target_bonus,special_bonus',
            'transaction_date' => 'required|date',
            'base_amount' => 'required|numeric|min:0',
            'commission_rate' => 'required|numeric|min:0',
            'commission_amount' => 'required|numeric|min:0',
            'additional_bonus' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'calculation_method' => 'required|in:percentage,fixed,tiered,custom',
            'commission_notes' => 'nullable|string',
            'period_month' => 'nullable|integer|min:1|max:12',
            'period_year' => 'nullable|integer|min:2020',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $validatedData['net_commission'] = $validatedData['commission_amount'] + ($validatedData['additional_bonus'] ?? 0) - ($validatedData['deductions'] ?? 0);

        $commission->update($validatedData);

        return redirect()->route('admin.sales-representatives.commissions.index')
            ->with('success', __('Sales commission updated successfully.'));
    }

    public function destroy(SalesCommission $commission)
    {
        if ($commission->payment_status === 'paid') {
            return redirect()->route('admin.sales-representatives.commissions.index')
                ->with('error', __('Cannot delete paid commission.'));
        }

        $commission->delete();

        return redirect()->route('admin.sales-representatives.commissions.index')
            ->with('success', __('Sales commission deleted successfully.'));
    }

    public function approve(Request $request, SalesCommission $commission)
    {
        if ($commission->payment_status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => __('Commission is not pending approval.')
            ], 422);
        }

        $commission->approve(Auth::user());

        return response()->json([
            'success' => true,
            'message' => __('Commission approved successfully.'),
            'status' => $commission->payment_status
        ]);
    }

    public function markAsPaid(Request $request, SalesCommission $commission)
    {
        $request->validate([
            'payment_reference' => 'required|string|max:255'
        ]);

        if ($commission->payment_status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => __('Commission must be approved before payment.')
            ], 422);
        }

        $commission->markAsPaid($request->payment_reference);

        return response()->json([
            'success' => true,
            'message' => __('Commission marked as paid successfully.'),
            'status' => $commission->payment_status
        ]);
    }

    public function bulkApprove(Request $request)
    {
        $request->validate([
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'exists:sales_commissions,id'
        ]);

        $commissions = SalesCommission::whereIn('id', $request->commission_ids)
            ->where('payment_status', 'pending')
            ->get();

        foreach ($commissions as $commission) {
            $commission->approve(Auth::user());
        }

        return response()->json([
            'success' => true,
            'message' => __('Commissions approved successfully.'),
            'count' => $commissions->count()
        ]);
    }

    public function calculate(Request $request)
    {
        $representatives = SalesRepresentative::where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('is_active', true)
            ->where('status', 'active')
            ->with(['salesCommissions' => function($query) {
                $query->whereBetween('transaction_date', [now()->startOfMonth(), now()->endOfMonth()]);
            }])
            ->get();

        $calculationResults = [];

        foreach ($representatives as $representative) {
            $monthlyCommissions = $representative->salesCommissions;
            $totalCommission = $monthlyCommissions->sum('net_commission');
            $pendingCommission = $monthlyCommissions->where('payment_status', 'pending')->sum('net_commission');
            $approvedCommission = $monthlyCommissions->where('payment_status', 'approved')->sum('net_commission');
            $paidCommission = $monthlyCommissions->where('payment_status', 'paid')->sum('net_commission');

            $calculationResults[] = [
                'representative' => $representative,
                'total_commission' => $totalCommission,
                'pending_commission' => $pendingCommission,
                'approved_commission' => $approvedCommission,
                'paid_commission' => $paidCommission,
                'commission_count' => $monthlyCommissions->count(),
            ];
        }

        return view('admin.sales-representatives.commissions.calculate', compact('calculationResults'));
    }

    public function getCommissionsByRepresentative(Request $request)
    {
        $representativeId = $request->get('representative_id');
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $commissions = SalesCommission::with(['customer'])
            ->where('tenant_id', Auth::user()->tenant_id ?? Auth::id())
            ->where('sales_representative_id', $representativeId)
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $commissions
        ]);
    }

    public function calculateCommissionAmount(Request $request)
    {
        $request->validate([
            'sales_representative_id' => 'required|exists:sales_representatives,id',
            'base_amount' => 'required|numeric|min:0',
            'commission_type' => 'required|in:sales,collection,target_bonus,special_bonus'
        ]);

        $representative = SalesRepresentative::find($request->sales_representative_id);
        $calculation = SalesCommission::calculateCommission(
            $representative, 
            $request->base_amount, 
            $request->commission_type
        );

        return response()->json([
            'success' => true,
            'data' => $calculation
        ]);
    }
}
